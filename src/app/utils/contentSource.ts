// Libraries
import { cloneDeep, pick } from 'lodash';

/**
 * Processes content source filters by removing specific value types and keeping only relevant properties.
 *
 * @param {Object} contentSourceGroups - The collection of content source groups
 * @param {number|string} groupId - The ID of the group to process filters for
 * @returns {Object} The processed filters object
 */
export function processContentSourceFilters({ filters }) {
  const draftFilters = cloneDeep(filters);

  // Process filter conditions for business object queries
  if (draftFilters?.OR) {
    draftFilters.OR = draftFilters.OR.map(orItem => {
      if (orItem.AND) {
        // Process AND conditions within each OR group
        orItem.AND = orItem.AND.map(andItem => {
          // Extract metadata fields from the condition
          const { value_type } = andItem || {};

          // Skip conditions if value_type is customer, visitor, or event
          if (['customer', 'visitor', 'event'].includes(value_type)) {
            return null;
          }

          // Keep only relevant filter properties for the API call
          return pick(andItem, [
            'operator',
            'data_type',
            'column',
            'condition_type',
            'value_type',
            'value',
            'time_unit',
          ]);
        }).filter(Boolean); // Remove null entries
      }
      return orItem;
    });
  }

  return draftFilters;
}
