// Services
import { SavedImage } from 'app/models/SavedImage';
import { services } from 'app/services';

// Types
import type { MediaType } from 'app/components/molecules/UploadImage';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const getListingSavedMedia = async ({ type = 1 }: { type: MediaType }): Promise<SavedImage[]> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/saved-media/index?type=${type}`,
    });

    let savedImages = data?.data || [];

    savedImages = savedImages.map(savedImage => new SavedImage(savedImage));

    return savedImages;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListingSavedMediaPagination = async ({
  type = 1,
  page = 1,
}: {
  type: MediaType;
  page: number;
}): Promise<SavedImage[]> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/saved-media/index?type=${type}&page=${page}&limit=10`,
    });

    let savedImages = data?.data || [];

    savedImages = savedImages.map(savedImage => new SavedImage(savedImage));

    return savedImages;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const createSavedMedia = data => {
  try {
    return services.create({
      API_HOST: `${APP_CONFIG.API_URL}/saved-media/index`,
      ...data,
    });
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const deleteSavedMedia = id => {
  try {
    return services.create({
      API_HOST: `${APP_CONFIG.API_URL}/saved-media/index/update-status`,
      media_id: [id],
      status: 80,
    });
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getImageExternal = async (url: string): Promise<string> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/saved-image/external-url`,
      imageUrl: url,
    });

    return data?.data || '';
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
