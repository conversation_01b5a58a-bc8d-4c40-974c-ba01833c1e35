import { PermissionInstance } from 'app/services';
import { CDPResponseData } from 'types';

export const permissionServices = {
  getUserInfo: async (email: string, userId: string | number) => {
    const type = 'search-user-info';
    const res = await PermissionInstance.get<CDPResponseData>('api/account/info', {
      params: {
        type,
        email,
        userId,
      },
    });

    return res.data.data;
  },
};
