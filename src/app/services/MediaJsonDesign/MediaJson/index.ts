// Libraries
import get from 'lodash/get';

// Services
import { MediaTemplate } from 'app/models/MediaTemplate';
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const createMedia = async data => {
  try {
    const response = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/media-json/index`,
      ...data,
    });

    return response && response.data ? response.data.data : null;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const updateMedia = async data => {
  try {
    const response = await services.update({
      API_HOST: `${APP_CONFIG.API_URL}/media-json/index`,
      isGzip: true,
      ...data,
    });

    return response && response.data ? response.data.data : null;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const updateListMediaStatus = async data => {
  try {
    const response = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/media-template/index/update-status`,
      ...data,
    });

    return response && response.data ? response.data.data : null;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getMedia = async (id): Promise<MediaTemplate | null> => {
  try {
    const response = await services.get({
      API_HOST: `${APP_CONFIG.API_URL}/media-json/index`,
      id,
    });

    const mediaJsonData = response && response.data ? response.data.data : null;

    if (mediaJsonData) {
      return new MediaTemplate(mediaJsonData);
    }

    return null;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const validateMediaName = async name => {
  try {
    const response = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/media-json/validate/check-name`,
      template_name: name,
    });

    return get(response, 'data.data', null);
  } catch (error) {
    const message = get(error, 'response.data.data', 'Request failed');

    return new Promise((_, reject) => reject(new Error(message)));
  }
};

export const getDefaultName = async () => {
  try {
    const response = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/media-json/get-default-name`,
    });

    return get(response, 'data.data', {});
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
