// Model
import { ListSource, ListEvent, ListAttribute } from 'app/models/BusinesObject';

// Services
import { services } from 'app/services';

import {
  AlgorithmBO,
  Event,
  BusinessObject,
  CollectionBO,
  FilterBO,
  AttributeBO,
  Source,
  AttributeEvent,
  SuggestionItem,
  SuggestionObject,
  InfoMetadata,
} from 'app/models/BusinessObject';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { uniqBy } from 'lodash';

export const getListSources = async (): Promise<ListSource[]> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/source`,
    });

    let listSources = data.data || [];

    // listSources = listSources.map(listSource => new ListSource(listSource));
    // console.log(listSources)
    return listSources;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const getListEvents = async ({ source_id }): Promise<any[]> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/event-in-source/${source_id}`,
    });

    let listEvents = data.data || [];

    // listSources = listSources.map(listSource => new ListSource(listSource));
    // console.log(listSources)
    return listEvents;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const getListAttributes = async (params): Promise<ListAttribute[]> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/event-attribute`,
      ...params,
    });

    let listAttribute = data.data || [];

    return uniqBy<ListAttribute>(listAttribute, 'eventPropertyName');
  } catch (error) {
    return Promise.reject(error);
  }
};

export const getListBusinessObject = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/list-bo`,
    });

    let businessObjects = data?.data || [];

    businessObjects = Array.isArray(businessObjects) ? businessObjects.map(object => new BusinessObject(object)) : [];

    return {
      rows: businessObjects,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListAttributesBO = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/bo-attribute/${[...params.itemTypeIds]}`,
    });
    let attributesBO = data?.data || [];
    attributesBO = Array.isArray(attributesBO) ? attributesBO.map(object => new AttributeBO(object)) : [];
    return {
      rows: attributesBO,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListAlgorithmsBO = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/list-algorithms-bo`,
    });

    let algorithmsBO = data?.data || [];

    algorithmsBO = Array.isArray(algorithmsBO) ? algorithmsBO.map(object => new AlgorithmBO(object)) : [];

    return {
      rows: algorithmsBO,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListFiltersBO = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/list-filter-bo`,
    });

    let filtersBO = data?.data || [];

    filtersBO = Array.isArray(filtersBO) ? filtersBO.map(object => new FilterBO(object)) : [];

    return {
      rows: filtersBO,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListCollectionsBO = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/data-collection/${params.itemTypeId}`,
    });

    let collectionsBO = data?.data || [];

    collectionsBO = Array.isArray(collectionsBO) ? collectionsBO.map(object => new CollectionBO(object)) : [];

    return {
      rows: collectionsBO,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListAllEvents = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/all-event-tracking`,
    });

    let allEvents = data?.data || [];

    allEvents = Array.isArray(allEvents) ? allEvents.map(object => new Event(object)) : [];

    return {
      rows: allEvents,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListSourceByEvent = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/source-by-event?eventCategoryId=${params.eventCategoryId}&eventActionId=${params.eventActionId}`,
    });

    let sourceByEvent = data?.data || [];

    sourceByEvent = Array.isArray(sourceByEvent) ? sourceByEvent.map(object => new Source(object)) : [];

    return {
      rows: sourceByEvent,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListAttributesEvent = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/event-attribute?sourceId=${params.sourceId}&eventCategoryId=${params.eventCategoryId}&eventActionId=${params.eventActionId}`,
      signal: params.signal,
    });

    let attrEvent = data?.data || [];

    attrEvent = Array.isArray(attrEvent) ? attrEvent.map(object => new AttributeEvent(object)) : [];

    return {
      rows: attrEvent,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListSuggestionItems = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/suggestion-item`,
      ...params,
    });

    let suggestionItems = data?.data || [];

    suggestionItems = Array.isArray(suggestionItems) ? suggestionItems.map(object => new SuggestionItem(object)) : [];

    return {
      rows: suggestionItems,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListSuggestionObjects = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/suggestion-object`,
      ...params,
    });

    let suggestionObjects = data?.data || [];

    suggestionObjects = Array.isArray(suggestionObjects)
      ? suggestionObjects.map(object => new SuggestionObject(object))
      : [];

    return {
      rows: suggestionObjects,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListInfoMetadata = async (params?): Promise<Record<string, any>> => {
  try {
    const { data } = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/info-metadata`,
      ...params,
    });

    let infoMetadatas = data?.data || [];

    infoMetadatas = Array.isArray(infoMetadatas) ? infoMetadatas.map(object => new InfoMetadata(object)) : [];

    return {
      rows: infoMetadatas,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
