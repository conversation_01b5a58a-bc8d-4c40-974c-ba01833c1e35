// Services
import { Upload } from 'app/models/Upload';
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const uploadFile = async (files): Promise<Upload[]> => {
  try {
    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    const result = await services.upload({
      API_HOST: `${APP_CONFIG.API_URL}/file-upload/file`,
      formData,
    });

    const uploadData = result ? result.data?.data : null;

    return uploadData?.length
      ? uploadData.map(
          file =>
            new Upload({
              url: file.url,
              file: file.file,
              extension: file.extension,
              dimensions_file: file.dimensions_file,
              size: file.size,
              file_name: file.file_name,
            }),
        )
      : [];
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
