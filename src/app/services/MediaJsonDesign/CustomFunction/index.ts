// Libraries
import get from 'lodash/get';

// Services
import { services } from 'app/services';

// Models
import { MediaJsonCustomFunction } from 'app/models';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { serializeLabelToCode } from 'app/utils/common';

export const getListingTemplateCustomFunction = async (): Promise<any[]> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/custom-function`,
    });
    let customFunction = get(data, 'data.entries', []);

    // listSources = listSources.map(listSource => new ListSource(listSource));
    // console.log(listSources)
    return customFunction;
  } catch (error) {
    return Promise.reject(error);
  }
};
export const createSavedCustomFunction = async (payload: Partial<any>): Promise<any> => {
  try {
    const { displayFormat, dataFormat, name, dataType, functionCustom } = payload;
    let outputFormat = mapToAPI(displayFormat, dataFormat);
    const { data = {} } = await services.create({
      ...{
        templateCode: serializeLabelToCode(name),
        templateType: 'custom',
        templateName: name,
        customFunction: functionCustom,
        outputDataType: dataType,
        outputFormat,
      },
      API_HOST: `${APP_CONFIG.API_URL}/third-party/custom-function`,
    });
    return data.code;
  } catch (error) {
    return Promise.reject(error);
  }
};
export const updateSavedCustomFunction = async (payload: Partial<any>): Promise<any> => {
  try {
    const { displayFormat, dataFormat, name, dataType, functionCustom, templateId } = payload;
    let outputFormat = mapToAPI(displayFormat, dataFormat);
    const { data = {} } = await services.update({
      ...{
        id: templateId,
        templateCode: serializeLabelToCode(name),
        templateType: 'custom',
        templateName: name,
        customFunction: functionCustom,
        outputDataType: dataType,
        outputFormat,
      },
      API_HOST: `${APP_CONFIG.API_URL}/third-party/custom-function`,
    });

    return data.code;
  } catch (error) {
    return Promise.reject(error);
  }
};
// export const getSavedBlockDetail = async (savedBlockId: number): Promise<any> => {
//   const response = await services.get({
//     API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index`,
//     id: savedBlockId,
//   });

//   const savedBlock = get(response, 'data.data', null);

//   return !!savedBlock ? new MediaJsonCustomFunction(savedBlock).toJson() : null;
// };

// export const createSavedBlock = async (payload: Partial<MediaJsonCustomFunction>): Promise<any> => {
//   try {
//     const { data = {} } = await services.create({
//       ...{
//         block_name: payload.name,
//         block_type: payload.type,
//         notes: payload.notes,
//         properties: {
//           ...payload.settings,
//           ...(payload.children && { children: payload.children }),
//         },
//       },
//       API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index`,
//     });

//     return data.code;
//   } catch (error) {
//     return Promise.reject(error);
//   }
// };

// export const updateSavedBlock = async (payload: Partial<MediaJsonCustomFunction>): Promise<any> => {
//   try {
//     const { data = {} } = await services.update({
//       ...{
//         id: payload.id,
//         ...(payload.name && { block_name: payload.name }),
//         ...(payload.notes !== undefined && { notes: payload.notes }),
//         ...(payload.settings && {
//           properties: {
//             ...payload.settings,
//             ...(payload.children && { children: payload.children }),
//           },
//         }),
//       },
//       API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index`,
//     });

//     return data.code;
//   } catch (error) {
//     return Promise.reject(error);
//   }
// };

// export const deleteSavedBlock = async (payload: Partial<MediaJsonCustomFunction>): Promise<any> => {
//   try {
//     const { data = {} } = await services.create({
//       ...{
//         block_id: [payload.id],
//         status: 80,
//       },
//       API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index/update-status`,
//     });

//     return data.code;
//   } catch (error) {
//     return Promise.reject(error);
//   }
// };

export const validateSavedBlockName = async (savedBlockName: string) => {
  const { data = {} } = await services.getList({
    API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/validate/check-name`,
    block_name: savedBlockName,
  });

  return get(data, 'data', {});
};
export const mapToAPI = (displayFormat: any, dataFormat: any) => {
  let outputFormat = {};
  const regex = new RegExp(/\d\d/g);
  switch (displayFormat) {
    case 'currency': {
      outputFormat = {
        type: displayFormat.toUpperCase(),
        config: {
          group: dataFormat.grouping,
          decimal: dataFormat.decimal,
          decimalPlace: dataFormat.decimalPlaces,
          isCompactNumber: dataFormat.isCompact,
          currency: dataFormat.currencyCode,
          prefixType: dataFormat.prefixType,
        },
      };
      break;
    }
    case 'number':
    case 'percentage': {
      outputFormat = {
        type: displayFormat.toUpperCase(),
        config: {
          group: dataFormat.grouping,
          decimal: dataFormat.decimal,
          decimalPlace: dataFormat.decimalPlaces,
          isCompactNumber: dataFormat.isCompact,
        },
      };
      break;
    }
    case 'datetime': {
      outputFormat = {
        type: 'DATE_AND_TIME',
        config: {
          date: {
            check: dataFormat.hasDateFormat,
            value: dataFormat.dateParseOption,
          },
          time: {
            check: dataFormat.hasTimeFormat,
            value: dataFormat.timeParseOption,
            timeFormat: dataFormat.timeParseFormat.match(regex)[0].toString(),
          },
          format: dataFormat.dateParseFormat,
        },
      };

      break;
    }
    case 'raw_string': {
      outputFormat = {
        type: 'RAW_STRING',
        config: {},
      };

      break;
    }
    default:
      break;
  }
  return outputFormat;
};
