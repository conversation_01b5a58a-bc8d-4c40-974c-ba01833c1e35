// Model
import { ColorProfile } from 'app/models/ColorProfile';

// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

type TCreateColorProfilePayload = {
  label: string;
  values: string[];
};

export const getListingColorProfile = async (): Promise<ColorProfile[]> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/color-profile/index`,
    });

    let colorProfiles = data.data || [];

    colorProfiles = colorProfiles.map(colorProfile => new ColorProfile(colorProfile));

    return colorProfiles;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const createColorProfile = async (payload: TCreateColorProfilePayload): Promise<any> => {
  try {
    await services.create({
      ...{
        color_profile_name: payload.label,
        color_profile_values: payload.values,
      },
      API_HOST: `${APP_CONFIG.API_URL}/color-profile/index`,
    });
  } catch (error) {
    return Promise.reject(error);
  }
};

export const deleteColorProfile = async (colorProfileId: number): Promise<any> => {
  try {
    await services.create({
      ...{
        color_profile_id: [colorProfileId],
        status: 80,
      },
      API_HOST: `${APP_CONFIG.API_URL}/color-profile/index/update-status`,
    });
  } catch (error) {
    return Promise.reject(error);
  }
};
