// Libraries
import get from 'lodash/get';

// Services
import { services } from 'app/services';

// Models
import { MediaJsonSavedBlock } from 'app/models';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { QUERY_LIMIT_RESULTS } from 'constants/queries';

export const getListingSavedBlock = async ({
  pageParam = 1,
}): Promise<{ body: MediaJsonSavedBlock[]; total: number }> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index?page=${pageParam}&limit=${QUERY_LIMIT_RESULTS}`,
    });

    let savedBlocks = get(data, 'data.body', []);

    savedBlocks = savedBlocks.map(savedBlock => new MediaJsonSavedBlock(savedBlock));

    return { body: savedBlocks, total: get(data, 'data.total', 0) };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getSavedBlockDetail = async (savedBlockId: number): Promise<any> => {
  const response = await services.get({
    API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index`,
    id: savedBlockId,
  });

  const savedBlock = get(response, 'data.data', null);

  return !!savedBlock ? new MediaJsonSavedBlock(savedBlock).toJson() : null;
};

export const createSavedBlock = async (payload: Partial<MediaJsonSavedBlock>): Promise<any> => {
  try {
    const { data = {} } = await services.create({
      ...{
        block_name: payload.name,
        block_type: payload.type,
        notes: payload.notes,
        properties: {
          ...payload.settings,
          ...(payload.children && { children: payload.children }),
        },
      },
      API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index`,
    });

    return data.code;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const updateSavedBlock = async (payload: Partial<MediaJsonSavedBlock>): Promise<any> => {
  try {
    const { data = {} } = await services.update({
      ...{
        id: payload.id,
        ...(payload.name && { block_name: payload.name }),
        ...(payload.notes !== undefined && { notes: payload.notes }),
        ...(payload.settings && {
          properties: {
            ...payload.settings,
            ...(payload.children && { children: payload.children }),
          },
        }),
      },
      API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index`,
    });

    return data.code;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const deleteSavedBlock = async (payload: Partial<MediaJsonSavedBlock>): Promise<any> => {
  try {
    const { data = {} } = await services.create({
      ...{
        block_id: [payload.id],
        status: 80,
      },
      API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/index/update-status`,
    });

    return data.code;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const validateSavedBlockName = async (savedBlockName: string) => {
  const { data = {} } = await services.getList({
    API_HOST: `${APP_CONFIG.API_URL}/media-json/saved-block/validate/check-name`,
    block_name: savedBlockName,
  });

  return get(data, 'data', {});
};
