// Services
import { FileSave } from 'app/models/FileSave';
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const saveBase64File = async ({ data, name }): Promise<FileSave> => {
  try {
    const saveFileRes = services.create({
      API_HOST: `${APP_CONFIG.API_URL}/file-save/index`,
      data,
      name: name ? String(name) : undefined,
      type: 'base64-img',
    });

    if (saveFileRes) {
      return saveFileRes.then(res => {
        if (res?.data?.code === 200) {
          return new FileSave(res.data.data);
        }
        return new FileSave({});
      });
    }

    return new Promise((resolve, _) => resolve(new FileSave({})));
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
