// Services
import { PromotionPool } from 'app/models/PromotionPool';
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const getListPromotionPools = async (params): Promise<Record<string, any>> => {
  try {
    const { data } = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/promotion-pool/index`,
      ...params,
    });

    let promotionPools = data?.data?.entries || [];
    promotionPools = Array.isArray(promotionPools) ? promotionPools.map(object => new PromotionPool(object)) : [];

    return {
      rows: promotionPools,
      total: data?.data?.meta?.total || 0,
    };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
