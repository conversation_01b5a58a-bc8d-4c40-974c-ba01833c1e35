import { CDPInstance } from 'app/services';
import { CDPResponseData } from 'types';

// Request
type ValidateAliasIdData = {
  alias: {
    id: string;
    richMenuId: string;
  };
  authenToken: string;
};

// Response
type ValidateAliasIdResponse = {
  isAliasExist: boolean;
};

export const richMenuValidationService = {
  validateAliasId: async (data: ValidateAliasIdData) => {
    const response = await CDPInstance.post<CDPResponseData<ValidateAliasIdResponse>>(
      '/destinations/v2.1/destination-line/verify-alias-for-line-app',
      data,
    );

    return response.data.data;
  },
};
