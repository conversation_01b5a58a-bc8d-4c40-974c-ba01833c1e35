// Services
import { DynamicContentAttribute } from 'app/models/DynamicContentAttribute';
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const getDynamicContentAttributes = async (): Promise<DynamicContentAttribute> => {
  try {
    const { data = [] } = await services
      .getList({
        API_HOST: `${APP_CONFIG.API_URL}/third-party/personalize-attribute`,
      })
      .then(res => res?.data || {});

    if (Array.isArray(data)) {
      const customer = data.find(row => row.groupCode === 'customer');
      const visitor = data.find(row => row.groupCode === 'visitor');

      return new DynamicContentAttribute({
        customer: customer.properties.map(prop => ({
          value: prop.itemPropertyName,
          defaultLabel: prop.itemPropertyDisplay,
          label: prop.propertyDisplayMultilang,
          dataType: prop.dataType,
          status: prop.status,
          displayFormat: prop.displayFormat,
          ...(prop.autoSuggestion && {
            autoSuggestion: prop.autoSuggestion,
          }),
        })),
        visitor: visitor.properties.map(prop => ({
          value: prop.itemPropertyName,
          defaultLabel: prop.itemPropertyDisplay,
          label: prop.propertyDisplayMultilang,
          dataType: prop.dataType,
          status: prop.status,
          displayFormat: prop.displayFormat,
          ...(prop.autoSuggestion && {
            autoSuggestion: prop.autoSuggestion,
          }),
        })),
      });
    }

    throw new Error('Cannot get Personalize Attributes');
  } catch (error: any) {
    return new Promise((_, reject) => reject(error.message));
  }
};
