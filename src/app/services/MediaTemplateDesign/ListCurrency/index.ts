import { Currency } from './../../../models/Currency';
// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const getCurrencyList = async (lang: string): Promise<Currency[]> => {
  try {
    const { data = [] } = await services
      .getList({
        API_HOST: `${APP_CONFIG.API_URL}/third-party/list-currency`,
        _token: '5474r2x214r2c4d4v2e4y406r4o5t2i5j4c4l4m426',
        _user_id: '**********',
        _account_id: '**********',
        lang,
      })
      .then(res => res?.data || {});

    if (Array.isArray(data)) {
      return data.map(currency => new Currency(currency));
    }

    throw new Error('Cannot get Currency List');
  } catch (error: any) {
    return new Promise((_, reject) => reject(error.message));
  }
};
