// Libraries
import get from 'lodash/get';

// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

// Models
import { GalleryTemplate } from 'app/models/GalleryTemplate';

export const getListGalleryTemplate = async (
  params: Record<string, any>,
): Promise<{ body: GalleryTemplate[]; total: number }> => {
  try {
    const { data = {} } = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/gallery/performance`,
      ...params,
    });

    let galleryTemplates = get(data, 'data.body', []);

    galleryTemplates = galleryTemplates.map(galleryTemplate => new GalleryTemplate(galleryTemplate));

    return { body: galleryTemplates, total: get(data, 'data.total', 0) };
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const createGalleryTemplate = async (data: Partial<GalleryTemplate>): Promise<any> => {
  try {
    const response = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/gallery/index`,
      ...data,
    });

    return get(response, 'data.data', {});
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const updateGalleryTemplate = async (data: Partial<GalleryTemplate>): Promise<any> => {
  try {
    const response = await services.update({
      API_HOST: `${APP_CONFIG.API_URL}/gallery/index`,
      isGzip: true,
      ...data,
    });

    return get(response, 'data.data', {});
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const validateGalleryName = async (
  name: string,
): Promise<{
  existed: boolean;
  name: string;
}> => {
  try {
    const response = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/gallery/validate/check-name`,
      gallery_name: name,
    });

    return get(response, 'data.data', {});
  } catch (error) {
    const message = get(error, 'response.data.data', 'Request failed');

    return new Promise((_, reject) => reject(new Error(message)));
  }
};
