// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export const getListProvider = async (params): Promise<any> => {
  try {
    const res = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/destination/listing`,
      ...params,
    });
    const data = convertToCamelCase(res.data);
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getListVendorCreate = async (params): Promise<any> => {
  try {
    const res = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/catalog/listing`,
      ...params,
    });
    const data = convertToCamelCase(res.data);
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getDetailFieldVendor = async (params: any): Promise<any> => {
  try {
    const res = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/catalog/detail/${params.catalogId}`,
    });
    const data = convertToCamelCase(res.data);
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const getDetailProvider = async (params: any): Promise<any> => {
  try {
    const res = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/destination/detail/${params.destinationId}`,
    });
    const data = convertToCamelCase(res.data);
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const createProvider = async (params: any): Promise<any> => {
  try {
    const res = await services.create({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/destination/create`,
      ...params,
    });
    const data = convertToCamelCase(res.data);
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const updateProvider = async (params: any): Promise<any> => {
  try {
    const res = await services.update({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/destination/update`,
      ...params,
    });
    const data = convertToCamelCase(res.data);
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

export const deleteProvider = async (params: any): Promise<any> => {
  try {
    const res = await services.update({
      API_HOST: `${APP_CONFIG.API_URL}/destination-catalog/destination/update-status`,
      ...params,
    });
    const data = res.data;
    return data;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};

function convertToCamelCase(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertToCamelCase(item));
  }

  return Object.keys(obj).reduce((camelCaseObj, key) => {
    const camelCaseKey = toCamelCase(key);
    camelCaseObj[camelCaseKey] = convertToCamelCase(obj[key]);
    return camelCaseObj;
  }, {});
}

function toCamelCase(snakeCaseStr) {
  return snakeCaseStr.replace(/(_\w)/g, match => match[1].toUpperCase());
}
