import { getUserInfo } from 'app/components/templates/ListingPerformance/utils';
import { services } from '..';
// Constants
import { APP_CONFIG } from 'constants/appConfig';

const API = {
  user: {
    info: async (params): Promise<any> => {
      try {
        const userInfo = getUserInfo(APP_CONFIG.U_OGS);
        const { data = {} } = await services.getList({
          API_HOST: `${APP_CONFIG.AUTH_ADX_DOMAIN}api/3rd/info?type=user-info&userId=${userInfo.user_id}&networkId=${APP_CONFIG.NETWORK_ID}`,
          ...params,
        });

        return data;
      } catch (error) {
        return new Promise((_, reject) => reject(error));
      }
    },
  },
};

export default API;
