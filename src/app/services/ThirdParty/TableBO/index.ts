// Libraries
import get from 'lodash/get';

// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';

export interface TDataTableBO {
  header: any[];
  data: any[];
}

export interface TGetDataTableBOArgs {
  id: string | number;
  filters?: Record<string, any>;
}

export const getDataTableBO = async ({ id, filters }: TGetDataTableBOArgs): Promise<TDataTableBO> => {
  try {
    const response = await services.get({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/data-table-bo`,
      id,
      filters,
    });
    return get(response, 'data.data', null);
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
