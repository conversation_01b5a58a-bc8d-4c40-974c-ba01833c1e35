// Libraries
import get from 'lodash/get';

// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { FallbackBO } from 'app/models';

export const getListFallbackBO = async (): Promise<any[] | null> => {
  try {
    const response = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/list-fallback-bo`,
    });

    const listFallbackBO = get(response, 'data.data', []);

    return listFallbackBO.map(fallbackBo => new FallbackBO(fallbackBo));
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
