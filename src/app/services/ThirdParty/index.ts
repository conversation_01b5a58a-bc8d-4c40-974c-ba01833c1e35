// Libraries
import get from 'lodash/get';

// Services
import { services } from 'app/services';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { EventTrackingAttribute } from 'app/models/EventTrackingAttribute';

export interface TEventTrackingAttributesPayload {
  eventCategoryId: number;
  eventActionId: number;
}

export const getEventTrackingAttributes = async (
  payload: TEventTrackingAttributesPayload,
): Promise<EventTrackingAttribute> => {
  try {
    const response = await services.getList({
      API_HOST: `${APP_CONFIG.API_URL}/third-party/event-tracking-attributes`,
      ...payload,
    });

    const eventTrackingAttributeData = new EventTrackingAttribute(get(response, 'data.data', {}));

    return eventTrackingAttributeData;
  } catch (error) {
    return new Promise((_, reject) => reject(error));
  }
};
