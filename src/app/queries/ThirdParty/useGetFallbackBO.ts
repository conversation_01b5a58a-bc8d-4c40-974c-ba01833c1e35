// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Slice

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListFallbackBO } from 'app/services/ThirdParty/ListFallbackBO';

// Models
import { FallbackBO } from 'app/models';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetDataTableBOProps<T> = {
  options?: Omit<UseQueryOptions<any, any, T, any[]>, OptionHasDefault>;
};

export const useGetListFallbackBO = <T = FallbackBO[]>(props: GetDataTableBOProps<T>) => {
  const { options } = props || {};

  // Selectors
  return useQuery({
    queryKey: [QUERY_KEYS.GET_LIST_FALLBACK_BO],
    queryFn: () => getListFallbackBO(),
    placeholderData: [],
    ...options,
  });
};
