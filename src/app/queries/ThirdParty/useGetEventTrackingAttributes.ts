// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Slice

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getEventTrackingAttributes } from 'app/services/ThirdParty';

// Models
import { EventTrackingAttribute } from 'app/models/EventTrackingAttribute';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetDataTableBOProps<T> = {
  eventCategoryId: number;
  eventActionId: number;
  options?: Omit<UseQueryOptions<any, any, T, any[]>, OptionHasDefault>;
};

export const useGetEventTrackingAttributes = <T = EventTrackingAttribute>(props: GetDataTableBOProps<T>) => {
  const { options, eventCategoryId, eventActionId } = props || {};

  // Selectors
  return useQuery({
    queryKey: [QUERY_KEYS.GET_EVENT_TRACKING_ATTRIBUTES, eventActionId, eventCategoryId],
    queryFn: () => getEventTrackingAttributes({ eventActionId, eventCategoryId }),
    enabled: !!eventCategoryId && !!eventActionId,
    placeholderData: {
      eventAttributes: [],
      foreignObjects: [],
      mainObjects: [],
    },
    ...options,
  });
};
