// Libraries
import { useMutation } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { createSavedMedia } from 'app/services/Media/SavedImage';

// Query client
import { queryClient } from 'index';

export const useAddSavedMedia = () => {
  return useMutation(createSavedMedia, {
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVED_IMAGES], {
        exact: true,
      });
    },
  });
};
