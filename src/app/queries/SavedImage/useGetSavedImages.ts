// Libraries
import { useInfiniteQuery, useQuery, UseQueryOptions } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListingSavedMedia, getListingSavedMediaPagination } from 'app/services/Media/SavedImage';

// Models
import { SavedImage } from 'app/models';

// Types
import type { MediaType } from 'app/components/molecules/UploadImage';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetSavedImagesProps<T = SavedImage[]> = {
  type?: MediaType;
  options?: Omit<UseQueryOptions<any, any, T, (string | number)[]>, OptionHasDefault>;
};

export const useGetSavedMedia = <T = SavedImage[]>(props?: GetSavedImagesProps<T>) => {
  const { options, type = 1 } = props || {};

  return useQuery<SavedImage[], any, T, (string | number)[]>({
    queryKey: [QUERY_KEYS.GET_SAVED_IMAGES],
    queryFn: () => getListingSavedMedia({ type }),
    placeholderData: [],
    ...options,
  });
};

export const useStoreSavedMedia = <T = SavedImage[]>(props?: GetSavedImagesProps<T>) => {
  const { options, type = 1 } = props || {};

  return useInfiniteQuery<SavedImage[], any, T, any>({
    queryKey: [QUERY_KEYS.GET_SAVED_IMAGES_PAGINATION, options, type],
    queryFn: ({ pageParam }) => getListingSavedMediaPagination({ type, page: pageParam }),
    placeholderData: [],
    getNextPageParam: (lastPage, pages) => {
      return lastPage?.length >= 10 ? pages?.length + 1 : undefined;
    },
    ...options,
  });
};
