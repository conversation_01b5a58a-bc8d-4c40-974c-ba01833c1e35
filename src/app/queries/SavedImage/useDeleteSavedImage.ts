// Libraries
import { useMutation } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { deleteSavedMedia } from 'app/services/Media/SavedImage';

// Query client
import { queryClient } from 'index';

export const useDeleteSavedMedia = () => {
  return useMutation(deleteSavedMedia, {
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVED_IMAGES], {
        exact: true,
      });
    },
  });
};
