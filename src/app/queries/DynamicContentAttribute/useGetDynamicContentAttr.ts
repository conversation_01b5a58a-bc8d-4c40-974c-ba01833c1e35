import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { QUERY_KEYS } from 'constants/queries';
import { getDynamicContentAttributes } from 'app/services/MediaTemplateDesign/DynamicContentAttribute';
import { DynamicContentAttribute } from 'app/models';

interface TData {
  visitor: DynamicContentAttribute['visitor'];
  customer: DynamicContentAttribute['customer'];
}

export const useGetDynamicContentAttr = <T = TData>(
  queryOptions?: Omit<UseQueryOptions<TData, any, T>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery<TData, any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_DYNAMIC_CONTENT_ATTR],
    queryFn: async () => {
      const data = await getDynamicContentAttributes();

      return {
        visitor: data.visitor,
        customer: data.customer,
      };
    },
    ...queryOptions,
  });
};
