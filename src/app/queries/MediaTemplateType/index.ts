// Libraries
import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { MediaTemplateType } from 'app/models';
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListMediaTemplateTypes } from 'app/services/MediaTemplateDesign/MediaTemplateType';

export type TGetListMediaTemplateTypes<T = MediaTemplateType[]> = {
  options: UseQueryOptions<MediaTemplateType[], any, T, any>;
};

export function useGetListMediaTemplateTypes<T = MediaTemplateType[]>(params?: TGetListMediaTemplateTypes<T>) {
  return useQuery({
    queryKey: [QUERY_KEYS.GET_LIST_MEDIA_TEMPLATE_TYPE],
    queryFn: () => getListMediaTemplateTypes(),
    ...params?.options,
  });
}
