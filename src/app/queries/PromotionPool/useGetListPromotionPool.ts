import { getListPromotionPools } from 'app/services/MediaTemplateDesign/PromotionPool';
import { QUERY_KEYS } from 'constants/queries';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

interface TReturnData {
  rows: any[];
  total: number;
}

export const useGetListPromotionPool = <T = TReturnData>(
  queryOptions?: Omit<UseQueryOptions<TReturnData, any, T>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery<TReturnData, any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_PROMOTION_POOL],
    queryFn: async () => {
      const data = await getListPromotionPools({
        limit: 2000,
        page: 1,
        sd: 'asc',
        search: '',
        decryptFields: [],
        feServices: 'suggestionMultilang',
        feKey: '10-pool_name',
        objectType: 'PROMOTION_POOL',
        sort: 'asc',
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'process_status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [2],
                },
                {
                  type: 1,
                  column: 'status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [1],
                },
              ],
            },
          ],
        },
      });

      let result: TReturnData = {
        rows: [],
        total: 0,
      };

      if (data.rows.length) {
        result = {
          rows: data.rows,
          total: data.total,
        };
      }

      return result;
    },
    ...queryOptions,
  });
};
