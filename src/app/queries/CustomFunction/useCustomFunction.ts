// Libraries
import { UseQueryOptions, useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';

// Services
import {
  getListingTemplateCustomFunction,
  createSavedCustomFunction,
  updateSavedCustomFunction,
} from 'app/services/MediaJsonDesign/CustomFunction';

// Models
import { MediaJsonCustomFunction } from 'app/models';

// Constants
import { QUERY_KEYS, QUERY_LIMIT_RESULTS } from 'constants/queries';
// Query client
import { queryClient } from 'index';

export const useGetCustomFunction = <T = MediaJsonCustomFunction[]>(
  queryOptions?: Omit<UseQueryOptions<MediaJsonCustomFunction[], any, T>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery<MediaJsonCustomFunction[], any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_CUSTOM],
    queryFn: async () => {
      const data = await getListingTemplateCustomFunction();

      return data;
    },
    ...queryOptions,
  });
};
export const useAddSavedCSFunction = () => {
  return useMutation(createSavedCustomFunction, {
    onMutate: (_payload: Partial<any>) => {
      const previousSavedBlock = queryClient.getQueryData([QUERY_KEYS.GET_SAVE_CUSTOM_FUNCTION]);
      return { previousSavedBlock };
    },
    onError: (_error, _payload, context) => {
      queryClient.setQueryData([QUERY_KEYS.GET_SAVE_CUSTOM_FUNCTION], context?.previousSavedBlock);
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVE_CUSTOM_FUNCTION], {
        exact: true,
      });
    },
  });
};
export const useUpdateCSFunction = () => {
  return useMutation(updateSavedCustomFunction, {
    onMutate: (_payload: Partial<any>) => {
      const previousSavedBlock = queryClient.getQueryData([QUERY_KEYS.GET_SAVE_CUSTOM_FUNCTION]);

      return { previousSavedBlock };
    },
    onError: (_error, _payload, context) => {
      queryClient.setQueryData([QUERY_KEYS.GET_SAVE_CUSTOM_FUNCTION], context?.previousSavedBlock);
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVE_CUSTOM_FUNCTION], {
        exact: true,
      });
    },
  });
};
// export const useGetMediaJsonSavedBlocks = () => {
//   const useInfiniteSavedBlocks = useInfiniteQuery(
//     [QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK],
//     getListingMediaJsonSavedBlock,
//     {
//       getNextPageParam: (_lastPage, pages) => {
//         if (pages.length < Math.ceil(pages[0].total / QUERY_LIMIT_RESULTS)) {
//           return pages.length + 1;
//         } else {
//           return undefined;
//         }
//       },
//     },
//   );

//   let savedBlocks: SavedBlock[] = [];

//   savedBlocks =
//     useInfiniteSavedBlocks.data?.pages.reduce(
//       (savedBlocks: SavedBlock[], page: { body: SavedBlock[] }): SavedBlock[] => [...savedBlocks, ...page.body],
//       [],
//     ) || [];

//   return { ...useInfiniteSavedBlocks, data: savedBlocks };
// };
