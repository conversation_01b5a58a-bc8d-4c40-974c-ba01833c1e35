// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListAllEvents } from 'app/services/MediaTemplateDesign/BusinessObject';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetListAllEvents<T> = {
  options?: Omit<UseQueryOptions<any, any, T, any[]>, OptionHasDefault>;
};

export const useGetListAllEvents = <T = any>(props: GetListAllEvents<T>) => {
  const { options } = props || {};

  // Selectors
  return useQuery({
    queryKey: [QUERY_KEYS.GET_LIST_ALL_EVENT],
    queryFn: () => getListAllEvents(),
    placeholderData: {
      rows: [],
      total: 0,
    },
    ...options,
  });
};
