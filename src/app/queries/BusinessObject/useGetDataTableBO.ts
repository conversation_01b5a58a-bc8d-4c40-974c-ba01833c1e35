// Libraries
import { useSelector } from 'react-redux';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Slice
import { selectBoSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getDataTableBO, TDataTableBO } from 'app/services/ThirdParty/TableBO';
import { queryClient } from 'index';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetDataTableBOProps<T> = {
  itemTypeId?: number;
  options?: Omit<UseQueryOptions<any, any, T, (string | number)[]>, OptionHasDefault>;
};

export const useGetDataTableBO = <T = TDataTableBO>(props?: GetDataTableBOProps<T>) => {
  const { options } = props || {};

  // Selectors
  const boSettings = useSelector(selectBoSettings);
  const itemTypeId = props?.itemTypeId || boSettings.itemTypeId || '';

  return useQuery<TDataTableBO, any, T, (string | number)[]>({
    queryKey: [QUERY_KEYS.GET_DATA_TABLE_BO, itemTypeId],
    queryFn: () => getDataTableBO({ id: itemTypeId }),
    enabled: !!itemTypeId,
    placeholderData: {
      header: [],
      data: [],
    },
    ...options,
  });
};

export const getDataTableBOQuery = async (itemTypeId: number | string, params?: Record<string, any>) => {
  const { filters } = params || {};

  const data = await queryClient.fetchQuery({
    queryKey: [QUERY_KEYS.GET_DATA_TABLE_BO, itemTypeId, filters],
    queryFn: () => getDataTableBO({ id: itemTypeId, filters }),
  });

  return {
    data: data ?? [],
    boId: itemTypeId,
    params,
  };
};
