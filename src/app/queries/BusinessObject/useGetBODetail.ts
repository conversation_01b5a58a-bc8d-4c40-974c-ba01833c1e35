// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Services
import { getBusinessObjectDetail } from 'app/services/MediaTemplateDesign/BusinessObject';

// Constants
import { QUERY_KEYS } from 'constants/queries';

type TDataFnQuery = Record<string, any>;

export const useGetBODetail = <T = TDataFnQuery>(
  itemTypeId: number | null,
  queryOptions?: UseQueryOptions<TDataFnQuery, any, T>,
) => {
  return useQuery<TDataFnQuery, any, T>({
    queryKey: [QUERY_KEYS.GET_BO_DETAIL, itemTypeId],
    queryFn: async () => {
      const { rows: businessObjectDetail } = await getBusinessObjectDetail(itemTypeId);

      return businessObjectDetail;
    },
    enabled: !!itemTypeId,
    ...queryOptions,
  });
};
