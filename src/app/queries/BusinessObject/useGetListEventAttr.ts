import { getListAttributes } from 'app/services/MediaTemplateDesign/BusinessObject';
import { QUERY_KEYS } from 'constants/queries';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

export const useGetListEventAttr = <T = any[]>(
  eventSource: string,
  sourceId: string,
  queryOptions?: Omit<UseQueryOptions<any[], any, T>, 'queryKey' | 'queryFn'>,
) => {
  const [eventActionId, eventCategoryId] = (eventSource || '')?.split(':');

  return useQuery<any[], any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_EVENT_ATTR, sourceId, eventSource],
    queryFn: () =>
      getListAttributes({
        eventActionId,
        eventCategoryId,
        sourceId,
      }),
    ...queryOptions,
    enabled: queryOptions?.enabled && !!eventActionId && !!eventCategoryId,
  });
};
