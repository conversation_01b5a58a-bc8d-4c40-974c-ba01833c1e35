// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListCollectionsBO } from 'app/services/MediaTemplateDesign/BusinessObject';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetGetListCollectionBOProps<T> = {
  itemTypeId?: number;
  options?: Omit<UseQueryOptions<any, any, T, any[]>, OptionHasDefault>;
};

export const useGetListCollectionBO = <T = any>(props?: GetGetListCollectionBOProps<T>) => {
  const { options, itemTypeId } = props || {};

  // Selectors
  return useQuery<any, any, T, any[]>({
    queryKey: [QUERY_KEYS.GET_LIST_COLLECTION, itemTypeId],
    queryFn: () => getListCollectionsBO({ itemTypeId }),
    enabled: !!itemTypeId,
    placeholderData: {
      rows: [],
      total: 0,
    },
    ...options,
  });
};
