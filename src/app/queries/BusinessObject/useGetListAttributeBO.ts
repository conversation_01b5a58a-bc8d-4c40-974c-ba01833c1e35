import get from 'lodash/get';
import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListAttributesBO } from 'app/services/MediaTemplateDesign/BusinessObject';
import { queryClient } from 'index';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetListAttributeBOProps<T> = {
  itemTypeIds?: number[];
  options?: Omit<UseQueryOptions<Record<string, any>, any, T, any[]>, OptionHasDefault>;
};

export const useGetListAttributeBO = <T = Record<string, any>>(
  props?: GetListAttributeBOProps<T>,
): UseQueryResult<T, unknown> => {
  const { options } = props || {};

  const itemTypeIds = props?.itemTypeIds || [];

  return useQuery<Record<string, any>, any, T, any>({
    queryKey: [QUERY_KEYS.GET_EVENT_ATTRIBUTE_BO, itemTypeIds],
    queryFn: () => getListAttributesBO({ itemTypeIds }),
    placeholderData: {
      rows: [],
      total: [],
    },
    ...options,
    enabled: itemTypeIds.filter(Boolean).length > 0,
  });
};

export const getListAttributeBOQuery = async ({ itemTypeIds }: { itemTypeIds: (number | null)[] }) => {
  try {
    if (itemTypeIds.filter(itemTypeId => !!itemTypeId).length) {
      const { rows } = (await queryClient.fetchQuery({
        queryKey: [QUERY_KEYS.GET_EVENT_ATTRIBUTE_BO, itemTypeIds],
        queryFn: () => getListAttributesBO({ itemTypeIds: itemTypeIds }),
      })) as any;

      return get(rows, `[0].properties`, []);
    }

    return [];
  } catch (error) {
    return [];
  }
};
