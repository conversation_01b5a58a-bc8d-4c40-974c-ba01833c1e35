import { ListSource } from 'app/models/BusinesObject';
import { getListSources } from 'app/services/MediaTemplateDesign/BusinessObject';
import { QUERY_KEYS } from 'constants/queries';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

export const useGetListSourceBO = <T = ListSource[]>(
  queryOptions?: Omit<UseQueryOptions<ListSource[], any, T>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery<ListSource[], any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_SOURCE_BO],
    queryFn: () => getListSources(),
    ...queryOptions,
  });
};
