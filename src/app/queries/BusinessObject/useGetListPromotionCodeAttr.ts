import { getListAttributesBO } from 'app/services/MediaTemplateDesign/BusinessObject';
import { QUERY_KEYS } from 'constants/queries';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

type TQueryFnData = any[];

export const useGetListPromotionCodeAttr = <T = TQueryFnData>(
  queryOptions?: Omit<UseQueryOptions<TQueryFnData, any, T>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery<TQueryFnData, any, T>({
    queryKey: [QUERY_KEYS.GET_LIST_PROMOTION_CODE_ATTR],
    queryFn: async () => {
      const data = await getListAttributesBO({ itemTypeIds: [-100] });

      if (data?.rows?.length && data?.rows[0]) {
        const item = data.rows[0];

        if (Array.isArray(item?.properties) && item?.properties?.length) {
          return item.properties;
        }

        return [];
      }
    },
    ...queryOptions,
  });
};
