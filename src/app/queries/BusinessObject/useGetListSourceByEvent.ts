// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListSourceByEvent } from 'app/services/MediaTemplateDesign/BusinessObject';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetListSourceByEvent<T> = {
  eventCategoryId: number;
  eventActionId: number;
  options?: Omit<UseQueryOptions<any, any, T, any[]>, OptionHasDefault>;
};

export const useGetListSourceByEvent = <T = any>(props: GetListSourceByEvent<T>) => {
  const { options, eventCategoryId, eventActionId } = props || {};

  // Selectors
  return useQuery({
    queryKey: [QUERY_KEYS.GET_LIST_SOURCE_BY_EVENT, eventActionId, eventCategoryId],
    queryFn: () => getListSourceByEvent({ eventActionId, eventCategoryId }),
    enabled: !!eventCategoryId && !!eventActionId,
    placeholderData: { rows: [], total: 0 },
    ...options,
  });
};
