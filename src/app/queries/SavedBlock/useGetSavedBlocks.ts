// Libraries
import { useInfiniteQuery } from '@tanstack/react-query';

// Services
import { getListingSavedBlock } from 'app/services/MediaTemplateDesign/SavedBlock';
import { getListingSavedBlock as getListingMediaJsonSavedBlock } from 'app/services/MediaJsonDesign/SavedBlock';

// Models
import { SavedBlock } from 'app/models/SavedBlock';

// Constants
import { QUERY_KEYS, QUERY_LIMIT_RESULTS } from 'constants/queries';

export const useGetSavedBlocks = () => {
  const useInfiniteSavedBlocks = useInfiniteQuery([QUERY_KEYS.GET_SAVED_BLOCKS], getListingSavedBlock, {
    getNextPageParam: (_lastPage, pages) => {
      if (pages.length < Math.ceil(pages[0].total / QUERY_LIMIT_RESULTS)) {
        return pages.length + 1;
      } else {
        return undefined;
      }
    },
  });

  let savedBlocks: SavedBlock[] = [];

  savedBlocks =
    useInfiniteSavedBlocks.data?.pages.reduce(
      (savedBlocks: SavedBlock[], page: { body: SavedBlock[] }): SavedBlock[] => [...savedBlocks, ...page.body],
      [],
    ) || [];

  return { ...useInfiniteSavedBlocks, data: savedBlocks };
};

export const useGetMediaJsonSavedBlocks = () => {
  const useInfiniteSavedBlocks = useInfiniteQuery(
    [QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK],
    getListingMediaJsonSavedBlock,
    {
      getNextPageParam: (_lastPage, pages) => {
        if (pages.length < Math.ceil(pages[0].total / QUERY_LIMIT_RESULTS)) {
          return pages.length + 1;
        } else {
          return undefined;
        }
      },
    },
  );

  let savedBlocks: SavedBlock[] = [];

  savedBlocks =
    useInfiniteSavedBlocks.data?.pages.reduce(
      (savedBlocks: SavedBlock[], page: { body: SavedBlock[] }): SavedBlock[] => [...savedBlocks, ...page.body],
      [],
    ) || [];

  return { ...useInfiniteSavedBlocks, data: savedBlocks };
};
