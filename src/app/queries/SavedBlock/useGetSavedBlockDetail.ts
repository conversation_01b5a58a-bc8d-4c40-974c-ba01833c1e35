// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { TDataTableBO } from 'app/services/ThirdParty/TableBO';
import { getSavedBlockDetail } from 'app/services/MediaJsonDesign/SavedBlock';

// Models
import { MediaJsonSavedBlock } from 'app/models';

// Query client
import { queryClient } from 'index';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetMediaJsonSavedBlockDetailProps<T> = {
  savedBlockId: number | undefined;
  options?: Omit<UseQueryOptions<any, any, T, (string | number)[]>, OptionHasDefault>;
};

export const useGetMediaJsonSavedBlockDetail = <T = MediaJsonSavedBlock>(
  props?: GetMediaJsonSavedBlockDetailProps<T>,
) => {
  const { options, savedBlockId } = props || {};

  return useQuery<TDataTableBO, any, T, (string | number)[]>({
    queryKey: [QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK_DETAIL, savedBlockId || 0],
    queryFn: () => getSavedBlockDetail(savedBlockId || 0),
    enabled: !!savedBlockId,
    placeholderData: {},
    ...options,
  });
};

export const getMediaJsonSavedBlockQuery = async (savedBlockId: number, params?: Record<string, any>) => {
  const data = await queryClient.fetchQuery({
    queryKey: [QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK_DETAIL, savedBlockId],
    queryFn: () => getSavedBlockDetail(savedBlockId),
  });

  return {
    ...data,
  };
};
