// Libraries
import { useMutation } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { updateSavedBlock } from 'app/services/MediaTemplateDesign/SavedBlock';
import { updateSavedBlock as updateMediaJsonSavedBlock } from 'app/services/MediaJsonDesign/SavedBlock';

// Models
import { SavedBlock } from 'app/models';

// Query Client
import { queryClient } from 'index';

export const useUpdateSavedBlock = () => {
  return useMutation(updateSavedBlock, {
    onMutate: async (payload: Partial<SavedBlock>) => {
      await queryClient.cancelQueries([QUERY_KEYS.GET_SAVED_DETAILS_BLOCK, payload.id]);

      // const previousSavedDetailsBlock = queryClient.getQueryData(QUERY_KEYS.GET_SAVED_DETAILS_BLOCK);

      // console.log({ previousSavedDetailsBlock });

      // return { previousSavedDetailsBlock };
    },
    onError: (_error, _payload, context: any) => {
      queryClient.setQueryData([QUERY_KEYS.GET_SAVED_DETAILS_BLOCK], context.previousSavedDetailsBlock.id);
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVED_BLOCKS], { exact: true });
    },
  });
};

export const useUpdateMediaJsonSavedBlock = () => {
  return useMutation(updateMediaJsonSavedBlock, {
    onMutate: async (payload: Partial<SavedBlock>) => {
      await queryClient.cancelQueries([QUERY_KEYS.GET_MEDIA_JSON_SAVED_DETAILS_BLOCK, payload.id]);

      // const previousSavedDetailsBlock = queryClient.getQueryData(QUERY_KEYS.GET_MEDIA_JSON_SAVED_DETAILS_BLOCK);

      // console.log({ previousSavedDetailsBlock });

      // return { previousSavedDetailsBlock };
    },
    onError: (_error, _payload, context: any) => {
      queryClient.setQueryData([QUERY_KEYS.GET_MEDIA_JSON_SAVED_DETAILS_BLOCK], context.previousSavedDetailsBlock.id);
    },
    onSettled: (_data, _error, variables) => {
      const { id } = variables || {};

      queryClient.invalidateQueries([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK_DETAIL, id], { exact: true });

      queryClient.invalidateQueries([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK], { exact: true });
    },
  });
};
