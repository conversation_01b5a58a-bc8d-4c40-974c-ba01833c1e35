// Libraries
import { useMutation } from '@tanstack/react-query';

// constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { deleteSavedBlock } from 'app/services/MediaTemplateDesign/SavedBlock';
import { deleteSavedBlock as deleteMediaJsonSavedBlock } from 'app/services/MediaJsonDesign/SavedBlock';

// Query Client
import { queryClient } from 'index';

export const useDeleteSavedBlock = () => {
  return useMutation(deleteSavedBlock, {
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_SAVED_BLOCKS], { exact: true });
    },
  });
};

export const useDeleteMediaJsonSavedBlock = () => {
  return useMutation(deleteMediaJsonSavedBlock, {
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK], { exact: true });
    },
  });
};
