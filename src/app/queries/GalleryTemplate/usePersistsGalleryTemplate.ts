// Libraries
import { useMutation } from '@tanstack/react-query';

// Services
import { createGalleryTemplate, updateGalleryTemplate } from 'app/services/MediaTemplateDesign/GalleryTemplate';
import {
  createGalleryTemplate as createMediaJsonGalleryTemplate,
  updateGalleryTemplate as updateMediaJsonGalleryTemplate,
} from 'app/services/MediaJsonDesign/GalleryTemplate';

// Molecules
import { message } from 'app/components/molecules';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Locales
import { translations } from 'locales/translations';

// Query client
import { queryClient } from 'index';

// Constants
import { QUERY_KEYS } from 'constants/queries';

type MutationFnProps = {
  persistsType: 'create' | 'update';
  data: Record<string, any>;
};

export const usePersistsGalleryTemplate = () => {
  return useMutation({
    mutationFn: (variables: MutationFnProps) => {
      const { persistsType, data } = variables;

      if (persistsType === 'create') {
        return createGalleryTemplate(data);
      } else {
        return updateGalleryTemplate(data);
      }
    },
    onSuccess: (_, variables: MutationFnProps) => {
      const { persistsType } = variables;

      message.success(
        getTranslateMessage(
          translations.saveAsGallery[persistsType === 'create' ? 'saveAsNewGallery' : 'saveAsExistingGallery'].success,
        ),
      );
    },
    onError: (_, variables: MutationFnProps) => {
      const { persistsType } = variables;

      message.error(
        getTranslateMessage(
          translations.saveAsGallery[persistsType === 'create' ? 'saveAsNewGallery' : 'saveAsExistingGallery'].error,
        ),
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_LIST_GALLERY_TEMPLATE]);
    },
  });
};

export const usePersistsMediaJsonGalleryTemplate = () => {
  return useMutation({
    mutationFn: (variables: MutationFnProps) => {
      const { persistsType, data } = variables;

      if (persistsType === 'create') {
        return createMediaJsonGalleryTemplate(data);
      } else {
        return updateMediaJsonGalleryTemplate(data);
      }
    },
    onSuccess: (_, variables: MutationFnProps) => {
      const { persistsType } = variables;

      message.success(
        getTranslateMessage(
          translations.saveAsGallery[persistsType === 'create' ? 'saveAsNewGallery' : 'saveAsExistingGallery'].success,
        ),
      );
    },
    onError: (_, variables: MutationFnProps) => {
      const { persistsType } = variables;

      message.error(
        getTranslateMessage(
          translations.saveAsGallery[persistsType === 'create' ? 'saveAsNewGallery' : 'saveAsExistingGallery'].error,
        ),
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_LIST_MEDIA_JSON_GALLERY_TEMPLATE]);
    },
  });
};
