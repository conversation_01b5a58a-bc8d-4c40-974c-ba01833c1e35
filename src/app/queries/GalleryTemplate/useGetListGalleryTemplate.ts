// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Models
import { GalleryTemplate } from 'app/models/GalleryTemplate';

import { OptionHasDefault } from 'types/ReactQuery';
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getListGalleryTemplate } from 'app/services/MediaTemplateDesign/GalleryTemplate';
import { getListGalleryTemplate as getListMediaJsonGalleryTemplate } from 'app/services/MediaJsonDesign/GalleryTemplate';

type TGetListGalleryTemplateProps<T> = {
  filterSetting?: {
    limit?: number;
    page?: number;
    columns: string[];
    filter?: string;
  };
  options?: Omit<UseQueryOptions<unknown, unknown, T, any[]>, OptionHasDefault>;
};

export function useGetListGalleryTemplate<T = { body: GalleryTemplate[]; total: number }>(
  props?: TGetListGalleryTemplateProps<T>,
) {
  // Props
  const { filterSetting, options } = props || {};

  return useQuery({
    queryKey: [QUERY_KEYS.GET_LIST_GALLERY_TEMPLATE, filterSetting].filter(Boolean),
    queryFn: () => getListGalleryTemplate(filterSetting || {}),
    ...options,
  });
}

export function useGetListMediaJsonGalleryTemplate<T = { body: GalleryTemplate[]; total: number }>(
  props?: TGetListGalleryTemplateProps<T>,
) {
  // Props
  const { filterSetting, options } = props || {};

  return useQuery({
    queryKey: [QUERY_KEYS.GET_LIST_MEDIA_JSON_GALLERY_TEMPLATE, filterSetting].filter(Boolean),
    queryFn: () => getListMediaJsonGalleryTemplate(filterSetting || {}),
    ...options,
  });
}
