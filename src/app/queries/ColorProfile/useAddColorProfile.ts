// Libraries
import { useMutation } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { createColorProfile } from 'app/services/MediaTemplateDesign/ColorProfile';

// Query client
import { queryClient } from 'index';

export const useAddColorProfile = () => {
  return useMutation(createColorProfile, {
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_COLOR_PROFILES], {
        exact: true,
      });
    },
  });
};
