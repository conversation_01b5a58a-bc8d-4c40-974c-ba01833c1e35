// Libraries
import { useMutation } from '@tanstack/react-query';

// Query client
import { queryClient } from 'index';

// Services
import { deleteColorProfile } from 'app/services/MediaTemplateDesign/ColorProfile';

// Constants
import { QUERY_KEYS } from 'constants/queries';

export const useDeleteColorProfile = () => {
  return useMutation([QUERY_KEYS.DELETE_COLOR_PROFILE], deleteColorProfile, {
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.GET_COLOR_PROFILES], { exact: true });
    },
  });
};
