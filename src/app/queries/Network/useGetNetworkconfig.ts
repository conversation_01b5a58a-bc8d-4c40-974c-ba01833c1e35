// Libraries
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

// Constants
import { QUERY_KEYS } from 'constants/queries';

// Services
import { getNetWorkInfo } from 'app/services/Network';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetNetworkConfigProps<T> = {
  appId: number | string;
  options?: Omit<UseQueryOptions<any, any, T, (string | number)[]>, OptionHasDefault>;
};

export const useGetNetworkInfo = <T = Record<string, any>>(props?: GetNetworkConfigProps<T>) => {
  const { options, appId } = props || {};

  return useQuery<Record<string, any>, any, T, (string | number)[]>({
    queryKey: [QUERY_KEYS.GET_MEDIA_JSON_SAVED_BLOCK_DETAIL, appId || 0],
    queryFn: () => getNetWorkInfo(appId || 0),
    enabled: !!appId,
    placeholderData: {},
    ...options,
  });
};
