// Libraries
import { useSelector } from 'react-redux';
import { useCallback, useMemo } from 'react';

// Selectors
import { selectTrackingModule } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

export const useHaveElementNameTracking = () => {
  const trackingData = useSelector(selectTrackingModule)?.data;

  const checkElementNameTracking = useCallback(() => {
    for (const key in trackingData) {
      const items = trackingData[key];
      if (Array.isArray(items)) {
        for (const item of items) {
          if (item.value === '#ELEMENT_NAME#') {
            return true;
          }
        }
      }
    }
    return false;
  }, [trackingData]);

  const hasElementNameTracking = useMemo(() => {
    return checkElementNameTracking();
  }, [checkElementNameTracking]);

  return {
    hasElementNameTracking,
  };
};
