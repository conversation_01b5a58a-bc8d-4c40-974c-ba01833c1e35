// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { Select } from '../../../../Select';

// Components
import CustomDragBlock from '../../../components/CustomDragBlock';

// Constants
import { DATE_FORMAT, DISPLAY_TYPE_DATE_TIME, TIME_FORMAT } from '../../../constant';

interface DateTimeSettingsProps {
  blockSetting: any;
  onChangeBlockSetting: (key: string, value: any) => void;
  settings: any;
  fieldsArray: any[];
}

export const DateTimeSettings: React.FC<DateTimeSettingsProps> = props => {
  const { blockSetting, onChangeBlockSetting, settings, fieldsArray } = props;
  const { t } = useTranslation();

  return (
    <>
      {blockSetting.isCustom && blockSetting.type === 'datetime' && (
        <>
          <div className="ants-flex ants-flex-col">
            <Text>{t(translations.displayType.title)}</Text>
            <Select
              className="ants-w-100"
              options={Object.values(DISPLAY_TYPE_DATE_TIME)}
              value={settings?.datetimeType}
              onChange={value => onChangeBlockSetting('datetimeType', value)}
            />
          </div>

          {settings?.datetimeType === 'datetime' ? (
            <div className="ants-flex ants-flex-col">
              <Text>Date Format</Text>
              <Select
                className="ants-w-100"
                options={Object.values(DATE_FORMAT)}
                value={blockSetting?.dateTimeFormat}
                onChange={value => onChangeBlockSetting('dateTimeFormat', value)}
              />
            </div>
          ) : (
            <>
              <CustomDragBlock
                id={`${blockSetting?.id}-dropdownDate`}
                type={blockSetting.type}
                idField={blockSetting?.id}
                titles={['Date', 'Display Alias']}
                value={settings?.dropdownDate}
                onChange={value => onChangeBlockSetting('dropdownDate', value)}
                fieldsArray={fieldsArray}
              />

              <CustomDragBlock
                id={`${blockSetting?.id}-dropdownTime`}
                idField={blockSetting?.id}
                type={blockSetting.type}
                titles={['Time']}
                value={settings?.dropdownTime}
                onChange={value => onChangeBlockSetting('dropdownTime', value)}
                fieldsArray={fieldsArray}
              />
            </>
          )}
          <div className="ants-flex ants-flex-col">
            <Text>Time Format</Text>

            <Select
              className="ants-w-100"
              options={Object.values(TIME_FORMAT)}
              value={settings?.timeFormat}
              onChange={value => onChangeBlockSetting('timeFormat', value)}
            />
          </div>
        </>
      )}
    </>
  );
};
