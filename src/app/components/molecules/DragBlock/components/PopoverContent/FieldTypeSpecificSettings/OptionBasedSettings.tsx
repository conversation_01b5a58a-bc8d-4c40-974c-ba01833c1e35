// Libraries
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { InputNumber } from 'app/components/molecules/InputNumber';

// Components
import CustomDragBlock from '../../../components/CustomDragBlock';

interface OptionBasedSettingsProps {
  blockSetting: any;
  onChangeBlockSetting: (key: string, value: any) => void;
  settings: any;
  fieldsArray: any[];
}

export const OptionBasedSettings: React.FC<OptionBasedSettingsProps> = props => {
  const { blockSetting, onChangeBlockSetting, settings, fieldsArray } = props;
  const { t } = useTranslation();

  return (
    <Fragment>
      {(blockSetting.type === 'radioButton' || blockSetting.type === 'checkbox') && (
        <Fragment>
          <div className="ants-flex ants-flex-col ants-mt-4">
            <div className="ants-w-full">
              <CustomDragBlock
                id={`${blockSetting?.id}-${blockSetting.type}`}
                idField={blockSetting?.id}
                type={blockSetting.type}
                titles={['Field Options']}
                value={settings?.fieldOptions}
                onChange={value => onChangeBlockSetting('fieldOptions', value)}
                fieldsArray={fieldsArray}
              />
            </div>
          </div>

          <div className="ants-flex ants-justify-between ants-mt-4 ants-items-center">
            <Text>{t(translations.optionColumn.title)}</Text>
            <div className="ants-w-[57px]">
              <InputNumber
                min={0}
                value={blockSetting?.optionColumn || ''}
                onChange={value => onChangeBlockSetting('optionColumn', value)}
              />
            </div>
          </div>
        </Fragment>
      )}
    </Fragment>
  );
};
