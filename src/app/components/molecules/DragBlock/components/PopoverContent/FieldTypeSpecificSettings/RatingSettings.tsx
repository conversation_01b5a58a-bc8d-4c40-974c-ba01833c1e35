// Libraries
import React from 'react';
import { Text, SliderWithInputNumber, InputNumber, Button } from '@antscorp/antsomi-ui';
import tinycolor from 'tinycolor2';
import { ColorSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/ColorSetting';
import { AlignSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/AlignSetting';
import {
  RATING_AMOUNT_BY_TYPE,
  RATING_OPTIONS_BY_TYPE,
  RATING_SETTINGS_BY_TYPE,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FormFieldsSetting/constants';
import { RatingIcon, RATING_TYPE } from 'app/components/atoms/Rating';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { random } from 'app/utils';
import { cloneDeep, omit, toString } from 'lodash';

// Components
import { RatingTypeSelector } from './RatingTypeSelector';

// Translations
import CustomDragBlock, { TFieldArrays } from '../../CustomDragBlock';
import { Tfield } from '../../DragOption';
import { isRatingType, RatingType } from 'app/components/atoms/Rating';
import produce from 'immer';
import { THEME } from '@antscorp/antsomi-ui/es/constants';
import { TRatingField } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FormFieldsSetting/types';

interface RatingSettingsProps {
  blockSetting: any;
  settings: TRatingField;
  fieldsArray?: TFieldArrays[];
  onChangeBlockSetting: (key: string, value: any) => void;
}

const getStarsPreselectScore = (ratingOptions: Tfield[]): number | null => {
  let prevPreselectedScore: number | null = null;

  ratingOptions.forEach(opt => {
    if (!opt.isPreselect || typeof opt.score !== 'number') return;

    if (prevPreselectedScore === null || opt.score > prevPreselectedScore) {
      prevPreselectedScore = opt.score;
    }
  });

  return prevPreselectedScore;
};

function findNextScoreToInsert(arrA: number[], arrB: number[]): number | null {
  const sortedArrA = [...arrA].sort((a, b) => a - b);

  const setB = new Set(arrB);

  let maxElementInB: number | null = null;
  if (arrB.length > 0) {
    maxElementInB = Math.max(...arrB);
  }

  let candidateForPriority1: number | null = null;
  let minElementNotInB: number | null = null;

  for (const numA of sortedArrA) {
    if (setB.has(numA)) {
      continue;
    }

    if (maxElementInB !== null && numA > maxElementInB) {
      if (candidateForPriority1 === null) {
        candidateForPriority1 = numA;
      }
    }

    if (minElementNotInB === null) {
      minElementNotInB = numA;
    }
  }

  if (candidateForPriority1 !== null) {
    return candidateForPriority1;
  } else if (minElementNotInB !== null) {
    return minElementNotInB;
  }

  return null;
}

const newRatingOptCreating = (params: { ratingType: RatingType; currentOptions: Tfield[] }): Tfield | null => {
  const { ratingType, currentOptions } = params;

  const scores: number[] = RATING_OPTIONS_BY_TYPE[ratingType].map(opt => opt.score);
  const currentScores: number[] = [];

  currentOptions.forEach(opt => {
    if (opt.score === undefined || !scores.includes(opt.score)) {
      return;
    }

    currentScores.push(opt.score);
  });

  switch (ratingType) {
    case RATING_TYPE.EMOTION:
    case RATING_TYPE.YESNO: {
      let newOptionScore = findNextScoreToInsert(scores, currentScores);

      if (!newOptionScore) break;

      const option = RATING_OPTIONS_BY_TYPE[ratingType].find(opt => opt.score === newOptionScore);

      if (!option) break;

      return {
        ...option,
        label: '',
        isPreselect: false,
        id: random(6),
      };
    }

    case RATING_TYPE.STAR: {
      const defaultOpt = RATING_OPTIONS_BY_TYPE[RATING_TYPE.STAR].at(0);

      const currentIntValues: number[] = [];

      currentOptions.forEach(opt => {
        if (opt.value !== undefined && isFinite(+opt.value)) {
          currentIntValues.push(+opt.value);
        }
      });

      if (!defaultOpt) break;

      return {
        ...defaultOpt,
        id: random(6),
        isPreselect: false,
        label: '',
        score: currentOptions.length + 1,
        value: currentIntValues.length > 0 ? toString(Math.max(...currentIntValues) + 1) : defaultOpt.value,
      };
    }

    default:
      break;
  }

  return null;
};

export const withRatingSettings = (Component: React.ComponentType<RatingSettingsProps>) => {
  return (props: RatingSettingsProps) => {
    const { blockSetting } = props;

    if (blockSetting.isCustom && blockSetting.type === 'rating') {
      return <Component {...props} />;
    }

    return null;
  };
};

export const RatingSettings = withRatingSettings(props => {
  const { t } = useTranslation();

  const { blockSetting, settings, fieldsArray = [], onChangeBlockSetting } = props;

  const ratingType: RatingType = isRatingType(settings.ratingType) ? settings.ratingType : 'star';

  const onChangeRatingOptions = (value: Tfield[]) => {
    let updatedRatingOptions = [...value];

    const useMaxColumnOption = settings.optionColumn === settings.ratingOptions.length;

    if (ratingType === 'star') {
      const prevPreselectedScore = getStarsPreselectScore(value);

      updatedRatingOptions = value.map((option, idx) => {
        const starScore = idx + 1;

        return {
          ...option,

          score: starScore,

          ...(prevPreselectedScore !== null && {
            isPreselect: starScore <= prevPreselectedScore,
          }),
        };
      });
    }

    onChangeBlockSetting('BLOCK_SETTINGS', {
      ratingOptions: updatedRatingOptions,
      optionColumn: useMaxColumnOption
        ? updatedRatingOptions.length
        : Math.min(settings.optionColumn, updatedRatingOptions.length),
    });
  };

  const onChangeRatingType = (newRatingType: string) => {
    if (!isRatingType(newRatingType)) {
      return;
    }

    const { ratingType: previousRatingType } = settings;

    if (previousRatingType === newRatingType) {
      return;
    }

    const defaultSettings = RATING_SETTINGS_BY_TYPE[newRatingType];

    if (!defaultSettings) {
      return;
    }

    const cachedSettingsForNewType = settings.settingsByTypeCaching?.[newRatingType];

    onChangeBlockSetting('BLOCK_SETTINGS', {
      ...(cachedSettingsForNewType || defaultSettings),

      settingsByTypeCaching: produce(settings.settingsByTypeCaching, draft => {
        const currentSettingsToCache = cloneDeep(omit(settings, ['settingsByTypeCaching', 'id']));

        if (draft === undefined) {
          return { [previousRatingType]: currentSettingsToCache };
        }

        draft[previousRatingType] = currentSettingsToCache;
      }),
    });
  };

  const onChangePreselect = ({ score }: Tfield) => {
    if (score === undefined) return;

    const ratingOptions = Array.from<Tfield>(settings?.ratingOptions);

    onChangeBlockSetting(
      'ratingOptions',

      produce(ratingOptions, draft => {
        switch (ratingType) {
          case RATING_TYPE.EMOTION:
          case RATING_TYPE.YESNO:
            draft.forEach(opt => {
              opt.isPreselect = opt.score === score ? !opt.isPreselect : false;
            });
            break;

          case RATING_TYPE.STAR:
            const isToggle = getStarsPreselectScore(ratingOptions) === score;

            draft.forEach(opt => {
              if (opt.score === undefined) return;

              opt.isPreselect = isToggle ? false : opt.score <= score;
            });
            break;

          default:
            break;
        }
      }),
    );
  };

  const addNewable = settings?.ratingOptions?.length < RATING_AMOUNT_BY_TYPE[ratingType].max;

  return (
    <>
      {/* Rating Type Selector */}
      <RatingTypeSelector value={ratingType} onChange={onChangeRatingType} />

      {/* Options */}
      <CustomDragBlock
        id={`${blockSetting?.id}-ratingOptions`}
        addNewable={addNewable}
        type={blockSetting.type}
        idField={blockSetting?.id}
        titles={['Field Options']}
        value={settings?.ratingOptions}
        fieldsArray={fieldsArray}
        extensible={false}
        className={`ants-border-b-[1px] ants-border-dashed ants-border-[#D4D4D4] ants-pb-2`}
        newOptionCreating={() =>
          newRatingOptCreating({
            ratingType,
            currentOptions: settings?.ratingOptions || [],
          })
        }
        customRenderer={{
          isPreselect: option => {
            const style: React.CSSProperties = {};

            let bgColor: string | undefined;

            if (option.isPreselect) {
              style.backgroundColor = THEME.token?.blue1_1;
            }

            if (option.isPreselect && ratingType === RATING_TYPE.STAR) {
              style.backgroundColor = undefined;
              bgColor = THEME.token?.colorPrimary;
            }

            return (
              <Button
                className={'ants-w-[40px] ants-justify-self-center'}
                type="text"
                style={style}
                onClick={() => onChangePreselect(option)}
              >
                <RatingIcon
                  className="ants-shrink-0"
                  type={ratingType}
                  value={option.score}
                  size={24}
                  backgroundColor={bgColor}
                />
              </Button>
            );
          },
        }}
        onChange={onChangeRatingOptions}
      />

      {/* Align */}
      <AlignSetting
        label={t(translations.align.title)}
        align={settings?.alignment}
        onChange={value => onChangeBlockSetting('alignment', value)}
      />

      {/* Size */}
      <SliderWithInputNumber
        label={`${t(translations.size.title)} (px)`}
        min={10}
        max={100}
        value={settings?.size || 30}
        onAfterChange={value => onChangeBlockSetting('size', value)}
      />

      {/* Option Column */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{t(translations.optionColumn.title)}</Text>

        <InputNumber
          min={1}
          style={{ width: 57 }}
          max={settings.ratingOptions.length}
          value={settings?.optionColumn}
          onChange={value => onChangeBlockSetting('optionColumn', value)}
          showHandler
        />
      </div>

      {/* Border Color */}
      <ColorSetting
        label={t(translations.borderColor.title)}
        color={settings?.borderColor || '#F7DA64'}
        onChange={v => {
          if (tinycolor(v).isValid()) {
            onChangeBlockSetting('borderColor', v);
          }
        }}
      />

      <div className="ants-flex ants-justify-between">
        {/* BG Before Rating Color */}
        <ColorSetting
          vertical
          label={t(translations.rating.colors.before)}
          color={settings?.bgBeforeColor || '#FFFFFF'}
          onChange={v => {
            if (tinycolor(v).isValid()) {
              onChangeBlockSetting('bgBeforeColor', v);
            }
          }}
        />

        {/* BG After Rating Color */}
        <ColorSetting
          className="ants-items-end"
          vertical
          label={t(translations.rating.colors.after)}
          color={settings?.bgAfterColor || '#F7DA64'}
          onChange={v => {
            if (tinycolor(v).isValid()) {
              onChangeBlockSetting('bgAfterColor', v);
            }
          }}
        />
      </div>
    </>
  );
});
