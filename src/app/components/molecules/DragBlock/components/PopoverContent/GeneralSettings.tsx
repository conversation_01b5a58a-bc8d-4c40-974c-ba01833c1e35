// Libraries
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Input, Text } from 'app/components/atoms';

// Molecules
import { SwitchLabel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/SwitchLabel';

// Constants
import { FIELD_CUSTOM_OPTIONS, FIELD_OPTIONS } from '../../constant';

interface GeneralSettingsProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeBlockSetting: (key: string, value: any) => void;
  optinFields: any;
}

export const GeneralSettings: React.FC<GeneralSettingsProps> = props => {
  const { blockSetting, setBlockSetting, onChangeBlockSetting, optinFields } = props;
  const { t } = useTranslation();

  return (
    <Fragment>
      {(FIELD_OPTIONS.includes(blockSetting.id) ||
        blockSetting.id === 'privacyText' ||
        blockSetting.id === 'phoneInput' ||
        (blockSetting.isCustom && FIELD_CUSTOM_OPTIONS.includes(blockSetting.type))) && (
        <Fragment>
          <div className="ants-flex ants-flex-col">
            <Text>{t(translations.fieldName.title)}</Text>
            <Input
              autoFocus
              onFocus={event => {
                event.target.select();
              }}
              value={blockSetting?.nameField}
              placeholder={t(translations.fieldName.placeholder)}
              onChange={e => setBlockSetting({ ...blockSetting, nameField: e.target.value })}
              maxLength={70}
              status={optinFields.errors.fieldName ? 'error' : undefined}
              errorMsg={optinFields.errors.fieldName}
            />
          </div>

          <div className="ants-flex ants-flex-col">
            <Text>{t(translations.fieldLabel.title)}</Text>
            <Input
              autoFocus
              onFocus={event => {
                event.target.select();
              }}
              value={blockSetting?.label}
              placeholder={t(translations.fieldLabel.placeholder)}
              onChange={e => setBlockSetting({ ...blockSetting, label: e.target.value })}
              maxLength={255}
              status={optinFields.errors.label ? 'error' : undefined}
              errorMsg={typeof optinFields.errors.label === 'string' ? optinFields.errors.label : ''}
            />
          </div>
          <SwitchLabel
            label={t(translations.displayLabel.title)}
            checked={blockSetting?.displayLabel}
            onChange={checked => onChangeBlockSetting('displayLabel', checked)}
          />
        </Fragment>
      )}

      {blockSetting.id !== 'privacyText' &&
        !['radioButton', 'checkbox', 'datetime', 'rating'].includes(blockSetting.type) && (
          <div className="ants-flex ants-flex-col">
            <Text>{t(translations.Placeholder.title)}</Text>
            <Input
              autoFocus
              onFocus={event => {
                event.target.select();
              }}
              value={blockSetting?.placeholder}
              placeholder={t(translations.enterPlaceholder.title)}
              onChange={e => setBlockSetting({ ...blockSetting, placeholder: e.target.value })}
            />
          </div>
        )}
    </Fragment>
  );
};
