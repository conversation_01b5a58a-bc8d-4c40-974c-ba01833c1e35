// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Molecules
import { Collapse } from 'app/components/molecules/Collapse';

// Styled
import { StyledPanel } from '../../../styled';

// Translations
import { translations } from 'locales/translations';

// Components
import { NumberInputSettings } from './NumberInputSettings';
import { PrivacyTextSettings } from './PrivacyTextSettings';
import { PhoneInputSettings } from './PhoneInputSettings';
import { ValidationSettings } from './ValidationSettings';
import { Input, Text } from 'app/components/atoms';

interface AdvancedSettingsProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeBlockSetting: (key: string, value: any) => void;
  settings: any;
  handleOnchangeRange: (value: any, type: string) => void;
}

export const AdvancedSettings: React.FC<AdvancedSettingsProps> = props => {
  const { blockSetting, setBlockSetting, onChangeBlockSetting, settings, handleOnchangeRange } = props;
  const { t } = useTranslation();

  return (
    <Collapse defaultActiveKey={['1']} ghost>
      <StyledPanel header={t(translations.advanced.title)} key="1" className="!ants-p-0">
        <div className="ants-flex ants-flex-col ants--mt-2">
          <Text className="ants-mb-5px">{t(translations.fieldID.title)}</Text>
          <Input
            value={blockSetting?.fieldId}
            placeholder={t(translations.fieldID.title)}
            onChange={e => onChangeBlockSetting('fieldId', e.target.value)}
          />
        </div>

        <NumberInputSettings
          blockSetting={blockSetting}
          setBlockSetting={setBlockSetting}
          onChangeBlockSetting={onChangeBlockSetting}
          handleOnchangeRange={handleOnchangeRange}
        />

        <PrivacyTextSettings
          blockSetting={blockSetting}
          setBlockSetting={setBlockSetting}
          onChangeBlockSetting={onChangeBlockSetting}
          settings={settings}
        />

        <PhoneInputSettings
          blockSetting={blockSetting}
          setBlockSetting={setBlockSetting}
          onChangeBlockSetting={onChangeBlockSetting}
          settings={settings}
        />

        <ValidationSettings
          blockSetting={blockSetting}
          setBlockSetting={setBlockSetting}
          onChangeBlockSetting={onChangeBlockSetting}
          settings={settings}
        />
      </StyledPanel>
    </Collapse>
  );
};
