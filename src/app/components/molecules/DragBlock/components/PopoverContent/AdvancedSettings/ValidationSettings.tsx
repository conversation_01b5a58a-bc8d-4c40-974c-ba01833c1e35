// Libraries
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Text, TextArea } from 'app/components/atoms';

// Molecules
import { SwitchLabel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/SwitchLabel';

// Constants
import { FIELD_CUSTOM_OPTIONS, FIELD_OPTIONS } from '../../../constant';

interface ValidationSettingsProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeBlockSetting: (key: string, value: any) => void;
  settings: any;
}

export const ValidationSettings: React.FC<ValidationSettingsProps> = props => {
  const { blockSetting, setBlockSetting, onChangeBlockSetting, settings } = props;
  const { t } = useTranslation();

  const shouldShowRequiredSwitch =
    blockSetting.id === 'phoneInput' ||
    FIELD_CUSTOM_OPTIONS.includes(blockSetting.type) ||
    FIELD_OPTIONS.includes(blockSetting.id);

  const shouldShowInvalidMessage = FIELD_OPTIONS.filter(
    item => !['lastNameInput', 'firstNameInput', 'nameInput'].includes(item),
  ).includes(blockSetting.id);

  return (
    <Fragment>
      {shouldShowRequiredSwitch && (
        <Fragment>
          <div className="ants-mt-4">
            <SwitchLabel
              label={t(translations.required.title)}
              checked={blockSetting?.required}
              onChange={checked =>
                blockSetting.isCustom
                  ? onChangeBlockSetting('required', checked)
                  : setBlockSetting({ ...blockSetting, required: checked })
              }
            />
          </div>
          {blockSetting?.required && (
            <div className="ants-flex ants-flex-col ants-mt-4">
              <Text className="ants-mb-5px">{t(translations.requireErrorMessage.title)}</Text>
              <TextArea
                showBorder
                value={blockSetting?.errorText}
                placeholder={t(translations.requireErrorMessage.placeholder, {
                  name: settings && settings?.name ? settings?.name.toLowerCase() : '',
                })}
                status={blockSetting?.required && !blockSetting?.errorText ? 'error' : ''}
                rows={3}
                onChange={e => setBlockSetting({ ...blockSetting, errorText: e.target.value })}
              />
              {blockSetting?.required && !blockSetting?.errorText && (
                <span style={{ marginTop: 5, marginLeft: '0.5rem', fontSize: '12px', color: 'rgb(255, 77, 79)' }}>
                  {t(translations.requireErrorMessage.placeholder, {
                    name: settings && settings?.name ? settings?.name.toLowerCase() : '',
                  })}
                </span>
              )}
            </div>
          )}
        </Fragment>
      )}

      {shouldShowInvalidMessage && (
        <div className="ants-flex ants-flex-col ants-mt-4">
          <Text className="ants-mb-5px">{t(translations.invalidMessage.title)}</Text>
          <TextArea
            showBorder
            value={blockSetting?.invalidText}
            placeholder={t(translations.invalidMessage.placeholder, {
              name: settings?.name,
            })}
            rows={3}
            onChange={e => setBlockSetting({ ...blockSetting, invalidText: e.target.value })}
          />
        </div>
      )}
    </Fragment>
  );
};
