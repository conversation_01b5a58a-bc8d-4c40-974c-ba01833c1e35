import type { Alignment, GridItem, GridLayout } from './types';

export function calculateGridLayout(n: number, x: number, alignment: Alignment = 'center'): GridLayout {
  if (n <= 0 || x <= 0) {
    return { totalColumns: 0, totalRows: 0, items: [] };
  }

  const rows = Math.ceil(n / x);

  const totalColumns = x * rows;
  const spanPerItem = totalColumns / x;

  const items: GridItem[] = [];

  for (let i = 0; i < n; i++) {
    const row = Math.floor(i / x) + 1;
    const colInRow = i % x;

    // Tính toán vị trí cho hàng cuối nếu không đủ phần tử
    const itemsInCurrentRow = row === rows ? n - (rows - 1) * x : x;

    let columnStart: number;

    if (itemsInCurrentRow === x) {
      // Hàng đầy đủ phần tử
      columnStart = colInRow * spanPerItem + 1;
    } else {
      // Hàng cuối không đủ phần tử - áp dụng alignment
      const totalSpanUsed = itemsInCurrentRow * spanPerItem;
      const emptySpace = totalColumns - totalSpanUsed;
      let startPosition: number;

      switch (alignment) {
        case 'left':
          // Bắt đầu từ cột 1
          startPosition = colInRow * spanPerItem + 1;
          break;
        case 'right':
          // Căn phải - bắt đầu từ cuối grid
          startPosition = emptySpace + colInRow * spanPerItem + 1;
          break;
        case 'center':
        default:
          // Căn giữa - tính toán chính xác
          const offset = emptySpace / 2;
          startPosition = offset + colInRow * spanPerItem + 1;
          break;
      }

      columnStart = startPosition;
    }

    const columnEnd = columnStart + spanPerItem;

    items.push({
      id: i + 1,
      gridColumnStart: columnStart,
      gridColumnEnd: columnEnd,
      gridRowStart: row,
      gridRowEnd: row + 1,
      span: spanPerItem,
    });
  }

  return {
    totalColumns,
    totalRows: rows,
    items,
  };
}
