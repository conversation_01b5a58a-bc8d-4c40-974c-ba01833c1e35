// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { THEME } from '@antscorp/antsomi-ui/es/constants/theme';

// Styled
import {
  EmotionLevel10Icon,
  EmotionLevel1Icon,
  EmotionLevel2Icon,
  EmotionLevel3Icon,
  EmotionLevel4Icon,
  EmotionLevel5Icon,
  EmotionLevel6Icon,
  EmotionLevel7Icon,
  EmotionLevel8Icon,
  EmotionLevel9Icon,
  NoIcon,
  StarIcon,
  YesIcon,
} from './icons';
import { dataAttrArrayToObject } from 'app/utils';
import { RATING_TYPE } from './constants';
import { RatingIconProps, RatingOption, RatingProps } from './types';
import { calculateGridLayout } from './utils';

export const RatingIcon: React.FC<RatingIconProps> = props => {
  const {
    style,
    className = '',
    size = 24,
    width = size,
    height = size,
    value,
    borderColor = THEME.token?.colorPrimary,
    backgroundColor = 'transparent',
    type = 'star',
  } = props;

  if (value === undefined) {
    return null;
  }

  switch (type) {
    case RATING_TYPE.EMOTION:
      const EmotionIcon = {
        1: EmotionLevel1Icon,
        2: EmotionLevel2Icon,
        3: EmotionLevel3Icon,
        4: EmotionLevel4Icon,
        5: EmotionLevel5Icon,
        6: EmotionLevel6Icon,
        7: EmotionLevel7Icon,
        8: EmotionLevel8Icon,
        9: EmotionLevel9Icon,
        10: EmotionLevel10Icon,
      }[value];

      if (!EmotionIcon) break;

      return (
        <EmotionIcon
          style={style}
          className={className}
          width={size || width}
          height={size || height}
          borderColor={borderColor}
          backgroundColor={backgroundColor}
        />
      );

    case RATING_TYPE.YESNO: {
      const YesNoIcon = {
        2: NoIcon,
        1: YesIcon,
      }[value];

      if (!YesNoIcon) break;

      return (
        <YesNoIcon
          style={style}
          width={size || width}
          height={size || height}
          className={className}
          borderColor={borderColor}
          backgroundColor={backgroundColor}
        />
      );
    }

    case RATING_TYPE.STAR: {
      return (
        <StarIcon
          style={style}
          className={className}
          width={size || width}
          height={size || height}
          borderColor={borderColor}
          backgroundColor={backgroundColor}
        />
      );
    }

    default:
      return null;
  }

  return null;
};

function Rating<T>(props: RatingProps<T>) {
  const {
    value,
    ratingType = 'star',
    ratingOptions = [],
    alignment = 'left',
    size = 30,
    optionColumn = 5,
    optionSpacing,
    labelPosition = 'left',
    labelStyle,
    innerOptionSpacing = '5px',
    borderColor = '#F7DA64',
    bgBeforeColor = '#FFFFFF',
    bgAfterColor = '#F7DA64',
    onChange,
    disabled = false,
    className,
    id,
    name,
    style,
    onClick,
    dataAttributes,
    ratingIconRender = RatingIcon,
  } = props;

  const attributesToSpread = dataAttrArrayToObject(dataAttributes);

  const [hoverValue, setHoverValue] = useState<string | null>(null);
  const [selectedValue, setSelectedValue] = useState<string | null>(null);

  const selectedIdx = useMemo(
    () => ratingOptions.findIndex(opt => opt.value === selectedValue),
    [ratingOptions, selectedValue],
  );

  useEffect(() => {
    setSelectedValue(value || null);
  }, [value]);

  const handleClick = (option: RatingOption<T>) => {
    if (disabled) return;

    if (onChange) {
      onChange(option);
    }
  };

  const handleMouseEnter = (value: string) => {
    if (!disabled) {
      setHoverValue(value);
    }
  };

  const handleMouseLeave = () => {
    if (!disabled) {
      setHoverValue(null);
    }
  };

  const getRatingIcon = (
    option: RatingOption<T>,
    idx: number,
    others: {
      style: React.CSSProperties;
      isSelected: boolean;
      isHovered: boolean;
    },
  ) => {
    const { isSelected, isHovered } = others;

    const iconClass = classNames('rating-icon', {
      'rating-icon--selected': isSelected,
      'rating-icon--hovered': isHovered,
    });

    let bgColor = isSelected ? bgAfterColor : bgBeforeColor;

    if (ratingType === RATING_TYPE.STAR && idx <= selectedIdx) {
      bgColor = bgAfterColor;
    }

    return ratingIconRender?.({
      size: size,
      type: ratingType,
      value: option.value,
      className: iconClass,
      borderColor: borderColor,
      backgroundColor: bgColor,
      style: others.style,
    });
  };

  const layout = calculateGridLayout(ratingOptions.length, optionColumn, alignment);

  const containerStyle: React.CSSProperties = {
    ...style,
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    alignItems: {
      left: 'flex-start',
      right: 'flex-end',
      center: 'center',
    }[alignment],
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${layout.totalColumns}, 1fr)`,
    gridTemplateRows: `repeat(${layout.totalRows}`,
    columnGap: optionSpacing?.horizontal || '8px',
    rowGap: optionSpacing?.vertical || '8px',
  };

  return (
    <div
      className={classNames('rating-component', className, {
        'rating-component--disabled': disabled,
      })}
      id={id}
      style={containerStyle}
      onClick={onClick}
      {...attributesToSpread}
    >
      <input type="hidden" name={name} value={value} />

      <div className="rating-options" style={gridStyle}>
        {ratingOptions.map((option, idx) => {
          const isSelected = value === option.value;
          const isHovered = hoverValue === option.value;

          return (
            <div
              key={option.value}
              className={classNames('rating-option', {
                'rating-option--selected': isSelected,
                'rating-option--hovered': isHovered,
              })}
              onClick={() => handleClick(option)}
              onMouseEnter={() => handleMouseEnter(option.value)}
              onMouseLeave={handleMouseLeave}
              style={{
                gap: innerOptionSpacing,
                gridColumnStart: layout.items[idx].gridColumnStart,
                gridColumnEnd: layout.items[idx].gridColumnEnd,
                gridRowStart: layout.items[idx].gridRowStart,
                gridRowEnd: layout.items[idx].gridRowEnd,
                cursor: disabled ? 'default' : 'pointer',
                fontSize: `${size}px`,
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                flexDirection: (
                  {
                    left: 'row',
                    right: 'row-reverse',
                    top: 'column',
                    bottom: 'column-reverse',
                  } as const
                )[labelPosition],

                justifyContent: (
                  {
                    left: 'start',
                    right: 'end',
                    center: 'center',
                  } as const
                )[alignment],
              }}
              tabIndex={idx}
              {...dataAttrArrayToObject(option.dataAttributes)}
            >
              <div
                style={{
                  width: 'fit-content',
                  ...labelStyle,
                }}
                className="rating-label"
              >
                {option.label}
              </div>

              {getRatingIcon(option, idx, {
                isSelected,
                isHovered,
                style: {
                  flexShrink: 0,
                },
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default Object.assign(Rating, {
  Icon: RatingIcon,
});
