import { RATING_TYPE } from './constants';
import { DataAttributes } from 'types/Component';
import { CSSProperties } from 'react';

export type Alignment = 'left' | 'center' | 'right';

export interface GridItem {
  id: number;
  gridColumnStart: number;
  gridColumnEnd: number;
  gridRowStart: number;
  gridRowEnd: number;
  span: number;
}

export interface GridLayout {
  totalColumns: number;
  totalRows: number;
  items: GridItem[];
}

export type RatingType = typeof RATING_TYPE[keyof typeof RATING_TYPE];

export const isRatingType = (v: unknown): v is RatingType => Object.values(RATING_TYPE).some(t => t === v);

export type RatingOption<T = {}> = {
  value: string;
  label: React.ReactNode;
  dataAttributes?: DataAttributes;
} & T;

export interface RatingProps<T = {}> {
  value?: string;
  ratingType?: 'star' | 'emotion' | 'yesno';
  ratingOptions?: RatingOption<T>[];
  alignment?: Alignment;
  size?: number;
  optionColumn?: number;
  labelPosition?: 'top' | 'right' | 'bottom' | 'left';
  labelStyle?: Pick<
    React.CSSProperties,
    | 'fontFamily'
    | 'color'
    | 'fontSize'
    | 'fontWeight'
    | 'lineHeight'
    | 'letterSpacing'
    | 'textTransform'
    | 'textDecoration'
    | 'fontStyle'
  >;
  optionSpacing?: {
    horizontal?: CSSProperties['gap'];
    vertical?: CSSProperties['gap'];
  };
  innerOptionSpacing?: CSSProperties['gap'];
  borderColor?: string;
  bgBeforeColor?: string;
  bgAfterColor?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
  style?: React.CSSProperties;
  dataAttributes?: DataAttributes;
  onClick?: () => void;
  onChange?: (option: RatingOption<T>) => void;
  ratingIconRender?: (props: RatingIconProps) => React.ReactNode;
}

export interface RatingIconProps {
  className?: string;
  size?: number;
  width?: number;
  height?: number;
  borderColor?: string;
  backgroundColor?: string;
  type?: 'star' | 'emotion' | 'yesno';
  value?: number | string;
  style?: React.CSSProperties;
}
