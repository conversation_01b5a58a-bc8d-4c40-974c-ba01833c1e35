// Libraries
import { useDrop } from 'react-dnd';
import React, { memo } from 'react';
import cn from 'classnames';
import { useDispatch, useSelector } from 'react-redux';

// Store

// Selectors
import { selectDragDropManage } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Styled
import { DropAreaWrapper } from './styled';

// Constants
import { BLOCK_ITEM } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';

// Queries
import { getMediaJsonSavedBlockQuery } from 'app/queries/SavedBlock/useGetSavedBlockDetail';

type TPlacement = 'top' | 'bottom';

interface DropAreaProps {
  placement?: TPlacement;
  dropIdx: number;
  dropBlockId: string | undefined;
  dropBlockType: string;
}

export const DropArea: React.FC<DropAreaProps> = memo(props => {
  // Hook
  const dispatch = useDispatch();

  // Props
  const { placement, dropIdx, dropBlockId, dropBlockType } = props;

  // Selector
  const { isDraggingBlock } = useSelector(selectDragDropManage);

  // Actions
  const { addBlock, reorderBlock } = mediaJsonDesignActions;

  // React dnd
  const [{ isOver }, dropRef] = useDrop(() => ({
    accept: [BLOCK_ITEM],
    collect: monitor => {
      return {
        isOver: monitor.isOver(),
      };
    },
    drop: async (item: any, monitor) => {
      if (!!dropBlockId) {
        if (item.isAddBlock) {
          const savedBlock = await getMediaJsonSavedBlockQuery(item.savedBlockId);

          dispatch(
            addBlock({
              dragBlockType: item.blockType,
              dropIndex: dropIdx,
              dropBlockId: dropBlockId,
              savedBlock,
            }),
          );

          return;
        }

        dispatch(
          reorderBlock({
            source: {
              id: item.blockId,
              index: item.index,
            },
            destination: {
              id: dropBlockId || 'root',
              index: dropIdx,
            },
          }),
        );
      }
    },
  }));

  return (
    <DropAreaWrapper
      ref={dropRef}
      className={cn({
        '!ants-flex': isDraggingBlock,
      })}
    >
      <div
        className={cn('__line', {
          '!ants-opacity-100': isOver,
        })}
      ></div>
    </DropAreaWrapper>
  );
});

DropArea.defaultProps = {
  dropIdx: 0,
};
