// Libraries
import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cn from 'classnames';

// Types
import { BlockProps, TSettings } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';
import { Icon } from 'app/components/atoms';

// Molecules
import { DropArea } from '../../molecules/DropArea';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import { selectTrackingModule } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Utils
import { handleError } from 'app/utils/handleError';
import { buildAtmTrackingParameters } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/utils';

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Workspace/components/organisms/GlobalTracking/index.tsx';

interface GlobalTrackingProps extends BlockProps {}

export const GlobalTracking: React.FC<GlobalTrackingProps> = props => {
  // Hooks
  const dispatch = useDispatch();

  // Actions
  const { setBlockSettings } = mediaJsonDesignActions;

  // Selectors
  const trackingModule = useSelector(selectTrackingModule);

  // Props
  const { blockId, type, settings, idx, parentBlockId } = props;
  const { isCollapsed, key } = settings || {};

  // Memo
  const memoizedData = useMemo(() => {
    let draftData = {
      impression: `#TRACKING_IMPRESSION#`,
      view: `#TRACKING_VIEWABLE#`,
      atmTrackingParameters: buildAtmTrackingParameters(trackingModule),
    };

    return draftData;
  }, [trackingModule]);

  // Handlers
  const updateBlockSettings = useCallback(
    (settings: Partial<TSettings>, ignoreUndoAction?: boolean) => {
      try {
        dispatch(
          setBlockSettings({
            blockId,
            settings,
            ignoreUndoAction,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'updateBlockSettings',
          args: {},
        });
      }
    },
    [dispatch, blockId, setBlockSettings],
  );

  return (
    <>
      {idx === 0 && <DropArea dropBlockId={parentBlockId} dropBlockType={type} dropIdx={idx} />}

      <div className="object-content">
        <div className="object-key-val">
          <span>
            <span
              className="ants-inline-block ants-cursor-pointer"
              onClick={() => updateBlockSettings({ isCollapsed: !isCollapsed }, true)}
            >
              <div className="collapse-icon-block">
                <Icon
                  type="icon-ants-caret-down"
                  className={cn('icon-collapse', {
                    '--collapsed': isCollapsed,
                  })}
                  size={10}
                />
              </div>

              {<ObjectKey objectKey={key} objectType={type} />}

              <span className="bracket ants-font-bold">{'{'}</span>
            </span>
          </span>

          <div
            className={cn('pushed-content object-container', {
              'node-ellipsis': isCollapsed,
            })}
            onClick={() =>
              isCollapsed &&
              updateBlockSettings(
                {
                  isCollapsed: false,
                },
                true,
              )
            }
          >
            {isCollapsed ? (
              '...'
            ) : (
              <div className="object-content ants-ml-10px">
                {Object.entries({ ...memoizedData }).map(([key, value], index) => (
                  <div key={key + index} style={{ paddingLeft: 20, borderLeft: `1px solid #ebebeb` }}>
                    <ObjectKey objectKey={key} objectType={''} />
                    <ObjectValue value={value} />
                  </div>
                ))}
              </div>
            )}
          </div>

          <span className="brace-row bracket ants-font-bold">
            <div
              className={cn('ants-text-center', {
                'ants-w-5': !isCollapsed,
              })}
            >{`}`}</div>
          </span>
        </div>
      </div>
    </>
  );
};
