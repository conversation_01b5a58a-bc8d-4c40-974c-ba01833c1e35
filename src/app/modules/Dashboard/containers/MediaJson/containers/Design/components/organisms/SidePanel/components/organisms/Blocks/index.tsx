// Libraries
import React, { useMemo, useState, startTransition, memo } from 'react';
import { useTranslation } from 'react-i18next';
import isEqual from 'react-fast-compare';

// Locales
import { translations } from 'locales/translations';

// Molecules
import { InputSearch } from 'app/components/molecules';

// Organisms
import StandardsBlocks from '../StandardsBlocks';
import { SavedBlocks } from '../SavedBlocks';

// Utils
import { handleError } from 'app/utils/handleError';

// Constants
import { STANDARDS_BLOCKS } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';

// Models
import { SavedBlock } from 'app/models';

// Styled
import { Wrapper } from './styled';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Workspace/components/organisms/SidePanel/components/organisms/Blocks/index.tsx';

type Props = {
  savedBlocks?: SavedBlock[] | undefined;
};

const blocksPropsAreEqual = (prev: Props, next: Props) => {
  return isEqual(prev.savedBlocks, next.savedBlocks);
};

export const Blocks = memo((props: Props) => {
  // const dispatch = useDispatch();

  const { savedBlocks } = props;

  // State
  const [search, setSearch] = useState('');

  // const template = useSelector(selectTemplate);
  // const currentViewPage = useSelector(selectCurrentViewPage);

  // I18n
  const { t } = useTranslation();

  // Memoized
  const memoizedSavedBlocks = useMemo(() => {
    return (
      savedBlocks
        ?.filter(block => block.name.toLowerCase().indexOf(search.toLowerCase()) !== -1)
        .map(block => ({ ...block.toJson(), isDisable: false })) || []
    );
  }, [savedBlocks, search]);

  const standardsBlocks = useMemo(() => {
    try {
      return (
        Object.values(STANDARDS_BLOCKS)
          .filter(block => block.label.toLowerCase().indexOf(search.toLowerCase()) !== -1)
          .map(block => ({
            ...block,
          })) || []
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'standardsBlocks',
        args: {},
      });
    }
  }, [search]);

  return (
    <Wrapper>
      {/* Search Box */}
      <div className="__search-box">
        <InputSearch
          placeholder={t(translations.searchBlock.title)}
          onChange={e => {
            startTransition(() => {
              setSearch(e.target.value);
            });
          }}
        />
      </div>

      {/* Standards Blocks */}
      <StandardsBlocks blocks={standardsBlocks as any[]} />

      {/* Saved blocks */}
      <SavedBlocks
        blocks={
          memoizedSavedBlocks?.map(({ id, icon, name, type, isDisable }) => ({
            id,
            name: type,
            label: name,
            icon,
            isDisable,
          })) || []
        }
      />
    </Wrapper>
  );
}, blocksPropsAreEqual);
