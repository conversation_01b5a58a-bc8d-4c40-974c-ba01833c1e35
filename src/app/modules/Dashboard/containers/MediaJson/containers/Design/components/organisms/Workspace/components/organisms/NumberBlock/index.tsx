// Libraries
import React, { memo } from 'react';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Styled
import { NumberBlockWrapper } from './styled';

interface NumberBlockProps extends BlockProps {}

export const NumberBlock: React.FC<NumberBlockProps> = memo(props => {
  // Props
  const { settings, type } = props;

  // Variables
  const { key, value } = settings || {};

  return (
    <NumberBlockWrapper className="variable-row">
      <ObjectKey objectKey={key} objectType={type} />
      <ObjectValue value={value} />
    </NumberBlockWrapper>
  );
});
