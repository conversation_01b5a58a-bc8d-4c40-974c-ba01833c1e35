// Libraries
import React, { memo, useState } from 'react';
import styled from 'styled-components/macro';
import tw from 'twin.macro';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { LoadingOutlined } from '@ant-design/icons';

// Libs
import { ActionCreators } from 'app/libs/redux-undo';

// Locales
import { translations } from 'locales/translations';

// Atoms
import { Button, Divider, Icon, Popover, Space, Spin, Text, Tooltip } from 'app/components/atoms';
import { Dropdown } from '@antscorp/antsomi-ui';

// Molecules
import { message, RadioGroup } from 'app/components/molecules';

// Icons
import { WarningIcon } from 'app/components/icons';

// Actions

// Selectors

// Utils
import { handleError } from 'app/utils/handleError';
import { random } from 'app/utils/common';
import { getTranslateMessage } from 'utils/messages';
import { getTemplateSetting } from '../../../slice/utils';

//Services
import { createMedia } from 'app/services/MediaJsonDesign/MediaJson';

// Slice
import {
  selectUndoAbleIndex,
  selectUndoAbleLimit,
  selectWorkspace,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';
import { ACTIONS } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/sagaActions';

// Store
import { createSagaAction } from 'store/configureStore';
import { mediaJsonDesignActions } from '../../../slice';
import { SAVE_TYPES } from '../../../constants';
import {
  StyledButtonDropdown,
  StyledButtonSave,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Toolbar/styled';
import classNames from 'classnames';

interface ToolbarProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  leftToolbar?: React.ReactNode;
  rightToolbar?: React.ReactNode;
  onClickCancel?: () => void;
  onClickSave?: () => void;
  saveText?: string;
  showCloneButton?: boolean;
  dropdownOnSave?: React.ReactNode;
}

const PATH = 'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Toolbar/index.tsx';

export const Toolbar: React.FC<ToolbarProps> = memo(props => {
  const dispatch = useDispatch();

  // State
  const [loadingCloneTemplate, setLoadingCloneTemplate] = useState<boolean>(false);

  // Props
  const { leftToolbar, rightToolbar, saveText, showCloneButton, dropdownOnSave } = props;

  // I18next
  const { t } = useTranslation();

  // Actions
  const { verifyFormat } = mediaJsonDesignActions;

  // Selectors
  // const toolbar = useSelector(selectToolbar);
  // const deviceType = useSelector(selectDeviceType);
  const undoAbleIndex = useSelector(selectUndoAbleIndex);
  const undoAbleLimit = useSelector(selectUndoAbleLimit);
  const workspace = useSelector(selectWorkspace);

  // state
  const [showPopupMoreOption, setShowPopupMoreOption] = useState(false);

  const handleSaveMediaTemplate = (type: string) => {
    dispatch(createSagaAction(ACTIONS.SAVE_MEDIA_JSON, { type }));
  };

  const handleCloneTemplate = async () => {
    try {
      setLoadingCloneTemplate(true);

      const { name } = workspace;

      await createMedia({
        template_name: `${name}-copy ${random(6)}`,
        template_type: 4,
        device_type: 1,
        template_setting: getTemplateSetting(workspace),
        properties: {
          ...workspace,
          isInitial: true,
        },
        thumbnail: workspace.thumbnail,
      });

      message.success(t(translations.cloneMediaTemplate.cloneTemplateSuccess.title));
    } catch (error) {
      message.error(t(translations.cloneMediaTemplate.cloneTemplateFailed.title));
    } finally {
      setLoadingCloneTemplate(false);
    }
  };

  const onClickVerifyFormat = () => {
    try {
      dispatch(verifyFormat({}));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'verifyFormat',
        args: {},
      });
    }
  };

  return (
    <ToolbarWrapper>
      <div className="__left">
        <Text>JSON</Text>
        {leftToolbar}
      </div>
      <div className="__right">
        {rightToolbar}
        <Space size={10}>
          {showCloneButton && (
            <Tooltip overlay={t(translations.cloneMediaTemplate.cloneTemplate.title)}>
              <Button
                loading={loadingCloneTemplate}
                icon={
                  <Icon
                    type="icon-ants-material-outline-content-copy"
                    overlayStyle={{ color: '#005FB8', fontSize: '20px' }}
                  />
                }
                onClick={handleCloneTemplate}
              />
            </Tooltip>
          )}

          <Button type="default" onClick={onClickVerifyFormat}>
            {t(translations.verifyFormat.title)}
          </Button>
        </Space>
        <Divider type="vertical" dot height={28} />
        <Space size={10}>
          <Icon
            type="icon-ants-undo-2"
            className="ants-text-primary ants-cursor-pointer"
            disabled={undoAbleIndex === 0}
            onClick={() => dispatch(ActionCreators.undo())}
          />
          <Icon
            type="icon-ants-redo-2"
            className="ants-text-primary ants-cursor-pointer"
            disabled={undoAbleLimit ? undoAbleIndex === undoAbleLimit - 1 : false}
            onClick={() => dispatch(ActionCreators.redo())}
          />
        </Space>
        <Divider type="vertical" dot height={28} />
        <Space size={10}>
          {typeof props.onClickCancel === 'function' && (
            <Button type="default" onClick={props.onClickCancel}>
              {t(translations.cancel.title, 'Cancel')}
            </Button>
          )}

          {/* <Button
            onClick={() => handleSaveMediaTemplate(SAVE_TYPES.SAVE)}
            type="primary"
            // disabled={isSavingTemplate}
            className="ants-relative"
          >
            {saveText}
          </Button> */}
          <Dropdown.Button
            menu={{
              items: [
                {
                  key: '0',
                  label: dropdownOnSave,
                },
              ],
              style: { width: 150 },
            }}
            type="primary"
            icon={<Icon type="icon-ants-expand-more" size={20} />}
            trigger={['click']}
            onClick={() => handleSaveMediaTemplate(SAVE_TYPES.SAVE)}
          >
            {saveText}
          </Dropdown.Button>

          {/* <Spin
            // spinning={isSavingTemplate}
            indicator={<LoadingOutlined className="ants-text-primary" style={{ fontSize: 18 }} spin />}
          >
            
          </Spin> */}
        </Space>
      </div>
    </ToolbarWrapper>
  );
});

Toolbar.defaultProps = {
  saveText: getTranslateMessage(translations.save.title, 'Save'),
  showCloneButton: true,
};

const ToolbarWrapper = styled.div`
  ${tw`ants-relative ants-flex ants-flex-shrink-0 ants-items-center ants-justify-between ants-w-full ants-h-50px ants-px-15px ants-bg-background ants-shadow-cus-sm ants-z-[390]`}

  .__left {
    ${tw`ants-flex ants-items-center ants-space-x-1`}
  }

  .__right {
    ${tw`ants-flex ants-items-center`}
  }
`;
