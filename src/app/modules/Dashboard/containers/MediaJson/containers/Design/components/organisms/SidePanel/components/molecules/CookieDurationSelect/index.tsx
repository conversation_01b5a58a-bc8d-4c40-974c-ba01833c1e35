// Libraries
import React, { ReactNode } from 'react';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { InputNumber, Select } from 'app/components/molecules';

// Styled
import { CookieDurationSelectWrapper } from './styled';

// Constants
import { COOKIE_DURATION_OPTION } from './constants';

// Utils
import { handleError } from 'app/utils/handleError';

// Types
import { useTranslation } from 'react-i18next';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/CookieDurationSelect/index.tsx';

interface CookieDurationSelectProps {
  label: string | ReactNode;
  type: string;
  duration: number;
  onChange?: ({ type, duration }: { type: string; duration: number }) => void;
}

export const CookieDurationSelect: React.FC<CookieDurationSelectProps> = props => {
  // I18n
  const { t } = useTranslation();

  // Props
  const { type, duration, label, onChange = () => {} } = props;

  const onChangeCookieDurationSelect = (type: any, option: any) => {
    try {
      // Callback onChange
      onChange({
        type,
        duration: option.duration,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const onChangeDuration = (duration: any) => {
    try {
      const cookieDurationType = Object.values(COOKIE_DURATION_OPTION).find(option => option.duration === duration);

      onChange({
        type: cookieDurationType ? cookieDurationType.value : type,
        duration,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeDuration',
        args: {},
      });
    }
  };

  return (
    <CookieDurationSelectWrapper>
      <Text className="!text-gray-4">{label}</Text>
      <Select value={type} options={Object.values(COOKIE_DURATION_OPTION)} onChange={onChangeCookieDurationSelect} />
      {type === COOKIE_DURATION_OPTION.AFTER_X_DAYS.value ? (
        <InputNumber
          value={duration}
          min={-1}
          max={5999}
          onChange={onChangeDuration}
          addonAfter={t(translations.days.title)}
        />
      ) : null}
    </CookieDurationSelectWrapper>
  );
};

CookieDurationSelect.defaultProps = {
  label: '',
  type: 'after_x_days',
  duration: 30,
};
