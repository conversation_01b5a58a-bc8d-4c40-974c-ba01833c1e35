// Libraries
import React, { memo } from 'react';

// Atoms
import { Text } from 'app/components/atoms';

// Styled
import { StyledSettingWrapper } from './styled';
import classNames from 'classnames';

interface SettingWrapperProps {
  label: string;
  className?: string;
  labelColor?: string;
  labelClassName?: string;
  labelStyle?: Record<string, any>;
  vertical?: boolean;
  children?: React.ReactNode;
}

export const SettingWrapper: React.FC<SettingWrapperProps> = memo(props => {
  // Props
  const { label, className, labelClassName, labelStyle, labelColor, vertical, children } = props;

  return (
    <StyledSettingWrapper className={className} vertical={vertical}>
      {label && (
        <Text className={classNames(labelClassName)} color={labelColor} style={labelStyle} title={label}>
          {label}
        </Text>
      )}
      <div className="ants-shrink-0">{children}</div>
    </StyledSettingWrapper>
  );
});

SettingWrapper.defaultProps = {
  label: '',
  className: '',
  labelColor: '#666666',
  labelClassName: '',
};
