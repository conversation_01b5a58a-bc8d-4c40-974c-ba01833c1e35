// Libraries
import React from 'react';

// Atoms
import { Switch, Text } from 'app/components/atoms';

// Styled
import { SwitchLabelWrapper } from './styled';

// Types
import { SwitchProps } from '@antscorp/antsomi-ui';

interface SwitchLabelProps extends SwitchProps {
  label: string;
}

export const SwitchLabel: React.FC<SwitchLabelProps> = props => {
  const { label, ...restOf } = props;

  return (
    <SwitchLabelWrapper className={restOf.className}>
      <Text>{label}</Text>
      <Switch {...restOf} />
    </SwitchLabelWrapper>
  );
};
