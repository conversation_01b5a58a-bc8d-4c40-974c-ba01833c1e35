// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

export const BlockWrapperStyled = styled.div`
  ${tw`ants-relative ants-transition-all`}

  &.--active {
    background-color: #cae5fe;
  }

  &.--error {
    * {
      color: var(--text-error-color) !important;
    }
  }
`;

export const OptionsWrapper = styled.div`
  ${tw`ants-absolute ants-right-2.5 ants-z-50`}
`;

export const PopoverContent = styled.div`
  ${tw`
  ants-bg-white ants-p-2.5 ants-gap-x-2.5
  ants-flex ants-items-center ants-justify-center`}

  .option__button {
    ${tw`ants-cursor-pointer hover:ants-text-primary ants-transition-colors`}
  }
`;
