// Libraries
import React from 'react';

// Atoms
import { Select } from 'app/components/molecules';

// Molecules
import { SettingWrapper } from '../SettingWrapper';

// Constant
import { ITEMS_ALIGN_TYPE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Styled
import { AlignSettingWrapper } from './styled';

// Utils
import { getTranslateMessage } from 'utils/messages';
import { translations } from 'locales/translations';

export type TItemsAlign = 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | undefined;
interface AlignEditProps {
  className?: string;
  value?: string;
  onChange: (align: TItemsAlign) => void;
  style?: Object;
}

interface AlignSettingProps extends AlignEditProps {
  label: string;
  labelClassName?: string;
}

const ITEMS_ALIGN_VALUE = [
  {
    label: getTranslateMessage(translations.stretch.title),
    value: ITEMS_ALIGN_TYPE.STRETCH,
  },
  {
    label: getTranslateMessage(translations.top.title),
    value: ITEMS_ALIGN_TYPE.TOP,
  },
  {
    label: getTranslateMessage(translations.center.title),
    value: ITEMS_ALIGN_TYPE.CENTER,
  },
  {
    label: getTranslateMessage(translations.bottom.title),
    value: ITEMS_ALIGN_TYPE.BOTTOM,
  },
  {
    label: getTranslateMessage(translations.baseline.title),
    value: ITEMS_ALIGN_TYPE.BASELINE,
  },
];

export const ItemsAlignSetting: React.FC<AlignSettingProps> = props => {
  const { label, labelClassName, ...restOf } = props;

  return (
    <SettingWrapper label={label} labelClassName={labelClassName}>
      <ItemsAlign {...restOf} />
    </SettingWrapper>
  );
};

export const ItemsAlign: React.FC<AlignEditProps> = props => {
  const { className, style, onChange, value } = props;

  return (
    <AlignSettingWrapper className={className} style={style}>
      <Select
        label={''}
        options={ITEMS_ALIGN_VALUE}
        onChange={select => onChange(select)}
        value={value}
        className={`ants-w-24`}
      />
    </AlignSettingWrapper>
  );
};
