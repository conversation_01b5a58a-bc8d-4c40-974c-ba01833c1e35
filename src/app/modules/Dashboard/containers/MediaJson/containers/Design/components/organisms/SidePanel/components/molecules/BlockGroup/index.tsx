// Libraries
import React from 'react';
import { DraggableChildrenFn } from 'react-beautiful-dnd';

// Components
import { Text } from 'app/components/atoms';

// Styled
import { BlockGroupContent, BlockGroupHeader, BlockGroupWrapper } from './styled';

interface BlockGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  label?: string;
  rightHeader?: React.ReactNode;
  isDroppable?: boolean;
  droppableType?: string;
  renderClone?: DraggableChildrenFn;
}

export const BlockGroup: React.FC<BlockGroupProps> = props => {
  // Props
  const { label, rightHeader, isDroppable, droppableType, children, renderClone, ...restOf } = props;

  return (
    <BlockGroupWrapper {...restOf}>
      <BlockGroupHeader>
        <Text size="medium">{label}</Text>
        {rightHeader}
      </BlockGroupHeader>
      <BlockGroupContent>{children}</BlockGroupContent>
    </BlockGroupWrapper>
  );
};

BlockGroup.defaultProps = {
  label: '',
  droppableType: 'DEFAULT',
  isDroppable: true,
};
