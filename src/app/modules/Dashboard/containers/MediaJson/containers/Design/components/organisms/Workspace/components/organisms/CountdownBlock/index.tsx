// Libraries
import React, { memo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cn from 'classnames';

// Types
import { BlockProps, TSettings } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';
import { Icon } from 'app/components/atoms';

// Hooks
import { useDeepCompareMemo } from 'app/hooks';

// Styled
import { CountdownBlockWrapper } from './styled';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import { selectBlockById } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Utils
import { handleError } from 'app/utils/handleError';

// Store

// Constants
import { STANDARDS_BLOCKS } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Workspace/components/organisms/CountdownBlock/index.tsx';

const { ARRAY } = STANDARDS_BLOCKS;

interface CountdownBlockProps extends BlockProps {}

export const CountdownBlock: React.FC<CountdownBlockProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // Props
  const { parentBlockId, blockId, settings, type, idx } = props;

  // Variables
  const { key, countdownType, timeZone, dynamic, static: countdownStatic, isCollapsed } = settings || {};

  // Selectors
  const parentBlock = useSelector(selectBlockById(parentBlockId));

  // Actions
  const { setBlockSettings } = mediaJsonDesignActions;

  // Memo
  const valueObject: Record<string, any> = useDeepCompareMemo(() => {
    let draftObject = {
      countdownType,
    };

    switch (countdownType) {
      case 'dynamic':
        draftObject = {
          ...draftObject,
          ...dynamic,
        };

        break;
      case 'static':
        draftObject = {
          ...draftObject,
          ...countdownStatic,
          timeZone,
        };
        break;
      default:
        break;
    }

    return draftObject;
  }, [countdownType, dynamic, countdownStatic]);

  const formattedObjectKey: string | number = useDeepCompareMemo(() => {
    switch (parentBlock.type) {
      case ARRAY.name:
        return idx;

      default:
        return key;
    }
  }, [idx, key, parentBlock.type]);

  const updateBlockSettings = useCallback(
    (settings: Partial<TSettings>) => {
      try {
        dispatch(
          setBlockSettings({
            blockId,
            settings,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'updateBlockSettings',
          args: {},
        });
      }
    },
    [dispatch, blockId, setBlockSettings],
  );

  return (
    <CountdownBlockWrapper className="object-content">
      <div className="object-key-val">
        <span>
          <span
            className="ants-inline-block ants-cursor-pointer"
            onClick={() => updateBlockSettings({ isCollapsed: !isCollapsed })}
          >
            <div className="collapse-icon-block">
              <Icon
                type="icon-ants-caret-down"
                className={cn('icon-collapse', {
                  '--collapsed': isCollapsed,
                })}
                size={10}
              />
            </div>

            <ObjectKey objectKey={formattedObjectKey} objectType={type} />

            <span className="bracket ants-font-bold">{'{'}</span>
          </span>
        </span>

        <div
          className={cn('pushed-content object-container', {
            'node-ellipsis': isCollapsed,
          })}
          onClick={() =>
            isCollapsed &&
            updateBlockSettings({
              isCollapsed: false,
            })
          }
        >
          {isCollapsed ? (
            '...'
          ) : (
            <div className="object-content ants-ml-10px">
              {Object.entries(valueObject).map(([key, value]) => {
                return (
                  <div
                    key={key}
                    style={{
                      paddingLeft: 20,
                      borderLeft: `1px solid #ebebeb`,
                    }}
                  >
                    <ObjectKey objectKey={key} objectType={key} />
                    <ObjectValue value={value} />
                  </div>
                );
              })}
            </div>
          )}
        </div>

        <span className="brace-row bracket ants-font-bold">
          <div
            className={cn('ants-text-center', {
              'ants-w-5': !isCollapsed,
            })}
          >{`}`}</div>
        </span>
      </div>
    </CountdownBlockWrapper>
  );
});
