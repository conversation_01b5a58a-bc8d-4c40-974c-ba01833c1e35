// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input, Switch } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../../molecules';
import { KeyCodeInput } from '../../../molecules/KeyCodeInput';

// Translations
import { translations } from 'locales/translations';

// Styled
import { BooleanWrapper } from './styled';

// Store

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Types
import { SidePanelContentProps } from '../../../../types';

interface BooleanProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

export const Boolean: React.FC<BooleanProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key, value } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  return (
    <BooleanWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      <SettingWrapper label={t(translations.value.title)}>
        <Switch checked={value} onChange={checked => updateBlockSettings({ value: checked })} />
      </SettingWrapper>
    </BooleanWrapper>
  );
});
