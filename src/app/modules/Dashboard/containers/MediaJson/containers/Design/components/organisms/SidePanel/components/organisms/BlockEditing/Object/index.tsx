// Libraries
import React, { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input } from 'app/components/atoms';

// Translations
import { translations } from 'locales/translations';

// Styled
import { ObjectWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

// Store

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
  selectParentBlockById,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Constants
import { STANDARDS_BLOCKS } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';

// Types
import { SidePanelContentProps } from '../../../../types';
import { KeyCodeInput } from '../../../molecules/KeyCodeInput';

interface ObjectProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Object/index.tsx';

const { ARRAY } = STANDARDS_BLOCKS;

export const Object: React.FC<ObjectProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const parentBlock = useSelector(selectParentBlockById(blockSelected.id));
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Memo
  const isInsideArray = parentBlock.type === ARRAY.name;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  const onAfterChangeKeyCode = (value: any) => {
    try {
      updateBlockSettings({
        key: value,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onAfterChangeKeyCode',
        args: {},
      });
    }
  };

  return (
    <ObjectWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        disabled={isInsideArray}
        onAfterChange={onAfterChangeKeyCode}
      />
    </ObjectWrapper>
  );
});
