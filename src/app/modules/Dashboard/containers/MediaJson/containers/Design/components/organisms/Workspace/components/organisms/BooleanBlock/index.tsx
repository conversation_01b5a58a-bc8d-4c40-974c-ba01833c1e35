// Libraries
import React, { memo } from 'react';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Styled
import { BooleanBlockWrapper } from './styled';

interface BooleanBlockProps extends BlockProps {}

export const BooleanBlock: React.FC<BooleanBlockProps> = memo(props => {
  // Props
  const { settings, type } = props;

  // Variables
  const { key, value } = settings || {};

  return (
    <BooleanBlockWrapper className="variable-row">
      <ObjectKey objectKey={key} objectType={type} />
      <ObjectValue value={value} />
    </BooleanBlockWrapper>
  );
});
