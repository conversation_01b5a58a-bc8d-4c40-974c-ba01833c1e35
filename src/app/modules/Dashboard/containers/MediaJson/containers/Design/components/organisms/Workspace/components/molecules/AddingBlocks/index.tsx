// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';

// Atoms
import { Text } from 'app/components/atoms';

// Images
import AddingBlocksImage from 'assets/images/adding-blocks.png';

// Styled
import { AddingBlocksWrapper } from './styled';
import { translations } from 'locales/translations';

interface AddingBlocksProps {}

export const AddingBlocks: React.FC<AddingBlocksProps> = memo(() => {
  // I18n
  const { t } = useTranslation();

  return (
    <AddingBlocksWrapper>
      <img
        src={AddingBlocksImage}
        alt="adding block"
        style={{
          height: '58px',
          width: 'auto',
        }}
      />
      <Text className="ants-text-black">{t(translations.addingBlocks.description)}</Text>
    </AddingBlocksWrapper>
  );
});
