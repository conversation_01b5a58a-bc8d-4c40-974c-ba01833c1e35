import styled from 'styled-components';
import tw from 'twin.macro';

export const BlockItemWrapper = styled.div`
  ${tw`ants-relative ants-flex ants-border ants-border-box ants-rounded-box ants-bg-white ants-transform-none`}
  cursor: grab;

  .__icon-drag {
    ${tw`ants-absolute ants-left-1 ants-text-large ants-text-cus-second ants-opacity-40`}
  }

  .__label {
    ${tw`ants-truncate ants-w-full`}
  }

  &.--block {
    ${tw`ants-flex-col ants-justify-center ants-items-center ants-py-10px ants-w-full`}

    .__icon-drag {
      ${tw`ants-top-10px`}
    }

    .__label {
      ${tw`ants-mt-3 ants-px-1 ants-text-center`}
    }
  }

  &.--inline {
    ${tw`ants-w-full ants-items-center ants-h-10 ants-space-x-2.5 ants-pr-2.5`}

    .__icon-drag {
      ${tw`ants-relative`}
    }
  }

  &.--disable {
    opacity: 0.5 !important;
  }

  &.--clone ~ div {
    transform: none !important;
  }
`;
