// Libraries
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { useDrop } from 'react-dnd';
import cn from 'classnames';
import { useDispatch, useSelector } from 'react-redux';

// Constants
import { BLOCK_ITEM } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Styled
import { EmptyDropAreaWrapper } from './styled';

// Store

// Slice
import { selectDragDropManage } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';

// Types
import { TSettings } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Utils
import { handleError } from 'app/utils/handleError';
import { createSagaAction } from 'store/configureStore';
import { getMediaJsonSavedBlockQuery } from 'app/queries/SavedBlock/useGetSavedBlockDetail';

interface DropAreaProps {
  blockId: string;
  blockType: string;
  isCollapsed?: boolean;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Workspace/components/molecules/EmptyDropArea/index.tsx';

export const EmptyDropArea: React.FC<DropAreaProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // Props
  const { blockId, isCollapsed } = props;

  // Actions
  const { addBlock, reorderBlock, setBlockSettings } = mediaJsonDesignActions;

  // Selector
  const { isDraggingBlock } = useSelector(selectDragDropManage);

  // React dnd
  const [{ isOver }, dropRef] = useDrop(() => ({
    accept: [BLOCK_ITEM],
    collect: monitor => {
      return {
        isOver: monitor.isOver(),
      };
    },
    drop: async (item: any, monitor) => {
      const savedBlock = await getMediaJsonSavedBlockQuery(item.savedBlockId);

      if (item.isAddBlock) {
        dispatch(
          addBlock({
            dragBlockType: item.blockType,
            dropIndex: 0,
            dropBlockId: blockId,
            savedBlock,
          }),
        );
        return;
      }

      // Reorder block
      dispatch(
        reorderBlock({
          source: {
            id: item.blockId,
            index: item.index,
          },
          destination: {
            id: blockId || 'root',
            index: 0,
          },
        }),
      );
    },
  }));

  // Effects
  useEffect(() => {
    if (isOver && isCollapsed) {
      updateBlockSettings(
        {
          isCollapsed: false,
        },
        true,
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOver, isCollapsed]);

  // Handlers
  const updateBlockSettings = useCallback(
    (settings: Partial<TSettings>, ignoreUndoAction?: boolean) => {
      try {
        dispatch(
          setBlockSettings({
            blockId,
            settings,
            ignoreUndoAction,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'updateBlockSettings',
          args: {},
        });
      }
    },
    [dispatch, blockId, setBlockSettings],
  );

  return (
    <EmptyDropAreaWrapper
      ref={dropRef}
      className={cn({
        '!ants-flex': isDraggingBlock,
      })}
    >
      <div
        className={cn('__line', {
          '!ants-opacity-100': isOver && !isCollapsed,
        })}
      />
    </EmptyDropAreaWrapper>
  );
});
