// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input } from 'app/components/atoms';

// Molecules
import { UploadImage } from 'app/components/molecules';

// Translations
import { translations } from 'locales/translations';

// Styled
import { ImageWrapper } from './styled';

// Store

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Types
import { SidePanelContentProps } from '../../../../types';
import { KeyCodeInput } from '../../../molecules/KeyCodeInput';

interface ImageProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

export const Image: React.FC<ImageProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key, value } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  return (
    <ImageWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      <div>
        <UploadImage
          focused={isShowErrorAlert}
          required={true}
          selectedImage={{ url: value } || ''}
          onRemoveImage={() =>
            updateBlockSettings({
              value: '',
            })
          }
          onChangeImage={({ url }) => {
            updateBlockSettings({
              value: url,
            });
          }}
        />
      </div>
    </ImageWrapper>
  );
});
