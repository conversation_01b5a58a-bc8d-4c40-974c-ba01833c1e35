// Libraries
import React, { memo } from 'react';
import { useSelector } from 'react-redux';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';

// Styled
import { TextBlockWrapper } from './styled';

// Slice
import { getDataBOfromDM, getRawDynamicData } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/utils';
import { selectCSDataOfGroup } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Hooks
import { useDeepCompareMemo } from 'app/hooks';

interface TextBlockProps extends BlockProps {}

export const TextBlock: React.FC<TextBlockProps> = memo(props => {
  // Props
  const { settings, type } = props;

  // Variables
  const { key, value, dynamics } = settings || {};

  // Selectors
  const contentSourcesData = useSelector(selectCSDataOfGroup);

  // Memo
  const rawValue = useDeepCompareMemo(() => {
    const { isDynamic } = dynamics?.value || {};

    const rawData = isDynamic
      ? getRawDynamicData({
          dataTableBO: getDataBOfromDM(dynamics?.value, contentSourcesData.data),
          dynamicItem: {
            ...dynamics?.value,
          },
        })
      : value;

    return rawData != null ? `${rawData}` : rawData;
  }, [dynamics?.value, contentSourcesData.data, value]);

  return (
    <TextBlockWrapper className="variable-row">
      <ObjectKey objectKey={key} objectType={type} />
      <ObjectValue value={rawValue} />
    </TextBlockWrapper>
  );
});
