// Libraries
import classNames from 'classnames';
import React from 'react';

// Atoms
import { Icon, Text } from 'app/components/atoms';

// Styled
import { BlockItemWrapper } from './styled';
import { handleError } from 'app/utils/handleError';
import { STANDARDS_BLOCKS } from '../../../../../../constants';
import { Modal } from 'app/components/molecules';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Constants
import { BLOCK_ITEM } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';
import { useDrag } from 'react-dnd';
import { useDispatch } from 'react-redux';
import { mediaJsonDesignActions } from '../../../../../../slice';

interface BlockItemProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: 'block' | 'inline';
  disable?: boolean;
  savedBlockId?: number;
  draggableId?: string;
  name?: string;
  label?: string;
  icon?: string;
  idx: number;
}

const BlockItem: React.FC<BlockItemProps> = props => {
  // Hooks
  const dispatch = useDispatch();

  // Props
  const { type, disable, name, label, icon, savedBlockId, className, ...restOf } = props;

  // Actions
  const { setDragDropManage } = mediaJsonDesignActions;

  const [{ isDragging }, drag] = useDrag(() => ({
    type: BLOCK_ITEM,
    collect: monitor => {
      return {
        isDragging: !!monitor.isDragging(),
      };
    },
    canDrag() {
      dispatch(setDragDropManage({ isDraggingBlock: true }));

      return !disable;
    },
    end() {
      setTimeout(() => {
        dispatch(setDragDropManage({ isDraggingBlock: false }));
      }, 500);
    },
    item: {
      isAddBlock: true,
      blockType: name,
      savedBlockId,
    },
  }));

  return (
    <BlockItemWrapper
      ref={drag}
      className={classNames(`--${type}`, className, {
        '--disable': disable,
      })}
      {...restOf}
      style={{
        opacity: isDragging ? 0.5 : 1,
      }}
      // onClick={onClickBlockItem}
    >
      <Icon type="icon-ants-double-three-dots" className="__icon-drag" />
      <Icon type={icon} className="ants-text-cus-second" />
      <Text className="__label">{label}</Text>
    </BlockItemWrapper>
  );
};

BlockItem.defaultProps = {
  type: 'block',
  disable: false,
  name: '',
  label: '',
  icon: 'icon-ants-square-outline',
  idx: 0,
  draggableId: 'item',
};

export default BlockItem;
