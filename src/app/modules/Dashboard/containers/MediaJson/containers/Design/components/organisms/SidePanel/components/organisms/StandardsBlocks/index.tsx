// Libraries
import React from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Molecules
import { BlockGroup } from '../../molecules/BlockGroup';
import BlockItem from '../../molecules/BlockItem';
import { Empty } from 'app/components/molecules';

// Types
import { TBlock } from 'types';

interface StandardsBlocksProps {
  blocks: TBlock[];
}

const StandardsBlocks: React.FC<StandardsBlocksProps> = props => {
  // Props
  const { blocks = [] } = props;

  // I18n
  const { t } = useTranslation();

  return (
    <BlockGroup label={t(translations.standards.title)}>
      <Wrapper>
        {blocks.map(({ name, label, icon, isDisable }, idx) => {
          return (
            <BlockItem
              key={name}
              name={name}
              disable={isDisable}
              draggableId={name}
              label={label}
              icon={icon}
              idx={idx}
            />
          );
        })}
      </Wrapper>

      {Array.isArray(blocks) && !blocks.length ? (
        <Empty description={`${t(translations.noBlocksFound.title)}`} />
      ) : null}
    </BlockGroup>
  );
};

export default StandardsBlocks;

const Wrapper = styled.div`
  ${tw`ants-grid ants-grid-cols-3 ants-gap-4`}
`;
