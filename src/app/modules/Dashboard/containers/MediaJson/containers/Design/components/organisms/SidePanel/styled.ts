import styled from 'styled-components';
import tw from 'twin.macro';

export const SidePanelWrapper = styled.div`
  ${tw`
    ants-relative ants-items-center ants-w-343px ants-flex-shrink-0 
    ants-h-full ants-bg-background ants-shadow-cus-xl ants-z-[380]
    ants-transition-all ants-duration-500
  `}
`;

export const ToggleSidePanelButton = styled.div`
  ${tw`ants-absolute ants-flex ants-left-[-21px] ants-items-center ants-top-1/2 ants-cursor-pointer`}

  transform: translateY(-50%);
`;

export const SidePanelHeader = styled.div`
  ${tw`ants-flex ants-justify-between ants-items-center ants-w-full ants-h-12 ants-shadow-cus-input ants-px-15px ants-border-b ants-border-gray-3 ants-z-[10]`}
`;

export const SidePanelContentWrapper = styled.div`
  ${tw`ants-absolute ants-z-[10]`}
`;

export const SidePanelFooter = styled.div`
  ${tw`ants-absolute ants-bottom-0 
  ants-h-12 ants-w-full 
  ants-flex ants-items-center ants-justify-end 
  ants-px-15px ants-border-t-2 ants-border-gray-3`}
`;
