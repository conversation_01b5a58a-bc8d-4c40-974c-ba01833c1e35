// Libraries
import React, { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import produce from 'immer';
import { DragDropContext, Draggable, DropResult, Droppable } from 'react-beautiful-dnd';
import classNames from 'classnames';

// Atoms
import { Button, Divider, Icon, Input, Space, Switch, Text } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/molecules';
import { KeyCodeInput } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/molecules/KeyCodeInput';

// Organisms
import { AddDynamicContent } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/AddDynamicContent';

// Translations
import { translations } from 'locales/translations';

// Styled
import { DynamicContentWrapper } from './styled';

// Store

// Utils
import { handleError } from 'app/utils/handleError';
import { addSuffixToName, random, reorder } from 'app/utils/common';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectContentSources,
  selectIsShowErrorAlert,
  selectJourneySettings,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Types
import { SidePanelContentProps } from '../../../../types';
import { InputNumber } from 'app/components/molecules';
import { REGEX } from 'constants/regex';

interface DynamicContentProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/DynamicContent/index.tsx';

export const DynamicContent: React.FC<DynamicContentProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // State
  const [isOpenDynamicContent, setOpenDynamicContent] = useState(false);
  const [editedField, setEditedField] = useState<Record<string, any> | null>(null);

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);
  const journeySettings = useSelector(selectJourneySettings);
  const contentSources = useSelector(selectContentSources);

  // Variables
  const { settings } = blockSelected;
  const { key, dynamics, showTop, orderDynamicKeys = [] } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  const onChangeDynamicContent = ({ key, data }) => {
    updateBlockSettings({
      dynamics: produce(dynamics, draftDynamics => {
        draftDynamics[key] = { ...draftDynamics[key], ...data };
      }),
    });
  };

  const onOkDynamicContent = dynamic => {
    const listAttributeKey = Object.values({ ...dynamics }).map(dynamic => get(dynamic, 'key', ''));
    const suffixFieldKey = addSuffixToName(get(dynamic, 'attribute.value', ''), listAttributeKey);

    // Case add dynamic content
    if (!editedField) {
      const newKey = random(8);

      updateBlockSettings({
        dynamics: produce(dynamics, draftDynamics => {
          draftDynamics[newKey] = {
            ...dynamic,
            id: newKey,
            key: suffixFieldKey,
            trackingClick: false,
          };
        }),
        orderDynamicKeys: produce(orderDynamicKeys, draft => {
          draft.push(newKey);
        }),
      });
    } else {
      onChangeDynamicContent({ key: editedField.id, data: { ...dynamic, key: suffixFieldKey } });
    }

    setOpenDynamicContent(false);
    setEditedField(null);

    try {
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onOkDynamicContent',
        args: {},
      });
    }
  };

  const onClickRemoveDynamicField = (dynamicKey: string) => {
    try {
      updateBlockSettings({
        dynamics: produce(dynamics, draftDynamics => {
          delete draftDynamics[dynamicKey];
        }),
        orderDynamicKeys: produce(orderDynamicKeys, draft => {
          const dynamicIndex = draft.findIndex(key => key === dynamicKey);

          if (dynamicIndex !== -1) {
            draft = draft.splice(dynamicIndex, 1);
          }
        }),
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickRemoveDynamicField',
        args: {},
      });
    }
  };

  const onDragEndFields = (result: DropResult) => {
    try {
      const { source, destination } = result;
      const { index: sourceIndex } = source || {};
      const { index: destinationIndex } = destination || {};

      if (destinationIndex != null) {
        const reorderDynamicKeys = reorder(orderDynamicKeys, sourceIndex, destinationIndex);

        updateBlockSettings({
          orderDynamicKeys: reorderDynamicKeys,
        });
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onDragEndFields',
        args: {},
      });
    }
  };

  const renderListDynamic = () => {
    return (
      <DragDropContext onDragEnd={onDragEndFields}>
        <div>
          <Droppable droppableId="fields">
            {provided => (
              <div ref={provided.innerRef} {...provided.droppableProps}>
                {/* <Divider dot style={{ marginTop: 0 }} /> */}
                <Text size="medium" className="ants-font-medium !ants-text-cus-dark ants-mb-2.5">
                  {t(translations.dynamicFields.title)}
                </Text>

                {orderDynamicKeys?.map((dynamicKey, index) => {
                  const dynamic = dynamics[dynamicKey];
                  const { key, attribute, trackingClick } = dynamic || {};
                  const { label } = attribute || {};
                  let errorMessage = '';

                  const isDuplicatedKey =
                    Object.values({ ...dynamics }).filter((dynamic: any) => dynamic?.key === key)?.length > 1;
                  const isInvalidKeyCode = !!key && !REGEX.KEY_CODE.test(key);

                  if (isDuplicatedKey) {
                    errorMessage = t(translations.duplicatedKey.title);
                  } else if (isInvalidKeyCode) {
                    errorMessage = t(translations.validateKeyCode.title);
                  }

                  return (
                    <Draggable key={dynamicKey} draggableId={dynamicKey} index={index}>
                      {(provided, snapshot) => (
                        <div
                          {...provided.draggableProps}
                          ref={provided.innerRef}
                          className={classNames({
                            'ants-bg-white ants-py-2': true,
                            'ants-border ants-border-gray-2 ants-rounded-sm': snapshot.isDragging,
                          })}
                        >
                          <div className="ants-flex ants-gap-2">
                            <div
                              style={{
                                height: 16,
                              }}
                              {...provided.dragHandleProps}
                            >
                              <Icon
                                type="icon-ants-double-three-dots"
                                size={16}
                                className="ants-text-cus-second ants-opacity-40"
                              />
                            </div>
                            <div className="ants-w-full">
                              <Input
                                focused={isShowErrorAlert}
                                required
                                value={key}
                                status={(isDuplicatedKey || isInvalidKeyCode) && isShowErrorAlert ? 'error' : ''}
                                errorMsg={errorMessage}
                                label={t(translations.fieldKeyCode.title)}
                                maxLength={50}
                                placeholder={t(translations.fieldKeyCode.placeholder)}
                                onAfterChange={value =>
                                  onChangeDynamicContent({ key: dynamicKey, data: { key: value } })
                                }
                              />
                              <SettingWrapper label={label} className="ants-mt-2.5">
                                <Space>
                                  <Button
                                    type="text"
                                    icon={<Icon type="icon-ants-edit-2" />}
                                    onClick={() => {
                                      setEditedField(dynamic);
                                      setOpenDynamicContent(true);
                                    }}
                                  />
                                  <Button
                                    type="text"
                                    icon={<Icon type="icon-ants-outline-delete" size={18} />}
                                    onClick={() => onClickRemoveDynamicField(dynamicKey)}
                                  />
                                </Space>
                              </SettingWrapper>
                              <SettingWrapper label={t(translations.trackingClick.title)} className="ants-mt-2.5">
                                <Switch
                                  checked={trackingClick}
                                  onChange={checked =>
                                    onChangeDynamicContent({ key: dynamicKey, data: { trackingClick: checked } })
                                  }
                                />
                              </SettingWrapper>
                            </div>
                          </div>

                          {index !== Object.keys({ ...dynamics }).length - 1 && <Divider dot />}
                        </div>
                      )}
                    </Draggable>
                  );
                })}

                {provided.placeholder}
              </div>
            )}
          </Droppable>

          <Button type="text" block={false} className="ants-mt-2.5" onClick={() => setOpenDynamicContent(true)}>
            + {t(translations.addMore.title)}
          </Button>
        </div>
      </DragDropContext>
    );
  };

  return (
    <DynamicContentWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      {renderListDynamic()}

      <AddDynamicContent
        showIndex={false}
        defaultData={editedField ? editedField : {}}
        visible={isOpenDynamicContent}
        defaultDynamicIndex={get(blockSelected, 'settings.defaultDynamicIndex', 1)}
        onCancel={() => {
          setOpenDynamicContent(false);
          setEditedField(null);
        }}
        showDisplayFormat={false}
        contentSources={contentSources}
        journeySettings={journeySettings}
        onOk={onOkDynamicContent}
      />

      <SettingWrapper label={t(translations.showTop.title)}>
        <InputNumber
          required
          min={1}
          max={90}
          value={showTop}
          onChange={value => updateBlockSettings({ showTop: value })}
        />
      </SettingWrapper>
    </DynamicContentWrapper>
  );
});
