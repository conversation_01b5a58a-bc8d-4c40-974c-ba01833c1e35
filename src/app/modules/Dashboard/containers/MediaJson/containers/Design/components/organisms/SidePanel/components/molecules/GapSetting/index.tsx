// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Molecules
import { EdgeSetting } from '../EdgeSetting';

export type GapSettings = {
  gapSuffix: string;
  linkedGapInput: boolean;
  gapX: string;
  gapY: string;
};

interface GapSettingProps {
  settings: GapSettings;
  onChange: (settings: GapSettings) => void;
}

export const GapSetting: React.FC<GapSettingProps> = memo(props => {
  // Props
  const { settings, onChange } = props;

  // I18n
  const { t } = useTranslation();

  return (
    <EdgeSetting
      label={t(translations.gap.title)}
      unit={settings.gapSuffix}
      linked={settings.linkedGapInput}
      values={[parseInt(settings.gapX), parseInt(settings.gapY), 0, 0]}
      edgeLabels={[t(translations.column.title), t(translations.row.title)]}
      onChange={({ values, linked, unit }) => {
        onChange({ gapX: values[0] + unit, gapY: values[1] + unit, gapSuffix: unit, linkedGapInput: linked });
      }}
    />
  );
});
