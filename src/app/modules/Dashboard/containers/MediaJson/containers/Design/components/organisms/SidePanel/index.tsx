// Libraries
import React, { memo, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { theme } from 'twin.macro';

// Selectors
import {
  selectBlocks,
  selectBlockSelected,
  selectSidePanel,
  selectTree,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Atoms
import { Button, Icon, ScrollBox, Text } from 'app/components/atoms';

// Organisms
import { Blocks } from './components/organisms/Blocks';
import {
  NumberEditing,
  ArrayEditing,
  ObjectEditing,
  SettingsEditing,
  TextEditing,
  BooleanEditing,
  ImageEditing,
  VideoEditing,
  CountdownEditing,
  DynamicContentEditing,
} from './components/organisms/BlockEditing';

// Icons
import { ToggleSidePanel } from 'app/components/icons';

// Utils
import { handleError } from 'app/utils/handleError';
import { checkIsDuplicatedKey } from '../../../slice/utils';

// Hooks queries
import { useGetMediaJsonSavedBlocks } from 'app/queries/SavedBlock';

// Styled
import { SidePanelFooter, SidePanelHeader, SidePanelWrapper, ToggleSidePanelButton } from './styled';

// Constants
import { SIDE_PANEL_TYPE } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';
import { REGEX } from 'constants/regex';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';

// Store

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Workspace/components/organisms/SidePanel/index.tsx';

const { BLOCKS, SETTINGS } = SIDE_PANEL_TYPE;

interface SidePanelProps extends React.HtmlHTMLAttributes<HTMLDivElement> {}

type TSidePanelType = {
  name?: string;
  label?: string;
  icon?: string;
};

export const SidePanel: React.FC<SidePanelProps> = memo(props => {
  const dispatch = useDispatch();

  // Props
  const { ...restOf } = props;

  // Actions
  const { setSidePanel } = mediaJsonDesignActions;

  // Selectors
  const sidePanel = useSelector(selectSidePanel);
  const blockSelected = useSelector(selectBlockSelected);
  const blocks = useSelector(selectBlocks);
  const tree = useSelector(selectTree);

  // Use Hooks queries
  const { data: savedBlocks, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetMediaJsonSavedBlocks();

  // Memo
  const sidePanelType: TSidePanelType = useMemo(() => {
    return Object.values(SIDE_PANEL_TYPE).find(type => type.name === sidePanel.type) || {};
  }, [sidePanel.type]);

  const isShowPanelHeader = useMemo(() => {
    return ![BLOCKS.name].includes(sidePanel.type);
  }, [sidePanel.type]);

  // Handlers
  const renderSidePanelContent = () => {
    try {
      const blockKeyCode = blockSelected?.settings?.key;
      const sidePanelContentProps = {
        isDuplicatedKey: checkIsDuplicatedKey({
          blocks,
          tree,
          blockId: blockSelected.id,
        }),
        isInvalidKey: !!blockKeyCode && !REGEX.KEY_CODE.test(blockKeyCode),
      };

      // Render Blocks
      if (sidePanel.type === SIDE_PANEL_TYPE.BLOCKS.name) {
        return <Blocks savedBlocks={savedBlocks} />;
      }

      if (sidePanel.type === SIDE_PANEL_TYPE.SETTINGS.name) {
        return <SettingsEditing />;
      }

      if (blockSelected?.settings) {
        switch (sidePanel.type) {
          case SIDE_PANEL_TYPE.ARRAY.name:
            return <ArrayEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.OBJECT.name:
            return <ObjectEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.TEXT.name:
            return <TextEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.NUMBER.name:
            return <NumberEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.BOOLEAN.name:
            return <BooleanEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.IMAGE.name:
            return <ImageEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.VIDEO.name:
            return <VideoEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.COUNT_DOWN.name:
            return <CountdownEditing {...sidePanelContentProps} />;

          case SIDE_PANEL_TYPE.DYNAMIC_CONTENT.name:
            return <DynamicContentEditing {...sidePanelContentProps} />;

          default:
            return null;
        }
      }

      // If BlockSelected not detect redirect to Blocks editing
      dispatch(
        setSidePanel({
          blockSelectedId: '',
          type: SIDE_PANEL_TYPE.BLOCKS.name,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const renderSidePanelHeader = () => {
    try {
      return (
        isShowPanelHeader && (
          <SidePanelHeader>
            <div className="ants-flex ants-items-center ants-space-x-2.5">
              {![SETTINGS.name].includes(sidePanel.type) ? <Icon type={sidePanelType.icon} /> : null}
              <Text size={16}>{sidePanelType.label}</Text>
            </div>
            <Button
              type="text"
              icon={<Icon type="icon-ants-home" />}
              onClick={() => {
                dispatch(
                  setSidePanel({
                    type: SIDE_PANEL_TYPE.BLOCKS.name,
                    blockSelectedId: '',
                  }),
                );
              }}
            ></Button>
          </SidePanelHeader>
        )
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderSidePanelHeader',
        args: {},
      });
    }
  };

  const onClickSettings = () => {
    try {
      dispatch(
        setSidePanel({
          type: SIDE_PANEL_TYPE.SETTINGS.name,
          blockSelectedId: '',
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSettings',
        args: {},
      });
    }
  };

  return (
    <SidePanelWrapper
      {...restOf}
      style={{
        ...restOf.style,
        marginRight: sidePanel.isCollapsed ? -343 : 0,
      }}
    >
      {/* Toggle side panel button */}
      <ToggleSidePanelButton
        onClick={() =>
          dispatch(
            setSidePanel({
              isCollapsed: !sidePanel.isCollapsed,
            }),
          )
        }
      >
        <ToggleSidePanel />
        <Icon
          type="icon-ants-angle-right"
          className="!ants-absolute ants-left-[14px] ants-transition-transform ants-duration-500"
          style={{
            transform: sidePanel.isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
          }}
          size={10}
        />
      </ToggleSidePanelButton>

      {/* Side panel header */}
      {renderSidePanelHeader()}

      {/* Side panel content */}
      <ScrollBox
        className={`ants-absolute ants-flex ants-flex-col`}
        style={{ height: `calc(100% - ${isShowPanelHeader ? theme('space.24') : theme('space.12')})` }}
        loadMore={() => {
          if (hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        }}
      >
        {renderSidePanelContent()}
      </ScrollBox>

      {/* Side panel footer */}
      <SidePanelFooter>
        <Button
          type={sidePanel.type === SETTINGS.name ? 'primary' : 'text'}
          icon={<Icon type="icon-ants-settings" />}
          onClick={onClickSettings}
        />
      </SidePanelFooter>
    </SidePanelWrapper>
  );
});
