// Libraries
import React, { useMemo, useState } from 'react';
import unionBy from 'lodash/unionBy';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';

// Atoms
import { Button, Icon, Input, ScrollBox, Space, Text, TextArea, Spin } from 'app/components/atoms';

// Translations
import { translations } from 'locales/translations';

// Constants
import { DROPPABLE_SAVED_BLOCK_ID } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Molecules
import { BlockGroup, BlockItem } from '../../molecules';
import { Empty, Form, InputSearch, Modal, Select } from 'app/components/molecules';

// Styled
import { SavedBlocksWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

// Hooks queries
import {
  useGetMediaJsonSavedBlocks,
  useUpdateMediaJsonSavedBlock,
  useDeleteMediaJsonSavedBlock,
  useAddMediaJsonSavedBlock,
} from 'app/queries/SavedBlock';

import { FORM_VALIDATE } from 'constants/formValidate';

type TSavedBlocksModal = {
  search: string;
  sortBy: string;
  isVisible: boolean;
  // isLoading: boolean;
  removeBlockId: number;
  handleBlockIdx: number;
};
interface SavedBlocksProps {
  blocks: any[];
}

enum Option {
  Edit = 'EDIT',
  Duplicate = 'DUPLICATE',
  Delete = 'DELETE',
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/SavedBlocks/index.tsx';

const defaultSavedBlocksModal = {
  search: '',
  sortBy: '',
  isVisible: false,
  removeBlockId: 0,
  handleBlockIdx: -1,
};

export const SavedBlocks: React.FC<SavedBlocksProps> = props => {
  // Props
  const { blocks } = props;

  // Hooks queries
  const { data: savedBlocks, isFetching: isFetchingSavedBlocks } = useGetMediaJsonSavedBlocks();
  const { mutate: updateSavedBlock, isLoading: isLoadingUpdateSavedBlock } = useUpdateMediaJsonSavedBlock();
  const { mutate: deleteSavedBlock, isLoading: isLoadingDeleteSavedBlock } = useDeleteMediaJsonSavedBlock();
  const { mutate: addSavedBlock, isLoading: isLoadingAddSavedBlock } = useAddMediaJsonSavedBlock();

  // Form
  const [editedForm] = Form.useForm();
  const [duplicatedForm] = Form.useForm();

  // State
  const [savedBlocksModal, setSavedBlocksModal] = useState<TSavedBlocksModal>(defaultSavedBlocksModal);

  // I18n
  const { t } = useTranslation();

  const blockTypeOptions = useMemo(() => {
    let options = [
      {
        value: '',
        label: t(translations.allBlocks.title),
      },
    ];

    if (savedBlocks) {
      unionBy(savedBlocks, 'type').forEach(savedBlock => {
        options.push({
          value: savedBlock.type,
          label: savedBlock.typeLabel,
        });
      });
    }

    return options;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [savedBlocks]);

  const memoizedSavedBlocks = useMemo(() => {
    return savedBlocks?.filter(({ type, name }) => {
      const isMatchWithSort = type.includes(savedBlocksModal.sortBy);
      const isMatchWithSearch = name.toLowerCase().trim().includes(savedBlocksModal.search.toLowerCase().trim());

      return isMatchWithSort && isMatchWithSearch;
    });
  }, [savedBlocksModal.sortBy, savedBlocksModal.search, savedBlocks]);

  // Handlers
  const onCancelSavedBlockModal = () => {
    try {
      setSavedBlocksModal(defaultSavedBlocksModal);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancelSavedBlockModal',
        args: {},
      });
    }
  };

  const onClickBlockOption = (option: Option, blockIdx: number) => {
    try {
      if (memoizedSavedBlocks && memoizedSavedBlocks.length) {
        const draftSavedBlocksModal = { ...savedBlocksModal };

        draftSavedBlocksModal.handleBlockIdx = blockIdx;

        const savedBlock = memoizedSavedBlocks[blockIdx].toJson();

        switch (option) {
          case Option.Edit:
            editedForm.setFieldsValue({
              id: savedBlock.id,
              blockName: savedBlock.name,
              blockNotes: savedBlock.notes,
            });

            duplicatedForm.resetFields();

            draftSavedBlocksModal.removeBlockId = 0;
            break;
          case Option.Duplicate:
            duplicatedForm.setFieldsValue({
              blockName: savedBlock.name + ' - Copy',
              blockNotes: savedBlock.notes,
              blockSettings: savedBlock.settings,
              blockType: savedBlock.type,
              blockChildren: savedBlock.children,
              icon: savedBlock.icon,
            });

            editedForm.resetFields();

            draftSavedBlocksModal.removeBlockId = 0;

            break;
          case Option.Delete:
            duplicatedForm.resetFields();
            editedForm.resetFields();
            draftSavedBlocksModal.removeBlockId = memoizedSavedBlocks[blockIdx].id;
            break;

          default:
            break;
        }

        setSavedBlocksModal(draftSavedBlocksModal);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickBlockOption',
        args: {},
      });
    }
  };

  const onClickCancel = () => {
    try {
      setSavedBlocksModal(state => ({
        ...state,
        removeBlockId: 0,
        handleBlockIdx: -1,
      }));

      editedForm.resetFields();
      duplicatedForm.resetFields();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickCancel',
        args: {},
      });
    }
  };

  const onClickConfirmDeletion = async () => {
    try {
      const { removeBlockId } = savedBlocksModal;

      // setSavedBlocksModal(state => ({
      //   ...state,
      //   isLoading: true,
      // }));

      // await deleteSavedBlock({
      //   id: removeBlockId,
      // });

      deleteSavedBlock({
        id: removeBlockId,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickConfirmDeletion',
        args: {},
      });
    } finally {
      // dispatch(createSagaAction(ACTIONS.FETCH_SAVED_BLOCKS));

      setSavedBlocksModal(state => ({
        ...state,
        removeBlockId: 0,
        handleBlockIdx: -1,
        // isLoading: false,
      }));
    }
  };

  const validateBlockName = (value: any, isDuplicate = false) => {
    const savedBlockNames = savedBlocks?.map(({ name }) => name);

    if (isDuplicate) {
      if (savedBlockNames?.some(name => name.trim() === value.trim())) {
        return Promise.reject(new Error(FORM_VALIDATE.DUPLICATED.message()));
      }
    } else {
      if ((savedBlockNames || [])[savedBlocksModal.handleBlockIdx] !== value.trim()) {
        if (savedBlockNames?.some(name => name.trim() === value.trim())) {
          return Promise.reject(new Error(FORM_VALIDATE.DUPLICATED.message()));
        }
      }
    }
    return Promise.resolve();
  };

  return (
    <>
      <Spin spinning={isFetchingSavedBlocks}>
        <BlockGroup
          label={t(translations.savedBlocks.title)}
          rightHeader={
            <Button
              type="text"
              onClick={() =>
                setSavedBlocksModal(state => ({
                  ...state,
                  isVisible: true,
                }))
              }
            >
              {t(translations.manage.title)}
            </Button>
          }
        >
          <SavedBlocksWrapper>
            {blocks.map(({ id, label, name, isDisable, icon }, idx) => {
              return (
                <BlockItem
                  key={id}
                  name={name}
                  disable={isDisable}
                  type={'inline'}
                  draggableId={id}
                  label={label}
                  icon={icon}
                  idx={idx}
                  savedBlockId={id}
                />
              );
            })}
          </SavedBlocksWrapper>

          {Array.isArray(blocks) && !blocks.length ? (
            <Empty description={`${t(translations.noBlocksFound.title)}`} />
          ) : null}
        </BlockGroup>
      </Spin>
      <Modal
        visible={savedBlocksModal.isVisible}
        destroyOnClose
        loading={isLoadingUpdateSavedBlock || isLoadingDeleteSavedBlock || isLoadingAddSavedBlock}
        centered
        width={900}
        title={t(translations.savedBlocks.title)}
        onCancel={onCancelSavedBlockModal}
        bodyStyle={{
          padding: '1.25rem 0px',
        }}
        // onOk={() => form.submit()}
        footer={null}
      >
        <div className="ants-flex ants-items-center ants-justify-between ants-pb-2 ants-px-5">
          <Space>
            <Text>{t(translations.sortBy.title)}:</Text>
            <Select
              className="ants-w-40"
              value={savedBlocksModal.sortBy}
              options={blockTypeOptions}
              onChange={sortBy =>
                setSavedBlocksModal(state => ({
                  ...state,
                  sortBy,
                }))
              }
            />
          </Space>
          <InputSearch
            className="ants-w-40"
            placeholder={t(translations.searchSavedBlocks.placeholder)}
            onChange={e =>
              setSavedBlocksModal(state => ({
                ...state,
                search: e.target.value,
              }))
            }
          />
        </div>
        <ScrollBox maxHeight={500} isPadding>
          <Space direction="vertical" size={10}>
            {memoizedSavedBlocks?.length ? (
              memoizedSavedBlocks.map(({ id, name, type, notes, icon }, idx) => {
                const { handleBlockIdx, removeBlockId } = savedBlocksModal;

                const isEdit = handleBlockIdx === idx && !!editedForm.getFieldValue('blockName');
                const isDuplicate = handleBlockIdx === idx && !!duplicatedForm.getFieldValue('blockName');
                const isRemove = handleBlockIdx === idx && !!removeBlockId;

                return (
                  <Space key={id} direction="vertical" size={10}>
                    <Form
                      name="editedForm"
                      form={editedForm}
                      labelCol={{ span: 24 }}
                      wrapperCol={{ span: 24 }}
                      initialValues={{
                        id: 0,
                        blockName: '',
                        blockNotes: '',
                      }}
                      onFinish={async (values: any) => {
                        try {
                          updateSavedBlock({
                            id: values.id,
                            name: values.blockName.trim(),
                            notes: values.blockNotes.trim(),
                          });
                        } catch (error) {
                          handleError(error, {
                            path: PATH,
                            name: 'onClickUpdateBlock',
                            args: {},
                          });
                        } finally {
                          setSavedBlocksModal(state => ({
                            ...state,
                            handleBlockIdx: -1,
                          }));

                          editedForm.resetFields();
                        }
                      }}
                    >
                      <div className="ants-flex ants-justify-between ants-items-start ants-py-2 ants-px-5 ants-space-x-2">
                        <div className={`ants-flex ants-items-start ants-space-x-5 ants-w-full`}>
                          <Icon type={icon} />
                          {isEdit ? (
                            <Form.Item
                              name="blockName"
                              className="ants-w-full"
                              rules={[
                                { required: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
                                {
                                  max: 70,
                                  message: FORM_VALIDATE.MAX.message('Block name', 70),
                                },
                                { whitespace: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
                                {
                                  validator: (_, value) => validateBlockName(value),
                                },
                              ]}
                            >
                              <Input placeholder={t(translations.enterNameBlock.placeholder)} />
                            </Form.Item>
                          ) : (
                            <Text>{name}</Text>
                          )}
                        </div>
                        <div className="ants-flex ants-items-center ants-space-x-2 ants-shrink-0">
                          <Button
                            className={classNames({
                              '!ants-text-gray-6': !isEdit,
                              '!ants-border-primary': isEdit,
                            })}
                            onClick={() => onClickBlockOption(Option.Edit, idx)}
                          >
                            <Icon type="icon-ants-edit-2" />
                          </Button>
                          <Button
                            className={classNames({
                              '!ants-text-gray-6': !isDuplicate,
                              '!ants-border-primary': isDuplicate,
                            })}
                            onClick={() => onClickBlockOption(Option.Duplicate, idx)}
                          >
                            <Icon type="icon-ants-material-outline-content-copy" />
                          </Button>
                          <Button
                            className={classNames({
                              '!ants-text-gray-6': !isRemove,
                              '!ants-border-primary': isRemove,
                            })}
                            onClick={() => onClickBlockOption(Option.Delete, idx)}
                          >
                            <Icon type="icon-ants-remove-trash" />
                          </Button>
                        </div>
                      </div>

                      {/* Edit Block */}
                      {!!isEdit && (
                        <div className="animate__animated animate__fadeIn ants-pl-[58px] ants-pr-5">
                          <Form.Item name="id" className="!ants-hidden">
                            <Input />
                          </Form.Item>
                          <Form.Item
                            name="blockNotes"
                            label={`${t(translations.notes.title)}:`}
                            rules={[
                              {
                                max: 500,
                                message: FORM_VALIDATE.MAX.message('Block Notes', 500),
                              },
                            ]}
                          >
                            <TextArea rows={4} placeholder={t(translations.addNote.placeholder)} />
                          </Form.Item>

                          <Space size={10} className="ants-mt-10px">
                            <Button type="primary" onClick={() => editedForm.submit()}>
                              {t(translations.updateBlockDetails.title)}
                            </Button>
                            <Button onClick={onClickCancel}>{t(translations.cancel.title)}</Button>
                          </Space>
                        </div>
                      )}
                    </Form>

                    {/* Duplicate Block */}

                    <Form
                      name="duplicatedForm"
                      form={duplicatedForm}
                      labelCol={{ span: 24 }}
                      wrapperCol={{ span: 24 }}
                      style={{
                        display: !!isDuplicate ? 'block' : 'none',
                      }}
                      initialValues={{
                        blockType: '',
                        settings: {},
                        blockName: '',
                        blockNotes: '',
                      }}
                      onFinish={async (values: any) => {
                        try {
                          addSavedBlock({
                            name: values.blockName,
                            type: values.blockType,
                            notes: values.blockNotes,
                            settings: duplicatedForm.getFieldValue('blockSettings'),
                            children: duplicatedForm.getFieldValue('blockChildren'),
                          });
                        } catch (error) {
                          handleError(error, {
                            path: PATH,
                            name: 'onClickSaveBlock',
                            args: {},
                          });
                        } finally {
                          setSavedBlocksModal(state => ({
                            ...state,
                            handleBlockIdx: -1,
                          }));

                          duplicatedForm.resetFields();
                        }
                      }}
                    >
                      <div className="animate__animated animate__fadeIn ants-flex ants-space-x-5 ants-bg-background-primary ants-p-5">
                        <Icon type={duplicatedForm.getFieldValue('icon')} />
                        <Form.Item name="blockType" className="!ants-hidden">
                          <Input />
                        </Form.Item>
                        <Space direction="vertical" size={10}>
                          <Form.Item
                            name="blockName"
                            label={t(translations.enterNameBlock.title)}
                            rules={[
                              { required: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
                              {
                                max: 70,
                                message: FORM_VALIDATE.MAX.message('Block name', 70),
                              },
                              { whitespace: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
                              {
                                validator: (_, value) => validateBlockName(value, true),
                              },
                            ]}
                          >
                            <Input placeholder={t(translations.enterNameBlock.placeholder)} />
                          </Form.Item>
                          <Form.Item
                            name="blockNotes"
                            label={`${t(translations.notes.title)}:`}
                            rules={[
                              {
                                max: 500,
                                message: FORM_VALIDATE.MAX.message('Block Notes', 500),
                              },
                            ]}
                          >
                            <TextArea rows={4} placeholder={t(translations.addNote.placeholder)} />
                          </Form.Item>
                          <Space size={10}>
                            <Button type="primary" onClick={() => duplicatedForm.submit()}>
                              {t(translations.saveBlock.title)}
                            </Button>
                            <Button onClick={onClickCancel}>{t(translations.cancel.title)}</Button>
                          </Space>
                        </Space>
                      </div>
                    </Form>

                    {/* Remove Block */}
                    {!!isRemove && (
                      <div className="ants-py-5 ants-pl-[58px] ants-bg-background-primary animate__animated animate__fadeIn">
                        <Text>{t(translations.deleteSavedBlock.title)}</Text>
                        <Space size={10} className="ants-mt-2">
                          <Button type="primary" onClick={onClickConfirmDeletion}>
                            {t(translations.deleteSavedBlock.buttonConfirm)}
                          </Button>
                          <Button onClick={onClickCancel}>{t(translations.cancel.title)}</Button>
                        </Space>
                      </div>
                    )}
                  </Space>
                );
              })
            ) : (
              <Empty />
            )}
          </Space>
        </ScrollBox>
      </Modal>
    </>
  );
};
