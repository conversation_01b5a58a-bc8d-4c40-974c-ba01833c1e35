// Libraries
import React, { memo, useCallback, useMemo } from 'react';
import cn from 'classnames';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';

// Molecules
import { EmptyDropArea } from '../../molecules/EmptyDropArea';
import { BlockWrapper } from '../../molecules/BlockWrapper';

// Organisms
import { ObjectBlock } from '../ObjectBlock';
import { CountdownBlock } from '../CountdownBlock';

// Store

// Styled
import { ArrayBlockWrapper } from './styled';

// Icons
import { Icon } from 'app/components/atoms';

// Types
import { BlockProps, TBlock } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';
import { TSettings } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Slice
import { selectChildrenBlocks } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';

// Constants
import { STANDARDS_BLOCKS } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';

interface ArrayBlockProps extends BlockProps {
  children?: React.ReactNode;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Workspace/components/organisms/ObjectBlock/index.tsx';

export const ArrayBlock: React.FC<ArrayBlockProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // Props
  const { blockId, type, settings } = props;
  const { isCollapsed, key } = settings || {};

  // Selectors
  const childrenBlocks = useSelector(selectChildrenBlocks(blockId));

  // Actions
  const { setBlockSettings } = mediaJsonDesignActions;

  // Memo
  const isDisableDrop = useMemo(() => {
    let isDisable = false;

    if (childrenBlocks.length) {
      isDisable = true;
    }

    return isDisable;
  }, [childrenBlocks.length]);

  // Handlers
  // Handlers
  const renderChildrenBlock = useCallback(
    ({ block, idx }: { block: TBlock; idx: number }) => {
      try {
        const blockProps: BlockProps = {
          blockId: block.id,
          parentBlockId: blockId,
          type: block.type,
          idx,
          settings: block.settings,
          savedBlockId: block.savedBlockId,
        };

        const renderContent = () => {
          switch (blockProps.type) {
            case STANDARDS_BLOCKS.OBJECT.name:
              return <ObjectBlock {...blockProps} isShowKey={true} />;

            case STANDARDS_BLOCKS.COUNT_DOWN.name:
              return <CountdownBlock {...blockProps} />;

            default:
              break;
          }
        };

        return (
          <BlockWrapper
            key={block.id + idx}
            {...blockProps}
            style={{
              paddingLeft: 20,
              borderLeft: `1px solid #ebebeb`,
            }}
          >
            {renderContent()}
          </BlockWrapper>
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'renderChildrenBlocks',
          args: {},
        });
      }
    },
    [blockId],
  );

  const updateBlockSettings = useCallback(
    (settings: Partial<TSettings>, ignoreUndoAction?: boolean) => {
      try {
        dispatch(
          setBlockSettings({
            blockId,
            settings,
            ignoreUndoAction,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'updateBlockSettings',
          args: {},
        });
      }
    },
    [dispatch, blockId, setBlockSettings],
  );

  return (
    <ArrayBlockWrapper className="object-content">
      <div className="object-key-val">
        <span>
          <span
            className="ants-inline-block ants-cursor-pointer"
            onClick={() => updateBlockSettings({ isCollapsed: !isCollapsed }, true)}
          >
            <div className="collapse-icon-block">
              <Icon
                type="icon-ants-caret-down"
                className={cn('icon-collapse', {
                  '--collapsed': isCollapsed,
                })}
                size={10}
              />
            </div>

            <ObjectKey objectKey={key} objectType={type} />

            <span className="bracket ants-font-bold">{'['}</span>
          </span>
        </span>

        <div
          className={cn('pushed-content object-container', {
            'node-ellipsis': isCollapsed,
          })}
          onClick={() =>
            isCollapsed &&
            updateBlockSettings(
              {
                isCollapsed: false,
              },
              true,
            )
          }
        >
          {isCollapsed ? (
            '...'
          ) : (
            <div className="object-content ants-ml-10px">
              {childrenBlocks.map((block, idx) => renderChildrenBlock({ block, idx }))}
            </div>
          )}

          {!isDisableDrop && <EmptyDropArea blockId={blockId} blockType={type} isCollapsed={isCollapsed} />}
        </div>

        <span className="brace-row bracket ants-font-bold">
          <div
            className={cn('ants-text-center', {
              'ants-w-5': !isCollapsed,
            })}
          >{`]`}</div>
        </span>
      </div>
    </ArrayBlockWrapper>
  );
});

ArrayBlock.defaultProps = {};
