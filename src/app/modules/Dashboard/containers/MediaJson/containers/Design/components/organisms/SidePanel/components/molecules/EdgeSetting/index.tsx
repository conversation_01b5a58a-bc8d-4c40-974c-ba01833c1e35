// Libraries
import React, { memo, useEffect, useState } from 'react';
import classNames from 'classnames';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Divider, Icon, Text } from 'app/components/atoms';

// Molecules
import { InputNumber } from 'app/components/molecules';

// Styled
import { EdgeSettingContent, EdgeSettingHeader, EdgeSettingWrapper } from './styled';

// Constants
import { UNIT } from 'constants/variables';

// Utils
import { handleError } from 'app/utils/handleError';
import { getTranslateMessage } from 'utils/messages';
import { TAlign } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Types
import { TUnit } from 'types';
import { AlignEdit } from '../AlignSetting';

export type TMargin = number | 'auto';
export type TValue = [TMargin, TMargin, TMargin, TMargin];
export type TValueLabel = [string, string, string?, string?];

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/EdgeSetting/index.tsx';

interface EdgeSettingProps {
  label: string;
  minValue?: number;
  maxValue?: number;
  edgeLabels?: TValueLabel;
  edgeLabelClassName?: string;
  contentClassName?: string;
  values: TValue;
  unit?: TUnit;
  linked?: boolean;
  onChange?: ({ values: TValue, linked: boolean, unit: TUnit, align: TAlign }) => void;
  align?: TAlign | null;
  disabledLinked?: boolean | false;
}

interface EdgeSettingState {
  values: TValue;
  unit: TUnit;
  linked: boolean;
  lastValueFocus: TMargin;
  align: TAlign | null;
}

export const EdgeSetting: React.FC<EdgeSettingProps> = memo(props => {
  const {
    label,
    values,
    minValue,
    maxValue,
    edgeLabels = [],
    edgeLabelClassName,
    contentClassName,
    unit,
    linked = false,
    onChange = () => {},
    align,
    disabledLinked = false,
  } = props;

  // State
  const [state, setState] = useState<EdgeSettingState>({
    values: [0, 0, 0, 0],
    unit: null,
    linked: false,
    lastValueFocus: 0,
    align: null,
  });

  useEffect(() => {
    setState(state => ({
      ...state,
      values,
      unit,
      linked,
      align: align as TAlign,
    }));
  }, [values, unit, linked, align]);

  // Handlers
  const onChangeState = (key: string, value: any) => {
    try {
      let draftValues: TValue = [...state.values];
      let draftLinked = state.linked;
      let draftAlign = state.align;

      // Case linked is true set all value is exam
      if (key === 'linked') {
        if (state.align) {
          // disabled
          return;
        }
        if (!state.linked) {
          draftValues = draftValues.map(() => state.lastValueFocus) as TValue;
        }
      }

      // Case align set linked = false & set values
      if (key === 'align') {
        draftLinked = false;

        draftValues = draftValues.map(value => (value === 'auto' ? 0 : value)) as TValue;

        if (value === state.align) {
          // reset align value
          value = '';
        } else {
          if (value === 'left') {
            // change values right to auto
            draftValues[1] = 'auto';
          } else if (value === 'right') {
            // change values left to auto
            draftValues[3] = 'auto';
          } else if (value === 'center') {
            // change values right + left to auto
            draftValues[1] = 'auto';
            draftValues[3] = 'auto';
          }
        }
      }

      setState(state => ({
        ...state,
        values: draftValues,
        [key]: value,
      }));

      // Callback onchange
      onChange({
        values: draftValues,
        unit: state.unit,
        align: draftAlign,
        linked: draftLinked,
        [key]: value,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeState',
        args: {},
      });
    }
  };

  const onChangeValue = (value: any, index: number) => {
    try {
      let draftValues = [...state.values];

      if (state.linked) {
        draftValues = draftValues.map(() => value);
      } else {
        draftValues[index] = value;
      }

      setState(state => ({ ...state, lastValueFocus: value }));
      onChangeState('values', draftValues);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeValue',
        args: {},
      });
    }
  };

  return (
    <EdgeSettingWrapper>
      <EdgeSettingHeader>
        <Text className="!ants-text-gray-4">{label}</Text>
        <div className="ants-flex ants-items-center ants-space-x-3">
          {state.align !== null ? (
            <div className="ants-flex ants-items-center ants-space-x-2">
              <AlignEdit align={state.align} onChange={value => onChangeState('align', value)} />
              <Divider type="vertical" dot height={12} />
            </div>
          ) : null}
          {state.unit ? (
            <div className="ants-flex ants-items-center ants-space-x-2">
              {Object.values(UNIT).map(({ value, label }) => {
                return (
                  <Text
                    key={value}
                    className={classNames('ants-cursor-pointer', {
                      '!ants-text-primary ants-font-bold': state.unit === value,
                      '!ants-text-gray-4': state.unit !== value,
                    })}
                    onClick={() => onChangeState('unit', value)}
                  >
                    {label}
                  </Text>
                );
              })}
              <Divider type="vertical" dot height={12} />
            </div>
          ) : null}
          <Icon
            type={`icon-ants-${state.linked ? 'hyperlink' : 'hyperlink-off'}`}
            size={18}
            className={classNames('ants-cursor-pointer', {
              '!ants-text-gray-4': !state.linked,
              '!ants-text-primary': state.linked,
            })}
            onClick={disabledLinked ? () => {} : () => onChangeState('linked', !state.linked)}
            disabled={disabledLinked ? true : !!state.align}
          />
        </div>
      </EdgeSettingHeader>
      <EdgeSettingContent className={contentClassName}>
        {state.values.map((value, index) => {
          if (!edgeLabels[index]) {
            return null;
          }

          return (
            <div key={edgeLabels[index]}>
              <Text className={classNames(edgeLabelClassName, 'ants-mb-5px !ants-text-gray-4')}>
                {edgeLabels[index]}
              </Text>
              {value !== 'auto' ? (
                <InputNumber
                  disabled={state.linked && index !== 0}
                  value={value}
                  required
                  min={minValue}
                  max={maxValue}
                  onFocus={e => setState(state => ({ ...state, lastValueFocus: +e.target.value }))}
                  onChange={val => onChangeValue(val, index)}
                />
              ) : (
                <Text size="medium" className="ants-mt-2">
                  AUTO
                </Text>
              )}
            </div>
          );
        })}
      </EdgeSettingContent>
    </EdgeSettingWrapper>
  );
});

EdgeSetting.defaultProps = {
  label: '',
  values: [0, 0, 0, 0],
  minValue: 0,
  maxValue: 9999,
  edgeLabels: [
    getTranslateMessage(translations.top.title),
    getTranslateMessage(translations.right.title),
    getTranslateMessage(translations.bottom.title),
    getTranslateMessage(translations.left.title),
  ],
  unit: null,
  align: null,
};
