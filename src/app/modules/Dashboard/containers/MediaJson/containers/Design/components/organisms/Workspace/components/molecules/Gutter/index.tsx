// Libraries
import { useReducer } from 'react';
import { useSelector } from 'react-redux';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Constants
import { ROOT_BLOCK_ID } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';

// Selectors
import { selectWorkspace } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Styled
import { GutterCell, GutterStyled } from './styled';

interface GutterProps {}

const LINE_HEIGHT = 18;

export const Gutter: React.FC<GutterProps> = () => {
  // Selector
  const workspace = useSelector(selectWorkspace);

  const [, forceUpdate] = useReducer(x => x + 1, 0);

  // Variables
  const rootBlockHeight = document.getElementById(ROOT_BLOCK_ID)?.clientHeight;
  const gutterCells =
    Array.from({ length: Math.floor((rootBlockHeight || 0) / LINE_HEIGHT) }, (_, index) => index + 1) || [];

  // Effects
  useDeepCompareEffect(() => {
    forceUpdate();
  }, [workspace]);

  return (
    <GutterStyled>
      {gutterCells.map(cellIdx => (
        <GutterCell key={cellIdx}>{cellIdx}</GutterCell>
      ))}
    </GutterStyled>
  );
};
