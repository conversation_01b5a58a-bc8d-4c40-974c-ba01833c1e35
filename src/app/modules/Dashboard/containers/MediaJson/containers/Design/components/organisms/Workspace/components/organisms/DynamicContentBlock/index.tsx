// Libraries
import React, { memo, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cn from 'classnames';
import produce from 'immer';

// Types
import { BlockProps, TSettings } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';
import { Icon } from 'app/components/atoms';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Styled
import { DynamicContentWrapper } from './styled';

// Utils
import { getDataBOfromDM, getRawDynamicData } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/utils';
import { handleError } from 'app/utils/handleError';

// Slice
import { selectCSDataOfGroup } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';

interface DynamicContentProps extends BlockProps {}

type TDynamicItem = {
  isCollapsed: boolean;
  data: Array<Record<string, any>>;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Workspace/components/organisms/DynamicContentBlock/index.tsx';

export const DynamicContentBlock: React.FC<DynamicContentProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // Actions
  const { setBlockSettings } = mediaJsonDesignActions;

  // Props
  const { settings, type, blockId } = props;
  const { showTop, dynamics, orderDynamicKeys, isCollapsed, rawData } = settings;

  // Variables
  const { key } = settings || {};

  // Selectors
  const contentSourcesData = useSelector(selectCSDataOfGroup);

  // Effects
  useDeepCompareEffect(() => {
    const draftData: Array<TDynamicItem> = [];

    Array.from({ length: showTop }).forEach((_, idx) => {
      const objectItems: Array<Record<string, any>> = [];

      orderDynamicKeys?.forEach(dynamicKey => {
        const dynamicItem = dynamics[dynamicKey];
        const { key } = dynamicItem || {};

        objectItems.push({
          key,
          value: getRawDynamicData({
            dataTableBO: getDataBOfromDM(dynamicItem, contentSourcesData.data),
            dynamicItem: {
              ...dynamicItem,
              index: idx + 1,
            },
          }),
        });
      });

      draftData.push({
        isCollapsed: rawData[idx]?.isCollapsed || false,
        data: objectItems,
      });
    });

    updateBlockSettings(
      {
        rawData: draftData,
      },
      true,
    );
  }, [showTop, dynamics, orderDynamicKeys]);

  // Handlers
  const toggleObjectItem = (index: number) => {
    try {
      updateBlockSettings(
        {
          rawData: produce(rawData, draftData => {
            draftData[index].isCollapsed = !draftData[index]?.isCollapsed;
          }),
        },
        true,
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'toggleObjectItem',
        args: {},
      });
    }
  };

  const updateBlockSettings = useCallback(
    (settings: Partial<TSettings>, ignoreUndoAction?: boolean) => {
      try {
        dispatch(
          setBlockSettings({
            blockId,
            settings,
            ignoreUndoAction,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'updateBlockSettings',
          args: {},
        });
      }
    },
    [dispatch, blockId, setBlockSettings],
  );

  const renderDynamicObjects = () => {
    try {
      return rawData.map((objectItem, index) => {
        const { isCollapsed, data } = objectItem;

        return (
          <div key={index} className="object-content" style={{ paddingLeft: 20, borderLeft: `1px solid #ebebeb` }}>
            <div className="object-key-val">
              <span>
                <span className="ants-inline-block ants-cursor-pointer" onClick={() => toggleObjectItem(index)}>
                  <div className="collapse-icon-block">
                    <Icon
                      type="icon-ants-caret-down"
                      className={cn('icon-collapse', {
                        '--collapsed': isCollapsed,
                      })}
                      size={10}
                    />
                  </div>

                  <ObjectKey objectKey={index} objectType={type} />

                  <span className="bracket ants-font-bold">{'{'}</span>
                </span>
              </span>

              <div
                className={cn('pushed-content object-container', {
                  'node-ellipsis': isCollapsed,
                })}
                onClick={() => isCollapsed && toggleObjectItem(index)}
              >
                {isCollapsed ? (
                  '...'
                ) : (
                  <div className="object-content ants-ml-10px">
                    {data.map(({ key, value }, index) => (
                      <div key={key + index} style={{ paddingLeft: 20, borderLeft: `1px solid #ebebeb` }}>
                        <ObjectKey objectKey={key} objectType={''} />
                        <ObjectValue value={value != null ? `${value}` : value} />
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <span className="brace-row bracket ants-font-bold">
                <div
                  className={cn('ants-text-center', {
                    'ants-w-5': !isCollapsed,
                  })}
                >{`}`}</div>
              </span>
            </div>
          </div>
        );
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderDynamicObjects',
        args: {},
      });
    }
  };

  return (
    <DynamicContentWrapper className="object-content">
      <div className="object-key-val">
        <span>
          <span
            className="ants-inline-block ants-cursor-pointer"
            onClick={() => updateBlockSettings({ isCollapsed: !isCollapsed }, true)}
          >
            <div className="collapse-icon-block">
              <Icon
                type="icon-ants-caret-down"
                className={cn('icon-collapse', {
                  '--collapsed': isCollapsed,
                })}
                size={10}
              />
            </div>

            <ObjectKey objectKey={key} objectType={type} />

            <span className="bracket ants-font-bold">{'['}</span>
          </span>
        </span>

        <div
          className={cn('pushed-content object-container', {
            'node-ellipsis': isCollapsed,
          })}
          onClick={() =>
            isCollapsed &&
            updateBlockSettings(
              {
                isCollapsed: false,
              },
              true,
            )
          }
        >
          {isCollapsed ? '...' : <div className="object-content ants-ml-10px">{renderDynamicObjects()}</div>}
        </div>

        <span className="brace-row bracket ants-font-bold">
          <div
            className={cn('ants-text-center', {
              'ants-w-5': !isCollapsed,
            })}
          >{`]`}</div>
        </span>
      </div>
    </DynamicContentWrapper>
  );
});
