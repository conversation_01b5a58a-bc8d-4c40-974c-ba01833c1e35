// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input } from 'app/components/atoms';

// Translations
import { translations } from 'locales/translations';

// Styled
import { NumberWrapper } from './styled';

// Store

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';
import { InputNumber } from 'app/components/molecules';

// Types
import { SidePanelContentProps } from '../../../../types';
import { KeyCodeInput } from '../../../molecules/KeyCodeInput';

interface NumberProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

export const Number: React.FC<NumberProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Hooks
  const dispatch = useDispatch();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key, value } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  return (
    <NumberWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      <InputNumber
        required
        value={value}
        label={t(translations.value.title)}
        placeholder={t(translations.value.placeholder)}
        width={'100%'}
        onChange={value => updateBlockSettings({ value })}
      />
    </NumberWrapper>
  );
});
