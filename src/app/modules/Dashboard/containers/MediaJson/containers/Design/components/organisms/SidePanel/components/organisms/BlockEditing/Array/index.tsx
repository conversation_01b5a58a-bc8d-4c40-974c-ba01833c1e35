// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Organisms
import { KeyCodeInput } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/molecules/KeyCodeInput';

// Translations
import { translations } from 'locales/translations';

// Styled
import { ArrayWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

// Store

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Types
import { SidePanelContentProps } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/types';

interface ArrayProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Array/index.tsx';

export const Array: React.FC<ArrayProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  const onAfterChangeKeyCode = (value: any) => {
    try {
      updateBlockSettings({
        key: value,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onAfterChangeKeyCode',
        args: {},
      });
    }
  };

  return (
    <ArrayWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={onAfterChangeKeyCode}
      />
    </ArrayWrapper>
  );
});
