// Libraries
import React, { useMemo, memo } from 'react';

interface ObjectKeyProps {
  objectKey: string | number;
  objectType: string;
}

export const ObjectKey: React.FC<ObjectKeyProps> = memo(props => {
  //  Props
  const { objectKey, objectType } = props;

  const formattedObjectKey = useMemo(() => {
    const typeOfObjectKey = typeof objectKey;

    switch (typeOfObjectKey) {
      case 'string':
        return `"${objectKey || objectType}"`;

      case 'number':
        return objectKey || 0;

      default:
        return '';
    }
  }, [objectKey, objectType]);

  const objectKeyClass = useMemo(() => {
    const typeOfObjectKey = typeof objectKey;

    switch (typeOfObjectKey) {
      case 'number':
        return 'object-key__number';

      default:
        return '';
    }
  }, [objectKey]);

  return (
    <span className={`object-key`}>
      <span className="ants-inline-block">
        <span className={objectKeyClass}>{formattedObjectKey}</span>
      </span>
      <span className="ants-mx-1">:</span>
    </span>
  );
});

ObjectKey.defaultProps = {
  objectKey: '',
};
