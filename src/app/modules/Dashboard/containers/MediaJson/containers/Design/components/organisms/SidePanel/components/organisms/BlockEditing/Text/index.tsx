// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input } from 'app/components/atoms';

// Molecules
import { DynamicSetting } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/molecules/DynamicSetting';
import { KeyCodeInput } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/molecules/KeyCodeInput';

// Translations
import { translations } from 'locales/translations';

// Styled
import { TextWrapper } from './styled';

// Store

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Types
import { SidePanelContentProps } from '../../../../types';
import { SettingWrapper } from '../../../molecules';

interface TextProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

export const Text: React.FC<TextProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key, value, dynamics } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  return (
    <TextWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      <DynamicSetting
        label={t(translations.value.title)}
        settings={dynamics.value}
        showDisplayFormat={false}
        showTrackingClick={true}
        onChange={setting => {
          updateBlockSettings({
            dynamics: {
              value: setting,
            },
          });
        }}
        renderStatic={() => (
          <Input
            value={value}
            placeholder={t(translations.value.placeholder)}
            onAfterChange={value => updateBlockSettings({ value })}
          />
        )}
      />
    </TextWrapper>
  );
});
