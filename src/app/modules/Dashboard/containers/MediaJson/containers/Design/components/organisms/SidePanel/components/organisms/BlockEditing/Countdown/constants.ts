// Translations
import { translations } from 'locales/translations';

// Utils
import { getTranslateMessage } from 'utils/messages';

export const COUNTDOWN_TYPE = {
  STATIC: {
    value: 'static',
    label: getTranslateMessage(translations.static.title),
  },
  DYNAMIC: {
    value: 'dynamic',
    label: getTranslateMessage(translations.dynamic.title),
  },
};

export const DYNAMIC_END_TIME = {
  DAYS: { label: getTranslateMessage(translations.days.title), value: 'days' },
  HOURS: { label: getTranslateMessage(translations.hours.title), value: 'hours' },
  MINUTES: { label: getTranslateMessage(translations.minutes.title), value: 'minutes' },
  SECONDS: { label: getTranslateMessage(translations.seconds.title), value: 'seconds' },
};
