// Libraries
import { useDispatch, useSelector } from 'react-redux';
import React, { memo, useMemo, useState } from 'react';
import { useDrag } from 'react-dnd';
import cn from 'classnames';
import { useTranslation } from 'react-i18next';
import mergeWith from 'lodash/mergeWith';
import isEmpty from 'lodash/isEmpty';

// Translations
import { translations } from 'locales/translations';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Atoms
import { Alert, Button, Divider, Icon, Input, Popover, Text, TextArea, Tooltip } from 'app/components/atoms';

// Molecules
import { DropArea } from '../DropArea';
import { Form, Modal } from 'app/components/molecules';

// Icons
import { WarningIcon } from 'app/components/icons';

// Types
import { BlockProps, TBlock } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Queries
import { useGetMediaJsonSavedBlockDetail } from 'app/queries/SavedBlock/useGetSavedBlockDetail';
import { useAddMediaJsonSavedBlock, useUpdateMediaJsonSavedBlock } from 'app/queries/SavedBlock';

// Styled
import { BlockWrapperStyled, OptionsWrapper, PopoverContent } from './styled';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';

// Utils
import { handleError } from 'app/utils/handleError';
import { getCloneBlocks } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/utils';

// Slice
import {
  selectBlockError,
  selectBlocks,
  selectBlockSelectedId,
  selectIsShowErrorAlert,
  selectTree,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Constants
import {
  BLOCK_ITEM,
  SIDE_PANEL_TYPE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';
import { BLOCK_OPTIONS } from './constants';
import { FORM_VALIDATE } from 'constants/formValidate';

// Services
import { validateSavedBlockName } from 'app/services/MediaJsonDesign/SavedBlock';

interface BlockWrapperProps extends React.HtmlHTMLAttributes<HTMLDivElement>, BlockProps {
  children?: React.ReactNode;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/Workspace/components/molecules/BlockWrapper/index.tsx';

const { OBJECT, ARRAY, COUNT_DOWN, DYNAMIC_CONTENT } = STANDARDS_BLOCKS;

export const BlockWrapper: React.FC<BlockWrapperProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // Translations
  const { t } = useTranslation();

  // Actions
  const { setSidePanel, setDragDropManage, setBlockSettings, duplicateBlock, removeBlock } = mediaJsonDesignActions;

  // Props
  const { children, blockId, parentBlockId, type, idx, savedBlockId, ...rest } = props;

  // State
  const [isOpenSavedBlockModal, setOpenSavedBlockModal] = useState(false);

  // Selectors
  const blockSelectedId = useSelector(selectBlockSelectedId);
  const blockError = useSelector(selectBlockError(blockId));
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const isShowError = !!blockError.length && isShowErrorAlert;

  // React dnd
  const [{ isDraggingCurrent }, dragRef] = useDrag(() => ({
    type: BLOCK_ITEM,
    collect: monitor => {
      return {
        isDraggingCurrent: !!monitor.isDragging(),
      };
    },
    canDrag() {
      if ([OBJECT.name, ARRAY.name, COUNT_DOWN.name, DYNAMIC_CONTENT.name].includes(type)) {
        dispatch(
          setBlockSettings({
            blockId,
            settings: {
              isCollapsed: true,
            },
            ignoreUndoAction: true,
          }),
        );
      }

      setTimeout(() => {
        dispatch(setDragDropManage({ isDraggingBlock: true }));
      }, 500);

      return true;
    },
    end() {
      setTimeout(() => {
        dispatch(setDragDropManage({ isDraggingBlock: false }));
      }, 500);
    },
    item: {
      isAddBlock: false,
      blockId,
      blockType: type,
      index: idx,
    },
  }));

  // Memo
  const isBlockSelected = useMemo(() => {
    return blockId === blockSelectedId;
  }, [blockId, blockSelectedId]);

  // Handlers
  const onClickBlockWrapper = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    try {
      e.stopPropagation();

      if (!isBlockSelected) {
        dispatch(
          setSidePanel({
            blockSelectedId: blockId,
            type,
          }),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickBlockWrapper',
        args: {},
      });
    }
  };

  const onClickOption = (optionType: string) => {
    try {
      switch (optionType) {
        case BLOCK_OPTIONS.DUPLICATE.value:
          dispatch(
            duplicateBlock({
              blockId,
              parentBlockId,
              blockType: type,
              blockIdx: idx,
            }),
          );
          break;

        case BLOCK_OPTIONS.DELETE.value:
          Modal.confirm({
            title: t(translations.confirmDeletionBlock.title),
            icon: null,
            centered: true,
            content: t(translations.confirmDeletionBlock.description),
            onOk() {
              if (blockSelectedId === blockId) {
                dispatch(
                  setSidePanel({
                    blockSelectedId: '',
                    type: SIDE_PANEL_TYPE.BLOCKS.name,
                  }),
                );
              }

              dispatch(
                removeBlock({
                  blockId,
                  parentBlockId,
                  blockType: type,
                }),
              );
            },
          });

          break;

        case BLOCK_OPTIONS.SAVE.value:
          setOpenSavedBlockModal(true);
          break;

        default:
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickOption',
        args: { optionType },
      });
    }
  };

  const renderOptionsContent = () => {
    try {
      return (
        <PopoverContent>
          {Object.values(BLOCK_OPTIONS).map(({ icon, value, tooltip }) => {
            if (value === BLOCK_OPTIONS.DIVIDER.value) {
              return <Divider key={value} type="vertical" dot style={{ minHeight: 20, margin: 0 }} />;
            }

            return (
              <Tooltip key={value} title={tooltip}>
                <span onClick={() => onClickOption(value)}>
                  <Icon type={icon} className="option__button" />
                </span>
              </Tooltip>
            );
          })}
        </PopoverContent>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderOptionsContent',
        args: {},
      });
    }
  };

  return (
    <>
      {idx === 0 && <DropArea dropBlockId={parentBlockId} dropBlockType={type} dropIdx={idx} />}

      <BlockWrapperStyled
        {...rest}
        id={`block-wrapper--${blockId}`}
        className={cn({
          'ants-group block-wrapper': true,
          '--active': isBlockSelected,
          // '--error': isShowError,
          // '!ants-bg-[#fffbe6]': isShowError && !isBlockSelected,
          '!ants-opacity-50 !ants-pointer-events-none': isDraggingCurrent,
        })}
        onClick={e => onClickBlockWrapper(e)}
      >
        {/* Errors */}
        {isShowError && (
          <div className="ants-absolute ants-top-[2px] ants-right-3px ants-z-50">
            <WarningIcon
              style={{
                width: 18,
                height: 14,
              }}
            />
          </div>
        )}

        {/* Options */}
        <OptionsWrapper>
          <Popover
            zIndex={900}
            getPopupContainer={() => document.querySelector('#workspace-editor') || document.body}
            visible={isBlockSelected && !isDraggingCurrent}
            placement="left"
            overlayClassName="no-arrow no-inner-padding"
            content={renderOptionsContent()}
          >
            <span
              className={cn('ants-opacity-0 ants-transition-all', {
                '!ants-opacity-100': isBlockSelected,
              })}
            >
              {/* <Icon type="icon-ants-more-horiz" className="ants-text-gray-4 ants-cursor-pointer" size={20} /> */}
            </span>
          </Popover>
        </OptionsWrapper>

        <div ref={dragRef}>{children}</div>
      </BlockWrapperStyled>

      <DropArea dropBlockId={parentBlockId} dropBlockType={type} dropIdx={idx + 1} />

      <SavedBlockModal
        isOpenModal={isOpenSavedBlockModal}
        block={{ id: blockId, type, savedBlockId, settings: rest.settings, parentBlockId }}
        onCancel={() => setOpenSavedBlockModal(false)}
      />
    </>
  );
});

interface SavedBlockModalProps {
  block: TBlock & { parentBlockId?: string };
  isOpenModal: boolean;
  onCancel: () => void;
}

let timeoutValidator: any = null;

export const SavedBlockModal: React.FC<SavedBlockModalProps> = memo(props => {
  // Props
  const { isOpenModal, block, onCancel } = props;
  const { savedBlockId, type, settings, parentBlockId } = block || {};

  // Locales
  const { t } = useTranslation();

  // Form
  const [form] = Form.useForm();

  // use hooks queries
  const { mutateAsync: addSavedBlock } = useAddMediaJsonSavedBlock();
  const { mutateAsync: updateSavedBlock } = useUpdateMediaJsonSavedBlock();

  // Selectors
  const blocks = useSelector(selectBlocks);
  const tree = useSelector(selectTree);

  // Queries
  const { data: savedBlock } = useGetMediaJsonSavedBlockDetail({ savedBlockId });

  const [saveBlockModal, setSaveBlockModal] = useState({
    isVisible: false,
    blockName: '',
    blockNote: '',
    isLoading: false,
  });
  const [checkSavedBlockModal, setCheckSavedBlockModal] = useState({
    isVisible: false,
    isSaveAsNew: true,
  });

  // Memo
  const blockLabel = useMemo(() => {
    const block = Object.values(STANDARDS_BLOCKS).find(({ name }) => type === name);

    return block ? block.label : '';
  }, [type]);

  // Effects
  useDeepCompareEffect(() => {
    if (isOpenModal) {
      if (isEmpty(savedBlock)) {
        setSaveBlockModal(prevState => ({ ...prevState, isVisible: true }));
      } else {
        setCheckSavedBlockModal(prevState => ({ ...prevState, isVisible: true }));
      }
    }
  }, [isOpenModal, savedBlock]);

  // Handlers
  const onCancelModal = () => {
    try {
      setSaveBlockModal(state => ({
        ...state,
        isVisible: false,
      }));

      form.resetFields();

      onCancel();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancelModal',
        args: {},
      });
    }
  };

  const validateBlockName = (_rule: any, value: any) => {
    try {
      const trimValue = value.trim();

      if (!value) {
        return Promise.resolve(true);
        // return Promise.reject(Error(FORM_VALIDATE.REQUIRED.message('Block name')));
      }

      const promise = new Promise((resolve, reject) => {
        if (timeoutValidator) {
          clearTimeout(timeoutValidator);
        }

        timeoutValidator = setTimeout(() => {
          validateSavedBlockName(trimValue).then(response => {
            const { existed } = response;

            if (existed) {
              if (!checkSavedBlockModal.isSaveAsNew && savedBlock?.name === trimValue) {
                resolve(true);
              } else {
                reject(Error(FORM_VALIDATE.DUPLICATED.message()));
              }
            } else {
              resolve(true);
            }
          });
        }, 500);
      });

      return promise;
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'validateBlockName',
        args: {},
      });
    }
  };

  const onCancelCheckSavedModal = () => {
    try {
      setCheckSavedBlockModal(state => ({
        ...state,
        isVisible: false,
      }));

      onCancel();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancelCheckSavedModal',
        args: {},
      });
    }
  };

  const onClickSaveAsNew = () => {
    try {
      setCheckSavedBlockModal(state => ({
        ...state,
        isSaveAsNew: true,
        isVisible: false,
      }));

      setSaveBlockModal(state => ({
        ...state,
        isVisible: true,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSaveAsNew',
        args: {},
      });
    }
  };

  const onClickReplace = () => {
    try {
      setCheckSavedBlockModal(state => ({
        ...state,
        isVisible: false,
        isSaveAsNew: false,
      }));

      setSaveBlockModal(state => ({
        ...state,
        isVisible: true,
      }));
      setTimeout(() => {
        form.setFieldsValue({
          blockName: savedBlock?.name,
          blockNotes: savedBlock?.notes,
        });
      }, 500);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickReplace',
        args: {},
      });
    }
  };

  return (
    <>
      <Modal
        visible={saveBlockModal.isVisible}
        loading={saveBlockModal.isLoading}
        destroyOnClose
        title={t(translations[checkSavedBlockModal.isSaveAsNew ? 'saveNewBlock' : 'replaceExistingBlock'].title, {
          name: blockLabel,
        })}
        onCancel={onCancelModal}
        onOk={() => form.submit()}
      >
        {checkSavedBlockModal.isSaveAsNew ? (
          <Text>{t(translations.saveNewBlock.description)}</Text>
        ) : (
          <Alert message={t(translations.replaceExistingBlock.warning)} type="warning" showIcon />
        )}
        <Form
          form={form}
          className="!ants-mt-5"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          initialValues={{
            blockName: '',
            blockNotes: '',
          }}
          onFinish={async (values: any) => {
            const { blockName = '', blockNotes = '' } = values;

            const children = {
              blockParentId: block.id,
              blocks: {},
              tree: {},
            };

            if (tree[block.id]?.length) {
              tree[block.id].forEach(childId => {
                const { childrenBlocks, childrenTree } = getCloneBlocks({
                  parentId: block.id,
                  blockId: childId,
                  blocks,
                  tree,
                });

                Object.assign(children.blocks, childrenBlocks);

                mergeWith(children.tree, childrenTree, (objValue, srcValue) => {
                  if (Array.isArray(objValue)) {
                    return objValue.concat(srcValue);
                  }
                });
              });
            }

            try {
              setSaveBlockModal(state => ({
                ...state,
                isLoading: true,
              }));

              if (checkSavedBlockModal.isSaveAsNew) {
                await addSavedBlock({
                  name: blockName.trim(),
                  type,
                  notes: blockNotes.trim(),
                  settings,
                  children,
                });
              } else {
                await updateSavedBlock({
                  id: savedBlock?.id,
                  name: blockName.trim(),
                  notes: blockNotes.trim(),
                  settings,
                  children,
                });
              }
            } catch (error) {
              handleError(error, {
                path: PATH,
                name: '',
                args: { values },
              });
            } finally {
              setSaveBlockModal(state => ({
                ...state,
                isLoading: false,
                isVisible: false,
              }));

              onCancel();
            }
          }}
        >
          <Form.Item
            label={t(translations.enterNameBlock.title)}
            name="blockName"
            rules={[
              { required: true, message: FORM_VALIDATE.REQUIRED.message('Block name') },
              {
                max: 70,
                message: FORM_VALIDATE.MAX.message('Block name', 70),
              },
              {
                validator: validateBlockName,
              },
            ]}
          >
            <Input placeholder={t(translations.enterNameBlock.placeholder)} />
          </Form.Item>
          <Form.Item
            label={t(translations.addNote.title)}
            name="blockNotes"
            rules={[
              {
                max: 500,
                message: FORM_VALIDATE.MAX.message('Block Notes', 500),
              },
            ]}
          >
            <TextArea placeholder={t(translations.addNote.placeholder)} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        visible={checkSavedBlockModal.isVisible}
        destroyOnClose
        centered
        title={t(translations.replaceOrCreateNewBlock.title, { name: blockLabel })}
        onCancel={onCancelCheckSavedModal}
        onOk={() => form.submit()}
        footer={
          <>
            <Button type="primary" onClick={onClickSaveAsNew}>
              {t(translations.saveAsNew.title)}
            </Button>
            <Button type="primary" onClick={onClickReplace}>
              {t(translations.replaceExisting.title)}
            </Button>
            <Button onClick={onCancelCheckSavedModal}>{t(translations.cancel.title)}</Button>
          </>
        }
      >
        <Alert
          message={t(translations.replaceOrCreateNewBlock.warning)}
          type="warning"
          showIcon
          className="ants-mb-2.5"
        />
        <Text>{t(translations.replaceOrCreateNewBlock.description)}</Text>
      </Modal>
    </>
  );
});
