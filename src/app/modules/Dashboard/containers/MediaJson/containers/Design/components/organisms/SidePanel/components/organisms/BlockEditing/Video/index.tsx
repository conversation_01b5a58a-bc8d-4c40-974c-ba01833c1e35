// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input } from 'app/components/atoms';

// Translations
import { translations } from 'locales/translations';

// Styled
import { VideoWrapper } from './styled';

// Store

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Types
import { SidePanelContentProps } from '../../../../types';
import { KeyCodeInput } from '../../../molecules/KeyCodeInput';

interface VideoProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

export const Video: React.FC<VideoProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Hooks
  const dispatch = useDispatch();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key, value } = settings || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Record<string, any>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  return (
    <VideoWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      <Input
        required
        focused={isShowErrorAlert}
        value={value}
        label={t(translations.link.title)}
        placeholder={t(translations.value.placeholder)}
        onAfterChange={value => updateBlockSettings({ value })}
      />
    </VideoWrapper>
  );
});
