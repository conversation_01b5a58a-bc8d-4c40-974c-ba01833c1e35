// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';

// Atoms
import { Input, Text } from 'app/components/atoms';

// Molecules
import { KeyCodeInput } from '../../../molecules/KeyCodeInput';

// Molecules
import { DatePicker, InputNumber, Select } from 'app/components/molecules';
import { TimePicker } from 'app/components/molecules/TimePicker';

// Translations
import { translations } from 'locales/translations';

// Styled
import { CountdownWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';
import { getDuration, getTimestampWithTimeZone } from './utils';

// Slice
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Constants
import { COUNTDOWN_TYPE, DYNAMIC_END_TIME } from './constants';
import { TIME_ZONES } from 'constants/datetime';

// Types
import { TBlock } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';
import { SidePanelContentProps } from '../../../../types';

interface CountdownProps extends SidePanelContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Text/index.tsx';

export const Countdown: React.FC<CountdownProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  // Props
  const { isDuplicatedKey, isInvalidKey } = props;

  // Hooks
  const dispatch = useDispatch();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Variables
  const { settings } = blockSelected;
  const { key, countdownType, dynamic, static: countdownStatic, timeZone } = settings || {};
  const { endDate, endTime } = countdownStatic || {};

  // Actions
  const { setSelectedBlockSettings } = mediaJsonDesignActions;

  // Handlers
  const updateBlockSettings = (payload: Partial<TBlock['settings']>) => {
    try {
      dispatch(setSelectedBlockSettings({ ...payload }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'updateBlockSettings',
        args: { payload },
      });
    }
  };

  const renderOptionCountdown = () => {
    try {
      switch (countdownType) {
        case COUNTDOWN_TYPE.DYNAMIC.value:
          return (
            <div className="ants-flex ants-justify-between">
              {Object.values(DYNAMIC_END_TIME).map(dynamicItem => {
                return (
                  <div key={dynamicItem.value}>
                    <Text className="ants-mb-1">{dynamicItem.label}</Text>
                    <InputNumber
                      value={Number(dynamic[dynamicItem.value])}
                      required
                      min={0}
                      max={88888888}
                      onChange={value =>
                        updateBlockSettings({
                          dynamic: {
                            ...dynamic,
                            [dynamicItem.value]: value,
                            timeInSeconds: getDuration({
                              ...dynamic,
                              [dynamicItem.value]: value,
                            }),
                          },
                        })
                      }
                    />
                  </div>
                );
              })}
            </div>
          );
        case COUNTDOWN_TYPE.STATIC.value:
          return (
            <>
              <div className="ants-flex ants-justify-between">
                <div>
                  <Text className="ants-mb-1">{t(translations.endDate.title)}</Text>
                  <DatePicker
                    onChange={(key, value) => {
                      updateBlockSettings({
                        static: {
                          ...countdownStatic,
                          endDate: moment(value).format('YYYY-MM-DD'),
                          epoch: getTimestampWithTimeZone({
                            date: moment(value).format('YYYY-MM-DD'),
                            time: endTime,
                            timeZone,
                          }),
                        },
                      });
                    }}
                    value={moment(endDate, 'YYYY-MM-DD')}
                    allowClear={false}
                  />
                </div>
                <div>
                  <Text className="ants-mb-1">{t(translations.endTime.title)}</Text>
                  <TimePicker
                    onChange={(key, value) =>
                      updateBlockSettings({
                        static: {
                          ...countdownStatic,
                          endTime: moment(value).format('HH:mm:ss'),
                          epoch: getTimestampWithTimeZone({
                            date: endDate,
                            time: moment(value).format('HH:mm:ss'),
                            timeZone,
                          }),
                        },
                      })
                    }
                    value={moment(endTime, 'HH:mm:ss') as any}
                    allowClear={false}
                  />
                </div>
              </div>
              <Select
                options={Object.values(TIME_ZONES)}
                onChange={value =>
                  updateBlockSettings({
                    timeZone: value,
                    static: {
                      ...countdownStatic,
                      epoch: getTimestampWithTimeZone({ date: endDate, time: endTime, timeZone: value }),
                    },
                  })
                }
                value={timeZone}
                showSearch={true}
                className="ants-w-full"
              />
            </>
          );
        default:
          return null;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderOptionCountdown',
        args: {},
      });
    }
  };

  return (
    <CountdownWrapper>
      <KeyCodeInput
        value={key}
        isShowErrorAlert={isShowErrorAlert}
        isDuplicatedKey={isDuplicatedKey}
        isInvalidKeyCode={isInvalidKey}
        onAfterChange={value => updateBlockSettings({ key: value })}
      />

      <Select
        label={t(translations.countDownType.title)}
        value={countdownType}
        key="settings"
        options={Object.values(COUNTDOWN_TYPE)}
        onChange={value => {
          updateBlockSettings({
            countdownType: value,
          });
        }}
        className="ants-w-full"
      />

      {renderOptionCountdown()}
    </CountdownWrapper>
  );
});
