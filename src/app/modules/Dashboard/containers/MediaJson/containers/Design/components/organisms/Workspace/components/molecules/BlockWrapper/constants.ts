// Utils
import { getTranslateMessage } from 'utils/messages';

// Translations
import { translations } from 'locales/translations';

export const BLOCK_OPTIONS = {
  DUPLICATE: {
    value: 'duplicate',
    label: getTranslateMessage(translations.duplicate.title),
    tooltip: getTranslateMessage(translations.duplicate.title),
    icon: 'icon-ants-material-outline-content-copy',
  },
  SAVE: {
    value: 'save',
    label: getTranslateMessage(translations.save.title),
    tooltip: getTranslateMessage(translations.save.title),
    icon: 'icon-ants-save-2',
  },
  DIVIDER: {
    value: 'divider',
    label: 'Divider',
    tooltip: 'Divider',
    icon: '',
  },
  DELETE: {
    value: 'delete',
    label: getTranslateMessage(translations.delete.title),
    tooltip: getTranslateMessage(translations.delete.title),
    icon: 'icon-ants-outline-delete',
  },
};
