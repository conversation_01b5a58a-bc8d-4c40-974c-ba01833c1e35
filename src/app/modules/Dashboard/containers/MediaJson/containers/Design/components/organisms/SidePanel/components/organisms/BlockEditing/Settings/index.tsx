// Libraries
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Text } from 'app/components/atoms';

// Common
import { TrackingModule } from 'app/modules/Dashboard/components/common/TrackingModule';

// Molecules
import { Collapse, CollapsePanel, Modal } from 'app/components/molecules';

// Organisms
import ContentSources from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/ContentSources';

// Styled
import { SettingsWrapper } from './styled';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/constants';
import { translations } from 'locales/translations';

// Queries
import { useGetListBO } from 'app/queries/BusinessObject';
import { useGetListFallbackBO } from 'app/queries/ThirdParty/useGetFallbackBO';

// Selectors
import {
  selectBlocks,
  selectContentSources,
  selectIsShowErrorAlert,
  selectTrackingModule,
  selectJourneySettings,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/selectors';

// Utils
import { handleError } from 'app/utils/handleError';
import { mediaJsonDesignActions } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice';
import { UpdateGroupAction } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/ContentSources/types';
import { TRanking, TTrackingModule } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Config
import {
  ARTICLE_RANKING_DEFAULT,
  FILTERS_DEFAULT,
  GET_TOP_RANKING_DEFAULT,
  PRODUCT_ITEM_TYPE_ID,
  PRODUCT_RANKING_DEFAULT,
} from 'app/modules/Dashboard/containers/MediaJson/containers/Design/config';
import { getBlocksUseBO } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/slice/utils';
import { ITEM_TYPE_NAME } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

interface SettingsProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Settings/index.tsx';

const MAX_NUM_OF_CONTENT_SOURCE = 2;
const ALGORITHM_QUANTITY_MAX = 60;

export const Settings: React.FC<SettingsProps> = memo(() => {
  // Hooks
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Selectors
  const blocks = useSelector(selectBlocks);
  const contentSources = useSelector(selectContentSources);
  const trackingModule = useSelector(selectTrackingModule);
  const journeySettings = useSelector(selectJourneySettings);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  // Queries
  const { data: listBO, isLoading: isLoadingGetListBO } = useGetListBO();
  const { data: listFallbackBO } = useGetListFallbackBO({});

  // Actions
  const {
    expandedContentSource,
    setContentSourceGroup,
    deleteContentSourceGroup,
    addContentSource,
    removeBlocks,
    setTrackingModule,
    setIsExcludeDuplicate,
  } = mediaJsonDesignActions;

  // Handlers
  const handleChangeContentSourceGroup = (groupId: string, action: UpdateGroupAction) => {
    if (action.type === 'BO_TYPE') {
      const { itemTypeId } = action.payload;
      if (itemTypeId) onChangeItemTypeId({ groupId, id: itemTypeId });
    }

    if (action.type === 'ALGORITHMS') {
      const { ranking } = action.payload;

      dispatch(setContentSourceGroup({ groupId, values: { ranking } }));
    }

    if (action.type === 'FILTER') {
      const { conditions } = action.payload;
      dispatch(setContentSourceGroup({ groupId, values: { filters: conditions } }));
    }

    if (action.type === 'FALLBACK') {
      const { fallback } = action.payload;
      dispatch(setContentSourceGroup({ groupId, values: { fallback } }));
    }

    if (action.type === 'NAME') {
      const { name } = action.payload;
      dispatch(setContentSourceGroup({ groupId, values: { groupName: name } }));
    }

    if (action.type === 'LEVEL') {
      const { level } = action.payload;
      dispatch(setContentSourceGroup({ groupId, values: { level } }));
    }
  };

  const onChangeItemTypeId = ({
    groupId,
    id,
    isDelete = false,
    callback,
  }: {
    groupId: string;
    id: number | null;
    isDelete?: boolean;
    callback?: () => void;
  }) => {
    try {
      const selected = listBO?.find((bo: any) => bo.value === id);
      const itemTypeDisplay = selected?.label;
      const itemTypeName = selected?.name;
      const blocksUseBO = getBlocksUseBO({
        checkedBlocks: Object.values({ ...blocks }),
        groupBoId: groupId || '',
      });

      let valueRanking: TRanking = GET_TOP_RANKING_DEFAULT;

      if (id === PRODUCT_ITEM_TYPE_ID) {
        valueRanking = PRODUCT_RANKING_DEFAULT;
      } else if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
        valueRanking = ARTICLE_RANKING_DEFAULT;
      }

      const newRanking: TRanking = valueRanking;

      if (blocksUseBO.length > 0) {
        const keyTranslation = isDelete ? 'confirmDeleteContentSourceGroup' : 'confirmChangeContentSource';

        Modal.confirm({
          icon: null,
          centered: true,
          title: t(translations[keyTranslation]?.title),
          content: (
            <div>
              <Text>{t(translations[keyTranslation]?.blocksDescription)}</Text>
              <div className="ants-my-1">
                {blocksUseBO.map(({ settings }, index) => (
                  <Text key={index}>- {settings.name}</Text>
                ))}
              </div>
              <Text>{t(translations[keyTranslation]?.deleteDescription)}</Text>
            </div>
          ),
          onOk() {
            if (groupId) {
              dispatch(
                setContentSourceGroup({
                  groupId,
                  values: {
                    itemTypeId: id,
                    itemTypeDisplay,
                    itemTypeName,
                    ranking: newRanking,
                    filters: FILTERS_DEFAULT,
                  },
                }),
              );
            }

            dispatch(removeBlocks(blocksUseBO.map(block => block.id)));

            if (callback) callback();
          },
        });

        return;
      }

      if (groupId) {
        dispatch(
          setContentSourceGroup({
            groupId,
            values: {
              itemTypeId: id,
              itemTypeDisplay,
              itemTypeName,
              ranking: newRanking,
              filters: FILTERS_DEFAULT,
            },
          }),
        );

        if (callback) callback();
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeItemTypeId',
        args: {},
      });
    }
  };

  const handleDeleteContentSourceGroup = (groupId: string) => {
    const group = contentSources.groups.find(g => g.groupId === groupId);

    if (group)
      // Re-use function in case change content source type
      // for case remove content source
      onChangeItemTypeId({
        groupId,
        id: group.itemTypeId,
        isDelete: true,
        callback: () => dispatch(deleteContentSourceGroup(groupId)),
      });
  };

  const onChangeTrackingModule = (trackingModule: TTrackingModule) => {
    try {
      dispatch(
        setTrackingModule({
          ...trackingModule,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTrackingModule',
        args: {},
      });
    }
  };

  return (
    <SettingsWrapper>
      <Collapse
        accordion
        defaultActiveKey={SIDE_PANEL_COLLAPSE.VIEW_STYLING}
        // activeKey={SIDE_PANEL_COLLAPSE.BUSINESS_OBJECT}
      >
        <CollapsePanel header={t(translations.contentSources.title)} key={SIDE_PANEL_COLLAPSE.CONTENT_SOURCES}>
          <ContentSources
            groups={contentSources.groups}
            listBO={listBO}
            expanded={contentSources.expanded}
            onChangeExpand={expanded => dispatch(expandedContentSource(expanded))}
            onChangeGroup={(groupId, action) => handleChangeContentSourceGroup(groupId, action)}
            onDeleteGroup={groupId => handleDeleteContentSourceGroup(groupId)}
            journeySettings={journeySettings}
            isShowErrorAlert={isShowErrorAlert}
            isLoadingListBO={isLoadingGetListBO}
            listFallbackBO={listFallbackBO ? listFallbackBO : []}
            algorithmQuantityMax={ALGORITHM_QUANTITY_MAX}
            isExcludeDuplicate={contentSources?.isExcludeDuplicate}
            onChangeExcludeDuplicate={isExcludeDuplicate => {
              dispatch(setIsExcludeDuplicate(isExcludeDuplicate));
            }}
            onAddGroup={() => dispatch(addContentSource())}
          />
        </CollapsePanel>
        <CollapsePanel header={t(translations.trackingModule.title)} key={SIDE_PANEL_COLLAPSE.TRACKING_MODULE}>
          <TrackingModule trackingModule={trackingModule} onChange={onChangeTrackingModule} />
        </CollapsePanel>
      </Collapse>
    </SettingsWrapper>
  );
});

export default Settings;
