// Libraries
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import scopeCSS from 'scope-css';

// Translations
import { translations } from 'locales/translations';

// Hooks
import { useDebounce } from 'app/hooks';

// Atoms
import { Space, Switch, Text } from 'app/components/atoms';

// Molecules
import { EditorScript } from 'app/components/molecules/EditorScript';
import { SettingWrapper } from '..';

// Utils
import { handleError } from 'app/utils/handleError';

// Constants
import { PREFIX_EL_NAME } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Types
import { TCustomCSS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/CustomCssEditorScript/index.tsx';

interface CustomCSSEditorScriptProps {
  templateId: string;
  customCSS: TCustomCSS;
  onChange: (customCss: Partial<TCustomCSS>) => void;
}

export const CustomCssEditorScript: React.FC<CustomCSSEditorScriptProps> = props => {
  // I18n
  const { t } = useTranslation();

  // State
  const [rawStyleEditorOutput, setRawStyleEditorOutput] = useState('');

  // Props
  const { templateId, customCSS, onChange } = props;

  // Debounce
  const debounceRawStyleEditorOutput = useDebounce(rawStyleEditorOutput, 1000);

  // Variables
  const prefixCSS = `html #${PREFIX_EL_NAME}-${templateId}`;

  // Handlers
  useEffect(() => {
    setRawStyleEditorOutput(customCSS.rawEditorOutput || '');
  }, [customCSS.rawEditorOutput]);

  useEffect(() => {
    if (debounceRawStyleEditorOutput) {
      onChange({
        rawEditorOutput: debounceRawStyleEditorOutput,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceRawStyleEditorOutput]);

  const onBlurCSSEditorScript = (_, editor) => {
    try {
      const rawEditorOutput = customCSS.applyCssPrefix
        ? scopeCSS(editor.getValue(), `html #${PREFIX_EL_NAME}-${templateId}`)
        : editor.getValue();

      // const rawEditorOutput = editor.getValue().replace(REGEX.CSS_SELECTOR, (value: string) => {
      //   // Check value replace was added prefix or not
      //   if (value.startsWith('html')) {
      //     return value;
      //   }

      //   return customCSS.applyCssPrefix ? `html #${PREFIX_EL_NAME}-${templateId} ${value}` : value;
      // });

      onChange({
        rawEditorOutput,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onBlurCSSEditorScript',
        args: {},
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <Text className="!ants-text-gray-4">
        {t(translations.customCSS.description)}: <strong>{`html #${PREFIX_EL_NAME}-{{id}}`}</strong>
      </Text>
      <SettingWrapper label={`${t(translations.autoApplyCSSPrefixes.title)}?`}>
        <Switch
          checked={customCSS.applyCssPrefix}
          onChange={checked =>
            onChange({
              applyCssPrefix: checked,
            })
          }
        />
      </SettingWrapper>
      <EditorScript
        expandModalLabel={t(translations.customCSS.title)}
        value={rawStyleEditorOutput}
        mode="css"
        onChange={value => setRawStyleEditorOutput(value)}
        onBlur={onBlurCSSEditorScript}
      />
    </Space>
  );
};

CustomCssEditorScript.defaultProps = {
  customCSS: {} as any,
};
