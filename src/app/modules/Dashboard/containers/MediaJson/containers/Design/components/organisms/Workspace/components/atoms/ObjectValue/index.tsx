import React, { memo, useMemo } from 'react';
import { ObjectValueWrapper } from './styled';

interface ObjectValueProps {
  value: any;
}

export const ObjectValue: React.FC<ObjectValueProps> = memo(props => {
  // Props
  const { value } = props;

  const typeOfValue = useMemo(() => {
    return typeof value;
  }, [value]);

  const typeClass = useMemo(() => {
    switch (true) {
      case typeOfValue === 'string':
        return 'string-value';

      case typeOfValue === 'boolean':
        return 'boolean-value';

      case typeOfValue === 'number':
        return 'number-value';

      case typeOfValue === 'undefined':
        return 'undefined-value';

      case value === null:
        return 'null-value';

      default:
        return '';
    }
  }, [typeOfValue, value]);

  const formattedValue = useMemo(() => {
    switch (true) {
      case typeOfValue === 'string':
        return `"${value}"`;
      case typeOfValue === 'boolean':
        return !!value ? 'true' : 'false';
      case typeOfValue === 'undefined':
        return 'undefined';
      case value === null:
        return 'null';
      default:
        return value;
    }
  }, [typeOfValue, value]);

  return <ObjectValueWrapper className={typeClass}>{formattedValue}</ObjectValueWrapper>;
});
