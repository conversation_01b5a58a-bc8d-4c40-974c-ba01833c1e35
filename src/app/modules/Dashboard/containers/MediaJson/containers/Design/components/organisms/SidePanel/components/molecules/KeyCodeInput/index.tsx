// Libraries
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// Atoms
import { Input, InputProps } from 'app/components/atoms/Input';

// Locales
import { translations } from 'locales/translations';

interface KeyCodeInputProps extends InputProps {
  isShowErrorAlert?: boolean;
  isDuplicatedKey?: boolean;
  isInvalidKeyCode?: boolean;
}

export const KeyCodeInput: React.FC<KeyCodeInputProps> = props => {
  // Props
  const { isShowErrorAlert, isDuplicatedKey, isInvalidKeyCode, ...restOf } = props;

  const { t } = useTranslation();

  // Memo
  const errorMessage = useMemo(() => {
    let draftErrorMessage = '';

    if (isDuplicatedKey) {
      draftErrorMessage = t(translations.duplicatedKey.title);
    } else if (isInvalidKeyCode) {
      draftErrorMessage = t(translations.validateKeyCode.title);
    }

    return draftErrorMessage;
  }, [isDuplicatedKey, isInvalidKeyCode, t]);

  return (
    <Input
      label={t(translations.keyCode.title)}
      placeholder={t(translations.keyCode.placeholder)}
      focused={isShowErrorAlert}
      required
      status={(isDuplicatedKey || isInvalidKeyCode) && isShowErrorAlert ? 'error' : ''}
      errorMsg={errorMessage}
      maxLength={50}
      {...restOf}
    />
  );
};
