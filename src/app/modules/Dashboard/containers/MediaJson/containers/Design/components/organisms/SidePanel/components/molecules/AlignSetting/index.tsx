// Libraries
import React from 'react';

// Atoms
import { Button, Icon } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../SettingWrapper';

// Constant
import { ALIGN_TYPE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Styled
import { AlignSettingWrapper } from './styled';

export type TAlign = 'left' | 'center' | 'right' | undefined;
interface AlignEditProps {
  className?: string;
  align?: TAlign;
  onChange?: (align: TAlign) => void;
  style?: Object;
}

interface AlignSettingProps extends AlignEditProps {
  label: string;
  labelClassName?: string;
}

export const AlignSetting: React.FC<AlignSettingProps> = props => {
  const { label, labelClassName, ...restOf } = props;

  return (
    <SettingWrapper label={label} labelClassName={labelClassName}>
      <AlignEdit {...restOf} />
    </SettingWrapper>
  );
};

export const AlignEdit: React.FC<AlignEditProps> = props => {
  const { className, style, onChange, align } = props;

  // const [align, setAlign] = useState<TAlign>(props.align !== undefined ? props.align : (ALIGN_TYPE.LEFT as TAlign));

  // useEffect(() => {
  //   setAlign(props.align);
  // }, [props.align]);

  return (
    <AlignSettingWrapper className={className} style={style}>
      <Button
        className={`ants-w-[30px] ants-aspect-square ${align !== ALIGN_TYPE.LEFT ? '!ants-text-gray-6' : ''}`}
        onClick={() => onChange?.(ALIGN_TYPE.LEFT as TAlign)}
      >
        <Icon type="icon-ants-align-left" />
      </Button>
      <Button
        className={`ants-w-[30px] ants-aspect-square ${align !== ALIGN_TYPE.CENTER ? '!ants-text-gray-6' : ''}`}
        onClick={() => onChange?.(ALIGN_TYPE.CENTER as TAlign)}
      >
        <Icon type="icon-ants-align-center" />
      </Button>
      <Button
        className={`ants-w-[30px] ants-aspect-square ${align !== ALIGN_TYPE.RIGHT ? '!ants-text-gray-6' : ''}`}
        onClick={() => onChange?.(ALIGN_TYPE.RIGHT as TAlign)}
      >
        <Icon type="icon-ants-align-right" />
      </Button>
    </AlignSettingWrapper>
  );
};
