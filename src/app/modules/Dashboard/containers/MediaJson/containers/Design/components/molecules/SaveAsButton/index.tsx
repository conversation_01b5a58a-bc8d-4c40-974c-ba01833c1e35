// Libraries
import React, { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Button, Input, Radio, Space, Text } from 'app/components/atoms';

// Molecules
import { Form, Modal, RadioGroup, Select } from 'app/components/molecules';

// Locales
import { translations } from 'locales/translations';

// Utils
import { handleError } from 'app/utils/handleError';

// Services
import { validateGalleryName } from 'app/services/MediaJsonDesign/GalleryTemplate';

// Queries
import { usePersistsMediaJsonGalleryTemplate, useGetListMediaJsonGalleryTemplate } from 'app/queries/GalleryTemplate';

// Constants
import { FORM_VALIDATE } from 'constants/formValidate';

// Slices
import { selectToolbar, selectWorkspace } from '../../../slice/selectors';
import { getTemplateSetting } from '../../../slice/utils';
import { mediaJsonDesignActions } from '../../../slice';

interface SaveAsButtonProps {}

enum ESaveAsType {
  SAVE_AS_NEW = 'saveAsNew',
  SAVE_AS_EXISTS = 'saveAsExists',
}

type TInitialValues = {
  saveAsType: ESaveAsType;
  newGalleryName: string;
  existsGalleryId: number | null;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SaveAsButton/index.tsx';

const initialValues: TInitialValues = {
  saveAsType: ESaveAsType.SAVE_AS_NEW,
  newGalleryName: '',
  existsGalleryId: null,
};

let timeoutValidator: any = null;
export const SaveAsButton: React.FC<SaveAsButtonProps> = memo(() => {
  // Hooks
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Selectors
  const workspace = useSelector(selectWorkspace);
  const { isOpenSaveAs } = useSelector(selectToolbar);

  // Actions
  const { setToolbar } = mediaJsonDesignActions;

  // Form
  const [form] = Form.useForm();

  // Form useWatch
  const saveAsType: ESaveAsType = Form.useWatch('saveAsType', form);

  // Queries
  const { data: galleryTemplateData } = useGetListMediaJsonGalleryTemplate();
  const { body: galleryTemplates } = galleryTemplateData || {};

  // Mutations
  const { mutateAsync: persistsGalleryTemplate, isLoading } = usePersistsMediaJsonGalleryTemplate();

  // Use Effect
  useEffect(() => {
    if (isOpenSaveAs) {
      form.setFieldsValue({
        existsGalleryId: (galleryTemplates || [])[0]?.id,
      });
    } else {
      form.resetFields();
    }
  }, [isOpenSaveAs, galleryTemplates, form]);

  // Handlers
  const validatorGalleryName = (_rule: any, value: any) => {
    try {
      if (!value) {
        return Promise.reject(Error(FORM_VALIDATE.DUPLICATED.message()));
      }

      const promise = new Promise((resolve, reject) => {
        if (timeoutValidator) {
          clearTimeout(timeoutValidator);
        }

        timeoutValidator = setTimeout(() => {
          validateGalleryName(value).then(response => {
            const { existed } = response;

            if (existed) {
              reject(Error(FORM_VALIDATE.DUPLICATED.message()));
            } else {
              resolve(true);
            }
          });
        }, 500);
      });

      return promise;
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'validatorGalleryName',
        args: {},
      });
    }
  };

  const onSubmitForm = async (values: TInitialValues) => {
    try {
      const { saveAsType, existsGalleryId, newGalleryName } = values || {};

      const data = {
        ...(saveAsType === ESaveAsType.SAVE_AS_NEW && { gallery_name: newGalleryName }),
        ...(saveAsType === ESaveAsType.SAVE_AS_EXISTS && { id: existsGalleryId }),
        template_type: 4,
        device_type: 1,
        template_setting: getTemplateSetting(workspace),
        properties: {
          ...workspace,
          isInitial: true,
        },
        thumbnail: workspace.thumbnail,
      };

      await persistsGalleryTemplate({
        persistsType: saveAsType === ESaveAsType.SAVE_AS_NEW ? 'create' : 'update',
        data,
      });

      toggleModal();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onSubmitForm',
        args: {},
      });
    }
  };

  const toggleModal = () => {
    try {
      dispatch(
        setToolbar({
          isOpenSaveAs: !isOpenSaveAs,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'toggleModal',
        args: {},
      });
    }
  };

  const onOkModal = () => {
    try {
      form.submit();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onOkModal',
        args: {},
      });
    }
  };

  const onClickSaveAs = () => {
    try {
      dispatch(
        setToolbar({
          isOpenSaveAs: true,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSaveAs',
        args: {},
      });
    }
  };

  return (
    <>
      <button
        style={{ textAlign: 'left', fontSize: 12 }}
        className="ants-border-none ants-outline-none ants-w-100 ants-h-full"
        onClick={onClickSaveAs}
      >
        {t(translations.cloneMediaJson.saveAs.title)}
        {'...'}
      </button>
      {/* <Button type="primary" onClick={onClickSaveAs}>
        {t(translations.cloneMediaJson.saveAs.title)}
      </Button> */}

      <Modal
        width={500}
        destroyOnClose
        okText={t(translations.save.title).toUpperCase()}
        title={t(translations.cloneMediaJson.saveAs.title)}
        visible={isOpenSaveAs}
        onCancel={toggleModal}
        onOk={onOkModal}
        loading={isLoading}
      >
        <Form
          form={form}
          initialValues={initialValues}
          layout="vertical"
          onFinish={values => onSubmitForm(values as TInitialValues)}
        >
          <Form.Item name={'saveAsType'}>
            <RadioGroup className="ants-w-full">
              <Space direction={'vertical'} size={10}>
                <Radio value={ESaveAsType.SAVE_AS_NEW}>
                  <Text>{t(translations.saveAsGallery.saveAsNewGallery.title)}</Text>
                </Radio>
                {saveAsType === ESaveAsType.SAVE_AS_NEW && (
                  <Form.Item
                    name={'newGalleryName'}
                    className="!ants-pl-6"
                    rules={[
                      {
                        validator: validatorGalleryName,
                      },
                    ]}
                  >
                    <Input placeholder={t(translations.saveAsGallery.saveAsNewGallery.placeholder)} />
                  </Form.Item>
                )}
                <Radio value={ESaveAsType.SAVE_AS_EXISTS}>
                  <Text>{t(translations.saveAsGallery.saveAsExistingGallery.title)}</Text>
                </Radio>
                {saveAsType === ESaveAsType.SAVE_AS_EXISTS && (
                  <Form.Item
                    name={'existsGalleryId'}
                    className="!ants-pl-6"
                    rules={[{ required: true, message: FORM_VALIDATE.SELECT_REQUIRED.message('Gallery') }]}
                  >
                    <Select
                      showSearch
                      options={galleryTemplates?.map(({ id, name }) => ({ value: id, label: name }))}
                      placeholder={t(translations.saveAsGallery.saveAsExistingGallery.placeholder)}
                    />
                  </Form.Item>
                )}
              </Space>
            </RadioGroup>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
});
