// Libraries
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import duration from 'dayjs/plugin/duration';
import timezone from 'dayjs/plugin/timezone';

export const getTimestampWithTimeZone = ({ date, time, timeZone }) => {
  // Check timezone empty => !!''
  if (!timeZone) {
    return dayjs(`${date} ${time}`).valueOf();
  }

  dayjs.extend(utc);
  dayjs.extend(timezone);

  return dayjs(`${date} ${time}`).tz(timeZone, true).valueOf();
};

export const getDuration = ({ days, hours, minutes, seconds }) => {
  dayjs.extend(duration);

  return dayjs.duration({ days, hours, minutes, seconds }).asSeconds();
};
