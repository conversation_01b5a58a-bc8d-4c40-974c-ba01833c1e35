// Libraries
import React, { memo } from 'react';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Styled
import { ImageBlockWrapper } from './styled';

interface ImageBlockProps extends BlockProps {}

export const ImageBlock: React.FC<ImageBlockProps> = memo(props => {
  // Props
  const { settings, type } = props;

  // Variables
  const { key, value } = settings || {};

  return (
    <ImageBlockWrapper className="variable-row">
      <ObjectKey objectKey={key} objectType={type} />
      <ObjectValue value={value} />
    </ImageBlockWrapper>
  );
});
