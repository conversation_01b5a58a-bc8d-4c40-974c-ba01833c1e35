// Constants
import { ROUTES } from 'constants/routes';

// Types
import { Routes } from 'types';

// Components
import { RichMenuDesign } from './containers/Design/Loadable';
import { RichMenuEmbed } from './containers/Embed/Loadable';
// import { Embed } from './containers/Embed';

const routes: Routes = [
  {
    key: ROUTES.RICH_MENU_DESIGN.key,
    name: ROUTES.RICH_MENU_DESIGN.name,
    path: ROUTES.RICH_MENU_DESIGN.path,
    exact: ROUTES.RICH_MENU_DESIGN.exact,
    component: RichMenuDesign,
  },
  {
    key: ROUTES.RICH_MENU_EMBED.key,
    name: ROUTES.RICH_MENU_EMBED.name,
    path: ROUTES.RICH_MENU_EMBED.path,
    exact: ROUTES.RICH_MENU_EMBED.exact,
    component: RichMenuEmbed,
  },
  {
    key: ROUTES.ADVANCED_IMAGE_MAP_DESIGN.key,
    name: ROUTES.ADVANCED_IMAGE_MAP_DESIGN.name,
    path: ROUTES.ADVANCED_IMAGE_MAP_DESIGN.path,
    exact: ROUTES.ADVANCED_IMAGE_MAP_DESIGN.exact,
    component: RichMenuDesign,
  },
  {
    key: ROUTES.ADVANCED_IMAGE_MAP_EMBED.key,
    name: ROUTES.ADVANCED_IMAGE_MAP_EMBED.name,
    path: ROUTES.ADVANCED_IMAGE_MAP_EMBED.path,
    exact: ROUTES.ADVANCED_IMAGE_MAP_EMBED.exact,
    component: RichMenuEmbed,
  },
];

export default routes;
