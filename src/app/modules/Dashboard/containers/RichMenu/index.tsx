// Libraries
import React from 'react';
import { useSelector } from 'react-redux';
import { HashRouter as Router, Route, Switch, Redirect } from 'react-router-dom';

// Routes
import routes from './routes';

// Utils
import { getLinkFromKey } from 'app/utils';

// Constants
import { ROUTES } from 'constants/routes';

// Slice
import { selectLayout } from 'app/modules/slice/selectors';

interface RichMenuProps {}

export const RichMenu: React.FC<RichMenuProps> = props => {
  const selectorLayout = useSelector(selectLayout);

  const userId = selectorLayout.user.user_id || '';
  // const url = getLinkFromKey(ROUTES.RICH_MENU_DESIGN.key, { userId, id: 123 });

  return (
    <Router>
      <Switch>
        {routes.map(route => {
          const { exact, path, component, key } = route;

          return <Route key={key} path={path} exact={exact} component={component} />;
        })}
      </Switch>
      {/* <Redirect to={url} /> */}
    </Router>
  );
};
