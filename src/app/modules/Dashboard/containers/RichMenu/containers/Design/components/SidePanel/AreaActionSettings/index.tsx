// Libraries
import { Collapse } from '@antscorp/antsomi-ui';
import React, { memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Constants
import { SIDE_PANEL_COLLAPSE, WORKSPACE_TYPE } from 'richMenu/containers/Design/constants';
import { ActionSetting } from '../components/ActionSetting';

// Selectors
import { selectSelectedRichMenuCell, selectWorkspaceType } from '../../../slice/selectors';

// Actions
import { richMenuDesignActions } from '../../../slice';

// Utils
import { tryCatchWrapper } from 'app/utils';

// Types
import { PanelActiveType, TAction, TActionImageMap } from '../../../types';
import { ActionSettingV2 } from '../components/ActionSettingV2';

interface AreaActionSettingsProps {}

const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/AreaActionSettings';

export const AreaActionSettings: React.FC<AreaActionSettingsProps> = memo(() => {
  const dispatch = useDispatch();
  const selectedCell = useSelector(selectSelectedRichMenuCell);
  const workspaceType = useSelector(selectWorkspaceType);

  const isRichMenu = workspaceType === WORKSPACE_TYPE.RICH_MENU;

  const { updateSelectedRichMenuCell, updateSidePanelKey } = richMenuDesignActions;

  if (!selectedCell) return null;

  const handleUpdateActionSettings = tryCatchWrapper((action: TAction) => {
    dispatch(updateSelectedRichMenuCell({ action }));
  }, PATH);

  const settingCollapses = [
    {
      ...SIDE_PANEL_COLLAPSE.ACTION,
      children: !isRichMenu ? (
        <ActionSettingV2
          action={selectedCell?.action as TActionImageMap}
          onChange={handleUpdateActionSettings as any}
        />
      ) : (
        <ActionSetting action={selectedCell?.action as TAction} onChange={handleUpdateActionSettings} />
      ),
    },
  ];

  return (
    <Collapse
      accordion
      defaultActiveKey={isRichMenu ? 'ACTION' : undefined}
      onChange={keys => {
        dispatch(updateSidePanelKey({ panelKey: (Array.isArray(keys) ? keys[0] : keys) as PanelActiveType }));
      }}
      destroyInactivePanel
      items={settingCollapses}
      bordered={false}
      expandIconPosition="right"
    />
  );
});
