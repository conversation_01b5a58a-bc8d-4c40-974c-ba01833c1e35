// Libraries
import { Flex } from '@antscorp/antsomi-ui';
import React from 'react';
import { useTranslation } from 'react-i18next';

// Components
import {
  ImageSetting,
  TImageSettingValues,
} from 'richMenu/containers/Design/components/SidePanel/components/ImageSetting';
import { SliderWithUnit } from 'app/components/molecules';
import { AlignSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules';

// Utils
import { tryCatchWrapper } from 'app/utils';

// Types
import { TChildrenCell } from 'richMenu/containers/Design/types';

// Locales
import { translations } from 'locales/translations';
import { TAlign } from 'types';
import { handleValueWhenChangeSuffix } from '../../../../../utils';

interface ImageSectionProps {
  cell: TChildrenCell;
  onChange: (data: Partial<TChildrenCell>) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/CellImageSettings/Content/ImageSection/index.tsx';

export const ImageSection: React.FC<ImageSectionProps> = props => {
  const { cell, onChange } = props;
  const { image, outerContainerStyles } = cell || {};
  const { imageSettings, imageStyles } = image || {};
  const { width, height } = imageStyles;
  const { widthSuffix, heightSuffix } = imageSettings;

  const { t } = useTranslation();

  const handleChangeImageSettings = tryCatchWrapper((values: TImageSettingValues) => {
    const { imageUrl, objectFit, objectPosition } = values;

    // Handlers
    onChange({
      image: {
        ...image,
        imageUrl,
        imageStyles: {
          ...(image.imageStyles || {}),
          objectFit: `${objectFit}`,
          objectPosition: `${objectPosition}`,
        },
      },
    });
  }, PATH);

  const handleChangeStylesAndSettings = tryCatchWrapper(({ settings, styles }) => {
    onChange({
      image: {
        ...image,
        imageSettings: {
          ...imageSettings,
          ...settings,
        },
        imageStyles: {
          ...imageStyles,
          ...styles,
        },
      },
    });
  }, PATH);

  return (
    <Flex gap={20} vertical>
      <ImageSetting
        values={{
          imageUrl: image?.imageUrl || '',
          objectFit: image?.imageStyles.objectFit,
          objectPosition: image?.imageStyles.objectPosition,
        }}
        onChange={handleChangeImageSettings}
      />

      <AlignSetting
        label={t(translations.align.title)}
        align={outerContainerStyles.textAlign}
        onChange={textAlign => onChange({ outerContainerStyles: { textAlign: textAlign as TAlign } })}
      />
      <SliderWithUnit
        label={t(translations.width.title)}
        value={parseInt(imageStyles.width)}
        unit={imageSettings?.widthSuffix || '%'}
        min={0}
        max={(imageSettings?.widthSuffix || '%') === '%' ? 100 : 1000}
        onAfterChange={value => handleChangeStylesAndSettings({ styles: { width: `${value}${widthSuffix}` } })}
        onChangeUnit={suffix => {
          const newValue = handleValueWhenChangeSuffix(width, suffix);

          handleChangeStylesAndSettings({
            styles: {
              width: newValue,
            },
            settings: {
              widthSuffix: suffix,
            },
          });
        }}
      />
      <SliderWithUnit
        label={t(translations.height.title)}
        value={parseInt(imageStyles.height)}
        unit={imageSettings?.heightSuffix || 'auto'}
        min={0}
        max={1000}
        hideUnits={['%']}
        onAfterChange={value => handleChangeStylesAndSettings({ styles: { height: `${value}${heightSuffix}` } })}
        onChangeUnit={suffix => {
          const newValue = handleValueWhenChangeSuffix(height, suffix);

          handleChangeStylesAndSettings({
            styles: {
              height: newValue,
            },
            settings: {
              heightSuffix: suffix,
            },
          });
        }}
      />
    </Flex>
  );
};
