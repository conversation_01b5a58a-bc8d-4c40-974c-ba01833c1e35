// Libraries
import styled from 'styled-components';

// Types
import { RichMenu } from 'richMenu/containers/Design/types';

export const MENU_CONTAINER_WIDTH = 370;

export const StyledContainer = styled.div`
  height: 100%;
  margin: 10px 10px 10px;
  // border: 1px dashed var(--primary-color);
  overflow: visible;
`;

export const StyledRichMenu = styled.div`
  // width: 185px;
  width: ${MENU_CONTAINER_WIDTH}px;
`;
