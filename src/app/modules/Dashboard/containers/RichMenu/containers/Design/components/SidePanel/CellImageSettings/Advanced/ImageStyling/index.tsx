// Libraries
import React from 'react';
import { ImageAdvancedSetting } from '../../../components/ImageAdvancedSetting';

// Types
import { TChildrenCell } from 'richMenu/containers/Design/types';

interface ImageStylingProps {
  cell: TChildrenCell;
  onChange: (data: Partial<TChildrenCell>) => void;
}

export const ImageStyling: React.FC<ImageStylingProps> = props => {
  const { cell, onChange } = props;
  const { image } = cell || {};

  return (
    <ImageAdvancedSetting
      styles={image.imageStyles}
      settings={image.imageSettings}
      onChange={(settings, styles) => onChange({ image: { ...image, imageSettings: settings, imageStyles: styles } })}
    />
  );
};
