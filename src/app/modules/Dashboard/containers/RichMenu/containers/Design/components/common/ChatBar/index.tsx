// Libraries
import React, { useState } from 'react';

// Styles
import { ChatBarMenuBox, ChatBarWrapper } from './styled';
import { Icon, Typography } from '@antscorp/antsomi-ui';
import { TypingChat } from './TypingChat';

interface ChatBarProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  label?: string;
  showRichMenu?: boolean;
  showTypingChat?: boolean;
  onToggleRichMenu?: (toggle: boolean) => void;
  onToggleTypingChat?: (toggle: boolean) => void;
  isSingleMode?: boolean;
}

const defaultProps: ChatBarProps = {
  label: 'Menu',
  showRichMenu: true,
  showTypingChat: false,
  isSingleMode: true,
};

export const ChatBar: React.FC<ChatBarProps> = props => {
  const {
    label,
    showRichMenu,
    showTypingChat,
    isSingleMode,
    onToggleRichMenu,
    onToggleTypingChat = () => {},
    ...restOfProps
  } = props;

  return (
    <ChatBarWrapper
      $showRichMenu={showRichMenu}
      $showTypingChat={showTypingChat}
      {...restOfProps}
      $isSingleMode={isSingleMode}
    >
      {showTypingChat ? (
        <TypingChat onClickMenu={() => onToggleTypingChat(!showTypingChat)} />
      ) : (
        <>
          <Icon
            type="icon-ants-keyboard"
            className="ants-cursor-pointer"
            onClick={() => onToggleTypingChat(!showTypingChat)}
          />
          <ChatBarMenuBox onClick={() => onToggleRichMenu && onToggleRichMenu(!showRichMenu)}>
            <Typography.Text className="ants-text-sm">{label}</Typography.Text>
            {label && <Icon className="icon-caret" type="icon-ants-caret-down" size={10} />}
          </ChatBarMenuBox>
        </>
      )}
    </ChatBarWrapper>
  );
};

ChatBar.defaultProps = defaultProps;
