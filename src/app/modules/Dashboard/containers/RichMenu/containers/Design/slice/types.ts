// Types
import { <PERSON><PERSON><PERSON>, ExternalConfig, General, RichMenu, RichMenuTemplate } from '../types';

/* --- STATE --- */
export interface RichMenuDesignState {
  general: General;
  externalConfig: ExternalConfig;
  sidePanel: {
    selectedRichMenuId: string | number | null;
    selectedCellIdx: number;
    selectedBlockType: BlockKey;
    isSaving: boolean;
  };
  richMenuTemplate: RichMenuTemplate;
}

/* PAYLOAD ACTION */
export interface UndoActionPayload {
  ignoreUndoAction?: boolean;
}

export interface RichMenuDesignPayloadData extends UndoActionPayload {
  id: string | number;
  data: Partial<RichMenu>;
}

export interface RichMenuDefaultPayload {
  richMenuId: string | number;
  isDefault?: boolean;
}

export interface DuplicateRichMenuPayload {
  richMenuId: string | number;
}
export interface RemoveRichMenuPayload extends DuplicateRichMenuPayload {}
export interface ResetLayoutPayload extends DuplicateRichMenuPayload {}

export interface UpdateModePayload {
  mode: RichMenuTemplate['mode'];
}

export interface UpdateModeSelectedRichMenuPayload {
  richMenuId: string | number;
}

export interface updateRichMenuDefaultPayload {
  richMenuId: string | number;
  isDefault: boolean;
}
