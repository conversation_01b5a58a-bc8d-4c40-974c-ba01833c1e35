// Libraries
import styled, { css } from 'styled-components';

// Types
import { RichMenu, WorkspaceType } from 'richMenu/containers/Design/types';

// Constants
import { WORKSPACE_TYPE } from '../../../../../constants';

interface StyledRichMenuProps {
  $isCompact?: boolean;
  $gridTemplateRows?: RichMenu['gridTemplateRows'];
  $gridTemplateCols?: RichMenu['gridTemplateCols'];
  $selected?: boolean;
  $workspaceType?: WorkspaceType;
}

export const AreaLayoutWrapper = styled.div``;

export const AreaLayoutItem = styled.div<StyledRichMenuProps>`
  display: grid;
  width: 100%;
  grid-gap: 1px;
  aspect-ratio: ${({ $isCompact, $workspaceType }) =>
    $workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP ? 1 : $isCompact ? '3 / 1' : '3/ 2'};
  grid-template-columns: ${({ $gridTemplateCols }) => $gridTemplateCols?.map(col => col).join(' ')};
  grid-template-rows: ${({ $gridTemplateRows }) => $gridTemplateRows?.map(row => row).join(' ')};
  transition: all 300ms;
  cursor: pointer;
`;

export const AreaLayoutCell = styled.div<{ $selected?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  outline: 1px solid ${({ $selected }) => ($selected ? '#fff' : 'var(--gray-4-color)')};
  transition: all 300ms;

  ${({ $selected }) =>
    $selected &&
    css`
      background-color: var(--primary-color);
    `}
`;
