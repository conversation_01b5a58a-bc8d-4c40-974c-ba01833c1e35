// Libraries
import React from 'react';
import { Icon } from '@antscorp/antsomi-ui';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Styled
import { StyledAddNewMenuBtn } from './styled';

interface AddNewMenuBtnProps extends React.HTMLAttributes<HTMLDivElement> {
  isCollapsed?: boolean;
}

export const AddNewMenuBtn: React.FC<AddNewMenuBtnProps> = props => {
  const { isCollapsed, ...restProps } = props;
  const { t } = useTranslation();

  return (
    <StyledAddNewMenuBtn align="center" gap={6} $isCollapsed={!!isCollapsed} {...restProps}>
      <Icon type="icon-ants-add-square" />
      {!isCollapsed && t(translations.addNewMenu.title)}
    </StyledAddNewMenuBtn>
  );
};
