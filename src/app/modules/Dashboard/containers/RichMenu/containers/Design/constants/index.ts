// Libraries
import dayjs from 'dayjs';

// layoutTypes
import {
  AreaLayout,
  RichMenu,
  AreaLayoutKey,
  RichMenuTemplate,
  TImage,
  TAction,
  TAreaStyles,
  TAreaStylesSettings,
  TOuterContainerStyles,
  LayoutGridType,
  AreaLayoutKeyAdvImageMap,
} from '../types';

// Constants
import { DATE_TIME_FORMAT } from 'constants/datetime';

// Translation
import { translations } from 'locales/translations';

// Utils
import { random } from 'app/utils';
import { getTranslateMessage } from 'utils/messages';
import { generateEvenCells } from '../utils';

/* Workspace mode */
export const WORKSPACE_TYPE = {
  RICH_MENU: 'richMenu',
  ADVANCED_IMAGE_MAP: 'advancedImageMap',
} as const;

export const WORKSPACE_DIMENSIONS = {
  [WORKSPACE_TYPE.ADVANCED_IMAGE_MAP]: {
    width: 520,
    height: 520,
  },
  [WORKSPACE_TYPE.RICH_MENU]: {
    width: 336,
    height: 224,
  },
} as const;

/* Keys */
export const AREA_CELL_MAX = 20;
export const AREA_CELL_MAX_IMAGE_MAP = 50;

export const AREA_LAYOUT_KEYS = {
  LAYOUT_1: 'LAYOUT_1',
  LAYOUT_2: 'LAYOUT_2',
  LAYOUT_3: 'LAYOUT_3',
  LAYOUT_4: 'LAYOUT_4',
  LAYOUT_5: 'LAYOUT_5',
  LAYOUT_6: 'LAYOUT_6',
  LAYOUT_7: 'LAYOUT_7',
  LAYOUT_8: 'LAYOUT_8',
  LAYOUT_9: 'LAYOUT_9',
  LAYOUT_10: 'LAYOUT_10',
  LAYOUT_11: 'LAYOUT_11',
  LAYOUT_12: 'LAYOUT_12',
} as const;

export const AREA_LAYOUT_KEYS_ADV_IMAGE_MAP = {
  LAYOUT_1: 'LAYOUT_1',
  LAYOUT_2: 'LAYOUT_2',
  LAYOUT_3: 'LAYOUT_3',
  LAYOUT_4: 'LAYOUT_4',
  LAYOUT_5: 'LAYOUT_5',
  LAYOUT_6: 'LAYOUT_6',
  LAYOUT_7: 'LAYOUT_7',
  LAYOUT_8: 'LAYOUT_8',
} as const;

export const BLOCK_KEYS = {
  RICH_MENU: 'RICH_MENU',
  CELL_ACTION: 'CELL_ACTION',
  CELL_IMAGE: 'CELL_IMAGE',
} as const;

export const SIDE_PANEL_COLLAPSE_KEYS = {
  MENU_DETAILS: 'MENU_DETAILS',
  AREA_LAYOUT: 'AREA_LAYOUT',
  MENU_IMAGE: 'MENU_IMAGE',
  CHAT_BAR: 'CHAT_BAR',
  ACTION: 'ACTION',
  IMAGE: 'IMAGE',
  IMAGE_STYLING: 'IMAGE_STYLING',
  CONTAINER_STYLING: 'CONTAINER_STYLING',
} as const;

export const UPLOAD_MODE_KEYS = {
  SINGLE: 'single',
  MULTIPLE: 'multiple',
} as const;

export const ACTION_TYPE = {
  NONE: 'none',
  MESSAGE: 'message',
  URI: 'uri',
  POST_BACK: 'postback',
  RICH_MENU_SWITCH: 'richmenuswitch',
  DATE_TIME_PICKER: 'datetimepicker',
} as const;

export const ACTION_TYPE_ADV_IMAGE_MAP = {
  NONE: 'none',
  MESSAGE: 'message',
  URL: 'url',
  VIDEO: 'video',
} as const;

export const URI_TYPE = {
  URL: 'url',
  TEL: 'tel',
} as const;

export const FORMAT_TYPE = {
  DATE: 'date',
  TIME: 'time',
  DATE_TIME: 'datetime',
} as const;

export const LAYOUT_SIZE_GRID: LayoutGridType = 'sm' as const;
export const LAYOUT_SIZE_GRID_SQUARE: LayoutGridType = 'square' as const;

const {
  LAYOUT_1,
  LAYOUT_2,
  LAYOUT_3,
  LAYOUT_4,
  LAYOUT_5,
  LAYOUT_6,
  LAYOUT_7,
  LAYOUT_8,
  LAYOUT_9,
  LAYOUT_10,
  LAYOUT_11,
  LAYOUT_12,
} = AREA_LAYOUT_KEYS;

const { RICH_MENU, CELL_ACTION: AREA_ACTION, CELL_IMAGE } = BLOCK_KEYS;
const { MENU_DETAILS, AREA_LAYOUT, MENU_IMAGE, CHAT_BAR, ACTION, IMAGE, IMAGE_STYLING, CONTAINER_STYLING } =
  SIDE_PANEL_COLLAPSE_KEYS;
const { MULTIPLE, SINGLE } = UPLOAD_MODE_KEYS;

/* Data Default */
export const IMAGE_SETTING_DEFAULT: TImage = {
  imageUrl: '',
  imageStyles: {
    borderTopLeftRadius: '0px',
    borderTopRightRadius: '0px',
    borderBottomRightRadius: '0px',
    borderBottomLeftRadius: '0px',
    borderTopWidth: '0px',
    borderRightWidth: '0px',
    borderBottomWidth: '0px',
    borderLeftWidth: '0px',
    borderStyle: 'solid',
    borderColor: '#ffffff',
    boxShadow: 'none',
    opacity: 1,
    width: '100%',
    height: 'auto',
    objectPosition: 'center center',
    objectFit: 'contain',
    paddingBottom: '0px',
    paddingLeft: '0px',
    paddingRight: '0px',
    paddingTop: '0px',
  },
  imageSettings: {
    borderRadiusStyle: 'none',
    borderRadiusSuffix: 'px',
    linkedBorderRadiusInput: true,
    linkedBorderWidthInput: true,
    boxShadowStyle: 'none',
    boxShadowColor: 'rgba(0, 0, 0, 0.5)',
    boxShadowBlur: '0px',
    boxShadowSpread: '0px',
    boxShadowHorizontal: '0px',
    boxShadowVertical: '0px',
    boxShadowInset: false,
    widthSuffix: '%',
    heightSuffix: 'auto',
    linkedPaddingInput: false,
    paddingSuffix: 'px',
  },
};

export const AREA_ACTION_DEFAULT: TAction = {
  type: 'none',
};

export const AREA_STYLES_DEFAULT: TAreaStyles = {
  borderTopLeftRadius: '0px',
  borderTopRightRadius: '0px',
  borderBottomRightRadius: '0px',
  borderBottomLeftRadius: '0px',

  borderTopWidth: '0px',
  borderRightWidth: '0px',
  borderBottomWidth: '0px',
  borderLeftWidth: '0px',

  paddingTop: '0px',
  paddingRight: '0px',
  paddingBottom: '0px',
  paddingLeft: '0px',
  marginTop: '0px',
  marginRight: '0px',
  marginBottom: '0px',
  marginLeft: '0px',

  background: 'transparent',
  borderStyle: 'none',
  borderColor: '#000000',
  boxShadow: 'none',
  position: 'relative',
};

export const AREA_STYLES_SETTING_DEFAULT: TAreaStylesSettings = {
  borderRadiusStyle: 'none',
  borderRadiusSuffix: 'px',
  linkedBorderRadiusInput: true,

  linkedBorderWidthInput: true,

  linkedPaddingInput: false,
  paddingSuffix: 'px',
  linkedMarginInput: true,
  marginSuffix: 'px',

  boxShadowStyle: 'none',
  boxShadowColor: '#ffffff',
  boxShadowBlur: '0px',
  boxShadowSpread: '0px',
  boxShadowHorizontal: '0px',
  boxShadowVertical: '0px',
  boxShadowInset: false,

  backgroundColor: 'transparent',
  backgroundColorStyle: 'color',
  backgroundPosition: 'left top',
  backgroundRepeat: 'no-repeat',
  backgroundSize: 'cover',
  gradientType: 'linear-gradient',
  radialPosition: 'center center',
  linearAngle: 45,
  gradients: [
    {
      gradientColor: '#ffffff',
      gradientColorLocation: 0,
    },
    {
      gradientColor: '#000000',
      gradientColorLocation: 50,
    },
  ],
};

export const OUTER_CONTAINER_STYLES_DEFAULT: TOuterContainerStyles = {
  textAlign: 'center',
};

/* Options */
export const AREA_LAYOUTS: Record<AreaLayoutKey, AreaLayout> = {
  [LAYOUT_1]: {
    key: LAYOUT_1,
    name: 'Layout 1',
    rows: 2,
    cols: 3,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['33.33%', '33.33%', '33.33%'],
    cells: generateEvenCells(2, 3),
  },
  [LAYOUT_2]: {
    key: LAYOUT_2,
    name: 'Layout 2',
    rows: 2,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['50%', '50%'],
    cells: generateEvenCells(2, 2),
  },
  [LAYOUT_3]: {
    key: LAYOUT_3,
    name: 'Layout 3',
    rows: 2,
    cols: 3,
    layoutType: 'large',
    cells: [
      {
        rowStart: 1,
        rowEnd: 2,
        colStart: 1,
        colEnd: 4,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
      {
        rowStart: 2,
        rowEnd: 3,
        colStart: 1,
        colEnd: 2,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
      {
        rowStart: 2,
        rowEnd: 3,
        colStart: 2,
        colEnd: 3,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
      {
        rowStart: 2,
        rowEnd: 3,
        colStart: 3,
        colEnd: 4,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
    ],
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['33.33%', '33.33%', '33.33%'],
  },
  [LAYOUT_4]: {
    key: LAYOUT_4,
    name: 'Layout 4',
    rows: 2,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['70%', '30%'],
    cells: generateEvenCells(2, 2),
  },
  [LAYOUT_5]: {
    key: LAYOUT_5,
    name: 'Layout 5',
    rows: 1,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['50%', '50%'],
    cells: generateEvenCells(1, 2),
  },
  [LAYOUT_6]: {
    key: LAYOUT_6,
    name: 'Layout 6',
    rows: 2,
    cols: 1,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['100%'],
    cells: generateEvenCells(2, 1),
  },
  [LAYOUT_7]: {
    key: LAYOUT_7,
    name: 'Layout 7',
    rows: 1,
    cols: 1,
    layoutType: 'large',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['100%'],
    cells: generateEvenCells(1, 1),
  },
  [LAYOUT_8]: {
    key: LAYOUT_8,
    name: 'Layout 8',
    rows: 1,
    cols: 3,
    layoutType: 'compact',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['33.33%', '33.33%', '33.33%'],
    cells: generateEvenCells(1, 3),
  },
  [LAYOUT_9]: {
    key: LAYOUT_9,
    name: 'Layout 9',
    rows: 1,
    cols: 2,
    layoutType: 'compact',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['70%', '30%'],
    cells: generateEvenCells(1, 2),
  },
  [LAYOUT_10]: {
    key: LAYOUT_10,
    name: 'Layout 10',
    rows: 1,
    cols: 2,
    layoutType: 'compact',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['30%', '70%'],
    cells: generateEvenCells(1, 2),
  },
  [LAYOUT_11]: {
    key: LAYOUT_11,
    name: 'Layout 11',
    rows: 1,
    cols: 2,
    layoutType: 'compact',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['50%', '50%'],
    cells: generateEvenCells(1, 2),
  },
  [LAYOUT_12]: {
    key: LAYOUT_12,
    name: 'Layout 12',
    rows: 1,
    cols: 1,
    layoutType: 'compact',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['100%'],
    cells: generateEvenCells(1, 1),
  },
};

export const AREA_LAYOUTS_ADV_IMAGE_MAP: Record<AreaLayoutKeyAdvImageMap, AreaLayout> = {
  [LAYOUT_1]: {
    key: LAYOUT_1,
    name: 'Layout 1',
    rows: 2,
    cols: 1,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['100%'],
    cells: generateEvenCells(2, 1),
  },
  [LAYOUT_2]: {
    key: LAYOUT_2,
    name: 'Layout 2',
    rows: 1,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['50%', '50%'],
    cells: generateEvenCells(1, 2),
  },
  [LAYOUT_3]: {
    key: LAYOUT_3,
    name: 'Layout 3',
    rows: 1,
    cols: 3,
    layoutType: 'large',
    gridTemplateRows: ['100%'],
    gridTemplateCols: ['33.33%', '33.33%', '33.33%'],
    cells: generateEvenCells(1, 3),
  },
  [LAYOUT_4]: {
    key: LAYOUT_4,
    name: 'Layout 4',
    rows: 3,
    cols: 1,
    layoutType: 'large',
    gridTemplateRows: ['33.33%', '33.33%', '33.33%'],
    gridTemplateCols: ['100%'],
    cells: generateEvenCells(3, 1),
  },
  [LAYOUT_5]: {
    key: LAYOUT_5,
    name: 'Layout 5',
    rows: 2,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['70%', '30%'],
    cells: generateEvenCells(2, 2),
  },
  [LAYOUT_6]: {
    key: LAYOUT_6,
    name: 'Layout 6',
    rows: 2,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['30%', '70%'],
    cells: generateEvenCells(2, 2),
  },
  [LAYOUT_7]: {
    key: LAYOUT_7,
    name: 'Layout 7',
    rows: 2,
    cols: 2,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['50%', '50%'],
    cells: generateEvenCells(2, 2),
  },
  [LAYOUT_8]: {
    key: LAYOUT_8,
    name: 'Layout 8',
    rows: 2,
    cols: 3,
    layoutType: 'large',
    gridTemplateRows: ['50%', '50%'],
    gridTemplateCols: ['33.33%', '33.33%', '33.33%'],
    cells: [
      {
        rowStart: 1,
        rowEnd: 2,
        colStart: 1,
        colEnd: 4,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
      {
        rowStart: 2,
        rowEnd: 3,
        colStart: 1,
        colEnd: 2,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
      {
        rowStart: 2,
        rowEnd: 3,
        colStart: 2,
        colEnd: 3,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
      {
        rowStart: 2,
        rowEnd: 3,
        colStart: 3,
        colEnd: 4,
        image: IMAGE_SETTING_DEFAULT,
        outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
        areaStyles: AREA_STYLES_DEFAULT,
        areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        action: AREA_ACTION_DEFAULT,
      },
    ],
  },
};

export const BLOCKS = {
  [RICH_MENU]: {
    key: RICH_MENU,
    label: getTranslateMessage(translations.richMenuSettings.title),
    icon: 'icon-ants-multiple-menu',
  },
  [AREA_ACTION]: {
    key: AREA_ACTION,
    label: getTranslateMessage(translations.areaAction.title),
    icon: 'icon-ants-bars',
  },
  [CELL_IMAGE]: {
    key: CELL_IMAGE,
    label: getTranslateMessage(translations.image.title),
    icon: 'icon-ants-image-3',
  },
};

export const SIDE_PANEL_COLLAPSE = {
  [MENU_DETAILS]: {
    key: MENU_DETAILS,
    label: getTranslateMessage(translations.richMenuDetails.title),
  },
  [AREA_LAYOUT]: {
    key: AREA_LAYOUT,
    label: getTranslateMessage(translations.areaLayout.title),
  },
  [MENU_IMAGE]: {
    key: MENU_IMAGE,
    label: getTranslateMessage(translations.menuImage.title),
  },
  [CHAT_BAR]: {
    key: CHAT_BAR,
    label: getTranslateMessage(translations.chatBar.title),
  },
  [ACTION]: {
    key: ACTION,
    label: getTranslateMessage(translations.action.title),
  },
  [IMAGE]: {
    key: IMAGE,
    label: getTranslateMessage(translations.image.title),
  },
  [IMAGE_STYLING]: {
    key: IMAGE_STYLING,
    label: getTranslateMessage(translations.imageStyling.title),
  },
  [CONTAINER_STYLING]: {
    key: CONTAINER_STYLING,
    label: getTranslateMessage(translations.containerStyling.title),
  },
};

export const RICH_MENU_DEFAULT: RichMenu = {
  id: random(10), // random id
  menuName: `Untitled Menu ${dayjs().format(DATE_TIME_FORMAT.TITLE_DATE_TIME)}`,
  aliasId: '',
  isDefault: true,
  cells: AREA_LAYOUTS['LAYOUT_1'].cells || [],
  cols: AREA_LAYOUTS['LAYOUT_1'].cols,
  rows: AREA_LAYOUTS['LAYOUT_1'].rows,
  areaLayoutId: 'LAYOUT_1',
  gridTemplateRows: AREA_LAYOUTS['LAYOUT_1'].gridTemplateRows,
  gridTemplateCols: AREA_LAYOUTS['LAYOUT_1'].gridTemplateCols,
  layoutType: AREA_LAYOUTS['LAYOUT_1'].layoutType,
  uploadMode: 'single',
  chatBar: {
    label: 'Menu',
    displayDefault: true,
  },
  image: {
    imageUrl: '',
    imageStyles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      borderStyle: 'solid',
      borderColor: '#ffffff',
      boxShadow: 'none',
      opacity: 1,
      width: '100%',
      height: 'auto',
      objectPosition: 'center center',
      objectFit: 'contain',
      paddingBottom: '0px',
      paddingLeft: '0px',
      paddingRight: '0px',
      paddingTop: '0px',
    },
    imageSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      widthSuffix: '%',
      heightSuffix: 'auto',
      linkedPaddingInput: false,
      paddingSuffix: 'px',
    },
  },
};

export const ADV_IMAGE_MAP_DEFAULT: RichMenu = {
  id: random(10), // random id
  menuName: `Untitled Menu ${dayjs().format(DATE_TIME_FORMAT.TITLE_DATE_TIME)}`,
  aliasId: '',
  isDefault: true,
  cells: AREA_LAYOUTS_ADV_IMAGE_MAP['LAYOUT_1'].cells || [],
  cols: AREA_LAYOUTS_ADV_IMAGE_MAP['LAYOUT_1'].cols,
  rows: AREA_LAYOUTS_ADV_IMAGE_MAP['LAYOUT_1'].rows,
  areaLayoutId: 'LAYOUT_1',
  gridTemplateRows: AREA_LAYOUTS_ADV_IMAGE_MAP['LAYOUT_1'].gridTemplateRows,
  gridTemplateCols: AREA_LAYOUTS_ADV_IMAGE_MAP['LAYOUT_1'].gridTemplateCols,
  layoutType: AREA_LAYOUTS_ADV_IMAGE_MAP['LAYOUT_1'].layoutType,
  uploadMode: 'single',
  chatBar: {
    label: 'Menu',
    displayDefault: true,
  },
  image: {
    imageUrl: '',
    imageStyles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '0px',
      borderRightWidth: '0px',
      borderBottomWidth: '0px',
      borderLeftWidth: '0px',
      borderStyle: 'solid',
      borderColor: '#ffffff',
      boxShadow: 'none',
      opacity: 1,
      width: '100%',
      height: 'auto',
      objectPosition: 'center center',
      objectFit: 'contain',
      paddingBottom: '0px',
      paddingLeft: '0px',
      paddingRight: '0px',
      paddingTop: '0px',
    },
    imageSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      boxShadowStyle: 'none',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '0px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      widthSuffix: '%',
      heightSuffix: 'auto',
      linkedPaddingInput: false,
      paddingSuffix: 'px',
    },
  },
};

export const UPLOAD_MODE = {
  [SINGLE]: {
    value: SINGLE,
    label: getTranslateMessage(translations.single.title),
  },
  [MULTIPLE]: {
    value: MULTIPLE,
    label: getTranslateMessage(translations.multiple.title),
  },
};

export const ACTION_SETTINGS = {
  [ACTION_TYPE.NONE]: {
    label: getTranslateMessage(translations.actionType.none.title),
    value: ACTION_TYPE.NONE,
  },
  [ACTION_TYPE.MESSAGE]: {
    label: getTranslateMessage(translations.actionType.message.title),
    value: ACTION_TYPE.MESSAGE,
  },
  [ACTION_TYPE.URI]: {
    label: getTranslateMessage(translations.actionType.uri.title),
    value: ACTION_TYPE.URI,
  },
  [ACTION_TYPE.RICH_MENU_SWITCH]: {
    label: getTranslateMessage(translations.actionType.richMenuSwitch.title),
    value: ACTION_TYPE.RICH_MENU_SWITCH,
  },
  [ACTION_TYPE.POST_BACK]: {
    label: getTranslateMessage(translations.actionType.postBack.title),
    value: ACTION_TYPE.POST_BACK,
  },
  [ACTION_TYPE.DATE_TIME_PICKER]: {
    label: getTranslateMessage(translations.actionType.dateTimePicker.title),
    value: ACTION_TYPE.DATE_TIME_PICKER,
  },
};

export const ACTION_SETTINGS_ADV_IMAGE_MAP = {
  [ACTION_TYPE_ADV_IMAGE_MAP.NONE]: {
    label: getTranslateMessage(translations.actionType.none.title),
    value: ACTION_TYPE_ADV_IMAGE_MAP.NONE,
  },
  [ACTION_TYPE_ADV_IMAGE_MAP.MESSAGE]: {
    label: getTranslateMessage(translations.actionType.message.title),
    value: ACTION_TYPE_ADV_IMAGE_MAP.MESSAGE,
  },
  [ACTION_TYPE_ADV_IMAGE_MAP.URL]: {
    label: getTranslateMessage(translations.actionType.redirectUrl.title),
    value: ACTION_TYPE_ADV_IMAGE_MAP.URL,
  },
  [ACTION_TYPE_ADV_IMAGE_MAP.VIDEO]: {
    label: getTranslateMessage(translations.actionType.video.title),
    value: ACTION_TYPE_ADV_IMAGE_MAP.VIDEO,
  },
};

export const URI_OPTIONS = {
  [URI_TYPE.URL]: {
    label: getTranslateMessage(translations.uriType.url.title),
    value: URI_TYPE.URL,
  },
  [URI_TYPE.TEL]: {
    label: getTranslateMessage(translations.uriType.tel.title),
    value: URI_TYPE.TEL,
  },
};

export const FORMAT_OPTIONS = {
  [FORMAT_TYPE.DATE]: {
    label: getTranslateMessage(translations.formatType.date.title),
    value: FORMAT_TYPE.DATE,
  },
  [FORMAT_TYPE.TIME]: {
    label: getTranslateMessage(translations.formatType.time.title),
    value: FORMAT_TYPE.TIME,
  },
  [FORMAT_TYPE.DATE_TIME]: {
    label: getTranslateMessage(translations.formatType.dateTime.title),
    value: FORMAT_TYPE.DATE_TIME,
  },
};

/**
 * Rich menu default template
 */
export const RICH_MENU_TEMPLATE_DEFAULT: RichMenuTemplate = {
  id: random(10),
  name: `Untitled Menu ${dayjs().format(DATE_TIME_FORMAT.TITLE_DATE_TIME)}`,
  // name: ``,
  mode: '' as any,
  richMenus: [RICH_MENU_DEFAULT],
};
