// Libraries
import React, { CSSProperties, useEffect } from 'react';
import { cloneDeep, has, pick } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';

// Types
import { CellBounds, RichMenu, TChildrenCell } from 'richMenu/containers/Design/types';

// Components
import { ResizeGrid, TGrid, type TCell } from '@antscorp/antsomi-ui';
import { RichMenuCell } from '../RichMenuCell';

// Slice
import { richMenuDesignActions } from 'richMenu/containers/Design/slice';

// Selectors
import { selectSelectedRichMenu, selectSidePanel } from '../../../../slice/selectors';

// Utils
import { tryCatchWrapper } from 'app/utils';

// Constants
import {
  AREA_ACTION_DEFAULT,
  AREA_STYLES_DEFAULT,
  AREA_STYLES_SETTING_DEFAULT,
  IMAGE_SETTING_DEFAULT,
  OUTER_CONTAINER_STYLES_DEFAULT,
} from '../../../../constants';

interface RichMenuBlockProps {
  richMenu: RichMenu;
  isPreview?: boolean;
  isSingleMode?: boolean;
}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/Workspace/components/RichMenuBlock/index.tsx';

export const RichMenuBlock: React.FC<RichMenuBlockProps> = props => {
  const { richMenu, isPreview, isSingleMode } = props;
  const dispatch = useDispatch();

  const { gridTemplateCols, gridTemplateRows, cells, uploadMode, image, layoutType, rows, cols } = richMenu;
  const { imageUrl, imageStyles } = image;
  const { updateSidePanel, updateRichMenuSettings } = richMenuDesignActions;

  // Select
  const richMenuSelected = useSelector(selectSelectedRichMenu);
  const sidePanel = useSelector(selectSidePanel);

  const grid = {
    rows,
    cols,
    gridTemplateColumns: gridTemplateCols.map(col => +String(col).replace('%', '')),
    gridTemplateRows: gridTemplateRows.map(row => +String(row).replace('%', '')),
    childs: cells.map(item => item as TCell),
  };
  const isSingleUploadMode = uploadMode === 'single';

  // Handlers
  const onClickCell = (cellIdx: number) => {
    // If is preview is true then not do anything
    if (isPreview) return;

    dispatch(
      updateSidePanel({
        selectedCellIdx: cellIdx,
        selectedBlockType: isSingleUploadMode ? 'CELL_ACTION' : 'CELL_IMAGE',
      }),
    );

    if (['CELL_ACTION', 'CELL_IMAGE'].includes(sidePanel.selectedBlockType)) {
      dispatch(
        updateSidePanel({
          selectedBlockType: (sidePanel.selectedBlockType === 'CELL_IMAGE' ? 'RE_CELL_IMAGE' : 'RE_CELL_ACTION') as any, // Used to re-open action setting side panel
        }),
      );
    }
  };

  const handleGridChange = tryCatchWrapper((gridInfo: Partial<TGrid>, ctx: any[], source: 'user' | 'system') => {
    if (richMenu) {
      const { id } = richMenu;
      const { rows, cols, gridTemplateRows, gridTemplateColumns, childs } = gridInfo;
      const gridTemplateColsUnit = gridTemplateColumns?.map(col => `${col}%`) || [];
      const gridTemplateRowsUnit = gridTemplateRows?.map(row => `${row}%`) || [];
      const cells = childs?.map((child, idx) => {
        let item = cloneDeep(child) as any;
        const cellBounds: CellBounds = pick(ctx[idx], ['x', 'y', 'width', 'height']);
        if (!has(item, 'action')) {
          item = {
            ...item,
            image: IMAGE_SETTING_DEFAULT,
            outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
            areaStyles: AREA_STYLES_DEFAULT,
            areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
            action: AREA_ACTION_DEFAULT,
          };
        }

        return { ...item, ...cellBounds };
      });

      const payload = {
        id,
        data: {
          rows,
          cols,
          gridTemplateRows: gridTemplateRowsUnit,
          gridTemplateCols: gridTemplateColsUnit,
          cells: cells as TChildrenCell[],
        },
        // ignoreUndoAction: source === 'system',
        ignoreUndoAction: true,
      };

      dispatch(updateRichMenuSettings(payload));
    }
  }, PATH);

  useEffect(() => {
    if (['RE_CELL_IMAGE', 'RE_CELL_ACTION'].includes(sidePanel.selectedBlockType)) {
      dispatch(
        updateSidePanel({
          selectedBlockType: (sidePanel.selectedBlockType as any) === 'RE_CELL_IMAGE' ? 'CELL_IMAGE' : 'CELL_ACTION',
        }),
      );
    }
  }, [dispatch, sidePanel.selectedBlockType, updateSidePanel]);

  const renderCell = (cellInfo: TChildrenCell, ctx: number) => {
    return <RichMenuCell context={ctx} cell={cellInfo} isSingleMode={isSingleUploadMode} onClick={onClickCell} />;
  };

  return (
    <ResizeGrid
      grid={grid}
      containerStyle={imageStyles as CSSProperties}
      isCompact={layoutType === 'compact'}
      renderCell={renderCell}
      allowActive={richMenuSelected?.id === richMenu.id}
      cellActiveStyle={{ background: 'rgba(0, 95, 184, 0.5)' }}
      cellBorder={isSingleUploadMode ? '1px solid rgba(0, 95, 184, 1)' : '1px dotted #000000'}
      onChange={handleGridChange}
      isPreview={isPreview}
      {...{
        ...(isSingleUploadMode && {
          image: imageUrl,
          imagePosition: imageStyles.objectPosition,
          imageSize: imageStyles.objectFit as 'cover' | 'contain' | 'fill',
        }),
      }}
    />
  );
};
