// Types
import { getTranslateMessage } from 'utils/messages';
import { ExternalConfig, RichMenu, RichMenuErrors, RichMenuTemplate, TRichMenuMode, WorkspaceType } from '../types';
import { translations } from 'locales/translations';
// import { checkNameIsExist } from 'app/utils';
import { richMenuValidationService } from 'app/services/RichMenu';
import { has, isEmpty, omit } from 'lodash';
import { AREA_CELL_MAX, AREA_CELL_MAX_IMAGE_MAP, WORKSPACE_TYPE } from '../constants';

/**
 * Function handle validate Error for rich menu
 * @param richMenuTemplate
 */
export const validateRichMenuTemplate = async (richMenuTemplate: RichMenuTemplate, externalConfig?: ExternalConfig) => {
  const { mode, richMenus } = richMenuTemplate;
  // const { templateNameList } = externalConfig || {};
  const errors: RichMenuErrors = {
    global: {},
  };
  let isShowError = false;

  // Validate RichMenu template name
  // Đ<PERSON>i luồng không validate trùng rich menu template name
  // const errorMessage = validateRichMenuTemplateName(name, templateNameList);

  // if (errorMessage) {
  //   errors.templateName = errorMessage;
  // }

  // Validate per RichMenu
  await Promise.all(
    richMenus.map(async richMenu => {
      const richMenuErrors = await validateRichMenu({ richMenu, richMenus, mode, externalConfig, mainErrors: errors });

      // Init errors for richMenu
      if (isEmpty(errors.richMenus) && !isEmpty(richMenuErrors)) {
        errors.richMenus = {};
      }

      if (errors.richMenus && !isEmpty(richMenuErrors)) {
        errors.richMenus[richMenu.id] = richMenuErrors;
      }
    }),
  );

  if (Object.keys(omit(errors, 'global')).length || Object.keys(errors.global).length) {
    isShowError = true;
  }

  return { errors, isShowError };
};

/**
 * Validates a Rich Image Map Template.
 *
 * @param {RichMenuTemplate} richImageMapTemplate - The Rich Image Map Template to validate.
 * @returns {Object} An object containing validation results.
 * @property {RichMenuErrors} errors - The errors found during validation.
 * @property {boolean} isShowError - A boolean indicating whether errors should be displayed.
 *
 * @typedef {Object} RichMenuTemplate - The structure of a Rich Image Map Template.
 * @property {Array<RichImageMap>} richMenus - An array of Rich Image Maps within the template.
 *
 * @typedef {Object} RichMenuErrors - Object representing validation errors for a Rich Image Map Template.
 * @property {Object} global - Global errors applicable to the entire template.
 * @property {Object.<string, Object>} richMenus - Errors specific to each Rich Image Map, keyed by their IDs.
 *
 * @typedef {Object} RichImageMap - The structure of a Rich Image Map.
 * @property {string} id - The unique identifier of the Rich Image Map.
 *
 * @typedef {Object} RichTemplateErrors - Validation errors for a Rich Image Map.
 */
export const validateRichImageMapTemplate = (richImageMapTemplate: RichMenuTemplate) => {
  const { richMenus, name } = richImageMapTemplate;
  const errors: RichMenuErrors = {
    global: {},
  };
  let isShowError = false;

  if (!name) {
    errors.templateName = getTranslateMessage(translations.messageError.fieldEmpty.message);
  }

  richMenus.forEach(richImageMap => {
    const richTemplateErrors = validateRichImageMap({
      richImageMap,
    });

    // Init errors for richImageMap
    if (isEmpty(errors.richMenus) && !isEmpty(richTemplateErrors)) {
      errors.richMenus = {};
    }

    if (errors.richMenus && !isEmpty(richTemplateErrors)) {
      errors.richMenus[richImageMap.id] = richTemplateErrors;
    }
  });

  if (Object.keys(omit(errors, 'global')).length || Object.keys(errors.global).length) {
    isShowError = true;
  }

  return { errors, isShowError };
};

// const validateRichMenuTemplateName = (name: string = '', currentTemplateNameList: string[] = []) => {
//   let errorMessage: string | undefined;
//
//   if (!name) {
//     errorMessage = getTranslateMessage(translations.messageError.fieldEmpty.message);
//   }
//
//   if (name && currentTemplateNameList.length && currentTemplateNameList.includes(name)) {
//     errorMessage = getTranslateMessage(translations.messageError.nameExisted.message);
//   }
//
//   return errorMessage;
// };

const validateRichMenu = async ({
  richMenu,
  richMenus,
  mode,
  externalConfig,
  mainErrors,
}: {
  richMenu: RichMenu;
  richMenus: RichMenu[];
  mode: TRichMenuMode;
  externalConfig?: ExternalConfig;
  mainErrors: RichMenuErrors;
}) => {
  const { aliasId, menuName, cells, id: richMenuId, richMenuId: richMenuIdUpdate = '' } = richMenu as any;
  const { token } = externalConfig || {};
  const excludeRichMenu = richMenus.filter(r => r.id !== richMenu.id);

  const errors: Record<string, any> = {
    cells: [],
  };

  // If Mode equal multiple then validate menu detail
  if (mode === 'multiple') {
    // Initial errors to check has at least 1 action contain action type 'richmenuswitch'
    mainErrors.global.richmenuswitch = getTranslateMessage(translations.messageError.richMenuSwitchNoExist.message);

    // Check Name is empty
    if (!menuName) {
      errors.menuName = getTranslateMessage(translations.messageError.fieldEmpty.message);
    }

    // Check Name is existed
    if (menuName && excludeRichMenu.some(item => item.menuName === richMenu.menuName)) {
      errors.menuName = getTranslateMessage(translations.messageError.nameExisted.message);
    }

    // Check Alias is Empty
    if (!aliasId) {
      errors.aliasId = getTranslateMessage(translations.messageError.fieldEmpty.message);
    }

    // Check Alias is existed
    if (aliasId) {
      if (excludeRichMenu.some(item => item.aliasId === richMenu.aliasId)) {
        errors.aliasId = getTranslateMessage(translations.messageError.nameExisted.message);
      }

      // Validate alias id is existed per channel
      if (token) {
        const { entries } =
          (await richMenuValidationService.validateAliasId({
            alias: {
              id: `${aliasId}`,
              richMenuId: `${richMenuIdUpdate || richMenuId}`,
            },
            authenToken: token,
          })) || {};

        if (entries?.isAliasExist) {
          errors.aliasId = getTranslateMessage(translations.messageError.nameExisted.message);
        }
      }
    }
  }

  // Validate layout
  if (cells.length > AREA_CELL_MAX) {
    errors.cells = getTranslateMessage(translations.areaLayout.maxAreaError, '', { max: AREA_CELL_MAX });
  }

  // Validate actions
  cells.forEach(cell => {
    const { action } = cell;
    const cellErrors: Record<string, any> = {};

    /* If has any cell has action type 'richmenuswitch' then remove error */
    if (action?.type === 'richmenuswitch' && has(mainErrors.global, 'richmenuswitch')) {
      delete mainErrors.global.richmenuswitch;
    }

    if (action) {
      switch (action.type) {
        case 'datetimepicker':
        case 'postback':
        case 'richmenuswitch': {
          const { data } = action;

          // Check Data Field is Empty
          if (!data) {
            cellErrors.data = getTranslateMessage(translations.messageError.fieldEmpty.message);
          }

          // Check Data Field richMenuAliasId is empty
          if (action.type === 'richmenuswitch') {
            if (!action.richMenuAliasId) {
              cellErrors.richMenuAliasId = getTranslateMessage(translations.messageError.fieldEmpty.message);
            }
          }

          break;
        }
        case 'uri': {
          const { uri } = action;

          if (!uri) {
            cellErrors.uri = getTranslateMessage(translations.messageError.fieldEmpty.message);
          }
          break;
        }
        case 'message': {
          const { text } = action;

          if (!text) {
            cellErrors.text = getTranslateMessage(translations.messageError.fieldEmpty.message);
          }
          break;
        }
        default:
          break;
      }
    }

    if (!isEmpty(cellErrors) && Array.isArray(errors.cells)) {
      errors.cells.push(cellErrors);
    }
  });

  if (Object.values(errors).filter(e => !isEmpty(e)).length) {
    return errors;
  }

  return {};
};

/**
 * Validates a Rich Image Map object.
 *
 * @param {Object} params - Parameters for validation.
 * @param {RichMenu} params.richImageMap - The Rich Image Map object to validate.
 * @returns {Object} Validation errors for the Rich Image Map.
 */
const validateRichImageMap = ({ richImageMap }: { richImageMap: RichMenu }) => {
  const { cells } = richImageMap;
  const errors: Record<string, any> = {
    cells: [],
  };

  if (cells.length > AREA_CELL_MAX_IMAGE_MAP) {
    errors.cells = getTranslateMessage(translations.areaLayout.maxAreaError, '', { max: AREA_CELL_MAX_IMAGE_MAP });
  }

  const emptyErrorMessage = getTranslateMessage(translations.messageError.fieldEmpty.message);

  // Validate actions
  cells.forEach(cell => {
    const { action } = cell;
    const cellErrors: Record<string, any> = {};

    if (action) {
      switch (action.type) {
        case 'url': {
          const { url } = action;

          if (!url) {
            cellErrors.url = emptyErrorMessage;
          }

          break;
        }
        case 'video': {
          const { previewImage, videoUrl, isAddLabel } = action;

          if (!previewImage) {
            cellErrors.previewImage = emptyErrorMessage;
          }
          if (!videoUrl) {
            cellErrors.videoUrl = emptyErrorMessage;
          }

          if (isAddLabel) {
            if (!action.label) {
              cellErrors.label = emptyErrorMessage;
            }
            if (!action.externalUrl) {
              cellErrors.externalUrl = emptyErrorMessage;
            }
          }
          break;
        }
        case 'message': {
          const { text } = action;

          if (!text) {
            cellErrors.text = emptyErrorMessage;
          }
          break;
        }
        default:
          break;
      }
    }

    if (!isEmpty(cellErrors) && Array.isArray(errors.cells)) {
      errors.cells.push(cellErrors);
    }
  });

  if (Object.values(errors).filter(e => !isEmpty(e)).length) {
    return errors;
  }

  return {};
};

export const genAlertErrorMessages = (errors?: RichMenuErrors, workspaceType?: WorkspaceType) => {
  let messages: string[] = [];

  if (errors && !isEmpty(errors)) {
    let prefix = 'Template Name';
    if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
      prefix = 'Alternative Text';
    }
    if (errors.templateName) {
      messages.push(`${prefix}: ${errors.templateName}`);
    }

    if (!isEmpty(errors.richMenus)) {
      messages.push(getTranslateMessage(translations.messageError.blockError.message));
    }

    if (!isEmpty(errors.global)) {
      messages = messages.concat(Object.values(errors.global));
    }
  }

  return messages;
};
