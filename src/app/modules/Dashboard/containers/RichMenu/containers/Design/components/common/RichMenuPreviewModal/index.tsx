// Libraries
import React, { useState, useEffect } from 'react';
import { PreviewModal, PreviewModalProps, RichMenuMobileView, PreviewTabs } from '@antscorp/antsomi-ui';

// Types
import { RichMenu, TRichMenuMode } from 'richMenu/containers/Design/types';

// Styles
import { useDeepCompareEffect } from 'app/hooks';

interface RichMenuPreviewProps extends PreviewModalProps {
  richMenus: RichMenu[];
  mode: TRichMenuMode;
}

export const RichMenuPreviewModal: React.FC<RichMenuPreviewProps> = props => {
  const { richMenus, mode, ...restOfProps } = props;
  const {
    chatBar: { displayDefault },
  } = richMenus[0];

  // State
  const [state, setState] = useState({
    showRichMenu: displayDefault,
    showTypingChat: false,
    currentRichMenuIdx: 0,
  });

  // Variables
  const { showRichMenu, showTypingChat, currentRichMenuIdx } = state;
  const currentRichMenu = richMenus[currentRichMenuIdx];

  useEffect(() => {
    setState(prevState => ({ ...prevState, showRichMenu: displayDefault }));
  }, [displayDefault]);

  // Effects
  useDeepCompareEffect(() => {
    if (props.open) {
      const richMenuIdx = richMenus.findIndex(richMenu => richMenu.isDefault);

      if (richMenuIdx !== -1) {
        setState(prevState => ({ ...prevState, currentRichMenuIdx: richMenuIdx }));
      }
    }
  }, [props.open]);

  return (
    <PreviewModal destroyOnClose {...restOfProps}>
      {/* Show RichMenuTabs when mode is multiple */}
      {mode === 'multiple' && (
        <PreviewTabs
          className="!ants-mt-4 !ants-mx-auto"
          current={currentRichMenuIdx + 1}
          total={richMenus.length}
          onChange={current => {
            setState(prevState => ({ ...prevState, currentRichMenuIdx: current - 1 }));
          }}
        />
      )}

      <RichMenuMobileView
        className={`${mode === 'multiple' ? 'ants-mt-4' : 'ants-mt-8'}`}
        richMenu={currentRichMenu as any}
        isPreview={true}
      />
    </PreviewModal>
  );
};
