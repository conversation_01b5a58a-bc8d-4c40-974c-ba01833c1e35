// Libraries
import styled, { css } from 'styled-components';

export const StyledItemSelctor = styled.div<{ $isSelected?: boolean }>`
  position: absolute;
  --border-width: 3px;
  border: var(--border-width) solid var(--primary-color);
  width: fit-content;
  overflow: visible;

  ${props =>
    props.$isSelected &&
    css`
      z-index: 100;
    `}
`;

export const StyledSelectorTop = styled.div`
  height: 30px;
  min-width: 120px;
  position: absolute;
  top: 0;
  left: calc(-1 * var(--border-width));
  transform: translateY(-100%);
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: var(--primary-color);
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;

  i {
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    text-align: center;

    &:first-child {
      cursor: move;
    }
  }
`;
