// Libraries
import styled from 'styled-components';

// Types
import { RichMenu } from 'richMenu/containers/Design/types';

interface StyledRichMenuProps {
  $isCompact?: boolean;
  $gridTemplateRows?: RichMenu['gridTemplateRows'];
  $gridTemplateCols?: RichMenu['gridTemplateCols'];
}

export const StyledRichMenu = styled.div<StyledRichMenuProps>`
  display: grid;
  width: 100%;
  grid-gap: 1px;
  aspect-ratio: ${({ $isCompact }) => ($isCompact ? '3 / 1' : '3/ 2')};
  grid-template-columns: ${({ $gridTemplateCols }) => $gridTemplateCols?.map(col => col).join(' ')};
  grid-template-rows: ${({ $gridTemplateRows }) => $gridTemplateRows?.map(row => row).join(' ')};
`;

export const StyledCell = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  outline: 1px solid var(--primary-color);
  cursor: pointer;
`;
