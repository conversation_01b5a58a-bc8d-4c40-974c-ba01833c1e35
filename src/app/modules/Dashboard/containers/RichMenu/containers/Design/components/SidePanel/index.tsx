// Libraries
import React from 'react';
import { useSelector } from 'react-redux';

// Components
import { DesignSidePanel } from 'app/components/organisms/DesignSidePanel';
import { RichMenuSettings } from './RichMenuSettings';
import { Header } from './Header';
import { AreaActionSettings } from './AreaActionSettings';
import { CellImageSettings } from './CellImageSettings';

// Translations
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

// Selectors
import { selectGeneral, selectSidePanel } from '../../slice/selectors';

// Constants
import { BLOCKS, BLOCK_KEYS, WORKSPACE_TYPE } from '../../constants';

interface SidePanelProps {}

export const SidePanel: React.FC<SidePanelProps> = () => {
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const { selectedBlockType } = useSelector(selectSidePanel);
  const { workspaceType } = useSelector(selectGeneral);

  // Variables
  const blockMeta = BLOCKS[selectedBlockType || 'RICH_MENU'] || {};

  const renderBlockSettings = {
    [BLOCK_KEYS.RICH_MENU]: <RichMenuSettings />,
    [BLOCK_KEYS.CELL_ACTION]: <AreaActionSettings />,
    [BLOCK_KEYS.CELL_IMAGE]: <CellImageSettings />,
  };

  let title = blockMeta.label;
  let icon = blockMeta.icon;
  if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
    title = getTranslateMessage(translations.advancedImageMapSettings.title);
    icon = 'icon-ants-grid-view';
  }

  return (
    <DesignSidePanel isCollapsed={isCollapsed} toggleSidePanel={setIsCollapsed}>
      <Header icon={icon} title={title} />

      <div className="ants-h-full ants-overflow-auto">{renderBlockSettings[selectedBlockType || 'RICH_MENU']}</div>
    </DesignSidePanel>
  );
};
