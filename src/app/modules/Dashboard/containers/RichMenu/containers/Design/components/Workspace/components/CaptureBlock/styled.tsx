// Libraries
import styled, { css } from 'styled-components';

// Types
import type { WorkspaceDimensions } from '../../../../types';

export const CaptureWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.5);
  z-index: 999;
`;

export const GridLayoutContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  gap: 8px;
  padding: 8px;
  opacity: 0;
  user-select: none;
  pointer-events: none;
`;

export const GridWrapper = styled.div<{ $isAdvancedImageMap: boolean; $dimensions: WorkspaceDimensions }>`
  min-width: 336px;
  max-width: 336px;
  max-height: 224px;

  ${props =>
    props.$isAdvancedImageMap &&
    css`
      width: ${props.$dimensions.width}px !important;
      height: ${props.$dimensions.height}px !important;
      min-width: unset;
      max-width: unset;
      max-height: unset;
    `}
`;

export const RichMenuContainer = styled.div<{ height: number; aspectRatio: number }>`
  height: ${props => props.height}px;

  .grid-wrapper .layers .grid-container {
    aspect-ratio: ${props => props.aspectRatio} !important;
  }
`;
