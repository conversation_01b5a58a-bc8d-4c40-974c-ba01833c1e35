// Libraries
import { Tabs } from '@antscorp/antsomi-ui';
import React from 'react';

// Constants
import { SIDE_PANEL_TABS } from 'constants/variables';

// Components
import { Content } from './Content';
import { Advanced } from './Advanced';

interface CellImageSettingsProps {}

const { CONTENT, ADVANCED } = SIDE_PANEL_TABS;

export const CellImageSettings: React.FC<CellImageSettingsProps> = props => {
  const { ...restProps } = props;

  const tabs = [
    {
      key: CONTENT.name,
      label: CONTENT.label,
      children: <Content />,
    },
    {
      key: ADVANCED.name,
      label: ADVANCED.label,
      children: <Advanced />,
    },
  ];

  return <Tabs items={tabs} destroyInactiveTabPane />;
};
