// Libraries
import React from 'react';
import { ImageSetting, TImageSettingValues } from '../../components/ImageSetting';

// Types
import { RichMenu, TImageSettings, TImageStyles } from 'richMenu/containers/Design/types';
import { tryCatchWrapper } from 'app/utils';
import { Flex, Radio, SettingWrapper } from '@antscorp/antsomi-ui';

// NOTE: Todo move to antsomi ui
// Components
import { BoxShadowSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BoxShadowSetting';
import { BorderSettingPopover } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BorderSetting';
import { RoundedCornersSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/RoundedCornersSetting';
import {
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';
import { SpacingSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/SpacingSetting';

// Locales
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import { UPLOAD_MODE } from '../../../../constants';
import { ImageAdvancedSetting } from '../../components/ImageAdvancedSetting';

interface MenuImageProps {
  richMenu: RichMenu;
  onChange: (data: Partial<RichMenu>) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/RichMenuSettings/MenuImage/index.tsx';

export const MenuImage: React.FC<MenuImageProps> = props => {
  const { richMenu, onChange } = props;
  const { t } = useTranslation();

  const { uploadMode } = richMenu;
  const { imageUrl, imageSettings, imageStyles } = richMenu.image || {};
  const { objectFit, objectPosition } = imageStyles || {};

  const handleChangeImageSettings = (values: TImageSettingValues) => {
    const { imageUrl, objectFit, objectPosition } = values;

    onChange({
      image: {
        ...richMenu.image,
        imageUrl,
        imageStyles: {
          ...imageStyles,
          objectFit: `${objectFit}`,
          objectPosition: `${objectPosition}`,
        },
      },
    });
  };

  const handleChangeStylesAndSettings = (imageSettings: TImageSettings, imageStyles: TImageStyles) => {
    onChange({
      image: {
        ...richMenu.image,
        imageSettings,
        imageStyles,
      },
    });
  };

  return (
    <Flex vertical gap={20}>
      <SettingWrapper label={t(translations.menuImage.uploadMode)}>
        <Radio.Group value={richMenu.uploadMode} onChange={e => onChange({ uploadMode: e.target.value })}>
          {Object.values(UPLOAD_MODE).map(uploadMode => (
            <Radio key={uploadMode.value} value={uploadMode.value}>
              {uploadMode.label}
            </Radio>
          ))}
        </Radio.Group>
      </SettingWrapper>

      {uploadMode === 'single' && (
        <>
          <ImageSetting
            values={{ imageUrl, objectFit, objectPosition }}
            onChange={values => tryCatchWrapper(handleChangeImageSettings, PATH)(values)}
          />

          <ImageAdvancedSetting
            styles={imageStyles}
            settings={imageSettings}
            onChange={handleChangeStylesAndSettings}
          />
        </>
      )}
    </Flex>
  );
};
