// Libraries
import { FC, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toPng } from 'html-to-image';

// Components
import { RichMenuBlock } from '@antscorp/antsomi-ui';
import { ProcessLoading } from 'app/components/molecules';

// Types
import { RichMenu } from '../../../../types';
import { TMessageEventPayload } from 'types';

// Styled
import { CaptureWrapper, GridLayoutContainer, GridWrapper, RichMenuContainer } from './styled';

// Services
import { uploadOriginFile } from 'app/services/MediaTemplateDesign/UploadFile';

// Selectors
import { selectGeneral, selectRichMenuTemplate, selectWorkspaceType } from '../../../../slice/selectors';
import { richMenuDesignActions } from '../../../../slice';

// Constants
import { LAYOUT_SIZE_GRID, WORKSPACE_TYPE } from '../../../../constants';

// Utils
import { random } from 'app/utils';
import { urlToFile } from './utils';
import { getImageMapWorkspaceDimensions, getSizeImageLayout } from '../../../../utils';
import { POST_MESSAGE_EVENT } from 'constants/variables';

export interface CaptureBlockProps {
  isLoading: boolean;
  onlyCapture?: boolean;
  callback?: (type: 'FINISH_CAPTURE') => void;
}

export const EXCLUSION_CLASSES = [
  'cell-add-container',
  'vertical',
  'horizontal',
  'resize-top',
  'resize-right',
  'resize-bottom',
  'resize-left',
  'cell-border',
  'video-capture-action-ignore',
];

export const CaptureBlock: FC<CaptureBlockProps> = props => {
  const { isLoading, onlyCapture, callback } = props;

  // Select
  const richMenuTemplate = useSelector(selectRichMenuTemplate);
  const workspaceType = useSelector(selectWorkspaceType);
  const general = useSelector(selectGeneral);

  const { updateRichMenuTemplate, updateStatusSaving } = richMenuDesignActions;

  const dispatch = useDispatch();

  const filterCaptureElement = (node: HTMLElement) => {
    return !EXCLUSION_CLASSES.some(className => node.classList && node.classList.contains(className));
  };

  const handleCaptureBlocks = useCallback(async () => {
    const { richMenus: richMenuList } = richMenuTemplate;

    const menuListElements = document.querySelectorAll('.grid-capture-container .grid-wrapper');

    if (menuListElements.length > 0) {
      const menuAllPromises = Array.from(menuListElements).map(async (menu, idx) => {
        const { layoutType } = richMenuList[idx];
        const isAdvancedImageMap = workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP;

        let dimensions = getSizeImageLayout(layoutType, LAYOUT_SIZE_GRID);
        if (isAdvancedImageMap) {
          dimensions = getImageMapWorkspaceDimensions(layoutType, general.dimensions);
        }

        const fontEmbedCss = '1';

        return toPng(menu as HTMLElement, {
          canvasWidth: dimensions.width,
          canvasHeight: dimensions.height,
          quality: 0.5,
          pixelRatio: 1,
          skipAutoScale: true,
          fontEmbedCSS: fontEmbedCss,
          // preferredFontFormat: 'truetype',
          // skipFonts: true,
          filter: filterCaptureElement,
        });
      });

      const listBase64 = await Promise.all(menuAllPromises);
      const listFiles = await Promise.all(
        listBase64.map(base64Item => {
          const fileName = random(10);
          return urlToFile(base64Item, `${fileName}.png`, 'image/png');
        }),
      );
      const fileResult = await uploadOriginFile(listFiles);

      const result = fileResult.map(fileInfo => fileInfo.url || '');
      if (result) {
        const newRichMenuList = richMenuList.map((richMenu, index) => ({
          ...richMenu,
          uploadImageUrl: result[index] || '',
        }));

        dispatch(updateRichMenuTemplate({ richMenus: newRichMenuList }));

        if (!onlyCapture) {
          // Post Message for saving
          window.postMessage(
            {
              actionType: POST_MESSAGE_EVENT.FINISH_CAPTURE,
              data: { ...richMenuTemplate, richMenus: newRichMenuList },
            },
            '*',
          );
        }
      }
    }

    if (!onlyCapture) {
      dispatch(updateStatusSaving({ isSaving: false }));
    }

    if (typeof callback === 'function') {
      callback('FINISH_CAPTURE');
    }
  }, [
    callback,
    dispatch,
    general.dimensions,
    onlyCapture,
    richMenuTemplate,
    updateRichMenuTemplate,
    updateStatusSaving,
    workspaceType,
  ]);

  const handleStartCapture = useCallback(
    (event: MessageEvent<TMessageEventPayload<null>>) => {
      if (event.data && event.data.actionType === POST_MESSAGE_EVENT.STARTING_SAVE_TEMPLATE) {
        handleCaptureBlocks();
      }
    },
    [handleCaptureBlocks],
  );

  useEffect(() => {
    window.addEventListener('message', handleStartCapture);

    return () => {
      window.removeEventListener('message', handleStartCapture);
    };
  }, [handleStartCapture]);

  const renderListCapture = (menuList: RichMenu[]) => {
    const content = menuList.map(menuItem => {
      const { id } = menuItem;

      let content = <RichMenuBlock isPreview richMenu={menuItem} layerActive="all" />;

      if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
        content = (
          <RichMenuContainer
            height={general.dimensions.height}
            aspectRatio={general.dimensions.width / general.dimensions.height}
            style={{ width: general.dimensions.width }}
          >
            <RichMenuBlock isPreview richMenu={menuItem} layerActive="all" />
          </RichMenuContainer>
        );
      }

      return (
        <GridWrapper
          key={id}
          id={`${id}`}
          className="grid-capture-container"
          $isAdvancedImageMap={workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP}
          $dimensions={general.dimensions}
        >
          {content}
        </GridWrapper>
      );
    });

    return <GridLayoutContainer>{content}</GridLayoutContainer>;
  };

  return (
    <CaptureWrapper>
      {renderListCapture(richMenuTemplate.richMenus)}
      <ProcessLoading visible={isLoading} overTimeSecond={3} />
    </CaptureWrapper>
  );
};
