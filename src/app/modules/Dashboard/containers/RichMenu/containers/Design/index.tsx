// Libraries
import React from 'react';

// Component
import { EditableName } from 'app/components/molecules';
import { Layout, LayoutV2 } from 'app/components/templates';
import { RichMenuEditor } from './RichMenuEditor';

interface RichMenuDesignProps {}

export const RichMenuDesign: React.FC<RichMenuDesignProps> = () => {
  const saveTemplateName = () => {};

  return (
    // <Layout
    <LayoutV2
    // header={{
    //   breadcrumb: {
    //     title: <EditableName name={''} onBlur={saveTemplateName} isLoading={false} />,
    //   },
    // }}
    >
      <RichMenuEditor />
    </LayoutV2>
  );
};
