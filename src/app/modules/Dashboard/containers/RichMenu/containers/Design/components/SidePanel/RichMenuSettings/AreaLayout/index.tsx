// Libraries
import React from 'react';
import { Col, Divider, Flex, InputNumber, Row, SettingWrapper, Typography } from '@antscorp/antsomi-ui';
import { omit, round, pick } from 'lodash';
import { useTranslation } from 'react-i18next';

// Constants
import {
  AREA_CELL_MAX,
  AREA_CELL_MAX_IMAGE_MAP,
  AREA_LAYOUTS,
  AREA_LAYOUTS_ADV_IMAGE_MAP,
  WORKSPACE_TYPE,
} from '../../../../constants';
import { translations } from 'locales/translations';

// Components
import { LayoutItem } from './LayoutItem';

// Types
import { RichMenu, WorkspaceType } from 'richMenu/containers/Design/types';

// Utils
import { tryCatchWrapper } from 'app/utils';
import { generateEvenCells, getAreaLayoutBySettings } from 'richMenu/containers/Design/utils';

interface AreaLayoutProps {
  workspaceType: WorkspaceType;
  richMenu: RichMenu;
  onChange: (data: Partial<RichMenu>) => void;
}

const { Text } = Typography;

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/RichMenuSettings/AreaLayout/index.tsx';

export const AreaLayout: React.FC<AreaLayoutProps> = props => {
  const { workspaceType, richMenu, onChange } = props;

  const { t } = useTranslation();

  // Variables
  const { rows, cols, areaLayoutId, layoutType, cells } = richMenu;

  let isShowMaxError = cells.length > AREA_CELL_MAX;
  let areaLayouts = Object.values(AREA_LAYOUTS);
  if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
    areaLayouts = Object.values(AREA_LAYOUTS_ADV_IMAGE_MAP);
    isShowMaxError = cells.length > AREA_CELL_MAX_IMAGE_MAP;
  }

  // Handlers
  const onHandleChangesRowCols = (rows: number, cols: number, type?: 'rows' | 'cols') => {
    /**
     * Even out the layout when changing rows or cols
     */
    const gridTemplateRows = new Array(rows).fill(round(100 / rows, 2) + '%');
    const gridTemplateCols = new Array(cols).fill(round(100 / cols, 2) + '%');
    const draftLayoutType = layoutType === 'compact' && type === 'rows' ? 'large' : layoutType; // Need change layout type to large when changing rows to comparison

    const areaLayoutId = getAreaLayoutBySettings(
      {
        rows,
        cols,
        gridTemplateRows,
        gridTemplateCols,
        layoutType: draftLayoutType,
        cells: generateEvenCells(rows, cols, cells),
      },
      workspaceType,
    )?.key;

    onChange({
      rows,
      cols,
      layoutType: draftLayoutType,
      gridTemplateRows,
      gridTemplateCols,
      cells: generateEvenCells(rows, cols, cells),
      areaLayoutId: areaLayoutId,
    });
  };

  return (
    <>
      <Row gutter={[15, 15]} align="middle">
        {areaLayouts.map(areaLayout => {
          const isSelected = areaLayout.key === areaLayoutId;

          return (
            <Col
              key={areaLayout.key}
              span={6}
              onClick={() =>
                tryCatchWrapper(
                  onChange,
                  PATH,
                )({
                  ...omit(areaLayout, ['key', 'name']),
                  areaLayoutId: areaLayout.key,
                  cells: areaLayout.cells?.map((areaCell, index) => ({
                    ...areaCell,
                    ...pick(cells[index], [
                      'action',
                      'image',
                      'outerContainerStyles',
                      'areaStyles',
                      'areaStylesSettings',
                    ]),
                  })),
                })
              }
            >
              <LayoutItem workspaceType={workspaceType} areaLayout={areaLayout} selected={isSelected} />
            </Col>
          );
        })}
      </Row>

      <Divider dashed />

      <Flex vertical gap={10}>
        <Text strong>{t(translations.areaLayout.customizedLayout)}</Text>
        <SettingWrapper label={t(translations.areaLayout.numOfColumns)}>
          <InputNumber
            min={1}
            max={50}
            value={cols}
            onChange={value => tryCatchWrapper(onHandleChangesRowCols, PATH)(rows, value as number)}
          />
        </SettingWrapper>
        <SettingWrapper label={t(translations.areaLayout.numOfRows)}>
          <InputNumber
            min={1}
            max={50}
            value={rows}
            onChange={value => tryCatchWrapper(onHandleChangesRowCols, PATH)(value as number, cols, 'rows')}
          />
        </SettingWrapper>
        {isShowMaxError && (
          <Text type="danger">
            {t(translations.areaLayout.maxAreaError, {
              max: workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP ? AREA_CELL_MAX_IMAGE_MAP : AREA_CELL_MAX,
            })}
          </Text>
        )}
      </Flex>
    </>
  );
};
