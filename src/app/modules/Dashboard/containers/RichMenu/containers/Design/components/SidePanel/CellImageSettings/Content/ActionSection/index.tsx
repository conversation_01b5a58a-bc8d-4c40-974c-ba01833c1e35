// Libraries
import React from 'react';
import { useSelector } from 'react-redux';

// Types
import { TAction, TActionImageMap, TChildrenCell } from 'richMenu/containers/Design/types';
import { WORKSPACE_TYPE } from '../../../../../constants';

// Selectors
import { selectWorkspaceType } from '../../../../../slice/selectors';

// Components
import { ActionSetting } from '../../../components/ActionSetting';
import { ActionSettingV2 } from '../../../components/ActionSettingV2';

interface ImageSectionProps {
  cell: TChildrenCell;
  onChange: (data: Partial<TChildrenCell>) => void;
}

export const ActionSection: React.FC<ImageSectionProps> = props => {
  const { cell, onChange } = props;

  const workspaceType = useSelector(selectWorkspaceType);

  const handleChangeActionSettings = (action: TAction | TActionImageMap) => {
    onChange({ action });
  };

  if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
    return <ActionSettingV2 action={cell.action as TActionImageMap} onChange={handleChangeActionSettings} />;
  }

  return <ActionSetting action={cell.action as TAction} onChange={handleChangeActionSettings} />;
};
