// Libraries
import { Flex, Icon } from '@antscorp/antsomi-ui';
import React, { useEffect, useState } from 'react';
import cn from 'classnames';

// Styled
import { NavigationButton, StyledRichMenuTabs } from './styled';
import { tryCatchWrapper } from 'app/utils';

interface RichMenuTabsProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  total?: number;
  current?: number;
  onChange?: (current: number) => void;
}

type TState = {
  disablePrev: boolean;
  disableNext: boolean;
};

const defaultProps: RichMenuTabsProps = {
  total: 20,
  current: 1,
};

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/common/RichMenuPreviewModal/RichMenuTabs/index.tsx';

export const RichMenuTabs: React.FC<RichMenuTabsProps> = props => {
  const { total = 20, current = 1, onChange, ...restProps } = props;
  const tabsRef = React.useRef<HTMLDivElement>(null);

  // State
  const [state, setState] = useState<TState>({
    disablePrev: true,
    disableNext: true,
  });

  // Variables
  const { disableNext, disablePrev } = state;

  // Check disable next navigation base length of tabs
  useEffect(() => {
    checkScroll();
  }, []);

  // Handlers
  const checkScroll = () => {
    if (tabsRef.current) {
      const { scrollLeft, clientWidth, scrollWidth } = tabsRef.current;

      setState(prevState => ({ ...prevState, disableNext: scrollLeft + clientWidth === scrollWidth ? true : false }));
      setState(prevState => ({ ...prevState, disablePrev: scrollLeft === 0 ? true : false }));
    }
  };

  const onClickNavigation = tryCatchWrapper((isPrev: boolean) => {
    if (tabsRef.current) {
      const { scrollLeft } = tabsRef.current;

      tabsRef.current.scrollTo({ left: scrollLeft + (isPrev ? -40 : 40), behavior: 'smooth' });

      checkScroll();
    }
  }, PATH);

  return (
    <StyledRichMenuTabs {...restProps}>
      <NavigationButton
        className={cn('navigation--previous', {
          'navigation--disabled': disablePrev,
        })}
        onClick={() => onClickNavigation(true)}
      >
        <Icon type="icon-ants-angle-left" />
      </NavigationButton>
      <Flex ref={tabsRef} className="tabs scrollbar-hidden" align="center" onScroll={() => checkScroll()}>
        {Array.from({ length: total }).map((_, index) => (
          <Flex
            key={index}
            align="center"
            justify="center"
            className={`tabs__item ${current === index + 1 ? 'tabs__item--active' : ''}`}
            onClick={() => onChange && onChange(index + 1)}
          >
            <Flex align="center" justify="center" className="tabs__label">
              {index + 1}
            </Flex>
          </Flex>
        ))}
      </Flex>
      <NavigationButton
        className={cn('navigation--next', {
          'navigation--disabled': disableNext,
        })}
        onClick={() => onClickNavigation(false)}
      >
        <Icon type="icon-ants-angle-right" />
      </NavigationButton>
    </StyledRichMenuTabs>
  );
};

RichMenuTabs.defaultProps = defaultProps;
