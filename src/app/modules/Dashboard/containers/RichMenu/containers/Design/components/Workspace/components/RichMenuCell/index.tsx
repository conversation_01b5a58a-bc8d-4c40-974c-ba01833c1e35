// Libraries
import { CSSProperties, FC } from 'react';
import { isEmpty } from 'lodash';

// Components
import { Icon } from 'app/components/atoms';

// Styled
import { AddContainer, AddLabel, CellContainer, CellImage } from './styled';

// Types
import { TChildrenCell } from '../../../../types';

// Utils
import { tryCatchWrapper } from 'app/utils';

// Translations
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Constants
import { EXCLUSION_CLASSES } from '../CaptureBlock';

interface RichMenuCellProps {
  cell: TChildrenCell;
  context: number;
  isSingleMode: boolean;
  onClick: (index: number) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/Workspace/components/RichMenuCell';

export const RichMenuCell: FC<RichMenuCellProps> = props => {
  const { cell, context, isSingleMode, onClick } = props;

  const { areaStyles } = cell;

  // Translations
  const { t } = useTranslation();

  const renderCellContent = (isShow: boolean, cellInfo: TChildrenCell) => {
    if (!isShow) return null;

    const { image } = cellInfo || {};
    const { imageUrl, imageStyles } = image || {};
    const { objectPosition, objectFit, ...restImageStyles } = imageStyles || {};
    const isBlank = isEmpty(imageUrl);

    if (isBlank)
      return (
        <AddContainer className={EXCLUSION_CLASSES[0]}>
          <Icon type="icon-ants-plus-circle" size={20} style={{ color: 'rgba(0, 95, 184, 1)' }} />
          <AddLabel>{t(translations.cellAction.title)}</AddLabel>
        </AddContainer>
      );

    return (
      <CellImage
        src={imageUrl}
        imagePosition={objectPosition}
        imageSize={objectFit}
        style={restImageStyles as CSSProperties}
      />
    );
  };

  return (
    <CellContainer
      key={context}
      style={!isSingleMode ? areaStyles : {}}
      onClick={e => {
        e.stopPropagation();
        tryCatchWrapper(onClick, PATH)(context);
      }}
    >
      {renderCellContent(!isSingleMode, cell)}
    </CellContainer>
  );
};
