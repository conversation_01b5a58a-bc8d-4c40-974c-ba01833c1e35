import { Flex } from '@antscorp/antsomi-ui';
import styled, { css } from 'styled-components';

interface ChatBarWrapperProps {
  $showRichMenu?: boolean;
  $showTypingChat?: boolean;
  $isSingleMode?: boolean;
}

export const ChatBarWrapper = styled(Flex)<ChatBarWrapperProps>`
  position: relative;
  height: ${({ $isSingleMode }) => ($isSingleMode ? '84px' : '40px')};
  padding: 10px 20px;
  background-color: #fff;
  z-index: 10;

  .icon-caret {
    transition: all 300ms;
    ${({ $showRichMenu }) => css`
      transform: ${$showRichMenu ? 'rotate(0deg)' : 'rotate(180deg)'};
    `}
  }
`;

export const ChatBarMenuBox = styled(Flex)`
  position: absolute;
  transform: translateX(-50%);
  top: 10px;
  left: 50%;
  cursor: pointer;
`;

ChatBarMenuBox.defaultProps = {
  align: 'center',
  gap: 20,
};
