export function urlToFile(url: string, filename: string, mimeType: string): Promise<File> {
  if (url.startsWith('data:')) {
    let arr = url.split(',') as any,
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[arr.length - 1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    let file = new File([u8arr], filename, { type: mime || mimeType });
    return Promise.resolve(file);
  }

  return fetch(url)
    .then(res => res.arrayBuffer())
    .then(buf => new File([buf], filename, { type: mimeType }));
}
