// Libraries
import { Flex, Select } from '@antscorp/antsomi-ui';
import React from 'react';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Components
import { UploadImage } from 'app/components/molecules';

// Constants
import { IMAGE_SIZE, POSITION } from 'constants/variables';

// Utils
import { tryCatchWrapper } from 'app/utils';

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/components/ImageSetting/index.tsx';

export type TImageSettingValues = {
  imageUrl: string;
  objectPosition?: string;
  objectFit?: string;
};

interface ImageSettingProps {
  values: TImageSettingValues;
  onChange: (value: TImageSettingValues) => void;
}

export const ImageSetting: React.FC<ImageSettingProps> = props => {
  const { values, onChange } = props;
  const { imageUrl, objectPosition, objectFit } = values;

  const { t } = useTranslation();

  const onChangeSettings = (data: Partial<TImageSettingValues>) => {
    onChange({
      ...values,
      ...data,
    });
  };

  return (
    <Flex vertical gap={20}>
      <UploadImage
        title={t(translations.backgroundImage.title)}
        selectedImage={{
          url: imageUrl,
        }}
        onChangeImage={image =>
          tryCatchWrapper(
            onChangeSettings,
            PATH,
          )({
            imageUrl: image?.url,
          })
        }
        onRemoveImage={() =>
          tryCatchWrapper(
            onChangeSettings,
            PATH,
          )({
            imageUrl: '',
          })
        }
      />

      <Select
        label={t(translations.imagePosition.title)}
        value={objectPosition || 'center center'}
        options={Object.values(POSITION)}
        onChange={value => tryCatchWrapper(onChangeSettings, PATH)({ objectPosition: value })}
      />
      <Select
        label={t(translations.imageSize.title)}
        value={objectFit || 'unset'}
        options={Object.values(IMAGE_SIZE)}
        onChange={value => tryCatchWrapper(onChangeSettings, PATH)({ objectFit: value })}
      />
    </Flex>
  );
};
