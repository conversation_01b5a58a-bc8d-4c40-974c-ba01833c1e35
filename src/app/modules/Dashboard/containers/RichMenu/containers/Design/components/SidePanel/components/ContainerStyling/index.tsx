// Libraries
import React from 'react';
import { Flex } from '@antscorp/antsomi-ui';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// NOTE: Move to global component
import BackgroundSetting from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BackgroundSetting';
import {
  getBackgroundSettings,
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getPositionSettings,
  getPositionStyles,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Types
import { TAreaStyles, TAreaStylesSettings } from 'richMenu/containers/Design/types';
import { BoxShadowSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BoxShadowSetting';
import { BorderSettingPopover } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BorderSetting';
import { RoundedCornersSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/RoundedCornersSetting';
import { SpacingSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/SpacingSetting';
import { tryCatchWrapper } from 'app/utils';
import { PositionSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/PositionSetting';

interface ContainerStylingProps {
  styles: TAreaStyles;
  stylesSettings: TAreaStylesSettings;
  onChange: (styleSettings: TAreaStylesSettings, styles: TAreaStyles) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/components/ContainerStyling/index.tsx';

export const ContainerStyling: React.FC<ContainerStylingProps> = props => {
  const { styles, stylesSettings, onChange } = props;
  const { t } = useTranslation();

  // Handlers
  const onUpdateBlockSettings = tryCatchWrapper((updatedSettings, updatedStyles) => {
    onChange(
      {
        ...stylesSettings,
        ...updatedSettings,
      },
      {
        ...styles,
        ...updatedStyles,
      },
    );
  }, PATH);

  return (
    <Flex vertical gap={20}>
      <BackgroundSetting
        label={t(translations.style.title)}
        settings={getBackgroundSettings(stylesSettings as any)}
        styles={{
          background: styles.background,
        }}
        onChange={onUpdateBlockSettings}
      />

      <BoxShadowSetting settings={getBoxShadowSettings(stylesSettings as any)} onChange={onUpdateBlockSettings} />
      <BorderSettingPopover
        settings={getBorderSettings(stylesSettings as any)}
        styles={getBorderStyles(styles)}
        onChange={onUpdateBlockSettings}
      />
      <RoundedCornersSetting
        settings={getRoundedCornersSettings(stylesSettings as any)}
        styles={getRoundedCornersStyles(styles)}
        onChange={onUpdateBlockSettings}
      />
      <SpacingSetting
        settings={getSpacingSettings(stylesSettings as any)}
        styles={getSpacingStyles(styles)}
        onChange={onUpdateBlockSettings}
      />
      <PositionSetting
        settings={getPositionSettings(stylesSettings as any)}
        styles={getPositionStyles(styles)}
        onChange={onUpdateBlockSettings}
      />
    </Flex>
  );
};
