// Libraries
import styled, { css } from 'styled-components';
import tw from 'twin.macro';

// Components
import { Flex } from '@antscorp/antsomi-ui';

export const StyledAddNewMenuBtn = styled(Flex)<{ $isCollapsed: boolean }>`
  ${tw`ants-text-primary ants-text-xs ants-font-bold ants-px-5px ants-cursor-pointer`}

  ${({ $isCollapsed }) =>
    $isCollapsed &&
    css`
      justify-content: center;
    `}
`;
