// Libraries
import dayjs from 'dayjs';
import { PayloadAction } from '@reduxjs/toolkit';
import { cloneDeep, has, get, set } from 'lodash';

// Utils
import { useInjectReducer, useInjectSaga } from 'utils/redux-injectors';
import { createSlice } from 'utils/@reduxjs/toolkit';
import { random } from 'app/utils';

// Libs
import undoable from 'app/libs/redux-undo/reducer';

// Saga
import { richMenuDesignSaga } from './saga';

// Types
import {
  DuplicateRichMenuPayload,
  RemoveRichMenuPayload,
  ResetLayoutPayload,
  RichMenuDesignPayloadData,
  RichMenuDesignState,
  UpdateModePayload,
  UpdateModeSelectedRichMenuPayload,
} from './types';
import { ExternalConfig, General, PanelActiveType, RichMenuTemplate, TChildrenCell } from '../types';

// Constants
import {
  BLOCK_KEYS,
  RICH_MENU_TEMPLATE_DEFAULT,
  RICH_MENU_DEFAULT,
  AREA_LAYOUTS,
  WORKSPACE_TYPE,
  ADV_IMAGE_MAP_DEFAULT,
  SIDE_PANEL_COLLAPSE_KEYS,
  WORKSPACE_DIMENSIONS,
} from '../constants';
import { DATE_TIME_FORMAT } from 'constants/datetime';
import { MENU_CONTAINER_WIDTH } from '../components/common/MultipleMenusContainer/styled';

export const initialRichMenu: RichMenuDesignState = {
  externalConfig: {
    token: '',
    templateNameList: [],
  },
  general: {
    dimensions: WORKSPACE_DIMENSIONS[WORKSPACE_TYPE.RICH_MENU],
    workspaceType: WORKSPACE_TYPE.RICH_MENU,
    isEmbed: false,
    isShowError: false,
    panelActiveKey: SIDE_PANEL_COLLAPSE_KEYS.AREA_LAYOUT,
    errors: {
      global: {},
    },
  },
  sidePanel: {
    isSaving: false,
    selectedRichMenuId: RICH_MENU_TEMPLATE_DEFAULT.richMenus[0].id,
    selectedCellIdx: -1,
    selectedBlockType: BLOCK_KEYS.RICH_MENU,
  },
  richMenuTemplate: RICH_MENU_TEMPLATE_DEFAULT,
};

const slice = createSlice({
  name: 'richMenuDesign',
  initialState: initialRichMenu,
  reducers: {
    resetRichMenuDesign(state) {
      state.sidePanel = initialRichMenu.sidePanel;
      state.richMenuTemplate = initialRichMenu.richMenuTemplate;
    },
    switchToAdvancedImageMap(state) {
      state.general.workspaceType = WORKSPACE_TYPE.ADVANCED_IMAGE_MAP;
      state.general.dimensions = WORKSPACE_DIMENSIONS[WORKSPACE_TYPE.ADVANCED_IMAGE_MAP];
      state.richMenuTemplate.name = '';
      state.richMenuTemplate.richMenus = [ADV_IMAGE_MAP_DEFAULT];
      state.richMenuTemplate.mode = 'single';
      state.sidePanel.selectedRichMenuId = ADV_IMAGE_MAP_DEFAULT.id;
    },
    updateStatusSaving(state, action: PayloadAction<{ isSaving: boolean }>) {
      const { isSaving } = action.payload;
      state.sidePanel.isSaving = isSaving;
    },
    updateMode(state, action: PayloadAction<UpdateModePayload>) {
      const { mode } = action.payload;

      state.sidePanel = initialRichMenu.sidePanel;
      state.richMenuTemplate = { ...initialRichMenu.richMenuTemplate, mode };
    },
    updateSidePanelKey(state, action: PayloadAction<{ panelKey: PanelActiveType }>) {
      const { panelKey } = action.payload;
      state.general.panelActiveKey = panelKey;
    },
    resetLayout(state, action: PayloadAction<ResetLayoutPayload>) {
      const { richMenuId } = action.payload;
      const { richMenus } = state.richMenuTemplate;

      const matchedRichMenuIdx = richMenus.findIndex(richMenu => richMenu.id === richMenuId);

      if (matchedRichMenuIdx >= 0) {
        const matchedRichMenu = richMenus[matchedRichMenuIdx];
        const { areaLayoutId, cells } = matchedRichMenu;

        const areaLayout = AREA_LAYOUTS[areaLayoutId || 'LAYOUT_1'];

        richMenus[matchedRichMenuIdx] = {
          ...matchedRichMenu,
          ...areaLayout,
          cells: areaLayout.cells.map((cell, idx) => ({
            ...cells[idx],
            ...cell,
            sizeX: undefined,
            sizeY: undefined,
            transformX: undefined,
            transformY: undefined,
          })),
        };
      }
    },
    setRichMenuTemplate(state, action: PayloadAction<RichMenuTemplate>) {
      state.richMenuTemplate = action.payload;
      const { richMenus } = action.payload;

      if (Array.isArray(richMenus) && richMenus.length) {
        state.sidePanel.selectedRichMenuId = richMenus[0].id;
      }
    },
    setExternalConfig(state, action: PayloadAction<Partial<ExternalConfig>>) {
      state.externalConfig = { ...state.externalConfig, ...action.payload };
    },
    setGeneral(state, action: PayloadAction<Partial<General>>) {
      state.general = { ...state.general, ...action.payload };
    },
    updateRichMenuTemplateName(state, action: PayloadAction<string>) {
      const { mode, richMenus } = state.richMenuTemplate;

      // If mode equal single then update rich menu name when rich menu template change
      if (mode === 'single' && richMenus[0]) {
        richMenus[0].menuName = action.payload;
      }

      state.richMenuTemplate.name = action.payload;
    },
    /* Handle Rich Menu */
    updateRichMenuSettings(state, action: PayloadAction<RichMenuDesignPayloadData>) {
      const { id, data } = action.payload;
      const { richMenus } = state.richMenuTemplate;

      const matchedIdx = richMenus.findIndex(richMenu => richMenu.id === id);

      if (matchedIdx !== -1) {
        // Handle Update is Default for richMenu
        if (data.isDefault != null && data.isDefault !== richMenus[matchedIdx].isDefault) {
          const { isDefault } = data;
          // Turn off default for all current rich menus
          richMenus.forEach(richMenu => {
            richMenu.isDefault = false;
          });

          if (!isDefault) {
            // If the current rich menu is the turn off default, set the next one as default
            const nextRichMenu = richMenus[matchedIdx + 1] || richMenus[0];

            nextRichMenu.isDefault = true;
          }
        }

        richMenus[matchedIdx] = { ...richMenus[matchedIdx], ...data };
      }
    },
    updateSidePanel(state, action: PayloadAction<Partial<RichMenuDesignState['sidePanel']>>) {
      state.sidePanel = { ...state.sidePanel, ...action.payload };
    },
    updateSelectedRichMenuCell(state, action: PayloadAction<Partial<TChildrenCell>>) {
      const { selectedRichMenuId, selectedCellIdx } = state.sidePanel;
      const { richMenus } = state.richMenuTemplate;

      // Do no thing if no rich menu is selected and no cell is selected
      if (!selectedRichMenuId && selectedCellIdx !== -1) {
        return;
      }

      const matchedIdx = richMenus.findIndex(richMenu => richMenu.id === selectedRichMenuId);

      if (matchedIdx !== -1) {
        const selectedCell = richMenus[matchedIdx].cells[selectedCellIdx];

        if (
          action.payload.action?.type &&
          ['postback', 'richmenuswitch'].includes(action.payload.action?.type) &&
          !has(action, 'payload.action.data') &&
          has(selectedCell, 'action.data')
        )
          return;

        richMenus[matchedIdx].cells[selectedCellIdx] = {
          ...selectedCell,
          ...action.payload,
        };
      }
    },
    updateRichMenuTemplate(state, action: PayloadAction<Partial<RichMenuTemplate>>) {
      state.richMenuTemplate = { ...state.richMenuTemplate, ...action.payload };
    },
    updateSelectedRichMenuId(state, action: PayloadAction<UpdateModeSelectedRichMenuPayload>) {
      const { richMenuId } = action.payload;
      state.sidePanel.selectedRichMenuId = richMenuId;
    },
    addNewRichMenu(state) {
      const newRichMenu = cloneDeep({
        ...RICH_MENU_DEFAULT,
        id: random(10),
        menuName: `Untitled Menu ${dayjs().format(DATE_TIME_FORMAT.TITLE_DATE_TIME)}`,
        isDefault: false,
      });

      const mode = get(state, 'richMenuTemplate.mode');
      if (mode === 'multiple') {
        const { richMenus } = state.richMenuTemplate;
        const { position } = richMenus[richMenus.length - 1] || {};
        const { top = 50, left = 20 } = position || {};
        const offset = 50;

        set(newRichMenu, 'position', { left: left + offset + MENU_CONTAINER_WIDTH, top });
      }

      state.richMenuTemplate.richMenus.push(newRichMenu);
    },
    duplicateRichMenu(state, action: PayloadAction<DuplicateRichMenuPayload>) {
      const { richMenuId } = action.payload;
      const { richMenus, mode } = state.richMenuTemplate;
      const matchedIdx = richMenus.findIndex(richMenu => richMenu.id === richMenuId);

      if (matchedIdx !== -1) {
        const duplicatedRichMenu = cloneDeep({
          ...richMenus[matchedIdx],
          id: random(10),
          menuName: `${richMenus[matchedIdx].menuName}-copy`,
          aliasId: `${richMenus[matchedIdx].aliasId}-copy`,
          isDefault: false,
          x: 0,
          y: 0,
        });

        let duplicatedMenuNameIdx = 2;
        let duplicatedAliasIdx = 2;

        // Handle Duplicate name
        while (richMenus.some(richMenu => richMenu.menuName === duplicatedRichMenu.menuName)) {
          duplicatedRichMenu.menuName = `${richMenus[matchedIdx].menuName}-copy-${duplicatedMenuNameIdx}`;
          duplicatedMenuNameIdx += 1;
        }

        // Handle Duplicate alias
        while (richMenus.some(richMenu => richMenu.aliasId === duplicatedRichMenu.aliasId)) {
          duplicatedRichMenu.aliasId = `${richMenus[matchedIdx].aliasId}-copy-${duplicatedAliasIdx}`;
          duplicatedAliasIdx += 1;
        }

        if (mode === 'multiple') {
          const { position } = richMenus[richMenus.length - 1] || {};
          const { top = 50, left = 20 } = position || {};
          const offset = 50;

          set(duplicatedRichMenu, 'position', { left: left + offset + MENU_CONTAINER_WIDTH, top });
        }

        richMenus.splice(matchedIdx + 1, 0, duplicatedRichMenu);

        // Set selected rich menu
        state.sidePanel.selectedBlockType = 'RICH_MENU';
        state.sidePanel.selectedRichMenuId = duplicatedRichMenu.id;
      }
    },
    removeRichMenu(state, action: PayloadAction<RemoveRichMenuPayload>) {
      const { richMenuId } = action.payload;
      const { richMenus } = state.richMenuTemplate;
      const matchedIdx = richMenus.findIndex(richMenu => richMenu.id === richMenuId);

      if (matchedIdx !== -1) {
        const matchedRichMenu = richMenus[matchedIdx];
        const nextRichMenu = richMenus[matchedIdx + 1] || richMenus[0];

        if (matchedRichMenu.isDefault) {
          // If the current rich menu is the turn off default, set the next one as default
          nextRichMenu.isDefault = true;
        }

        // Select next rich menu when deleted rich menu is selected
        if (state.sidePanel.selectedRichMenuId === matchedRichMenu.id) {
          state.sidePanel.selectedBlockType = 'RICH_MENU';
          state.sidePanel.selectedRichMenuId = nextRichMenu.id;
        }

        // Remove matched rich menu
        richMenus.splice(matchedIdx, 1);
      }
    },
  },
});

export const { actions: richMenuDesignActions } = slice;

const { addNewRichMenu, updateRichMenuSettings, updateSelectedRichMenuCell, duplicateRichMenu, removeRichMenu } =
  richMenuDesignActions;

export const useRichMenuDesignSlice = () => {
  useInjectReducer({
    key: slice.name,
    reducer: undoable(slice.reducer, {
      limit: 20,
      ignoreInitialState: true,
      syncFilter: true,
      nullifyStateProps: ['sidePanel'],
      filter: function filterActions(action) {
        const { payload, type } = action;

        if (!!payload?.ignoreUndoAction) {
          return false;
        }

        // if (!isEmpty(payload)) {
        return [
          updateRichMenuSettings.type,
          updateSelectedRichMenuCell.type,
          addNewRichMenu.type,
          duplicateRichMenu.type,
          removeRichMenu.type,
        ].includes(type);
        // }

        // return false;
      },
    }),
  });
  useInjectSaga({ key: slice.name, saga: richMenuDesignSaga });
  return { actions: slice.actions };
};
