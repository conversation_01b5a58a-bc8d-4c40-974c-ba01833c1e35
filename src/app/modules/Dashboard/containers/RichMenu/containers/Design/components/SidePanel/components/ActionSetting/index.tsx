// Libraries
import React, { useCallback, useEffect, useMemo } from 'react';
import { Moment } from 'moment';
import { range } from 'lodash';
import dayjs, { Dayjs } from 'dayjs';
import { useSelector } from 'react-redux';
import isEqual from 'react-fast-compare';
import customParseFormat from 'dayjs/plugin/customParseFormat';

// Types
import {
  TAction,
  TActionDateTimePickerSettings,
  TActionMessageSettings,
  TActionPostbackSettings,
  TActionRichmenuSwitchSettings,
  TActionType,
  TActionUriSettings,
} from 'richMenu/containers/Design/types';
import { NamePath } from 'antd/lib/form/interface';

// Components
import { FormInstance, TimePicker } from 'antd';
import { Form, Select, Input, Space, DatePicker, InputDynamic } from '@antscorp/antsomi-ui';

// Hooks
import { useDebounce, useDeepCompareEffect } from 'app/hooks';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import {
  ACTION_SETTINGS,
  ACTION_TYPE,
  FORMAT_OPTIONS,
  FORMAT_TYPE,
  URI_OPTIONS,
  URI_TYPE,
} from '../../../../constants';

// Selectors
import { selectGeneral, selectMode, selectRichMenus, selectSelectedRichMenu } from '../../../../slice/selectors';

// Translations
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Utils
import { tryCatchWrapper } from 'app/utils';
import { useOptimizeSelector } from 'store/configureStore';
import { serializedDateTimeAction } from '../../../../utils';
import { getUserInfo } from 'app/components/templates/ListingPerformance/utils';

const { Item, useForm } = Form;
const { DefaultInput } = Input;

dayjs.extend(customParseFormat);

interface ActionSettingProps {
  action?: TAction;
  onChange: (action: TAction) => void;
}

type ActionTypeField = TActionRichmenuSwitchSettings &
  TActionMessageSettings &
  TActionUriSettings &
  TActionPostbackSettings &
  TActionDateTimePickerSettings & {
    type: TActionType;
  };

type DateTimeType = 'FROM' | 'TO' | 'DEFAULT_DATE' | 'DEFAULT_TIME' | 'DEFAULT_DATETIME';

type DisabledTimeProps = {
  hours: number[];
  minutes: number[];
  compareInfo?: {
    type: DateTimeType;
    min?: { h: number; m: number };
    max?: { h: number; m: number };
    h?: number;
  };
};

type CheckExtraDateTimeProps = {
  type: DateTimeType;
  isNested?: boolean;
  getFieldValue: (namePath: NamePath) => any;
};

const formLayout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

const pickerStyle = { border: 'unset', borderBottom: '1px solid #B8CFE6' };

const dateFormat = 'MM/DD/YYYY';
const timeFormat = 'HH:mm';
const MAP_DEFAULT_TIME = [
  {
    label: 'From',
    name: 'min',
    type: 'FROM',
  },
  {
    label: 'To',
    name: 'max',
    type: 'TO',
  },
  {
    label: 'Default datetime',
    type: 'DEFAULT_DATETIME',
    name: 'initial',
  },
];

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/components/ActionSetting';

export const ActionSetting: React.FC<ActionSettingProps> = props => {
  const { action, onChange } = props;

  const { t } = useTranslation();

  const [form] = useForm<any>();

  // Select
  const richMenuList = useSelector(selectRichMenus);
  const richMenuSelected = useSelector(selectSelectedRichMenu);
  const richMenuTemplateMode = useSelector(selectMode);
  const { isShowError } = useOptimizeSelector(selectGeneral);

  const menuListMemoized = useMemo(() => {
    if (!richMenuList || !richMenuSelected || !Array.isArray(richMenuList)) return [];

    return richMenuList
      .filter(each => each.id !== richMenuSelected.id)
      .map(menuItem => ({
        label: menuItem.menuName,
        value: menuItem.id,
      }));
  }, [richMenuList, richMenuSelected]);

  const actionTypesMemoizedOpts = useMemo(() => {
    let opts = Object.values(ACTION_SETTINGS);

    if (richMenuTemplateMode !== 'multiple') {
      opts = opts.filter(eachOpt => eachOpt.value !== ACTION_TYPE.RICH_MENU_SWITCH);
    }

    return opts;
  }, [richMenuTemplateMode]);

  // Watcher
  const watcherValues = Form.useWatch([], form);
  const debounceValues = useDebounce(watcherValues, 500);

  const checkDisabledDate = useCallback((current: Dayjs, extra: CheckExtraDateTimeProps) => {
    const { type, isNested, getFieldValue } = extra;
    const minDate = dayjs('1900-01-01');
    const maxDate = dayjs('2100-12-31');

    // Can not select days before min or max date
    if (current && (current < minDate || current > maxDate)) return true;

    if (type && current && typeof getFieldValue === 'function') {
      const min = isNested ? getFieldValue(['min', 'date']) : getFieldValue('min');
      const max = isNested ? getFieldValue(['max', 'date']) : getFieldValue('max');

      if (
        (type === 'FROM' && max && current > max) ||
        (type === 'TO' && min && current < min) ||
        (min &&
          max &&
          (type === 'DEFAULT_DATE' || type === 'DEFAULT_DATETIME') &&
          (current < min || current > dayjs(max).add(1, 'day')))
      )
        return true;
    }

    return false;
  }, []);

  const getDisableTimeField = useCallback(
    ({ hours, minutes, compareInfo }: DisabledTimeProps) => ({
      disabledHours: () => [...hours],
      disabledMinutes: (selectedHour: number) => {
        if (selectedHour === -1) return range(0, 60); // Disabled all

        if (
          compareInfo?.h &&
          ((compareInfo?.type === 'FROM' && selectedHour < compareInfo?.h) ||
            (compareInfo?.type === 'TO' && selectedHour > compareInfo?.h))
        )
          return []; // Allowed all

        if (['DEFAULT_TIME', 'DEFAULT_DATETIME'].includes(compareInfo?.type || '') && compareInfo) {
          if (compareInfo?.min?.h === compareInfo?.max?.h && compareInfo.min && compareInfo.max) {
            return [...range(0, compareInfo.min.m), ...range(compareInfo.max.m + 1, 60)];
          } else if (compareInfo?.min?.h === selectedHour) {
            return range(0, compareInfo?.min?.m);
          } else if (compareInfo?.max?.h === selectedHour) {
            return range(compareInfo?.max?.m + 1, 60);
          }
        }

        return [...minutes];
      },
    }),
    [],
  );

  const checkDisabledTime = useCallback(
    (current: Moment, extra: CheckExtraDateTimeProps) => {
      const { type, getFieldValue, isNested } = extra;

      if (type && current && typeof getFieldValue === 'function') {
        const min = isNested ? getFieldValue(['min', 'time']) : getFieldValue('min');
        const max = isNested ? getFieldValue(['max', 'time']) : getFieldValue('max');
        const hoursRange = range(0, 24, 1);
        const minutesRange = range(0, 60, 1);

        if (type === 'FROM' && max) {
          const maxTime = dayjs(max);
          const h = maxTime.hour();
          const m = maxTime.minute();

          return getDisableTimeField({
            hours: hoursRange.splice(h + 1, 24),
            minutes: minutesRange.splice(m, 60),
            compareInfo: { h, type },
          });
        }
        if (type === 'TO' && min) {
          const minTime = dayjs(min);
          const h = minTime.hour();
          const m = minTime.minute();

          return getDisableTimeField({
            hours: hoursRange.splice(0, h),
            minutes: minutesRange.splice(0, m + 1),
            compareInfo: { h, type },
          });
        }
        if ((type === 'DEFAULT_TIME' || type === 'DEFAULT_DATETIME') && min && max) {
          const maxTime = dayjs(max);
          const hMax = maxTime.hour();
          const mMax = maxTime.minute();

          const minTime = dayjs(min);
          const hMin = minTime.hour();
          const mMin = minTime.minute();

          return getDisableTimeField({
            hours: [...hoursRange.slice(0, hMin), ...hoursRange.slice(hMax + 1, 24)],
            minutes: [],
            compareInfo: { type, min: { h: hMin, m: mMin }, max: { h: hMax, m: mMax } },
          });
        }
      }

      return getDisableTimeField({ hours: [], minutes: [] });
    },
    [getDisableTimeField],
  );

  const handleActionTypeChange = tryCatchWrapper((type: any): void => {
    switch (type) {
      case ACTION_TYPE.URI: {
        form.setFieldsValue({ uriType: URI_TYPE.URL });
        break;
      }
      case ACTION_TYPE.DATE_TIME_PICKER: {
        form.setFieldsValue({ mode: FORMAT_TYPE.DATE, min: undefined, max: undefined, initial: undefined });
        break;
      }
      default: {
        break;
      }
    }
  }, PATH);

  // Effects
  useDeepCompareEffect(() => {
    if (action?.type === 'datetimepicker') {
      const newAction = {
        ...action,
        min: serializedDateTimeAction(action?.min),
        max: serializedDateTimeAction(action?.max),
        initial: serializedDateTimeAction(action?.initial),
      };

      form.setFieldsValue(newAction);
    } else {
      form.setFieldsValue(action);
    }
  }, [form, action]);

  // Validate form error when isShowError equal true and action changed
  useDeepCompareEffect(() => {
    if (isShowError) {
      setTimeout(() => {
        form.validateFields();
      });
    }
  }, [action, form]);

  useDeepCompareEffect(() => {
    if (debounceValues && !isEqual(debounceValues, action)) {
      onChange(debounceValues);
    }
  }, [debounceValues]);

  const renderDateTimeMode: any = (formProps: FormInstance) => {
    const { getFieldValue } = formProps;

    if (getFieldValue('mode') === FORMAT_TYPE.DATE) {
      return (
        <>
          <Space direction="horizontal" className="ants-d-flex ants-w-100" size="large">
            <Item<ActionTypeField> label="From" name="min">
              <DatePicker
                size="middle"
                placeholder={dateFormat}
                format={dateFormat}
                disabledDate={current =>
                  checkDisabledDate(current as Dayjs, {
                    type: 'FROM',
                    getFieldValue,
                  })
                }
              />
            </Item>
            <Item<ActionTypeField> label="To" name="max">
              <DatePicker
                size="middle"
                placeholder={dateFormat}
                format={dateFormat}
                disabledDate={current => checkDisabledDate(current as Dayjs, { type: 'TO', getFieldValue })}
              />
            </Item>
          </Space>
          <Item<ActionTypeField> labelAlign="left" label="Default Date" name="initial">
            <DatePicker
              size="middle"
              placeholder={dateFormat}
              format={dateFormat}
              disabledDate={current => checkDisabledDate(current as Dayjs, { type: 'DEFAULT_DATE', getFieldValue })}
            />
          </Item>
        </>
      );
    }

    if (getFieldValue('mode') === FORMAT_TYPE.TIME) {
      return (
        <>
          <Space direction="horizontal" className="ants-d-flex ants-w-100" size="large">
            <Item<ActionTypeField> label="From" name="min">
              <TimePicker
                size="middle"
                placeholder="HH:MM"
                style={pickerStyle}
                format={timeFormat}
                disabledTime={current => checkDisabledTime(current as any, { type: 'FROM', getFieldValue })}
              />
            </Item>
            <Item<ActionTypeField> label="To" name="max">
              <TimePicker
                size="middle"
                placeholder="HH:MM"
                format={timeFormat}
                style={pickerStyle}
                disabledTime={current => checkDisabledTime(current as any, { type: 'TO', getFieldValue })}
              />
            </Item>
          </Space>
          <Item<ActionTypeField> labelAlign="left" label="Default Time" name="initial">
            <TimePicker
              size="middle"
              format={timeFormat}
              placeholder="HH:MM"
              style={pickerStyle}
              disabledTime={current => checkDisabledTime(current as any, { type: 'DEFAULT_TIME', getFieldValue })}
            />
          </Item>
        </>
      );
    }

    if (getFieldValue('mode') === FORMAT_TYPE.DATE_TIME) {
      return MAP_DEFAULT_TIME.map(({ name, label, type }) => (
        <Item key={name} label={label}>
          <Space direction="horizontal" className="ants-d-flex ants-w-100" size="large">
            <Item noStyle name={[name, 'date']}>
              <DatePicker
                format={dateFormat}
                size="middle"
                disabledDate={current =>
                  checkDisabledDate(current as Dayjs, {
                    type: type as DateTimeType,
                    isNested: true,
                    getFieldValue,
                  })
                }
                placeholder={dateFormat}
              />
            </Item>
            <span>at</span>
            <Item noStyle name={[name, 'time']}>
              <TimePicker
                size="middle"
                placeholder="HH:MM"
                format={timeFormat}
                style={pickerStyle}
                disabledTime={current =>
                  checkDisabledTime(current as any, { type: type as DateTimeType, getFieldValue, isNested: true })
                }
              />
            </Item>
          </Space>
        </Item>
      ));
    }

    return null;
  };

  const renderDynamicFields: any = (formProps: FormInstance) => {
    const { getFieldValue } = formProps;

    if (getFieldValue('type') === ACTION_TYPE.NONE) {
      return null;
    }

    switch (getFieldValue('type')) {
      case ACTION_TYPE.MESSAGE: {
        return (
          <Item<ActionTypeField> label="Message" name="text" rules={[{ required: true }]}>
            <DefaultInput maxLength={300} />
          </Item>
        );
      }
      case ACTION_TYPE.POST_BACK: {
        return (
          <Item<ActionTypeField> label="Data" name="data" rules={[{ required: true }]}>
            <DefaultInput maxLength={300} />
          </Item>
        );
      }
      case ACTION_TYPE.RICH_MENU_SWITCH: {
        return (
          <>
            <Item<ActionTypeField> label="Switching Target" name="richMenuAliasId" rules={[{ required: true }]}>
              <Select options={menuListMemoized} />
            </Item>
            <Item<ActionTypeField> label="Data" name="data" rules={[{ required: true }]}>
              <DefaultInput maxLength={300} />
            </Item>
          </>
        );
      }
      case ACTION_TYPE.URI: {
        // Variables
        const userInfo = getUserInfo(APP_CONFIG.U_OGS);
        const isProduction = APP_CONFIG.APPLICATION_ENV === 'production';

        return (
          <>
            <Item<ActionTypeField> label="URI Type" name="uriType" rules={[{ required: true }]}>
              <Select options={Object.values(URI_OPTIONS)} />
            </Item>

            <Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.uriType !== curValues.uriType}>
              {({ getFieldValue }) => {
                if (getFieldValue('uriType') === URI_TYPE.URL) {
                  return (
                    <Item<ActionTypeField> label="URL" name="uri" rules={[{ required: true }]}>
                      <InputDynamic
                        isRealTime
                        ms={350}
                        canMultipleLine={false}
                        disabledOpts={{ individual: true }}
                        // disabledOpts={{ individual: isDefaultRichMenu }}
                        apiConfig={{
                          domain: isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX,
                          slug: '/api/v1',
                          token: userInfo.token,
                          userId: String(userInfo.user_id),
                          accountId: String(userInfo.account_id),
                        }}
                      />
                    </Item>
                  );
                }

                return (
                  <Item<ActionTypeField> label="Phone number" name="uri" rules={[{ required: true }]}>
                    <DefaultInput maxLength={1000} type="number" />
                  </Item>
                );
              }}
            </Item>
          </>
        );
      }
      case ACTION_TYPE.DATE_TIME_PICKER: {
        return (
          <>
            <Item<ActionTypeField> label="Data" name="data" rules={[{ required: true }]}>
              <DefaultInput maxLength={300} />
            </Item>
            <Item<ActionTypeField> label="Format" name="mode" rules={[{ required: true }]}>
              <Select
                options={Object.values(FORMAT_OPTIONS)}
                onChange={() => {
                  form.setFieldsValue({ min: undefined, max: undefined, initial: undefined });
                }}
              />
            </Item>
            <Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.mode !== curValues.mode}>
              {renderDateTimeMode}
            </Item>
          </>
        );
      }
      default: {
        return null;
      }
    }
  };

  return (
    <Form
      {...formLayout}
      form={form}
      preserve={false}
      validateMessages={{ required: t(translations.messageError.fieldEmpty.message) }}
      name="action-settings"
      layout="vertical"
    >
      <Item<ActionTypeField> label="Action Type" name="type" rules={[{ required: true }]}>
        <Select options={actionTypesMemoizedOpts} onChange={handleActionTypeChange} />
      </Item>
      <Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.type !== curValues.type}>
        {renderDynamicFields}
      </Item>
    </Form>
  );
};
