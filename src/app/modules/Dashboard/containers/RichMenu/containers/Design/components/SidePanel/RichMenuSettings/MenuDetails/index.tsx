// Libraries
import { Form, SettingWrapper, Switch, Input } from '@antscorp/antsomi-ui';
import { useDebounce, useDeepCompareEffect } from 'app/hooks';
import React, { memo, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import isEqual from 'react-fast-compare';
import { get, random } from 'lodash';

// Services
import { richMenuValidationService } from 'app/services/RichMenu';

// Types
import { RichMenu } from 'richMenu/containers/Design/types';

// Translations
import { translations } from 'locales/translations';

// Utils
import { serializedAliasId } from 'richMenu/containers/Design/utils';
import { useSelector } from 'react-redux';

// Selectors
import { selectExternalConfig, selectGeneral, selectRichMenus } from 'richMenu/containers/Design/slice/selectors';

const { Item, useForm } = Form;
const { DefaultInput } = Input;

type FieldType = Pick<RichMenu, 'aliasId' | 'menuName' | 'isDefault'>;

interface MenuDetailsProps {
  richMenu: RichMenu;
  values: FieldType;
  onChange: (values: FieldType) => void;
}

export const MenuDetails: React.FC<MenuDetailsProps> = memo(props => {
  const { richMenu, values, onChange } = props;

  const { t } = useTranslation();
  const [form] = useForm<FieldType>();
  const { isShowError } = useSelector(selectGeneral);
  const richMenus = useSelector(selectRichMenus);
  const { token } = useSelector(selectExternalConfig);

  // Watchers
  const watchedValues = Form.useWatch([], form);
  const debounceValues = useDebounce(watchedValues, 500);

  const excludeRichMenu = useMemo(() => {
    return richMenus.filter(item => item.id !== richMenu.id);
  }, [richMenu.id, richMenus]);

  // Effects
  // Validate form error when isShowError equal true and values changed
  useEffect(() => {
    if (isShowError) {
      setTimeout(() => {
        form.validateFields();
      });
    }
  }, [form, isShowError, values]);

  useDeepCompareEffect(() => {
    form.setFieldsValue(values);
  }, [form, values]);

  useDeepCompareEffect(() => {
    if (debounceValues && !isEqual(debounceValues, values)) {
      onChange(debounceValues);
    }
  }, [debounceValues]);

  // Handlers
  const validateMenuName = async (_rule: any, value: string) => {
    if (!value) return Promise.reject(t(translations.messageError.fieldEmpty.message));

    if (excludeRichMenu.some(item => item.menuName === value)) {
      return Promise.reject(t(translations.messageError.nameExisted.message));
    }
    return Promise.resolve();
  };

  const validateAliasID = async (_rule: any, value: string) => {
    if (!value) return Promise.reject(t(translations.messageError.fieldEmpty.message));

    let isExisted = false;

    if (excludeRichMenu.some(item => item.aliasId === value)) {
      isExisted = true;
    }

    const data = await richMenuValidationService.validateAliasId({
      alias: {
        id: value,
        richMenuId: `${richMenu.id}`,
      },
      authenToken: token,
    });

    if (get(data, 'entries.isAliasExist', false)) {
      isExisted = true;
    }

    return isExisted ? Promise.reject(t(translations.messageError.nameExisted.message)) : Promise.resolve();
  };

  return (
    <Form<FieldType>
      name={`menu-details-${random(10)}`}
      form={form}
      layout="vertical"
      validateMessages={{
        required: t(translations.messageError.fieldEmpty.message),
      }}
    >
      <Item<FieldType>
        label="Menu Name"
        name="menuName"
        validateDebounce={500}
        rules={[{ required: true, validator: validateMenuName }]}
      >
        <DefaultInput maxLength={255} />
      </Item>
      <Item<FieldType>
        label="Alias ID"
        name="aliasId"
        validateDebounce={500}
        normalize={value => serializedAliasId(value)}
        rules={[{ required: true, validator: validateAliasID }]}
      >
        <DefaultInput maxLength={32} />
      </Item>
      <SettingWrapper label="Set as default Rich Menu" labelColor="#000">
        <Item<FieldType> name="isDefault" noStyle valuePropName="checked">
          <Switch size="small" />
        </Item>
      </SettingWrapper>
    </Form>
  );
});
