// Libraries
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import isEqual from 'react-fast-compare';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import { FormInstance } from 'antd';

// Types
import {
  RichMenu,
  TActionImageMap,
  TActionMessageSettings,
  TActionTypeADVImageMap,
  TActionUrlSettings,
  TActionVideoSettings,
} from '../../../../types';
import { TChildrenCell } from '@antscorp/antsomi-ui/es/types';

// Translations
import { translations } from 'locales/translations';

// Selectors
import { selectGeneral, selectRichMenus } from '../../../../slice/selectors';

// Components
import { Form, Select, Input, InputDynamic, Switch, UploadImage } from '@antscorp/antsomi-ui';

// Constants
import { ACTION_SETTINGS_ADV_IMAGE_MAP, ACTION_TYPE_ADV_IMAGE_MAP } from '../../../../constants';
import { APP_CONFIG } from 'constants/appConfig';

// Hooks
import { useDebounce, useDeepCompareEffect } from 'app/hooks';
import { useOptimizeSelector } from 'store/configureStore';

// Utils
import { tryCatchWrapper } from 'app/utils';
import { getUserInfo } from 'app/components/templates/ListingPerformance/utils';

const { Item, useForm } = Form;
const { DefaultInput } = Input;

interface ActionSettingV2Props {
  action: TActionImageMap;
  onChange: (action: TActionImageMap) => void;
}

type ActionTypeField = TActionMessageSettings &
  TActionUrlSettings &
  TActionVideoSettings & {
    type: TActionTypeADVImageMap;
  };

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/components/ActionSettingV2';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

const formLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 },
};

export const ActionSettingV2: React.FC<ActionSettingV2Props> = props => {
  const { action, onChange } = props;

  const { t } = useTranslation();
  const [form] = useForm<any>();
  const { isShowError } = useOptimizeSelector(selectGeneral);
  const richMenus = useSelector(selectRichMenus);

  const isExistedVideoAct = useMemo(() => {
    let isExisted = false;
    if (isEmpty(richMenus) || !Array.isArray(richMenus)) return isExisted;

    richMenus.forEach((richMenu: RichMenu) => {
      const { cells } = richMenu;

      if (Array.isArray(cells)) {
        cells.forEach((cell: TChildrenCell) => {
          const { action } = cell;

          if (action && action?.type === ACTION_TYPE_ADV_IMAGE_MAP.VIDEO) {
            isExisted = true;
          }
        });
      }
    });

    return isExisted;
  }, [richMenus]);

  const actionOptMemoized = useMemo(() => {
    const list = Object.values(ACTION_SETTINGS_ADV_IMAGE_MAP);
    if (isExistedVideoAct && action && action?.type !== ACTION_TYPE_ADV_IMAGE_MAP.VIDEO)
      return list.filter(item => item.value !== ACTION_TYPE_ADV_IMAGE_MAP.VIDEO);

    return list;
  }, [isExistedVideoAct, action]);

  // Watchers
  const watcherValues = Form.useWatch([], form);
  const debounceValues = useDebounce(watcherValues, 350);

  // Variables
  const userInfo = getUserInfo(APP_CONFIG.U_OGS);
  const isProduction = APP_CONFIG.APPLICATION_ENV === 'production';

  const handleActionTypeChange = tryCatchWrapper((type: any): void => {
    switch (type) {
      default: {
        break;
      }
    }
  }, PATH);

  // Effects
  useDeepCompareEffect(() => {
    form.setFieldsValue(action);
  }, [form, action]);

  // Validate form error when isShowError equal true and action changed
  useDeepCompareEffect(() => {
    if (isShowError) {
      setTimeout(() => {
        form.validateFields();
      });
    }
  }, [action, form, isShowError]);

  useDeepCompareEffect(() => {
    if (debounceValues && !isEqual(debounceValues, action)) {
      onChange(debounceValues);
    }
  }, [debounceValues]);

  const renderDynamicFields: any = (formProps: FormInstance) => {
    const { getFieldValue } = formProps;

    if (getFieldValue('type') === ACTION_TYPE_ADV_IMAGE_MAP.NONE) {
      return null;
    }

    switch (getFieldValue('type')) {
      case ACTION_TYPE_ADV_IMAGE_MAP.MESSAGE: {
        return (
          <Item<ActionTypeField> label="Message Text" name="text" rules={[{ required: true }]}>
            <InputDynamic
              canMultipleLine={false}
              allowDynamicOptions={['addPersonalization', 'shortlink']}
              apiConfig={{
                domain: isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX,
                slug: '/api/v1',
                token: userInfo.token,
                userId: String(userInfo.user_id),
                accountId: String(userInfo.account_id),
              }}
            />
          </Item>
        );
      }
      case ACTION_TYPE_ADV_IMAGE_MAP.URL: {
        return (
          <Item<ActionTypeField> label="URL" name="url" rules={[{ required: true }]}>
            <InputDynamic
              canMultipleLine={false}
              apiConfig={{
                domain: isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX,
                slug: '/api/v1',
                token: userInfo.token,
                userId: String(userInfo.user_id),
                accountId: String(userInfo.account_id),
              }}
            />
          </Item>
        );
      }
      case ACTION_TYPE_ADV_IMAGE_MAP.VIDEO: {
        return (
          <>
            <Item<ActionTypeField>
              label="Video URL"
              name="videoUrl"
              rules={[{ required: true }]}
              normalize={(value, preValue) => {
                if (_.isObject(value) && _.isString(preValue)) return preValue;
                return _.isString(value) ? value : _.isString(preValue) ? preValue : undefined;
              }}
            >
              <UploadImage
                isInputMode
                domainMedia={isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX}
                slug="api/v1"
                mode="video"
                maxSize={200}
                extensions={['video/mp4']}
                labelButtonSelect="Select video from computer"
                labelHeadingModal="Video Selection"
                labelModalDelete="Delete Video"
                placeholder="Enter video URL"
                searchPlaceholder="Search video..."
                iconName="video"
                paramConfigs={{
                  token: userInfo.token,
                  userId: String(userInfo.user_id),
                  accountId: String(userInfo.account_id),
                }}
                selectedImage={{ url: form.getFieldValue('videoUrl') }}
                onChangeImage={(image: any) => {
                  form.setFieldsValue({ videoUrl: image.url });
                }}
                onRemoveImage={() => {
                  form.setFieldsValue({ videoUrl: '' });
                }}
              />
            </Item>
            <Item<ActionTypeField>
              label="Preview Image"
              name="previewImage"
              rules={[{ required: true }]}
              normalize={(value, preValue) => {
                if (_.isObject(value) && _.isString(preValue)) return preValue;
                return _.isString(value) ? value : _.isString(preValue) ? preValue : undefined;
              }}
            >
              <UploadImage
                isInputMode
                domainMedia={isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX}
                slug="api/v1"
                paramConfigs={{
                  token: userInfo.token,
                  userId: String(userInfo.user_id),
                  accountId: String(userInfo.account_id),
                }}
                selectedImage={{ url: form.getFieldValue('previewImage') }}
                onChangeImage={(image: any) => {
                  form.setFieldsValue({ previewImage: image.url });
                }}
                onRemoveImage={() => {
                  form.setFieldsValue({ previewImage: '' });
                }}
              />
            </Item>
            <Item<ActionTypeField>
              label="Add label after finishing"
              name="isAddLabel"
              labelAlign="left"
              labelCol={{ span: 21 }}
              wrapperCol={{ span: 3 }}
              colon={false}
            >
              <Switch size="small" />
            </Item>
            <Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.isAddLabel !== curValues.isAddLabel}>
              {({ getFieldValue }) => {
                if (!getFieldValue('isAddLabel')) {
                  return null;
                }

                return (
                  <>
                    <Item<ActionTypeField> label="Label" name="label" rules={[{ required: true }]}>
                      <DefaultInput />
                    </Item>
                    <Item<ActionTypeField> label="External URL" name="externalUrl" rules={[{ required: true }]}>
                      <InputDynamic
                        canMultipleLine={false}
                        apiConfig={{
                          domain: isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX,
                          slug: '/api/v1',
                          token: userInfo.token,
                          userId: String(userInfo.user_id),
                          accountId: String(userInfo.account_id),
                        }}
                      />
                    </Item>
                  </>
                );
              }}
            </Item>
          </>
        );
      }
      default: {
        return null;
      }
    }
  };

  return (
    <Form
      {...formLayout}
      form={form}
      preserve={false}
      validateMessages={{ required: t(translations.messageError.fieldEmpty.message) }}
      name="action-settings-v2"
    >
      <Item<ActionTypeField> label="Action Type" name="type" rules={[{ required: true }]}>
        <Select options={actionOptMemoized} onChange={handleActionTypeChange} />
      </Item>
      <Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.type !== curValues.type}>
        {renderDynamicFields}
      </Item>
    </Form>
  );
};
