// Libraries
import React, { Fragment, memo, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { omit, get, isEmpty, has, cloneDeep, set } from 'lodash';
import dayjs from 'dayjs';
import { useDispatch, useSelector } from 'react-redux';

// Services
import { permissionServices } from 'app/services/Permission';
import { saveBase64File } from 'app/services/MediaTemplateDesign/FileSave';

// Components
import { ButtonSave, MenuWrapper, MenuItem, TemplateSaveAs, ModalSaveAs } from './styled';
import { Dropdown } from 'antd';
import {
  Button,
  Divider,
  Icon,
  Input,
  Space,
  Typography,
  Form,
  message,
  TemplateValueOptions,
  usePersistTemplate,
  useTemplateSave,
  camelCaseToSnakeCase,
  TShareAccess,
  useGetObjectTemplateDetail,
  snakeCaseToCamelCase,
  useGetSaveAsGalleryPermissionEmails,
} from '@antscorp/antsomi-ui';
import { DesignToolbar } from 'app/components/organisms';
import { RichMenuPreviewModal } from '../common/RichMenuPreviewModal';
import { CaptureBlock } from '../Workspace/components/CaptureBlock';

// Locales
import { translations } from 'locales/translations';

// Libs
import { ActionCreators } from 'app/libs/redux-undo';

// Utils
import { getTranslateMessage } from 'utils/messages';
import { tryCatchWrapper } from 'app/utils';
import { getUserInfo } from 'app/components/templates/ListingPerformance/utils';
import { initAccessInfoDefault } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/utils';

// Selectors
import {
  selectExternalConfig,
  selectGeneral,
  selectRichMenuTemplate,
  selectSelectedRichMenu,
  selectUndoAbleIndex,
  selectUndoAbleLimit,
  selectWorkspaceType,
} from 'richMenu/containers/Design/slice/selectors';
import { richMenuDesignActions } from 'richMenu/containers/Design/slice';

// Types
import { RichMenuTemplate } from '../../types';
import { ObjectTemplate } from '@antscorp/antsomi-ui/es/models/ObjectTemplate';
import { ObjectAccessInfo } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/types';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { WORKSPACE_TYPE } from '../../constants';
import { DATE_TIME_FORMAT } from 'constants/datetime';
import { POST_MESSAGE_EVENT } from 'constants/variables';
import { GET_LIST_TYPE, OBJECT_TYPES, PUBLIC_LEVEL } from '@antscorp/antsomi-ui/es/constants';
import {
  MENU_PERMISSION,
  PEOPLE_ACTION_KEYS,
} from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/constants';

// Hooks
import { useDeepCompareEffect, useUserInfoV2 } from 'app/hooks';

const { Text } = Typography;

interface ToolbarProps {
  saveText?: string;
  onSave?: (richMenuTemplate: RichMenuTemplate) => void;
  onCancel?: () => void;
}

type SliderThumbnail = {
  url: string;
  showSkeleton?: boolean;
};

type TState = {
  isOpenPreviewModal: boolean;
  thumbnailsResizing: (string | SliderThumbnail | undefined)[];
};

type TSaveAsType = 'myTemplate' | 'galleryTemplate';

interface SaveAsProps {
  isOpenSaveAsModal: boolean;
  isLoadingCapture: boolean;
  userId?: string;
  saveAsType: TSaveAsType;
  errors: any[];
}

type TSaveAsState = Partial<TemplateValueOptions> & SaveAsProps;

type TTemplateName = { id: undefined; label: string } | { id: string | number; label: string } | undefined;

const defaultProps: ToolbarProps = {
  saveText: getTranslateMessage(translations.save.title),
};

const { useForm } = Form;

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';
const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/Toolbar/index.tsx';

const initialTemplateName = (): TTemplateName => ({
  id: undefined,
  label: `Untitled Rich Menu Template ${dayjs().format(DATE_TIME_FORMAT.TITLE_DATE_TIME)}`,
});

const initialSaveAsState = (): TSaveAsState => ({
  isOpenSaveAsModal: false,
  isLoadingCapture: false,
  saveAsType: 'myTemplate',
  saveOption: 'new',
  description: '',
  errors: [],
});

export const Toolbar: React.FC<ToolbarProps> = memo(props => {
  const { saveText, onSave, onCancel } = props;
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const userInfo = getUserInfo(APP_CONFIG.U_OGS);
  const isProduction = APP_CONFIG.APPLICATION_ENV === 'production';
  const url = `${isProduction ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX}/api/v1`;
  const auth = {
    url,
    token: userInfo.token,
    userId: String(userInfo.user_id),
    accountId: String(userInfo.account_id),
  };

  // Selectors
  const richMenuTemplate = useSelector(selectRichMenuTemplate);
  const undoAbleIndex = useSelector(selectUndoAbleIndex);
  const undoAbleLimit = useSelector(selectUndoAbleLimit);
  const richMenu = useSelector(selectSelectedRichMenu);
  const { isShowError } = useSelector(selectGeneral);
  const { templateNameList } = useSelector(selectExternalConfig);
  const workspaceType = useSelector(selectWorkspaceType);
  const isRichMenu = workspaceType === WORKSPACE_TYPE.RICH_MENU;

  const [form] = useForm<{ name: string }>();
  const [messageApi, contextHolder] = message.useMessage();

  // State
  const [state, setState] = useState<TState>({
    isOpenPreviewModal: false,
    thumbnailsResizing: [],
  });

  const [saveAsState, setSaveAsState] = useState<TSaveAsState>(initialSaveAsState());

  const currentUserInfo = useUserInfoV2();

  // Queries
  const { isLoading: isLoadingSaveAs, mutateAsync: handleSaveAsTemplate } = usePersistTemplate({});
  const {
    isLoading: isLoadingTemplateDetail,
    isRefetching,
    data: templateDetail,
    refetch: refetchTemplate,
    isStale,
  } = useGetObjectTemplateDetail({
    args: {
      auth,
      params: {
        template_id: saveAsState?.templateName?.id || '',
        object_type: OBJECT_TYPES.RICH_MENU_TEMPLATE,
        public_level: saveAsState.saveAsType === 'galleryTemplate' ? PUBLIC_LEVEL.PUBLIC : PUBLIC_LEVEL.RESTRICTED,
      },
    },
    options: {
      enabled:
        isRichMenu && saveAsState.saveOption === 'exist' && !isEmpty(get(saveAsState, ['templateName', 'id'], '')),
    },
  });

  const {
    form: formSaveAs,
    templateItems,
    isLoadingTemplateList,
    onLoadMore: onLoadMoreTemplates,
    onSearchName,
  } = useTemplateSave({
    service: auth,
    config: {
      objectType: OBJECT_TYPES.RICH_MENU_TEMPLATE,
      getListType: saveAsState.saveAsType === 'myTemplate' ? GET_LIST_TYPE.OWNER : GET_LIST_TYPE.SHARE_WITH_ME, // In case Public Level == Public -> don't need to use get_list_type,
      publicLevel: saveAsState.saveAsType === 'galleryTemplate' ? PUBLIC_LEVEL.PUBLIC : PUBLIC_LEVEL.RESTRICTED,
      limitListPerPage: 20,
    },
    queriesOptions: {
      categoryList: {
        enabled: isRichMenu,
      },
      templateList: {
        enabled: isRichMenu,
      },
    },
  });

  const { isLoading: isLoadingPermission, data: userPermission } = useGetSaveAsGalleryPermissionEmails({
    args: {
      auth,
    },
    options: {
      enabled: isRichMenu,
    },
  });

  const { resetLayout, updateRichMenuTemplateName } = richMenuDesignActions;
  const { name, mode, richMenus } = richMenuTemplate;

  // Variables
  const isAdvImageMap = workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP;
  let labelName = t(translations.templateName.title);
  if (isAdvImageMap) {
    labelName = t(translations.alternativeText.title);
  }

  const thumbnails = useMemo(() => {
    if (!isRichMenu) return [];

    // in case have been resized thumbnails
    if (state.thumbnailsResizing.length) {
      return state.thumbnailsResizing;
    }

    return richMenus.map((richMenu: any) => richMenu.uploadImageUrl);
  }, [richMenus, isRichMenu, state.thumbnailsResizing]);

  const defaultThumbnail = useMemo(() => {
    if (!isRichMenu) return '';
    const defaultIdx = richMenus.findIndex(richMenu => richMenu.isDefault);

    if (defaultIdx !== -1) {
      // in case have been resized thumbnails
      if (state.thumbnailsResizing.length) {
        return state.thumbnailsResizing[defaultIdx] || '';
      }

      return get(richMenus[defaultIdx], ['uploadImageUrl'], '');
    }
    return '';
  }, [isRichMenu, richMenus, state.thumbnailsResizing]);

  const templateNames = useMemo(() => {
    if (templateItems && isRichMenu) {
      return templateItems.map(({ id, label }) => ({ id, label }));
    }

    return [];
  }, [templateItems, isRichMenu]);

  // Effects
  useEffect(() => {
    form.setFieldsValue({ name: name || '' });
  }, [form, name]);

  useEffect(() => {
    if (isShowError) {
      form.validateFields();
    }
  }, [form, isShowError]);

  // Handlers
  const togglePreviewModal = (toggle?: boolean) => {
    setState({
      ...state,
      isOpenPreviewModal: toggle != null ? toggle : !state.isOpenPreviewModal,
    });
  };

  const toggleSaveAsModal = (toggle?: boolean) => {
    setSaveAsState(prev => ({
      ...prev,
      isOpenSaveAsModal: toggle != null ? toggle : !saveAsState.isOpenSaveAsModal,
    }));
  };

  const onChangeTemplateName = (newName: string) => {
    if (!newName) {
      messageApi.error(t(translations.messageError.fieldEmpty.message));
      form.setFieldsValue({
        name,
      });

      return;
    }

    if (newName && templateNameList?.length && templateNameList.includes(newName)) {
      messageApi.error(t(translations.messageError.nameExisted.message));
      form.setFieldsValue({
        name,
      });

      return;
    }

    dispatch(updateRichMenuTemplateName(newName));
  };

  const handleSave = tryCatchWrapper(() => {
    onSave && onSave(richMenuTemplate);
  }, PATH);

  // Post message to close button expand drawer to cdp app
  const handleToggleExpandDrawer = (newShow: boolean) => {
    window.parent.postMessage(
      {
        actionType: POST_MESSAGE_EVENT.TOGGLE_EXPAND_DRAWER,
        data: { isShow: newShow },
      },
      '*',
    );
  };

  const handleClickSaveAs = tryCatchWrapper((saveAsType: TSaveAsType) => {
    handleToggleExpandDrawer(false);

    // Post Message to starting capture to save thumbnail
    window.postMessage(
      {
        actionType: POST_MESSAGE_EVENT.STARTING_SAVE_TEMPLATE,
        data: null,
      },
      '*',
    );

    setSaveAsState(prev => ({
      ...prev,
      saveAsType,
      isLoadingCapture: true,
      isOpenSaveAsModal: true,
    }));
  }, PATH);

  const handleResetSaveAsTemplate = () => {
    setSaveAsState(initialSaveAsState());
    setState(prev => ({
      ...prev,
      thumbnailsResizing: [],
    }));
    handleToggleExpandDrawer(true);
  };

  const handleChangeModalSaveAsInfo = tryCatchWrapper(
    (templateSaveAs: Partial<TemplateValueOptions>, errors?: any[]) => {
      if (!has(templateSaveAs, 'templateName') && templateSaveAs.saveOption === 'new') {
        templateSaveAs.templateName = initialTemplateName();
      }

      if (templateSaveAs.accessInfo?.listAccess) {
        templateSaveAs.accessInfo = {
          ...templateSaveAs.accessInfo,
          listAccess: templateSaveAs.accessInfo.listAccess.map(user => ({
            ...user,
            role: +user.role === 2 ? 3 : user.role,
          })),
        };
      }

      setSaveAsState(prev => ({
        ...prev,
        ...templateSaveAs,
        errors: errors || [],
      }));
    },
    PATH,
  );

  const handleOkSaveAs = async () => {
    if (saveAsState.errors.length > 0) return;
    const isUpdate = saveAsState.saveOption === 'exist';

    if (!isUpdate || (isUpdate && saveAsState?.templateName?.id)) {
      const shareAccess = camelCaseToSnakeCase<TShareAccess>(saveAsState?.accessInfo as any, true);
      const richMenusClone = cloneDeep(richMenus);
      let defaultThumbnailClone = cloneDeep(defaultThumbnail);

      if (state.thumbnailsResizing.length) {
        // Upload thumbnails resized
        const thumbnailPromises = state.thumbnailsResizing.map(async (thumbnail, thumbnailIdx) => {
          if (thumbnail && typeof thumbnail === 'string' && thumbnail.startsWith('data:image')) {
            const savedThumbnail = await saveBase64File({
              data: thumbnail,
              name: `${richMenus[thumbnailIdx]?.id || ''}_${dayjs().format('YYYY-MM-DD_HH:mm:ss')}`,
            });

            return savedThumbnail.filePath || '';
          }

          return Promise.resolve(thumbnail);
        });

        const newThumbnails = await Promise.all(thumbnailPromises);

        // set new thumbnail into richMenus to api
        newThumbnails.forEach((thumbnail, thumbnailIdx) => {
          set(richMenusClone, `${thumbnailIdx}.uploadImageUrl`, thumbnail);
          const isDefaultRichMenu = get(richMenusClone, `${thumbnailIdx}.isDefault`);

          if (isRichMenu && isDefaultRichMenu) {
            defaultThumbnailClone = thumbnail;
          }
        });

        setState(prev => ({
          ...prev,
          thumbnailsResizing: newThumbnails,
        }));
      }

      try {
        await handleSaveAsTemplate({
          persistType: isUpdate ? 'update' : 'create',
          params: {
            auth,
            data: {
              template_id: isUpdate ? Number(saveAsState?.templateName?.id) : undefined,
              template_name: saveAsState.templateName?.label,
              thumbnail: defaultThumbnailClone,
              object_type: OBJECT_TYPES.RICH_MENU_TEMPLATE,
              public_level:
                saveAsState.saveAsType === 'galleryTemplate' ? PUBLIC_LEVEL.PUBLIC : PUBLIC_LEVEL.RESTRICTED,
              description: saveAsState.description,
              properties: {
                mode,
                richMenus: richMenusClone,
              },
              share_access: saveAsState.saveAsType === 'myTemplate' ? shareAccess : undefined,
            },
          },
        });
      } catch (err) {
        if (err) {
          const code = get(err, 'response.data.code', '');

          if (code < 200 || code > 300) {
            const errMessage = get(err, 'response.data.data', '');
            messageApi.error({
              type: 'error',
              content: errMessage,
            });

            return;
          }
        }
      }
    }

    setSaveAsState(prev => {
      return {
        ...prev,
        isOpenSaveAsModal: false,
      };
    });
    handleResetSaveAsTemplate();
  };

  const handleCallbackCapture = tryCatchWrapper((type: 'FINISH_CAPTURE') => {
    if (type === 'FINISH_CAPTURE') {
      setSaveAsState(prev => ({
        ...prev,
        isLoadingCapture: false,
      }));
    }
  }, PATH);

  useEffect(() => {
    if (
      isStale &&
      isRichMenu &&
      !isRefetching &&
      saveAsState.templateName?.id &&
      saveAsState.saveAsType === 'myTemplate'
    ) {
      refetchTemplate();
    }
  }, [saveAsState.templateName?.id, isRichMenu, isRefetching, refetchTemplate, isStale, saveAsState.saveAsType]);

  useEffect(() => {
    if (
      isRichMenu &&
      !isRefetching &&
      !isLoadingTemplateDetail &&
      !isEmpty(templateDetail) &&
      saveAsState.saveAsType === 'myTemplate'
    ) {
      const { shareAccess, description } = templateDetail as ObjectTemplate;
      const accessInfo = snakeCaseToCamelCase<ObjectAccessInfo>(shareAccess || {}, true);
      accessInfo.ownerId = String(accessInfo.ownerId) as any;

      setSaveAsState(prev => ({
        ...prev,
        description,
        accessInfo: {
          ...(prev.accessInfo || {}),
          ...(accessInfo || {}),
        },
      }));
    }
  }, [isLoadingTemplateDetail, isRefetching, isRichMenu, saveAsState.saveAsType, templateDetail]);

  useDeepCompareEffect(() => {
    if (
      isRichMenu &&
      !isEmpty(currentUserInfo) &&
      saveAsState.saveAsType === 'myTemplate' &&
      saveAsState.isOpenSaveAsModal
    ) {
      const userId = get(currentUserInfo, ['user_id'], '');

      setSaveAsState(prev => ({
        ...prev,
        userId: String(userId),
      }));
      const accessInfo = initAccessInfoDefault({ ...currentUserInfo, user_id: userId } as any);
      accessInfo.ownerId = String(accessInfo.ownerId) as any;

      if (saveAsState.saveOption === 'new') {
        setSaveAsState(prev => ({
          ...prev,
          accessInfo,
        }));
      }
    }
  }, [isRichMenu, currentUserInfo, saveAsState.isOpenSaveAsModal, saveAsState.saveAsType, saveAsState.saveOption]);

  useEffect(
    () => () => {
      if (isRichMenu) {
        handleResetSaveAsTemplate();
      }
    },
    [isRichMenu],
  );

  const renderMenuSaveAs = () => (
    <MenuWrapper>
      <MenuItem isPreHeading>{getTranslateMessage(translations.saveAs.title)}</MenuItem>
      <MenuItem
        onClick={() => {
          handleClickSaveAs('myTemplate');
        }}
      >
        {getTranslateMessage(translations.saveAs.myTemplate)}
      </MenuItem>
      {!isLoadingPermission && currentUserInfo?.email && userPermission?.includes(currentUserInfo?.email) && (
        <MenuItem
          onClick={() => {
            handleClickSaveAs('galleryTemplate');
          }}
        >
          {getTranslateMessage(translations.saveAs.galleryTemplate)}
        </MenuItem>
      )}
    </MenuWrapper>
  );

  const renderButtonSave = () => {
    if (isRichMenu) {
      return (
        <Fragment>
          <Dropdown.Button
            destroyPopupOnHide
            overlay={renderMenuSaveAs()}
            overlayStyle={{ borderRadius: '3px' }}
            type="primary"
            disabled={isLoadingSaveAs}
            overlayClassName="test"
            trigger={['click']}
            buttonsRender={buttonsNode => {
              buttonsNode[0] = (
                <ButtonSave isLeft type="primary" disabled={isLoadingSaveAs} onClick={handleSave}>
                  {saveText}
                </ButtonSave>
              );
              buttonsNode[1] = (
                <ButtonSave
                  isLeft={false}
                  type="primary"
                  disabled={isLoadingSaveAs || isLoadingPermission}
                  icon={<Icon type="icon-ants-expand-more" size={20} />}
                />
              );

              return buttonsNode;
            }}
          />
          <ModalSaveAs
            destroyOnClose
            width={saveAsState.saveAsType === 'galleryTemplate' ? '550px' : '1200px'}
            open={saveAsState.isOpenSaveAsModal}
            confirmLoading={isLoadingSaveAs}
            title={<span style={{ fontWeight: 400 }}>{getTranslateMessage(translations.saveAs.fullTitle)}</span>}
            okText={getTranslateMessage(translations.save.title)}
            getContainer={() => {
              const modalAccess = document.body.querySelector(
                '& > div.antsomi-modal-root > div.antsomi-modal-wrap',
              ) as HTMLDivElement;

              if (modalAccess) {
                modalAccess.style.setProperty('z-index', '1149', 'important'); // descrease the z-index to show dropdown accessInfo
              }
              return document.body;
            }}
            styles={{
              header: { padding: 15 },
              body: { padding: '0px 15px', borderTop: '1px solid #e5e5e5' },
              footer: { marginTop: 15 },
            }}
            onCancel={() => {
              toggleSaveAsModal();
            }}
            afterClose={() => {
              handleResetSaveAsTemplate();
            }}
            okButtonProps={{
              disabled: saveAsState.errors.length > 0 || saveAsState.isLoadingCapture,
              loading: isLoadingSaveAs,
            }}
            onOk={handleOkSaveAs}
          >
            <TemplateSaveAs
              form={formSaveAs}
              imageReview={{
                hideDefaultButton: true,
                previewNavigation: false,
                skeleton: false,
                label: 'Thumbnail',
                thumbnails,
                isLoading: saveAsState.isLoadingCapture,
                hideThumbnailsList: mode === 'single',
              }}
              onChange={handleChangeModalSaveAsInfo}
              saveOptions={{
                saveExistText: getTranslateMessage(translations.saveAs.existingRichMenu),
                saveExistValue: 'exist',
                saveNewText: getTranslateMessage(translations.saveAs.newRichMenu),
                saveNewValue: 'new',
              }}
              onSaveThumbnail={thumbnailsOut => {
                if (thumbnailsOut?.thumbnails?.length) {
                  setState(prev => ({
                    ...prev,
                    thumbnailsResizing: thumbnailsOut.thumbnails,
                  }));
                }
              }}
              templateNamesOptions={{
                onSearch: onSearchName,
                isLoading: isLoadingTemplateList,
                selectPlaceholder: 'Select a Rich Menu template',
                onNamePopupScroll: e => {
                  if (e.currentTarget) {
                    const { scrollHeight, scrollTop, clientHeight } = e.currentTarget;

                    if (scrollHeight === scrollTop + clientHeight) {
                      onLoadMoreTemplates();
                    }
                  }
                },
                defaultNewTemplateName: `Untitled Rich Menu Template ${dayjs().format(
                  DATE_TIME_FORMAT.TITLE_DATE_TIME,
                )}`,
              }}
              shareAccess={{
                getUserInfo: (search): any => permissionServices.getUserInfo(search, saveAsState.userId as string),
                show: saveAsState.saveAsType === 'myTemplate',
                userId: saveAsState.userId as any,
                userPermission: {
                  edit: MENU_PERMISSION.CREATED_BY_USER,
                  view: MENU_PERMISSION.CREATED_BY_USER,
                },
                placeholder: 'Add people',
                excludeUserAccess: [PEOPLE_ACTION_KEYS.TO_EDITOR],
                generalAccessSettings: {
                  publicOnlyWith: true,
                },
              }}
              descriptionOptions={{
                placeholder: getTranslateMessage(translations.saveAs.description),
              }}
              templateNames={templateNames}
              value={omit(saveAsState, ['isOpenSaveAsModal', 'userId', 'isLoadingCapture', 'saveAsType', 'errors'])}
            />
          </ModalSaveAs>
          {saveAsState.isLoadingCapture && (
            <CaptureBlock isLoading={false} onlyCapture callback={handleCallbackCapture} />
          )}
        </Fragment>
      );
    }

    return (
      <Button
        type="primary"
        // disabled={isSavingTemplate}
        onClick={handleSave}
        className="ants-relative"
      >
        {saveText}
      </Button>
    );
  };

  return (
    <>
      <DesignToolbar
        leftToolbar={
          isAdvImageMap ? (
            <Form form={form} validateMessages={{ required: t(translations.messageError.fieldEmpty.message) }}>
              <Space size={24}>
                <Text className="label-required">{labelName}</Text>

                <Form.Item name="name" noStyle>
                  <Input.DefaultInput
                    className="!ants-w-[270px]"
                    max={isAdvImageMap ? 400 : 300}
                    onBlur={({ target: { value } }) => onChangeTemplateName(value)}
                  />
                </Form.Item>
              </Space>
            </Form>
          ) : null
        }
        rightToolbar={
          <>
            {/* <Radio.Group value={mode} onChange={e => dispatch(updateMode({ mode: e.target.value }))}>
              <Radio value="single">{t(translations.single.title)}</Radio>
              <Radio value="multiple">{t(translations.multiple.title)}</Radio>
            </Radio.Group> */}

            {mode === 'single' && (
              <Button
                icon={<Icon type="icon-ants-restore" />}
                type="text"
                onClick={() => dispatch(resetLayout({ richMenuId: `${richMenu?.id}` }))}
              />
            )}

            <Divider type="vertical" dashed className="!ants-h-7" />

            {!isAdvImageMap && (
              <>
                <Button onClick={() => togglePreviewModal()}>
                  <Icon type="icon-ants-visibility" />
                  {t(translations.preview.title)}
                </Button>
                <Divider type="vertical" dashed className="!ants-h-7" />
              </>
            )}

            <Space size={0}>
              <Button
                icon={<Icon type="icon-ants-undo-2" />}
                type="text"
                disabled={undoAbleIndex === 0}
                onClick={() => dispatch(ActionCreators.undo())}
              />
              <Button
                icon={<Icon type="icon-ants-redo-2" />}
                type="text"
                disabled={undoAbleLimit ? undoAbleIndex === undoAbleLimit - 1 : false}
                onClick={() => dispatch(ActionCreators.redo())}
              />
            </Space>

            <Divider type="vertical" dashed className="!ants-h-7" />

            <Space size={10}>
              {renderButtonSave()}
              <Button type="default" onClick={() => onCancel && onCancel()}>
                {t(translations.cancel.title, 'Cancel')}
              </Button>
            </Space>
          </>
        }
      />

      {richMenu && (
        <RichMenuPreviewModal
          open={state.isOpenPreviewModal}
          richMenus={richMenus}
          mode={mode}
          onCancel={() => togglePreviewModal()}
        />
      )}

      {contextHolder}
    </>
  );
});

Toolbar.defaultProps = defaultProps;
