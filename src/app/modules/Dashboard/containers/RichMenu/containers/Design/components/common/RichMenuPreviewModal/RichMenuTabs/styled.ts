// Libraries
import { Flex, Pagination } from '@antscorp/antsomi-ui';

// Styled
import styled from 'styled-components';

export interface StyledRichMenuTabsProps {
  width?: number;
}

export const StyledRichMenuTabs = styled.div<StyledRichMenuTabsProps>`
  display: flex;
  align-items: center;
  background: #f0f0f0;
  box-shadow: 0px 3px 3px 0px rgba(0, 46, 89, 0.1);
  height: 40px;
  width: ${({ width }) => width || 467}px;

  .tabs {
    flex: 1;
    width: 100%;
    overflow: auto;

    &__item {
      flex-shrink: 0;
      height: 40px;
      width: 40px;
      border-right: 1px solid #d4d4d4;
      cursor: pointer;
      transition: all 200ms;

      &--active {
        background-color: #fff;

        .tabs__label {
          background-color: var(--primary-color);
          color: #fff;
        }
      }
    }

    &__label {
      height: 24px;
      width: 24px;
      font-weight: 700;
      font-size: 14px;
      color: var(--primary-color);
      border-radius: 100%;
      border: 1px solid #b8cfe6;
      background-color: #fff;
      transition: all 200ms;
    }
  }
`;

export const NavigationButton = styled(Flex)`
  width: 40px;
  height: 40px;
  background-color: #fff;
  cursor: pointer;

  i {
    font-size: 12px;
    color: #595959;
  }

  &.navigation--previous {
    border-right: 1px solid #d4d4d4;
  }

  &.navigation--next {
    border-left: 1px solid #d4d4d4;
  }

  &.navigation--disabled {
    cursor: not-allowed;

    i {
      color: #59595980;
    }
  }
`;

NavigationButton.defaultProps = {
  align: 'center',
  justify: 'center',
};
