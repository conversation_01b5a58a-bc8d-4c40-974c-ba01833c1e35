import { RichMenuBlock } from '@antscorp/antsomi-ui';
import type { CSSProperties, FC } from 'react';
import type { XYCoord } from 'react-dnd';
import { useDragLayer } from 'react-dnd';
import { useSelector } from 'react-redux';
import ItemSelector from '.';
import { selectSelectedRichMenu } from '../../../../../slice/selectors';
import { ChatBar } from '../../../ChatBar';
import { StyledRichMenu } from '../../styled';

const layerStyles: CSSProperties = {
  position: 'fixed',
  pointerEvents: 'none',
  zIndex: 99999,
  left: 0,
  top: 0,
  width: '100%',
  height: '100%',
};

function getItemStyles(initialOffset: XYCoord | null, currentOffset: XYCoord | null) {
  if (!initialOffset || !currentOffset) {
    return {
      display: 'none',
    };
  }

  let { x, y } = currentOffset;

  const transform = `translate(${x}px, ${y}px)`;
  return {
    transform,
    WebkitTransform: transform,
  };
}

export interface CustomDragLayerProps {}

export const CustomDragLayer: FC<CustomDragLayerProps> = () => {
  const { isDragging, initialOffset, currentOffset } = useDragLayer(monitor => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    initialOffset: monitor.getInitialSourceClientOffset(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging(),
  }));

  const richMenu = useSelector(selectSelectedRichMenu);

  if (!isDragging || !richMenu) {
    return null;
  }

  return (
    <div style={layerStyles}>
      <div style={getItemStyles(initialOffset, currentOffset)}>
        <ItemSelector
          callback={() => {}}
          length={1}
          key={richMenu.id}
          id={richMenu.id}
          left={1}
          top={1}
          isSelected={true}
          style={{ width: 'fit-content' }}
        >
          <StyledRichMenu onMouseDown={() => {}}>
            <RichMenuBlock richMenu={richMenu} />
            <ChatBar label={richMenu.chatBar.label} isSingleMode={false} />
          </StyledRichMenu>
        </ItemSelector>
      </div>
    </div>
  );
};
