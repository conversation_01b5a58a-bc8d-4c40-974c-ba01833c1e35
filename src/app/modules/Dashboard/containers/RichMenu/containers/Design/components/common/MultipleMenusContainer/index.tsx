import { FC, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDrop } from 'react-dnd';

// Types
import type { RichMenu } from 'richMenu/containers/Design/types';
import type { XYCoord } from 'react-dnd';

// Components
import { RichMenuBlock } from '../../Workspace/components/RichMenuBlock';
import { ChatBar } from '../ChatBar';
import ItemSelector, { CALLBACK_TYPE, DragItem } from './components/ItemSelector';

// Styles
import { StyledContainer, StyledRichMenu } from './styled';

// Selectors
import { selectSelectedRichMenu } from '../../../slice/selectors';

// Actions
import { richMenuDesignActions } from '../../../slice';

export { CALLBACK_TYPE } from './components/ItemSelector';

interface MultipleMenusContainerProps {
  richMenus: RichMenu[];
  callback: Function;
}

const MultipleMenusContainer: FC<MultipleMenusContainerProps> = ({ richMenus, callback }) => {
  const richMenuSelected = useSelector(selectSelectedRichMenu);

  // Actions
  const { updateRichMenuSettings } = richMenuDesignActions;

  const dispatch = useDispatch();

  const moveBox = useCallback(
    (id: string | number, left: number, top: number) => {
      dispatch(
        updateRichMenuSettings({
          id,
          data: {
            position: { left, top },
          },
        }),
      );
    },
    [dispatch, updateRichMenuSettings],
  );

  const [, drop] = useDrop(
    () => ({
      accept: 'box',
      drop(item: DragItem, monitor) {
        const delta = monitor.getDifferenceFromInitialOffset() as XYCoord;
        const left = Math.round(item.left + delta.x);
        const top = Math.round(item.top + delta.y);
        moveBox(item.id, left, top);
        return undefined;
      },
    }),
    [moveBox],
  );

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, richMenuId: string | number) => {
    e.stopPropagation();
    callback(CALLBACK_TYPE.SELECT, richMenuId);
  };

  return (
    <StyledContainer ref={drop}>
      {richMenus.map(richMenu => {
        const { left = 20, top = 50 } = richMenu.position || {};

        return (
          <ItemSelector
            key={richMenu.id}
            id={richMenu.id}
            length={richMenus.length}
            left={left}
            top={top}
            isSelected={richMenuSelected?.id === richMenu.id}
            style={{ width: 'fit-content' }}
            callback={(type: any) => callback(type, richMenu.id)}
          >
            <StyledRichMenu onMouseDown={e => handleMouseDown(e, richMenu.id)}>
              <RichMenuBlock richMenu={richMenu} />
              <ChatBar label={richMenu.chatBar.label} isSingleMode={false} />
            </StyledRichMenu>
          </ItemSelector>
        );
      })}
    </StyledContainer>
  );
};

export default MultipleMenusContainer;
