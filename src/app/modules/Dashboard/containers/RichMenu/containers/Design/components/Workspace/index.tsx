// Libraries
import React, { CSSProperties, useState, useEffect, useMemo } from 'react';
import { isEmpty } from 'lodash';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useOptimizeSelector } from 'store/configureStore';
import { RichMenuBlock, RichMenuMobileView } from '@antscorp/antsomi-ui';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Styles
import { DesignWorkspaceWrapper } from 'styles/global-styles';

// Components
import { ErrorAlert } from 'app/components/molecules';
import MultipleMenusContainer, { CALLBACK_TYPE } from '../common/MultipleMenusContainer';
import { RichMenuContainer } from './components/CaptureBlock/styled';

// Slices
import {
  selectGeneral,
  selectRichMenuDesign,
  selectRichMenus,
  selectSelectedRichMenu,
  selectMode,
  selectWorkspaceType,
} from '../../slice/selectors';
import { richMenuDesignActions } from '../../slice';

// Utils
import { tryCatchWrapper } from 'app/utils';
import { calculateHeightRatio, genAlertErrorMessages } from '../../utils';

// Types
import { RichMenu, TRichMenuMode } from '../../types';

// Constants
import { SIDE_PANEL_COLLAPSE_KEYS, WORKSPACE_DIMENSIONS, WORKSPACE_TYPE } from '../../constants';
import { CustomDragLayer } from '../common/MultipleMenusContainer/components/ItemSelector/CustomDragLayer';

interface WorkspaceProps {}

const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/Workspace/index.tsx';

export const containerImageMapStyles: CSSProperties = {
  width: WORKSPACE_DIMENSIONS[WORKSPACE_TYPE.ADVANCED_IMAGE_MAP].width,
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
};
const workspaceImageMapStyle: CSSProperties = {
  height: 'calc(100% - 20px)',
  margin: 'auto 0px',
};

export const Workspace: React.FC<WorkspaceProps> = () => {
  const [reCalculateFlag, setReCalculateFlag] = useState(0);

  const dispatch = useDispatch();
  const { sidePanel, general } = useOptimizeSelector(selectRichMenuDesign);
  const selectedRichMenu = useSelector(selectSelectedRichMenu);
  const { isShowError, errors } = useSelector(selectGeneral);
  const mode = useSelector(selectMode);
  const richMenus = useSelector(selectRichMenus);
  const workspaceType = useSelector(selectWorkspaceType);
  const location = useLocation();

  const {
    updateSidePanel,
    updateMode,
    setGeneral,
    updateRichMenuSettings,
    updateSelectedRichMenuId,
    resetLayout,
    duplicateRichMenu,
    removeRichMenu,
  } = richMenuDesignActions;

  const isSingleMode = mode === 'single';

  const layerActiveMemoized = useMemo(() => {
    if (!general.panelActiveKey) return 'all';

    if (([SIDE_PANEL_COLLAPSE_KEYS.IMAGE, SIDE_PANEL_COLLAPSE_KEYS.MENU_IMAGE] as any).includes(general.panelActiveKey))
      return 'image';

    if (([SIDE_PANEL_COLLAPSE_KEYS.ACTION] as any).includes(general.panelActiveKey)) return 'video';

    return 'all';
  }, [general.panelActiveKey]);

  // Handlers
  const onClickWorkspace = () => {
    if (sidePanel.selectedBlockType !== 'RICH_MENU') {
      dispatch(
        updateSidePanel({
          selectedBlockType: 'RICH_MENU',
        }),
      );
    }
  };

  const handleChangeRichMenu = (richMenu: RichMenu, source?: 'user' | 'system') => {
    const { id, ...restData } = richMenu;

    dispatch(
      updateRichMenuSettings({
        id: richMenu.id,
        data: restData,
        ignoreUndoAction: source === 'system',
      }),
    );
  };

  const onClickCell = (cellIdx: number) => {
    const { uploadMode } = selectedRichMenu || {};
    const isSingleUploadMode = uploadMode === 'single';

    dispatch(
      updateSidePanel({
        selectedCellIdx: cellIdx,
        selectedBlockType: isSingleUploadMode ? 'CELL_ACTION' : 'CELL_IMAGE',
      }),
    );

    if (['CELL_ACTION', 'CELL_IMAGE'].includes(sidePanel.selectedBlockType)) {
      dispatch(
        updateSidePanel({
          selectedBlockType: (sidePanel.selectedBlockType === 'CELL_IMAGE' ? 'RE_CELL_IMAGE' : 'RE_CELL_ACTION') as any, // Used to re-open action setting side panel
        }),
      );
    }
  };

  const callbackMultiple = (type: string, richMenuId: string, position: { x: number; y: number }) => {
    let action: any;

    switch (type) {
      case CALLBACK_TYPE.SELECT:
        action = updateSelectedRichMenuId({
          richMenuId,
        });
        break;
      case CALLBACK_TYPE.RESET_LAYOUT:
        action = resetLayout({
          richMenuId,
        });
        break;
      case CALLBACK_TYPE.DUPLICATE:
        action = duplicateRichMenu({
          richMenuId,
        });
        break;
      case CALLBACK_TYPE.REMOVE:
        action = removeRichMenu({
          richMenuId,
        });
        break;
      case CALLBACK_TYPE.SET_POSITION:
        action = updateRichMenuSettings({
          id: richMenuId,
          data: position,
        });
        break;
    }

    if (action) {
      dispatch(action);
    }
  };

  useEffect(() => {
    if (['RE_CELL_IMAGE', 'RE_CELL_ACTION'].includes(sidePanel.selectedBlockType)) {
      dispatch(
        updateSidePanel({
          selectedBlockType: (sidePanel.selectedBlockType as any) === 'RE_CELL_IMAGE' ? 'CELL_IMAGE' : 'CELL_ACTION',
        }),
      );
    }
  }, [dispatch, sidePanel.selectedBlockType, updateSidePanel]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const modeParams = searchParams.get('mode');

    if (modeParams && ['single', 'multiple'].includes(modeParams)) {
      dispatch(updateMode({ mode: modeParams as TRichMenuMode }));
    }
  }, [dispatch, location, updateMode]);

  useDeepCompareEffect(() => {
    const isAdvancedImageMap = workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP;

    if (selectedRichMenu && isAdvancedImageMap) {
      const {
        uploadMode,
        image: { imageUrl },
      } = selectedRichMenu;

      if (uploadMode === 'single' && imageUrl) {
        const loadImage = async () => {
          const img = new Image();
          img.src = imageUrl;

          img.onload = () => {
            const { width, height } = img;
            const fixedWidth = WORKSPACE_DIMENSIONS[WORKSPACE_TYPE.ADVANCED_IMAGE_MAP].width;
            const newHeight = calculateHeightRatio({ width, height, fixedWidth });

            dispatch(
              setGeneral({
                dimensions: {
                  width: WORKSPACE_DIMENSIONS[WORKSPACE_TYPE.ADVANCED_IMAGE_MAP].width,
                  height: newHeight,
                },
              }),
            );
          };
        };

        loadImage();
      } else {
        dispatch(setGeneral({ dimensions: WORKSPACE_DIMENSIONS[WORKSPACE_TYPE.ADVANCED_IMAGE_MAP] }));
      }

      setReCalculateFlag(prev => prev + 1);
    }
  }, [selectedRichMenu?.uploadMode, selectedRichMenu?.image.imageUrl, dispatch, workspaceType]);

  const renderMainWorkspace = () => {
    if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
      if (!selectedRichMenu) return null;

      return (
        <RichMenuContainer
          height={general.dimensions.height}
          aspectRatio={general.dimensions.width / general.dimensions.height}
          style={containerImageMapStyles}
        >
          <RichMenuBlock
            richMenu={selectedRichMenu}
            layerActive={layerActiveMemoized}
            reCalculateFlag={reCalculateFlag}
            cellActiveIdx={sidePanel.selectedCellIdx}
            onClickCell={onClickCell}
            onChange={handleChangeRichMenu}
          />
        </RichMenuContainer>
      );
    }

    return isSingleMode ? (
      selectedRichMenu && (
        <RichMenuMobileView
          className="ants-mt-8"
          richMenu={selectedRichMenu as any}
          onChangeRichMenu={handleChangeRichMenu}
          onClickCell={onClickCell}
        />
      )
    ) : (
      <DndProvider backend={HTML5Backend}>
        <MultipleMenusContainer richMenus={richMenus} callback={callbackMultiple} />
        <CustomDragLayer />
      </DndProvider>
    );
  };

  return (
    <DesignWorkspaceWrapper
      style={workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP ? workspaceImageMapStyle : {}}
      onClick={() => tryCatchWrapper(onClickWorkspace, PATH)()}
    >
      <ErrorAlert
        visible={isShowError}
        content={genAlertErrorMessages(errors, workspaceType).map((error, index) => (
          <div key={index}>{error}</div>
        ))}
        onClose={() => {
          dispatch(
            setGeneral({
              isShowError: false,
            }),
          );
        }}
      />

      {!isEmpty(mode) && renderMainWorkspace()}
    </DesignWorkspaceWrapper>
  );
};
