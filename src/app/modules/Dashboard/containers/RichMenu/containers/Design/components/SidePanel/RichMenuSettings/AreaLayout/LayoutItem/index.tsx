// Libraries
import React from 'react';

// Types
import { AreaLayout, WorkspaceType } from 'richMenu/containers/Design/types';
import { AreaLayoutCell, AreaLayoutItem } from './styled';

interface LayoutItemProps extends React.HTMLAttributes<HTMLDivElement> {
  areaLayout: AreaLayout;
  workspaceType?: WorkspaceType;
  selected?: boolean;
}

export const LayoutItem: React.FC<LayoutItemProps> = props => {
  const { areaLayout, workspaceType, selected, ...rest } = props;
  const { gridTemplateCols, gridTemplateRows, layoutType, cells } = areaLayout || {};

  return (
    <AreaLayoutItem
      $gridTemplateCols={gridTemplateCols}
      $gridTemplateRows={gridTemplateRows}
      $isCompact={layoutType === 'compact'}
      $workspaceType={workspaceType}
      $selected={selected}
      {...rest}
    >
      {cells?.map((cel: any, index: number) => (
        <AreaLayoutCell
          key={index}
          $selected={selected}
          style={{
            gridColumnStart: cel.colStart,
            gridColumnEnd: cel.colEnd,
            gridRowStart: cel.rowStart,
            gridRowEnd: cel.rowEnd,
          }}
        />
      ))}
    </AreaLayoutItem>
  );
};
