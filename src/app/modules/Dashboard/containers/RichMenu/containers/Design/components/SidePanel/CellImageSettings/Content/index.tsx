// Libraries
import { Collapse } from '@antscorp/antsomi-ui';
import React, { memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'richMenu/containers/Design/constants';

// Selectors
import { selectSelectedRichMenuCell } from 'richMenu/containers/Design/slice/selectors';
import { richMenuDesignActions } from 'richMenu/containers/Design/slice';

// Components
import { ImageSection } from './ImageSection';

// Types
import { PanelActiveType, TChildrenCell } from 'richMenu/containers/Design/types';

// Utils
import { tryCatchWrapper } from 'app/utils';
import { ActionSection } from './ActionSection';

interface ContentProps {}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/CellImageSettings/Content/index.tsx';

const { IMAGE, ACTION } = SIDE_PANEL_COLLAPSE;

export const Content: React.FC<ContentProps> = memo(() => {
  const dispatch = useDispatch();
  const selectedCell = useSelector(selectSelectedRichMenuCell);
  const { updateSelectedRichMenuCell, updateSidePanelKey } = richMenuDesignActions;

  if (!selectedCell) {
    return null;
  }

  const handleUpdateCell = tryCatchWrapper((data: Partial<TChildrenCell>) => {
    dispatch(updateSelectedRichMenuCell(data));
  }, PATH);

  const settingCollapses = [
    {
      ...IMAGE,
      children: <ImageSection cell={selectedCell} onChange={handleUpdateCell} />,
    },
    {
      ...ACTION,
      children: <ActionSection cell={selectedCell} onChange={handleUpdateCell} />,
    },
  ];

  return (
    <Collapse
      destroyInactivePanel
      accordion
      defaultActiveKey={IMAGE.key}
      onChange={keys => {
        dispatch(updateSidePanelKey({ panelKey: (Array.isArray(keys) ? keys[0] : keys) as PanelActiveType }));
      }}
      items={settingCollapses}
      bordered={false}
      expandIconPosition="right"
    />
  );
});
