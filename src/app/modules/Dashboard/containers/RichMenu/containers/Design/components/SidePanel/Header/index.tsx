// Libraries
import React, { memo } from 'react';
import { StyledHeader } from './styled';
import { Icon, Typography } from '@antscorp/antsomi-ui';

interface HeaderProps {
  icon?: string | React.ReactNode;
  title?: string;
}

const defaultProps: HeaderProps = {
  icon: 'icon-ants-bars',
  title: 'Title',
};

export const Header: React.FC<HeaderProps> = memo(props => {
  const { icon, title } = props;

  return (
    <StyledHeader>
      {typeof icon === 'string' ? <Icon type={icon} /> : icon}
      <Typography.Text className="!ants-text-base">{title}</Typography.Text>
    </StyledHeader>
  );
});

Header.displayName = 'SidePanelHeader';
Header.defaultProps = defaultProps;
