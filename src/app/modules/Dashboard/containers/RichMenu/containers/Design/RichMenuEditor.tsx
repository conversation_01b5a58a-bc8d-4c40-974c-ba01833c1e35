// Libraries
import { useDispatch, useSelector } from 'react-redux';
import React, { useCallback, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// Components
import { DesignEditor } from 'app/components/organisms';
import { Toolbar } from './components/Toolbar';
import { Workspace } from './components/Workspace';
import { SidePanel } from './components/SidePanel';
import { LeftSidePanel } from './components/LeftSidePanel';
import { CaptureBlock } from './components/Workspace/components/CaptureBlock';

// Selectors
import { useRichMenuDesignSlice } from './slice';
import { selectGeneral, selectRichMenuDesign, selectSidePanel, selectWorkspaceType } from './slice/selectors';

// Utils
import { tryCatchWrapper } from 'app/utils';
import { ActionCreators } from 'app/libs/redux-undo';
import { mapSettingsToRichMenuAPI, validateRichImageMapTemplate, validateRichMenuTemplate } from './utils';

// Types
import { ExternalConfig, RichMenu, RichMenuTemplate } from './types';
import { TMessageEventPayload } from 'types';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Constants
import { LAYOUT_SIZE_GRID, LAYOUT_SIZE_GRID_SQUARE, WORKSPACE_TYPE } from './constants';
import { useGetNetworkInfo } from 'app/queries/Network';

// Constants
import { APP_CONFIG } from 'constants/appConfig';
import { POST_MESSAGE_EVENT } from 'constants/variables';

interface RichMenuEditorProps {
  isEmbed?: boolean;
  isDefaultRichMenu?: boolean;
  richMenuTemplate?: RichMenuTemplate;
  externalConfig?: ExternalConfig;
  onCancel?: () => void;
  onSave?: (richMenuTemplate: RichMenuTemplate) => void;
}

const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Design/RichMenuEditor.tsx';

export const RichMenuEditor: React.FC<RichMenuEditorProps> = props => {
  const { isEmbed, externalConfig, isDefaultRichMenu, onCancel, onSave } = props;
  const dispatch = useDispatch();

  const location = useLocation();
  // Queries
  const { data: networkInfo } = useGetNetworkInfo({
    appId: APP_CONFIG.NETWORK_ID,
  });

  const { actions } = useRichMenuDesignSlice();
  const { richMenuTemplate, general } = useSelector(selectRichMenuDesign);
  const { isShowError } = useSelector(selectGeneral);
  const { isSaving } = useSelector(selectSidePanel);
  const { mode } = richMenuTemplate;
  const workspaceType = useSelector(selectWorkspaceType);

  const { updateRichMenuTemplate, updateStatusSaving } = actions;

  // Effects
  useEffect(() => {
    window.addEventListener('keydown', handleUndoRedoMediaTemplate);
    window.addEventListener('message', handlePostMessage);

    return () => {
      dispatch(ActionCreators.clearHistory());

      window.removeEventListener('keydown', handleUndoRedoMediaTemplate);
      window.removeEventListener('message', handlePostMessage);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useDeepCompareEffect(() => {
    if (props.richMenuTemplate) {
      dispatch(actions.setRichMenuTemplate(props.richMenuTemplate));
    }
  }, [props.richMenuTemplate]);

  useDeepCompareEffect(() => {
    if (typeof isDefaultRichMenu === 'boolean') {
      dispatch(actions.setGeneral({ isDefaultRichMenu }));
    }
  }, [isDefaultRichMenu]);

  useDeepCompareEffect(() => {
    if (externalConfig) {
      dispatch(actions.setExternalConfig(externalConfig));
    }
  }, [externalConfig]);

  useEffect(() => {
    dispatch(actions.setGeneral({ isEmbed: !!isEmbed }));
  }, [actions, dispatch, isEmbed]);

  useEffect(() => {
    const regexPattern = /\/advanced-imagemap\b/;
    const isAdvancedImageMap = regexPattern.test(location.pathname);

    if (isAdvancedImageMap) {
      dispatch(actions.switchToAdvancedImageMap());
    }
  }, [location, dispatch, actions]);

  // Handlers
  const handleValidate = useCallback(async () => {
    let errors: any;
    let isShowError: boolean = false;

    if (workspaceType === WORKSPACE_TYPE.RICH_MENU) {
      ({ errors, isShowError } = await validateRichMenuTemplate(richMenuTemplate, externalConfig));
    } else if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
      ({ errors, isShowError } = validateRichImageMapTemplate(richMenuTemplate));
    }

    dispatch(
      actions.setGeneral({
        errors,
        isShowError,
      }),
    );

    return {
      errors,
      isShowError,
    };
  }, [actions, dispatch, externalConfig, richMenuTemplate, workspaceType]);

  const handlePostMessage = (event: MessageEvent<TMessageEventPayload<RichMenuTemplate>>) => {
    const { actionType, data: richMenuTemplate } = event.data || {};

    switch (actionType) {
      case 'finishCapture':
        onSave && onSave(richMenuTemplate);
        break;

      default:
        break;
    }
  };

  const handleUndoRedoMediaTemplate = tryCatchWrapper((event: KeyboardEvent) => {
    if (event.ctrlKey && event.key === 'z') {
      dispatch(ActionCreators.undo());
    } else if (event.ctrlKey && event.key === 'y') {
      dispatch(ActionCreators.redo());
    }
  }, PATH);

  const handleSave = tryCatchWrapper(async (richMenuTemplate: RichMenuTemplate) => {
    if (richMenuTemplate) {
      const { richMenus } = richMenuTemplate;

      // Handle validate error message
      const { isShowError } = await handleValidate();

      if (!isShowError) {
        let layoutSize = LAYOUT_SIZE_GRID;
        const isAdvancedImageMap = workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP;
        if (isAdvancedImageMap) {
          layoutSize = LAYOUT_SIZE_GRID_SQUARE;
        }

        const newRichMenuList: RichMenu[] = mapSettingsToRichMenuAPI(richMenus, networkInfo, layoutSize, general);

        dispatch(updateRichMenuTemplate({ richMenus: newRichMenuList }));
        dispatch(updateStatusSaving({ isSaving: true }));

        // Post Message to starting capture to save template
        window.postMessage(
          {
            actionType: POST_MESSAGE_EVENT.STARTING_SAVE_TEMPLATE,
            data: null,
          },
          '*',
        );
      }
    }
  }, PATH);

  useEffect(() => {
    return () => {
      dispatch(actions.resetRichMenuDesign());
    };
  }, [actions, dispatch]);

  // Validate error when isShowError equal true and richMenuTemplate changed
  useEffect(() => {
    if (isShowError) {
      handleValidate();
    }
  }, [handleValidate, isShowError, richMenuTemplate]);

  return (
    <DesignEditor
      toolbar={<Toolbar onCancel={onCancel} onSave={handleSave} />}
      main={
        <>
          {isSaving && <CaptureBlock isLoading={isSaving} />}
          {mode === 'multiple' && <LeftSidePanel />}
          <Workspace />
          <SidePanel />
        </>
      }
    />
  );
};
