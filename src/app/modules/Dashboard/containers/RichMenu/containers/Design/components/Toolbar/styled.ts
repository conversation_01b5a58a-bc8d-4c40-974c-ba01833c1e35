// Libraries
import styled, { css } from 'styled-components';

// Components
import { Button, ModalV2, TemplateSaveAs as TemplateSave } from '@antscorp/antsomi-ui';

export const ButtonSave = styled(Button)<{ isLeft?: boolean }>`
  ${props =>
    props.isLeft
      ? css`
          border-top-right-radius: 0px !important;
          border-bottom-right-radius: 0px !important;
          padding-right: 5px !important;
        `
      : css`
          border-top-left-radius: 0px !important;
          border-bottom-left-radius: 0px !important;
          padding: 4px 5px !important;
        `}

  overflow: hidden !important;
`;

export const MenuWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px 0px;
  border-radius: 10px;
  width: 168px;
  background-color: #ffffff;
  box-shadow: 0px 3px 20px 0px rgba(0, 46, 89, 0.2);
`;

export const MenuItem = styled.div<{ isPreHeading?: boolean }>`
  width: 100%;
  font-weight: 400;
  padding: 8px 10px;

  ${props =>
    props.isPreHeading
      ? css`
          font-size: 11px;
          color: rgba(127, 127, 127, 1);
          user-select: none;
        `
      : css`
          font-style: normal;
          font-size: 12px;
          color: rgba(0, 0, 0, 1);

          &:hover {
            background-color: rgba(242, 249, 255, 1);
            transition: background-color 0.3s ease-in-out;
            cursor: pointer;
          }
        `}
`;

export const ModalSaveAs = styled(ModalV2)``;

export const TemplateSaveAs = styled(TemplateSave)`
  padding: 0px;

  .template-container {
    flex-grow: unset;
  }

  .share-access-container {
    width: unset;
    flex: 1;
  }
`;
