import { Flex } from '@antscorp/antsomi-ui';
import styled, { css } from 'styled-components';

export const StyledRichMenuItem = styled.div<{ $isActive: boolean; $isCollapsed: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 5px;
  cursor: pointer;

  ${p =>
    p.$isActive &&
    css`
      background-color: var(--blue-4-color);
      font-weight: bold;

      .antsomi-typography,
      i {
        color: var(--primary-color);
      }
    `}

  ${p =>
    p.$isCollapsed &&
    css`
      justify-content: center;
    `}

  &:hover {
    background-color: var(--blue-4-color);
  }
`;

export const Actions = styled(Flex)`
  padding-left: 10px;
  i {
    color: #595959;
  }
`;
