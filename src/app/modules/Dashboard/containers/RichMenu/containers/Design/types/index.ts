// Constants
import {
  AREA_LAYOUT_KEYS,
  AREA_LAYOUT_KEYS_ADV_IMAGE_MAP,
  BLOCK_KEYS,
  SIDE_PANEL_COLLAPSE_KEYS,
  WORKSPACE_TYPE,
} from '../constants';

// Types
import { TAlign } from 'types';

/* --- STATE --- */
export type TRichMenuMode = 'single' | 'multiple';

export type TUPloadMode = 'single' | 'multiple';

export type TAreaLayoutType = 'large' | 'compact';

export type TActionType = 'none' | 'message' | 'uri' | 'postback' | 'richmenuswitch' | 'datetimepicker';
export type TActionTypeADVImageMap = 'none' | 'message' | 'url' | 'video';

export type AreaLayoutKey = typeof AREA_LAYOUT_KEYS[keyof typeof AREA_LAYOUT_KEYS];

export type AreaLayoutKeyAdvImageMap =
  typeof AREA_LAYOUT_KEYS_ADV_IMAGE_MAP[keyof typeof AREA_LAYOUT_KEYS_ADV_IMAGE_MAP];

export type BlockKey = typeof BLOCK_KEYS[keyof typeof BLOCK_KEYS];

export type TActionMessageSettings = {
  text: string;
};
export type TActionUriSettings = {
  uriType: 'url' | 'tel';
  uri: string | number;
};
export type TActionUrlSettings = {
  url: string;
};
export type TActionVideoSettings =
  | {
      videoUrl: string;
      previewImage: string;
      isAddLabel: false;
    }
  | {
      videoUrl: string;
      previewImage: string;
      isAddLabel: true;
      label: string;
      externalUrl: string;
    };
export type TActionPostbackSettings = {
  data: string | number;
};
export type TActionRichmenuSwitchSettings = {
  richMenuAliasId: string | number;
  data: string | number;
};
export type TActionDateTimePickerSettings = {
  data: string;
  mode: 'date' | 'time' | 'datetime';
  min: string | { date: string; time: string };
  max: string | { date: string; time: string };
  initial: string | { date: string; time: string };
};

export type TAction =
  | { type: 'none' }
  | ({ type: 'message' } & TActionMessageSettings)
  | ({ type: 'uri' } & TActionUriSettings)
  | ({ type: 'postback' } & TActionPostbackSettings)
  | ({ type: 'richmenuswitch' } & TActionRichmenuSwitchSettings)
  | ({ type: 'datetimepicker' } & TActionDateTimePickerSettings);

export type TActionImageMap =
  | { type: 'none' }
  | ({ type: 'message' } & TActionMessageSettings)
  | ({ type: 'url' } & TActionUrlSettings)
  | ({ type: 'video' } & TActionVideoSettings);

/* --- SETTINGS TYPES --- */
export type TImageStyles = {
  borderTopLeftRadius: string;
  borderTopRightRadius: string;
  borderBottomRightRadius: string;
  borderBottomLeftRadius: string;
  borderTopWidth: string;
  borderRightWidth: string;
  borderBottomWidth: string;
  borderLeftWidth: string;
  borderStyle: string;
  borderColor: string;
  boxShadow: string;
  opacity: number;
  width: string;
  height: string;
  objectPosition: string;
  objectFit: string;
  paddingTop: string;
  paddingRight: string;
  paddingBottom: string;
  paddingLeft: string;
};

export type TImageSettings = {
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedBorderRadiusInput: boolean;
  linkedBorderWidthInput: boolean;
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  boxShadowStyle: string;
  boxShadowColor: string;
  boxShadowBlur: string;
  boxShadowSpread: string;
  boxShadowHorizontal: string;
  boxShadowVertical: string;
  boxShadowInset: boolean;
  widthSuffix: string;
  heightSuffix: string;
};

export type TOuterContainerStyles = {
  textAlign: TAlign;
};

export type TAreaStyles = React.CSSProperties;

export type TAreaStylesSettings = {
  /* Border Radius */
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedBorderRadiusInput: boolean;

  /* Border Width */
  linkedBorderWidthInput: boolean;

  /* Spacing */
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  linkedMarginInput: boolean;
  marginSuffix: string;

  /* Box Shadow */
  boxShadowStyle: string;
  boxShadowColor: string;
  boxShadowBlur: string;
  boxShadowSpread: string;
  boxShadowHorizontal: string;
  boxShadowVertical: string;
  boxShadowInset: boolean;

  /* Background */
  backgroundColor: string;
  backgroundColorStyle: string;
  backgroundPosition: string;
  backgroundRepeat: string;
  backgroundSize: string;
  gradientType: string;
  radialPosition: string;
  linearAngle: number;
  gradients: {
    gradientColor: string;
    gradientColorLocation: number;
  }[];
};

export type TImage = {
  imageUrl: string;
  imageStyles: TImageStyles;
  imageSettings: TImageSettings;
};

export type CellBounds = {
  x: number;
  y: number;
  width: number;
  height: number;
};

export type CellArea = {
  bounds: CellBounds;
  action: TAction;
};

export interface RichMenuSettings {
  size: {
    width: number;
    height: number;
  };
  selected: boolean;
  name: string;
  chatBarText: string;
  areas: CellArea[];
}

export interface Position {
  left: number;
  top: number;
}

export interface RichMenu {
  id: number | string;
  menuName?: string;
  aliasId?: number | string;
  isDefault: boolean;
  rows: number;
  cols: number;
  cells: TChildrenCell[];
  areaLayoutId?: AreaLayoutKey;
  position?: Position;
  gridTemplateRows: (number | string)[];
  gridTemplateCols: (number | string)[];
  layoutType: TAreaLayoutType;
  uploadMode: TUPloadMode;
  image: TImage;
  chatBar: {
    label: string;
    displayDefault: boolean;
  };
  settings?: RichMenuSettings; // Property used to pass in body to call api in cdp
  x?: number;
  y?: number;
}

export interface RichMenuTemplate {
  name: string;
  id: number | string;
  mode: TRichMenuMode;
  richMenus: RichMenu[];
}

export type TChildrenCell = {
  rowStart: number;
  colStart: number;
  rowEnd: number;
  colEnd: number;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  action?: TAction | TActionImageMap;
  image: TImage;
  outerContainerStyles: TOuterContainerStyles;
  areaStyles: TAreaStyles;
  areaStylesSettings: TAreaStylesSettings;
};

export interface AreaLayout {
  key: any;
  name: string;
  rows: number;
  cols: number;
  layoutType: TAreaLayoutType;
  gridTemplateRows: (number | string)[];
  gridTemplateCols: (number | string)[];
  cells: TChildrenCell[];
}

export type LayoutGridType = 'sm' | 'md' | 'lg' | 'square';
/*----------  Menu Errors  ----------*/
/**
 *
 */
export interface RichMenuErrors {
  templateName?: string;
  richMenus?: Record<string, Record<string, string>>;
  global: Record<string, string>;
}

export interface PostMessageRichMenuPayload {
  token: string;
  // List of rich menu template name of per destination
  templateNameList: string[];
  richMenuTemplate?: Partial<RichMenuTemplate>;
  isDefaultRichMenu?: boolean;
}

export interface ExternalConfig {
  token: string;
  templateNameList: string[];
}

type ValuesType<T> = T[keyof T];
export type WorkspaceType = ValuesType<typeof WORKSPACE_TYPE>;
export type PanelActiveType = ValuesType<typeof SIDE_PANEL_COLLAPSE_KEYS>;

export interface WorkspaceDimensions {
  width: number;
  height: number;
}

export interface General {
  isEmbed: boolean;
  isShowError: boolean;
  isDefaultRichMenu?: boolean;
  workspaceType: WorkspaceType;
  dimensions: WorkspaceDimensions;
  panelActiveKey: PanelActiveType;
  errors?: RichMenuErrors;
}
