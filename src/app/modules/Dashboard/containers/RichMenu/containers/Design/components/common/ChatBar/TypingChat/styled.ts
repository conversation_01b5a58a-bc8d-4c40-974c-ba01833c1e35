import styled from 'styled-components';

export const TypingInput = styled.input`
  width: 100%;
  border-radius: 18px;
  height: 28px;
  font-size: 15px;
  outline: none;
  padding: 0px 30px 0px 10px;

  &::placeholder {
    color: var(--accent-5);
  }
`;

export const TypingChatWrapper = styled.div`
  position: relative;
  flex: 1 1 0%;

  i {
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    right: 10px;
  }
`;
