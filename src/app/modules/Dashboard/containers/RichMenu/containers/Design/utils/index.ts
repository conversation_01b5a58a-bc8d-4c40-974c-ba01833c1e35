// Libraries
import isEqual from 'react-fast-compare';
import { pick } from 'lodash';
import dayjs, { isDayjs } from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

// Types
import type { Dayjs } from 'dayjs';
import {
  AreaLayout,
  CellArea,
  General,
  LayoutGridType,
  RichMenu,
  TAreaLayoutType,
  TChildrenCell,
  WorkspaceDimensions,
  WorkspaceType,
} from '../types';

// Constants
import {
  AREA_ACTION_DEFAULT,
  AREA_LAYOUTS,
  AREA_LAYOUTS_ADV_IMAGE_MAP,
  AREA_STYLES_DEFAULT,
  AREA_STYLES_SETTING_DEFAULT,
  IMAGE_SETTING_DEFAULT,
  LAYOUT_SIZE_GRID_SQUARE,
  OUTER_CONTAINER_STYLES_DEFAULT,
  WORKSPACE_TYPE,
} from '../constants';

// Utils
import { getNumberFromString, tryCatchWrapper } from 'app/utils';

dayjs.extend(customParseFormat);

const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Design/utils/index.ts';

/**
 * Generate a serialized version of the given aliasId by replacing all spaces with underscores.
 *
 * @param {string} aliasId - The original aliasId to be serialized.
 * @return {string | undefined} - The serialized version of the aliasId, or undefined if the serialized version contains invalid characters.
 */
export const serializedAliasId = (aliasId: string | number) => {
  const cloneValue = `${aliasId}`.replace(/\s/g, '-').replace(/[^a-z0-9_-]/g, '');

  return cloneValue;
};

/**
 * Generates an array of even cells based on the number of rows and columns provided.
 *
 * @param {number} rows - The number of rows.
 * @param {number} cols - The number of columns.
 * @param {TChildrenCell[]} cells - The Current Cells.
 * @return {TChildrenCell[]} An array of even cells.
 */
export const generateEvenCells = (rows: number, cols: number, cells?: TChildrenCell[]): TChildrenCell[] => {
  const evenCells: TChildrenCell[] = [];
  let index: number = 0;
  for (let i = 0; i < rows; i += 1) {
    for (let j = 0; j < cols; j += 1) {
      evenCells.push({
        ...(cells?.[index] || {
          action: AREA_ACTION_DEFAULT,
          image: IMAGE_SETTING_DEFAULT,
          outerContainerStyles: OUTER_CONTAINER_STYLES_DEFAULT,
          areaStyles: AREA_STYLES_DEFAULT,
          areaStylesSettings: AREA_STYLES_SETTING_DEFAULT,
        }),
        rowStart: i + 1,
        rowEnd: i + 2,
        colStart: j + 1,
        colEnd: j + 2,
      });

      index += 1;
    }
  }
  return evenCells;
};

/**
 * Handles the change of the suffix for a given value.
 *
 * @param {number|string} value - The value to be modified.
 * @param {string} suffix - The suffix to be added to the value.
 * @return {string} The modified value with the new suffix.
 */
export const handleValueWhenChangeSuffix = (value: number | string, suffix: string) => {
  let newValue = `${value}`;
  const serializedValue = getNumberFromString(`${value}`) || 100;

  newValue = serializedValue + suffix;

  if (suffix === '%' && serializedValue > 100) {
    newValue = '100';
  }

  if (suffix === 'auto') {
    newValue = 'auto';
  }

  return newValue;
};

export const getAreaLayoutBySettings = (
  settings: Pick<AreaLayout, 'rows' | 'cols' | 'layoutType' | 'gridTemplateCols' | 'gridTemplateRows' | 'cells'>,
  workspaceType: WorkspaceType,
) => {
  const isAdvancedImageMap = workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP;
  const { rows, cols, gridTemplateCols, gridTemplateRows, layoutType, cells } = settings;

  return Object.values(isAdvancedImageMap ? AREA_LAYOUTS_ADV_IMAGE_MAP : AREA_LAYOUTS).find(
    areaLayout =>
      areaLayout.rows === rows &&
      areaLayout.cols === cols &&
      areaLayout.layoutType === layoutType &&
      isEqual(gridTemplateCols, areaLayout.gridTemplateCols) &&
      isEqual(gridTemplateRows, areaLayout.gridTemplateRows),
  );
};

/**
 * Determines the width and height of an image layout based on the layout type and size.
 *
 * @param {TAreaLayoutType} layoutType - The type of layout (e.g., "compact", "regular").
 * @param {LayoutGridType} size - The size of the layout (e.g., "sm", "md", "lg").
 * @returns {object} An object containing the width and height of the image layout.
 */
export const getSizeImageLayout = (
  layoutType: TAreaLayoutType,
  size: LayoutGridType,
): { width: number; height: number } => {
  const isCompact = layoutType === 'compact';

  switch (size) {
    case 'sm': {
      if (isCompact)
        return {
          width: 800,
          height: 270,
        };

      return {
        width: 800,
        height: 540,
      };
    }
    case 'md': {
      if (isCompact)
        return {
          width: 1200,
          height: 405,
        };

      return {
        width: 1200,
        height: 810,
      };
    }
    case 'lg': {
      if (isCompact)
        return {
          width: 2500,
          height: 843,
        };

      return {
        width: 2500,
        height: 1686,
      };
    }
    case 'square': {
      return {
        width: 1040,
        height: 1040,
      };
    }
    default: {
      if (isCompact)
        return {
          width: 1200,
          height: 405,
        };

      return {
        width: 1200,
        height: 810,
      };
    }
  }
};

/**
 * Calculates the new height ratio based on the provided width, height, and fixed width.
 *
 * @param {Object} params - The parameters for the calculation.
 * @param {number} params.width - The original width of the image.
 * @param {number} params.height - The original height of the image.
 * @param {number} params.fixedWidth - The fixed width used for the calculation.
 * @returns {number} - The calculated new height ratio.
 */
export const calculateHeightRatio = ({
  width,
  height,
  fixedWidth,
}: {
  width: number;
  height: number;
  fixedWidth: number;
}) => {
  const aspectRatio = width / height;

  return fixedWidth / aspectRatio;
};

/**
 * Calculates and returns cell areas based on their position and size within a grid wrapper element.
 *
 * @param {TChildrenCell[]} cellList - An array of cell objects containing position and size information.
 * @param {layoutSize: { width: number; height: number }} layoutSize - The width and height of the layout.
 * @returns {CellArea[]} An array of cell area objects with calculated bounds.
 */
const getAreas = tryCatchWrapper(
  (cellList: TChildrenCell[], layoutSize: { width: number; height: number }): CellArea[] => {
    const gridWrapperElement = document.querySelector('.container__main .grid-wrapper');
    const { offsetWidth, offsetHeight } = gridWrapperElement as HTMLDivElement;
    const { width, height } = layoutSize;
    const ratioWidth = width / offsetWidth;
    const ratioHeight = height / offsetHeight;

    const areas = cellList.map(cell => {
      const { action, x = 0, y = 0, width = 0, height = 0 } = cell;
      return {
        action,
        bounds: {
          x: x * ratioWidth,
          y: y * ratioHeight,
          width: width * ratioWidth,
          height: height * ratioHeight,
        },
      };
    });

    return areas as CellArea[];
  },
  PATH,
);

/**
 * Retrieves the dimensions of a workspace image map based on the specified layout type and workspace dimensions.
 *
 * @param {TAreaLayoutType} layoutType - The type of layout for the workspace.
 * @param {WorkspaceDimensions} dimensions - The dimensions of the workspace.
 * @returns {{ width: number; height: number }} - The width and height of the workspace image map.
 * @throws {Error} - Throws an error if there is an issue in the underlying operations.
 */
export const getImageMapWorkspaceDimensions = tryCatchWrapper(
  (layoutType: TAreaLayoutType, dimensions: WorkspaceDimensions): { width: number; height: number } => {
    const { width, height } = dimensions;

    const size = getSizeImageLayout(layoutType, LAYOUT_SIZE_GRID_SQUARE);
    const newHeight = calculateHeightRatio({ width, height, fixedWidth: size.width });

    return { width: size.width, height: newHeight };
  },
  PATH,
);

/**
 * Transforms an array of rich menu objects into a format suitable for API interaction.
 *
 * @param {RichMenu[]} richMenuList - An array of rich menu objects.
 * @param {Record<string, any> | undefined} networkInfo - An object contain all information of portal
 * @param {LayoutGridType} layoutSize - A size of layout (e.g., "sm", "md", "lg", "square").
 * @param {General} general - An object containing general information of workspace.
 * @returns {RichMenu[]} An array of rich menu objects with the "settings" object populated for API interaction.
 */
export const mapSettingsToRichMenuAPI = (
  richMenuList: RichMenu[],
  networkInfo: Record<string, any> | undefined,
  layoutSize: LayoutGridType,
  general: General,
): RichMenu[] => {
  const result = richMenuList.map(richMenu => {
    const { workspaceType, dimensions } = general;
    const { chatBar, menuName, layoutType, cells } = richMenu;
    const { label, displayDefault } = chatBar;
    let size = getSizeImageLayout(layoutType, layoutSize);

    if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
      size = getImageMapWorkspaceDimensions(layoutType, dimensions);
    }
    const areas = getAreas(cells, size);

    return {
      ...richMenu,
      settings: {
        size,
        areas,
        timezone: networkInfo?.timezone,
        selected: displayDefault,
        name: menuName,
        chatBarText: label,
      },
    };
  });

  return result as RichMenu[];
};

/**
 * Converts a serialized date-time representation into Dayjs objects.
 *
 * @param dateTime - A string representing a date-time, an object with 'date' and 'time' properties,
 * or a Dayjs object.
 * @returns If `dateTime` is a string, returns a Dayjs object representing the parsed date-time.
 * If `dateTime` is an object with 'date' and/or 'time' properties, returns an object with the
 * same structure where the 'date' and 'time' properties are Dayjs objects.
 * If `dateTime` is already a Dayjs object, returns the original object.
 */
export const serializedDateTimeAction = (
  dateTime: string | { date: string | Dayjs; time: string | Dayjs } | Dayjs,
): Dayjs | { [s: string]: Dayjs } => {
  if (typeof dateTime === 'string') return dayjs(dateTime);
  if (typeof dateTime === 'object' && dateTime !== null && !isDayjs(dateTime)) {
    const keys = Object.keys(dateTime);
    const dataOut = {};

    keys.forEach(key => {
      if (['date', 'time'].includes(key)) {
        if (typeof dateTime[key] === 'string') {
          dataOut[key] = dayjs(dateTime[key]);
        } else {
          dataOut[key] = dateTime[key];
        }
      }
    });

    return dataOut;
  }

  return dateTime;
};

/* Validate Error */
export * from './validateError';
