// Libraries
import React, { memo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cn from 'classnames';

// Components
import { DesignLeftSidePanel } from 'app/components/organisms/DesignLeftSidePanel';
import { RichMenuItem } from './RichMenuItem';

//
import { selectRichMenus, selectSidePanel } from '../../slice/selectors';
import { useOptimizeSelector } from 'store/configureStore';
import { Divider, Flex } from '@antscorp/antsomi-ui';
import { AddNewMenuBtn } from './AddNewMenuBtn';
import { tryCatchWrapper } from 'app/utils';
import { richMenuDesignActions } from '../../slice';

interface LeftSidePanelProps {}

const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/LeftSidePanel/index.tsx';

export const LeftSidePanel: React.FC<LeftSidePanelProps> = memo(() => {
  const [isCollapsed, setCollapsed] = useState<boolean>(true);
  const dispatch = useDispatch();
  const richMenus = useSelector(selectRichMenus);
  const { selectedRichMenuId } = useOptimizeSelector(selectSidePanel);

  const { addNewRichMenu, updateSidePanel } = richMenuDesignActions;

  // Handlers
  const handleAddNewRichMenu = tryCatchWrapper(() => {
    dispatch(addNewRichMenu());
  }, PATH);

  const handleUpdateSelectedRichMenu = tryCatchWrapper((richMenuId: string | number) => {
    dispatch(
      updateSidePanel({
        selectedBlockType: 'RICH_MENU',
        selectedRichMenuId: richMenuId,
      }),
    );
  }, PATH);

  return (
    <DesignLeftSidePanel
      bodyClassName={cn({
        'scrollbar-hidden': isCollapsed,
      })}
      isCollapsed={isCollapsed}
      toggleCollapsed={() => setCollapsed(!isCollapsed)}
    >
      <div
        className={cn({
          'ants-w-10 ants-self-end': isCollapsed,
        })}
      >
        {richMenus.map((richMenu, index) => (
          <RichMenuItem
            key={richMenu.id}
            index={index}
            richMenu={richMenu}
            isActive={selectedRichMenuId === richMenu.id}
            isCollapsed={isCollapsed}
            onClick={() => handleUpdateSelectedRichMenu(richMenu.id)}
          />
        ))}

        <Divider className="!ants-mt-0" />

        <AddNewMenuBtn className="ants-pb-15px" isCollapsed={isCollapsed} onClick={handleAddNewRichMenu} />
      </div>
    </DesignLeftSidePanel>
  );
});
