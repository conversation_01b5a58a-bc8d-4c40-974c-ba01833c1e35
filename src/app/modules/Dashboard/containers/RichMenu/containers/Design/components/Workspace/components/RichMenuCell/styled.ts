// Libraries
import styled from 'styled-components';

export const CellContainer = styled.div`
  width: 100%;
  height: 100%;
  cursor: pointer;
`;

export const AddContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
  height: 100%;
  padding: 8px 0px;
  overflow: hidden;
`;

export const AddLabel = styled.span`
  width: 100%;
  padding: 0px 6px;

  font-size: 10px;
  font-weight: 400;
  color: #000000;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
  overflow: hidden;
`;

export const CellImage = styled.div<{ src: string; imagePosition: string; imageSize: string }>`
  min-height: 100%;
  background-image: url(${({ src }) => src});
  background-size: ${({ imageSize }) => imageSize};
  background-position: ${({ imagePosition }) => imagePosition};
`;
