// Libraries
import React from 'react';
import { Flex } from '@antscorp/antsomi-ui';
import { useTranslation } from 'react-i18next';

// Constants
import { translations } from 'locales/translations';

// Utils
import { tryCatchWrapper } from 'app/utils';

// Components
import { BorderSettingPopover } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BorderSetting';
import { BoxShadowSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BoxShadowSetting';
import { RoundedCornersSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/RoundedCornersSetting';
import { SpacingSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/SpacingSetting';
import { SliderWithInputNumber } from 'app/components/molecules';

// Utils
import {
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Types
import { TImageSettings, TImageStyles } from 'richMenu/containers/Design/types';

interface ImageAdvancedSettingProps {
  styles: TImageStyles;
  settings: TImageSettings;
  onChange: (settings: TImageSettings, styles: TImageStyles) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/components/ImageAdvancedSetting/index.tsx';

export const ImageAdvancedSetting: React.FC<ImageAdvancedSettingProps> = props => {
  const { styles, settings, onChange } = props;
  const { t } = useTranslation();

  // Handlers
  const handleChangeStylesAndSettings = tryCatchWrapper(
    (newSettings: Partial<TImageSettings>, newStyles: Partial<TImageStyles>) => {
      onChange({ ...settings, ...newSettings }, { ...styles, ...newStyles });
    },
    PATH,
  );

  return (
    <Flex vertical gap={20}>
      <BoxShadowSetting settings={getBoxShadowSettings(settings as any)} onChange={handleChangeStylesAndSettings} />
      <BorderSettingPopover
        settings={getBorderSettings(settings as any)}
        styles={getBorderStyles(styles as any)}
        onChange={handleChangeStylesAndSettings}
      />
      <RoundedCornersSetting
        settings={getRoundedCornersSettings(settings as any)}
        styles={getRoundedCornersStyles(styles as any)}
        onChange={handleChangeStylesAndSettings}
      />
      <SpacingSetting
        isMarginSetting={false}
        settings={getSpacingSettings(settings as any)}
        styles={getSpacingStyles(styles as any)}
        onChange={handleChangeStylesAndSettings}
      />
      <SliderWithInputNumber
        label={t(translations.opacity.title)}
        labelClassName="!ants-text-gray-4"
        min={0}
        max={100}
        value={styles.opacity * 100}
        onAfterChange={opacity => onChange(settings, { ...styles, opacity: opacity / 100 })}
      />
    </Flex>
  );
};
