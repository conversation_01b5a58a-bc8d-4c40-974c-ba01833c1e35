import { createSelector } from '@reduxjs/toolkit';

import { RootState } from 'types';
import { initialRichMenu } from '.';
import { StateWithHistory } from 'app/libs/redux-undo/typings';

const selectSlice = (state: RootState) =>
  (state.richMenuDesign || initialRichMenu) as any as StateWithHistory<typeof initialRichMenu>;

export const selectRichMenuDesign = createSelector([selectSlice], state => state.present);

export const selectRichMenuTemplate = createSelector([selectRichMenuDesign], state => state.richMenuTemplate);

export const selectSidePanel = createSelector([selectRichMenuDesign], state => state.sidePanel);

export const selectRichMenus = createSelector([selectRichMenuTemplate], state => state.richMenus || []);

export const selectMode = createSelector([selectRichMenuTemplate], state => state.mode || []);

export const selectSelectedRichMenu = createSelector([selectSidePanel, selectRichMenus], (sidePanel, richMenus) => {
  const { selectedRichMenuId } = sidePanel;

  return richMenus.find(richMenu => richMenu.id === selectedRichMenuId);
});

export const selectSelectedRichMenuCell = createSelector(
  [selectSidePanel, selectSelectedRichMenu],
  (sidePanel, selectedRichMenu) => {
    const { selectedCellIdx } = sidePanel;

    return selectedRichMenu?.cells[selectedCellIdx];
  },
);

export const selectGeneral = createSelector([selectRichMenuDesign], state => state.general);

export const selectWorkspaceType = createSelector([selectGeneral], state => state.workspaceType);

export const selectExternalConfig = createSelector([selectRichMenuDesign], state => state.externalConfig);

/* Undo able selectors */
export const selectUndoAbleIndex = createSelector([selectSlice], state => state.index);

export const selectUndoAbleLimit = createSelector([selectSlice], state => state.limit);
