// Libraries
import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Collapse } from '@antscorp/antsomi-ui';
import { pick } from 'lodash';
import { useOptimizeSelector } from 'store/configureStore';

// Components
import { MenuDetails } from './MenuDetails';
import { AreaLayout } from './AreaLayout';
import { ChatBar } from './ChatBar';
import { MenuImage } from './MenuImage';

// Translations
import { getTranslateMessage } from 'utils/messages';
import { translations } from 'locales/translations';

// Selectors
import {
  selectGeneral,
  selectRichMenuTemplate,
  selectSelectedRichMenu,
} from 'richMenu/containers/Design/slice/selectors';
import { richMenuDesignActions } from 'richMenu/containers/Design/slice';

// Types
import { PanelActiveType, RichMenu } from 'richMenu/containers/Design/types';

// Constants
import { SIDE_PANEL_COLLAPSE, WORKSPACE_TYPE } from 'richMenu/containers/Design/constants';

interface RichMenuSettingsProps {}

export const RichMenuSettings: React.FC<RichMenuSettingsProps> = () => {
  const dispatch = useDispatch();
  const selectedBlock = useSelector(selectSelectedRichMenu);
  const { workspaceType } = useSelector(selectGeneral);
  const { mode } = useOptimizeSelector(selectRichMenuTemplate);

  const { updateRichMenuSettings, updateSidePanelKey } = richMenuDesignActions;

  // Handlers
  const handleUpdateMenuSettings = useCallback(
    (data: Partial<RichMenu>) => {
      // Specific case for is default
      const { isDefault, ...restData } = data;

      if (selectedBlock) {
        dispatch(
          updateRichMenuSettings({
            id: selectedBlock.id,
            data,
          }),
        );

        // dispatch(updateRichMenuDefault({ richMenuId: selectedBlock.id, isDefault: !!isDefault }));
      }
    },
    [dispatch, selectedBlock, updateRichMenuSettings],
  );

  let labelMenuImage = SIDE_PANEL_COLLAPSE.MENU_IMAGE.label;
  if (workspaceType === WORKSPACE_TYPE.ADVANCED_IMAGE_MAP) {
    labelMenuImage = getTranslateMessage(translations.advancedImageMapSettings.menuImage);
  }

  // Memo
  const settingCollapses = useMemo(() => {
    let draftSettingsCollapses: any[] = [];

    if (selectedBlock) {
      if (mode === 'multiple') {
        draftSettingsCollapses.push({
          ...SIDE_PANEL_COLLAPSE.MENU_DETAILS,
          children: (
            <MenuDetails
              richMenu={selectedBlock}
              values={pick(selectedBlock, ['aliasId', 'menuName', 'isDefault'])}
              onChange={handleUpdateMenuSettings}
            />
          ),
        });
      }

      draftSettingsCollapses = draftSettingsCollapses.concat([
        {
          ...SIDE_PANEL_COLLAPSE.AREA_LAYOUT,
          children: (
            <AreaLayout workspaceType={workspaceType} richMenu={selectedBlock} onChange={handleUpdateMenuSettings} />
          ),
        },
        {
          ...SIDE_PANEL_COLLAPSE.MENU_IMAGE,
          label: labelMenuImage,
          children: <MenuImage richMenu={selectedBlock} onChange={handleUpdateMenuSettings} />,
        },
      ]);

      if (workspaceType === WORKSPACE_TYPE.RICH_MENU) {
        draftSettingsCollapses.push({
          ...SIDE_PANEL_COLLAPSE.CHAT_BAR,
          children: (
            <ChatBar
              values={pick(selectedBlock, ['chatBar']).chatBar}
              onChange={chatBar =>
                handleUpdateMenuSettings({
                  chatBar,
                })
              }
            />
          ),
        });
      }
    }

    return draftSettingsCollapses;
  }, [handleUpdateMenuSettings, labelMenuImage, mode, selectedBlock, workspaceType]);

  return (
    <Collapse
      accordion
      destroyInactivePanel
      onChange={keys => {
        dispatch(updateSidePanelKey({ panelKey: (Array.isArray(keys) ? keys[0] : keys) as PanelActiveType }));
      }}
      items={settingCollapses}
      bordered={false}
      expandIconPosition="right"
    />
  );
};
