// Libraries
import React from 'react';
import { Form, Input, SettingWrapper, Switch } from '@antscorp/antsomi-ui';
import { useTranslation } from 'react-i18next';
import isEqual from 'react-fast-compare';

// Locales
import { translations } from 'locales/translations';

// Components

// Hooks
import { useDebounce, useDeepCompareEffect } from 'app/hooks';
import { RichMenu } from 'richMenu/containers/Design/types';

type FieldType = RichMenu['chatBar'];

interface ChatBarProps {
  values: FieldType;
  onChange: (values: FieldType) => void;
}

const { Item, useForm } = Form;
const { DefaultInput } = Input;

export const ChatBar: React.FC<ChatBarProps> = props => {
  const { values, onChange } = props;
  const { t } = useTranslation();
  const [form] = useForm<FieldType>();

  // Watchers
  const watchedValues = Form.useWatch([], form);
  const debounceValues = useDebounce(watchedValues, 500);

  // Effects
  useDeepCompareEffect(() => {
    form.setFieldsValue(values);
  }, [values]);

  useDeepCompareEffect(() => {
    if (debounceValues && !isEqual(debounceValues, values)) {
      onChange(debounceValues);
    }
  }, [debounceValues]);

  return (
    <Form<FieldType>
      form={form}
      layout="vertical"
      validateMessages={{
        required: t(translations.messageError.fieldEmpty.message),
      }}
    >
      <Item<FieldType> name="label" label={t(translations.chatBar.menuBarLabel)} rules={[{ required: true }]}>
        <DefaultInput maxLength={14} />
      </Item>
      <SettingWrapper label={t(translations.chatBar.displayChatBarByDefault)} labelColor="#000">
        <Item<FieldType> name="displayDefault" noStyle valuePropName="checked">
          <Switch size="small" />
        </Item>
      </SettingWrapper>
    </Form>
  );
};
