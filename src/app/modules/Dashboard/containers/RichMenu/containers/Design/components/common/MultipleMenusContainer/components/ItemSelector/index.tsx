import React, { FC, useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { StyledItemSelctor, StyledSelectorTop } from './styled';
import { Icon } from '@antscorp/antsomi-ui';
import { getEmptyImage } from 'react-dnd-html5-backend';

export interface DragItem {
  type: string;
  id: string | number;
  top: number;
  left: number;
}

interface ItemSelectorProps extends Omit<DragItem, 'type'> {
  children: React.ReactNode;
  isSelected: boolean;
  length: number;
  style?: React.CSSProperties;
  callback: Function;
}

export const CALLBACK_TYPE = {
  SELECT: 'SELECT',
  RESET_LAYOUT: 'RESET_LAYOUT',
  DUPLICATE: 'DUPLICATE',
  REMOVE: 'REMOVE',
  SET_POSITION: 'SET_POSITION',
};

const ItemSelector: FC<ItemSelectorProps> = ({
  children,
  length,
  isSelected,
  style,
  id,
  top,
  left,
  callback,
  ...rest
}) => {
  const [{ isDragging }, drag, preview] = useDrag(
    () => ({
      type: 'box',
      item: { id, left, top },
      collect: monitor => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [id, left, top],
  );

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  return (
    <StyledItemSelctor
      style={{ ...style, left, top, height: isDragging ? 0 : '', opacity: isDragging ? 0 : 1 }}
      $isSelected={isSelected}
      {...rest}
    >
      <StyledSelectorTop
        style={{
          display: isSelected ? 'flex' : 'none',
          zIndex: 10,
        }}
      >
        <span ref={drag}>
          <Icon type="icon-ants-double-three-dots" />
        </span>
        <Icon type="icon-ants-restore" onClick={() => callback(CALLBACK_TYPE.RESET_LAYOUT)} />
        <Icon type="icon-ants-material-outline-content-copy" onClick={() => callback(CALLBACK_TYPE.DUPLICATE)} />
        <Icon type="icon-ants-delete" onClick={() => length > 1 && callback(CALLBACK_TYPE.REMOVE)} />
      </StyledSelectorTop>
      {children}
    </StyledItemSelctor>
  );
};

export default ItemSelector;
