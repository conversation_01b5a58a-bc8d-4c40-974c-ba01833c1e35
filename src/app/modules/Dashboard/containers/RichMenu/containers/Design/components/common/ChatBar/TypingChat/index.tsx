// Libraries
import { Flex, Icon } from '@antscorp/antsomi-ui';
import React, { memo } from 'react';

// Styled
import { TypingChatWrapper, TypingInput } from './styled';

interface TypingChatProps {
  onClickMenu?: () => void;
}

export const TypingChat: React.FC<TypingChatProps> = memo(props => {
  const { onClickMenu } = props;

  return (
    <Flex gap={8} justify="space-between" align="center" className="ants-w-full ants-h-fit ants-text-[#747474]">
      <Icon
        type="icon-ants-list-view-thumbnail"
        className="ants-cursor-pointer"
        onClick={() => onClickMenu && onClickMenu()}
      />
      <Icon type="icon-ants-angle-right" className="ants-px-4" size={14} />
      <TypingChatWrapper>
        <TypingInput className="ants-flex-1" placeholder="Aa" />
        <Icon type="icon-ants-smile-face" size={16} />
      </TypingChatWrapper>
      <Icon type="icon-ants-big-search" />
    </Flex>
  );
});
