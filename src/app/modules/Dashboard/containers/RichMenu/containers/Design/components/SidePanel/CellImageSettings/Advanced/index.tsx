// Libraries
import React, { memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Collapse } from '@antscorp/antsomi-ui';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'richMenu/containers/Design/constants';

// Components
import { ImageStyling } from './ImageStyling';
import { ContainerStyling } from 'richMenu/containers/Design/components/SidePanel/components/ContainerStyling';

// Selectors
import { selectSelectedRichMenuCell } from 'richMenu/containers/Design/slice/selectors';
import { richMenuDesignActions } from 'richMenu/containers/Design/slice';

// Utils
import { tryCatchWrapper } from 'app/utils';

// Types
import { TChildrenCell } from 'richMenu/containers/Design/types';

interface AdvancedProps {}

const { IMAGE_STYLING, CONTAINER_STYLING } = SIDE_PANEL_COLLAPSE;

const PATH =
  'src/app/modules/Dashboard/containers/RichMenu/containers/Design/components/SidePanel/CellImageSettings/Advanced/index.tsx';

export const Advanced: React.FC<AdvancedProps> = memo(() => {
  const dispatch = useDispatch();
  const selectedCell = useSelector(selectSelectedRichMenuCell);
  const { updateSelectedRichMenuCell } = richMenuDesignActions;

  if (!selectedCell) {
    return null;
  }

  const handleUpdateCell = tryCatchWrapper((data: Partial<TChildrenCell>) => {
    dispatch(updateSelectedRichMenuCell(data));
  }, PATH);

  const settingCollapses = [
    {
      ...IMAGE_STYLING,
      children: <ImageStyling cell={selectedCell} onChange={handleUpdateCell} />,
    },
    {
      ...CONTAINER_STYLING,
      children: (
        <ContainerStyling
          styles={selectedCell.areaStyles}
          stylesSettings={selectedCell.areaStylesSettings}
          onChange={(areaStylesSettings, areaStyles) => {
            handleUpdateCell({
              areaStyles,
              areaStylesSettings,
            });
          }}
        />
      ),
    },
  ];

  return (
    <Collapse
      defaultActiveKey={IMAGE_STYLING.key}
      accordion
      items={settingCollapses}
      bordered={false}
      expandIconPosition="right"
    />
  );
});
