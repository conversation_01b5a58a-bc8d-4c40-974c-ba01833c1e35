// Libraries
import React, { memo, useCallback, useEffect, useState } from 'react';

// Types
import { ExternalConfig, PostMessageRichMenuPayload, RichMenuTemplate } from '../Design/types';
import { TMessageEventPayload } from 'types';

// Slice
import { initialRichMenu, richMenuDesignActions } from '../Design/slice';

// Components
import { RichMenuEditor } from '../Design/RichMenuEditor';
import { tryCatchWrapper } from 'app/utils';

// Styles
import { EmbedWrapper } from 'styles/global-styles';

// Constants
import { POST_MESSAGE_EVENT } from 'constants/variables';

// Utils
import { validateRichMenuTemplate } from '../Design/utils';
import { useDispatch } from 'react-redux';

interface RichMenuEmbedProps {}

const PATH = 'src/app/modules/Dashboard/containers/RichMenu/containers/Embed/index.tsx';

type TState = {
  richMenuTemplate: RichMenuTemplate;
  externalConfig?: ExternalConfig;
  isDefaultRichMenu?: boolean;
};

export const RichMenuEmbed: React.FC<RichMenuEmbedProps> = memo(() => {
  const [state, setState] = useState<TState>({
    richMenuTemplate: initialRichMenu.richMenuTemplate,
    externalConfig: {
      templateNameList: [],
      token:
        'Bfro/bdyMdzN5/IsqqjqWFk6Id1x0s2F7k5uoCHx/MUxGsEivTKw3/IDHywKztrmjl2w+xeDAwvRdtseGOwgDoG4bNoEBp1xqjJmNDJzOZnka6QCcGHvMnYJd0ukngeM8Bv3u7U3J/SxvBAFBK9tegdB04t89/1O/w1cDnyilFU=',
    },
  });

  // Variables
  const { richMenuTemplate, externalConfig } = state;

  const handlePostMessage = useCallback(
    (event: MessageEvent<TMessageEventPayload<PostMessageRichMenuPayload>>) => {
      const { actionType, data } = event.data || {};

      switch (actionType) {
        case 'setTemplateDetail': {
          const { richMenuTemplate, templateNameList, token, isDefaultRichMenu } = data;

          setState(prevState => ({
            ...prevState,
            richMenuTemplate: { ...state.richMenuTemplate, ...richMenuTemplate },
            externalConfig: { token, templateNameList },
            isDefaultRichMenu,
          }));

          // Post message to parent when set success
          window.parent.postMessage(
            {
              actionType: POST_MESSAGE_EVENT.SET_TEMPLATE_DETAIL_SUCCESS,
            },
            '*',
          );
          break;
        }

        default:
          break;
      }
    },
    [state.richMenuTemplate],
  );

  // Effects
  useEffect(() => {
    window.addEventListener('message', handlePostMessage);

    return () => {
      window.removeEventListener('message', handlePostMessage);
    };
  }, [handlePostMessage]);

  // Handlers
  const handleSave = tryCatchWrapper((richMenuTemplate: RichMenuTemplate) => {
    if (richMenuTemplate) {
      window.parent.postMessage(
        {
          actionType: POST_MESSAGE_EVENT.SAVE_TEMPLATE,
          data: richMenuTemplate,
        },
        '*',
      );
    }
  }, PATH);

  const handleCancel = tryCatchWrapper(() => {
    window.parent.postMessage(
      {
        actionType: POST_MESSAGE_EVENT.CANCEL_TEMPLATE,
      },
      '*',
    );
  }, PATH);

  return (
    <EmbedWrapper>
      <RichMenuEditor
        isEmbed
        externalConfig={externalConfig}
        richMenuTemplate={richMenuTemplate}
        isDefaultRichMenu={state.isDefaultRichMenu}
        onCancel={handleCancel}
        onSave={handleSave}
      />
    </EmbedWrapper>
  );
});
