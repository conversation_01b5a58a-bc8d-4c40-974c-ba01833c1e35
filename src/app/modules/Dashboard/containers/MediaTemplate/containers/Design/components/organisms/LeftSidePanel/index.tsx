// Libraries
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { theme } from 'twin.macro';

// Atoms
import { Icon, ScrollBox } from 'app/components/atoms';

// Organisms
import { Layers } from './components/organisms/Layers';

// Selector
import { selectLeftSidePanel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Styled
import { LeftSidePanelBody, LeftSidePanelTabs, LeftSidePanelWrapper, TabItem } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

// Slice
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/index';

// Constants
import { LEFT_PANEL_TABS } from '../../../constants';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/LeftSidePanel/index.tsx';

interface LeftSidePanelProps extends React.HtmlHTMLAttributes<HTMLDivElement> {}

export const LeftSidePanel: React.FC<LeftSidePanelProps> = props => {
  const dispatch = useDispatch();

  // Props
  const { ...restOf } = props;

  // Selector
  const { isCollapsed, activeTab } = useSelector(selectLeftSidePanel);

  // Actions
  const { setLeftSidePanel } = mediaTemplateDesignActions;

  // Handlers
  const onClickCollapsePanel = () => {
    try {
      dispatch(
        setLeftSidePanel({
          isCollapsed: !isCollapsed,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickCollapsePanel',
        args: {},
      });
    }
  };

  const onClickTab = (tabValue: string) => {
    try {
      dispatch(
        setLeftSidePanel({
          activeTab: tabValue,
          ...(isCollapsed && { isCollapsed: false }),
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickTab',
        args: {},
      });
    }
  };

  const renderContentTab = (tab: string) => {
    try {
      switch (tab) {
        case LEFT_PANEL_TABS.LAYERS.value:
          return <Layers />;

        default:
          return <Layers />;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderContentTab',
        args: {},
      });
    }
  };

  return (
    <LeftSidePanelWrapper
      {...restOf}
      style={{
        ...restOf.style,
        marginLeft: isCollapsed ? -297 : 0,
      }}
    >
      <ScrollBox
        className={`ants-absolute ants-flex ants-flex-col`}
        style={{ height: `calc(100% - ${theme('space.10')})`, display: isCollapsed ? 'none' : 'flex' }}
      >
        <LeftSidePanelBody key={activeTab} className="animate__animated animate__fadeIn">
          {renderContentTab(activeTab)}
        </LeftSidePanelBody>
      </ScrollBox>
      <LeftSidePanelTabs
        key={isCollapsed + ''}
        collapsed={isCollapsed}
        className={classNames({
          'animate__animated animate__fadeIn': true,
          'ants-flex-col ants-items-end': isCollapsed,
        })}
      >
        {Object.values(LEFT_PANEL_TABS).map(({ value, label, icon }) => (
          <TabItem
            key={value}
            className={classNames({
              active: activeTab === value,
            })}
            onClick={() => onClickTab(value)}
          >
            <Icon type={icon} />
            <div className="__text">{label}</div>
          </TabItem>
        ))}
        <TabItem className="!ants-w-[46px] ants-shrink-0" onClick={onClickCollapsePanel}>
          <Icon
            type="icon-ants-angle-left"
            className="ants-transition-transform ants-duration-300"
            style={{
              transform: isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
            }}
            size={10}
          />
        </TabItem>
      </LeftSidePanelTabs>
    </LeftSidePanelWrapper>
  );
};
