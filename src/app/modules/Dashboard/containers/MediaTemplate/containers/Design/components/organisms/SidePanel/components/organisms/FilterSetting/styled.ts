// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

export const FilterSettingWrapper = styled.div``;

export const FilterBlockWrapper = styled.div`
  ${tw`ants-mb-6 ants-w-100`}
  border: 1px solid #e6e6e6;
  border-radius: 3px;
  border-left: 4px solid #005eb8;
`;

export const RowCondition = styled.div`
  ${tw`ants-items-start`}
  display: grid;
  // 3 first items = 84%;
  grid-template-columns: 34% 22% 28% 7% auto;
  grid-column-gap: 20px;
`;

export const RowSelect = styled.div`
  ${tw`ants-my-2 ants-items-center`}
  display: grid;
  grid-template-columns: 30% calc(70% - 16px);
  gap: 8px 16px;
`;
