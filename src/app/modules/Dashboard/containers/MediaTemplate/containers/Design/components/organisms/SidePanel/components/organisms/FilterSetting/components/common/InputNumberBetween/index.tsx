// Translations
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { InputNumber } from 'app/components/molecules';

interface InputNumberBetweenProps {
  value: string;
  onChange: (val: InputNumberBetweenProps['value']) => void;
}
export const InputNumberBetween: React.FC<InputNumberBetweenProps> = props => {
  const { t } = useTranslation();
  const { value, onChange } = props;

  const [from, to] = value.split(' AND ');

  const onChangeFrom = val => {
    const newValue = `${val} AND ${to}`;
    onChange(newValue);
  };

  const onChangeTo = val => {
    const newValue = `${from} AND ${val}`;
    onChange(newValue);
  };

  return (
    <div className="ants-flex ants-items-center">
      <InputNumber required value={from} onChange={onChangeFrom} width="100%" max={to} />
      <Text className="ants-mx-4">{t(translations.and.title)}</Text>
      <InputNumber required value={to} onChange={onChangeTo} width="100%" min={from} />
    </div>
  );
};
