// Libraries
import React, { useMemo } from 'react';

import { BlockProps } from '../../../../../../types';

// Atoms
import { IconRating } from '../../atoms/IconRating';

// Styled
import { Rating<PERSON>lockWrapper, RatingContainer } from './styled';

// Types
import { Skeleton } from 'app/components/atoms';
import { APP_CONFIG } from 'constants/appConfig';

// Utils
import { getRawDynamicData } from '../../../utils';
import { buildMergeTag } from '../../../../../../slice/utils';
import { getDataBOfromDM } from '../../../../SidePanel/components/organisms/AddDynamicContent/utils';
import { useSelector } from 'react-redux';
import { selectCSDataOfGroup } from '../../../../../../slice/selectors';

const ALIGN_MAPPING = {
  left: 'flex-start',
  center: 'center',
  right: 'flex-end',
};

interface RatingBlockProps extends BlockProps {}

export const RatingBlock: React.FC<RatingBlockProps> = props => {
  const { settings, isPreviewMode } = props;
  const { iconStyles, size, totalItems, totalFillItems, BGBeforeRating, BGAfterRating, dynamic } = settings;
  const { value } = dynamic;

  // Selectors
  const contentSoucesData = useSelector(selectCSDataOfGroup);

  const [memoziedTotalFillItems, memoziedTotalFillItemsPreview, isDynamic, dynamicIndexData] = useMemo(() => {
    let totalFillItemsPreview = totalFillItems;

    if (value.isDynamic) {
      totalFillItemsPreview =
        getRawDynamicData({
          dataTableBO: getDataBOfromDM(value, contentSoucesData.data),
          dynamicItem: value,
        }) || '';

      // eslint-disable-next-line use-isnan
      if (
        !Number.isNaN(Number(totalFillItemsPreview)) &&
        totalFillItemsPreview !== null &&
        typeof totalFillItemsPreview !== 'boolean'
      ) {
        const fillItemsNumber = Number(totalFillItemsPreview);

        if (fillItemsNumber < 0) {
          totalFillItemsPreview = 0;
        } else if (fillItemsNumber > 5) {
          totalFillItemsPreview = 5;
        } else {
          totalFillItemsPreview = fillItemsNumber;
        }
      } else {
        totalFillItemsPreview = 0;
      }

      return [isPreviewMode ? buildMergeTag(value) : totalFillItemsPreview, totalFillItemsPreview, true, [value.index]];
    }

    return [totalFillItemsPreview, totalFillItemsPreview, false, []];
  }, [totalFillItems, value, isPreviewMode, contentSoucesData]);

  const renderRating = (totalFillItems: number, isPreview = false) => {
    const { ratingTypes } = settings;

    const ratingElements = [] as any[];
    const renderRating = typeof totalFillItems === 'number' ? totalFillItems : 0.5;

    for (let i = 1; i <= totalItems; i++) {
      ratingElements.push(
        <IconRating
          stroke={iconStyles.borderColor}
          fill={BGAfterRating}
          beforeFill={BGBeforeRating}
          isFill={i <= renderRating}
          isHalfFill={i > renderRating && i === Math.ceil(renderRating)}
          type={ratingTypes}
          style={iconStyles}
          width={size}
          height={size}
          key={i}
        />,
      );
    }

    const previewAttr = isPreview ? APP_CONFIG.DYNAMIC_ATTRIBUTE.PREVIEW : APP_CONFIG.DYNAMIC_ATTRIBUTE.DISPLAY;
    return (
      <div
        {...{ [previewAttr]: 1 }}
        style={{
          display: !isPreview ? 'block' : 'none',
        }}
        data-rating={totalFillItems}
      >
        <RatingContainer>{ratingElements}</RatingContainer>
      </div>
    );
  };

  return (
    <RatingBlockWrapper
      style={{
        ...settings.outerContainerStyles,
        justifyContent: ALIGN_MAPPING[settings.outerContainerStyles.justify],
      }}
      {...(isDynamic && {
        [APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_IS_DYNAMIC]: 1,
        [APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_DYNAMIC_INDEX]: dynamicIndexData.join(','),
      })}
    >
      {value?.isDynamic && contentSoucesData.isLoading ? (
        <Skeleton.Input active />
      ) : (
        <>
          {renderRating(memoziedTotalFillItems, false)}
          {isPreviewMode && renderRating(memoziedTotalFillItemsPreview, true)}
        </>
      )}
    </RatingBlockWrapper>
  );
};
