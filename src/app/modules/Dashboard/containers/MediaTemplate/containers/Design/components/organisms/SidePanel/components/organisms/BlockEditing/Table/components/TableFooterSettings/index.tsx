import { TTableSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { SettingWrapper } from '../../../../../molecules';
import { Button, Popover } from 'app/components/atoms';
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import Icon from '@antscorp/icons';
import { BorderSetting } from '../../../../BorderSetting';
import { getBorderSettings, getBorderStyles } from '../../../../../../utils';

type TableFooterSetingsProps = {
  tableFooter: TTableSettings['tableFooter'];
  onChange: (updatedTableFooter: TTableSettings['tableFooter']) => void;
};

const TableFooterSettings = (props: TableFooterSetingsProps) => {
  const { t } = useTranslation();

  const { tableFooter } = props;

  const content = (
    <div className="ants-w-72">
      <BorderSetting
        settings={getBorderSettings(tableFooter.settings as any)}
        styles={getBorderStyles(tableFooter.styles)}
        onChange={(settings, styles) =>
          props.onChange({
            settings: {
              ...tableFooter.settings,
              ...settings,
            },
            styles: {
              ...tableFooter.styles,
              ...styles,
            },
          })
        }
      />
    </div>
  );

  return (
    <SettingWrapper label={t(translations.tableFooter.title)} labelClassName="ants-font-bold">
      <Popover placement="topRight" content={content} trigger={['click']}>
        <Button icon={<Icon type="icon-ants-edit-2" />} type="text" />
      </Popover>
    </SettingWrapper>
  );
};

export default TableFooterSettings;
