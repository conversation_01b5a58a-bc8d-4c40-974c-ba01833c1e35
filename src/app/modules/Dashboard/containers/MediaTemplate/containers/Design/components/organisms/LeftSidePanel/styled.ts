// Libraries
import styled, { css } from 'styled-components';
import tw from 'twin.macro';

interface LeftSidePanelTabsProps {
  collapsed?: boolean;
}

export const LeftSidePanelWrapper = styled.div`
  ${tw`
    ants-relative ants-items-center ants-w-343px ants-flex-shrink-0 
    ants-flex ants-flex-col
    ants-h-full ants-bg-background ants-shadow-cus-xl ants-z-[380]
    ants-transition-all ants-duration-500
  `}
`;

export const LeftSidePanelBody = styled.div`
  ${tw`ants-h-full ants-w-full`}
`;

export const LeftSidePanelTabs = styled.div<LeftSidePanelTabsProps>`
  ${tw`ants-absolute ants-bottom-0 ants-flex ants-flex-shrink-0 ants-w-full ants-border-t ants-border-t-divider-2`}

  ${p => {
    if (p.collapsed) {
      return css`
        ${TabItem} {
          ${tw`ants-w-[46px]`}
          border-right: 0px;

          &:not(:last-child) {
            ${tw`ants-border-b`}
          }

          .__text {
            ${tw`ants-hidden`}
          }
        }
      `;
    }
  }}
`;

export const TabItem = styled.div`
  ${tw`ants-flex ants-h-[46px] ants-w-full ants-space-x-5px ants-items-center ants-justify-center ants-transition-colors ants-cursor-pointer`}

  .__text {
    ${tw`ants-text-normal`}
  }

  &:hover,
  &.active {
    ${tw`ants-text-white ants-bg-primary`}
  }

  &:not(:last-child) {
    ${tw`ants-border-r ants-border-alto`}
  }
`;
