// Libraries
import React from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

// Slice
import { selectDeviceType } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import { DEVICE_TYPE } from 'app/modules/Dashboard/containers/MediaTemplate/constants';

// Atoms
import { Alert, Divider } from 'app/components/atoms';

// Locales
import { translations } from 'locales/translations';

interface MobileWarningProps {}

export const MobileWarning: React.FC<MobileWarningProps> = () => {
  // Selectors
  const deviceType = useSelector(selectDeviceType);

  // I18n
  const { t } = useTranslation();

  return deviceType === DEVICE_TYPE.MOBILE.value ? (
    <>
      <Alert type="warning" className="ants-text-center ants-m-2.5" message={t(translations.mobile.warning)} />
      <Divider style={{ margin: 0, borderColor: '#d9d9d9' }} />
    </>
  ) : (
    <></>
  );
};

export default MobileWarning;
