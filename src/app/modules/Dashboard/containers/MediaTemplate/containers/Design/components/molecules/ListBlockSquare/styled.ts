import styled from 'styled-components';

export const WrapperBlockSquare = styled.div`
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
`;

export const WrapperItem = styled.div`
  position: relative;
  display: flex;
  width: 225px;
  height: 175px;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &.active {
    border-color: #005fb8;
  }

  .provider-title {
    font-size: 14px;
    font-weight: 700;
    margin-top: 20px;
  }
  .cover-item {
    position: absolute;
    display: flex;
    opacity: 0;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.38);
    width: 100%;
    height: 100%;
    transition: all 250ms ease;
    .block {
      display: flex;
      gap: 10px;
    }
  }

  &:hover {
    background-color: #f7fcfe;
    .cover-item {
      opacity: 1 !important;
    }
  }

  .add-new-icon {
    padding: 15px;
    background-color: #f2f7fb;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

export const Logo = styled.img`
  max-width: 100px;
  max-height: 50px;
`;
