// Library
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import produce from 'immer';

// Selector
import {
  selectBlockSelected,
  selectContentSourcesGroups,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Query
import { useGetDataTableBO, useGetListBO } from 'app/queries/BusinessObject';

// Components
import Advanced from './Advanced';
import Content from './Content';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Types
import {
  TColumnTableBlock,
  TTableSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Molecules
import { Tabs, TabPane } from 'app/components/molecules';

// Constants, Config
import { SIDE_PANEL_TABS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/constants';
import {
  BO_DIMENSION_TYPE,
  BO_METRICS_TYPE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { TABLE_COLUMN_SETTINGS_DEFAULT } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

// Utils
import { random } from 'app/utils/common';
import { handleError } from 'app/utils/handleError';
import MobileWarning from '../../../molecules/MobileWarning';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Columns/index.tsx';

const tabs = [
  {
    name: SIDE_PANEL_TABS.CONTENT.name,
    label: SIDE_PANEL_TABS.CONTENT.label,
  },
  {
    name: SIDE_PANEL_TABS.ADVANCED.name,
    label: SIDE_PANEL_TABS.ADVANCED.label,
  },
];

export const Table = () => {
  const dispatch = useDispatch();

  const contentSourceGroups = useSelector(selectContentSourcesGroups);

  const tableBlock = useSelector(selectBlockSelected);
  const tableBlockSettings = tableBlock.settings as TTableSettings;
  const { updateBlockFieldsSelected } = mediaTemplateDesignActions;

  const [defaultBoId, setDefaultBoId] = useState<number | null>(null);
  const [state, setState] = useState({
    tabSelected: SIDE_PANEL_TABS.CONTENT.name,
  });

  const { data: dataTableBO, isFetching: isLoadingDataTableBo } = useGetDataTableBO({
    itemTypeId: tableBlockSettings.boId,
  });
  const { data: listBo, isFetching: isLoadingGetListBo } = useGetListBO();

  const dimensions = useMemo(() => {
    if (!dataTableBO?.header) return [];

    return dataTableBO.header.filter((head: any) => BO_DIMENSION_TYPE.includes(head.dataType));
  }, [dataTableBO]);

  const metrics = useMemo(() => {
    if (!dataTableBO?.header) return [];

    return dataTableBO.header.filter((head: any) => BO_METRICS_TYPE.includes(head.dataType));
  }, [dataTableBO]);

  const { selectedDimensionCols, selectedMetricCols } = useMemo(
    () =>
      tableBlockSettings.columns.reduce(
        (accumulator, currentValue) => {
          if (currentValue.colType === 'index') return accumulator;

          if (currentValue.colType === 'dimension') {
            return produce(accumulator, draft => {
              draft.selectedDimensionCols.push(currentValue);
            });
          }

          return produce(accumulator, draft => {
            draft.selectedMetricCols.push(currentValue);
          });
        },
        { selectedDimensionCols: [] as TColumnTableBlock[], selectedMetricCols: [] as TColumnTableBlock[] },
      ),
    [tableBlockSettings.columns],
  );

  useEffect(() => {
    let draftBoId: number | null = null;

    if (
      contentSourceGroups.length &&
      listBo?.find((bo: any) => get(bo, 'model.item_type_id') === contentSourceGroups[0].itemTypeId) !== -1
    ) {
      draftBoId = contentSourceGroups[0].itemTypeId;
    }

    if (!draftBoId) {
      draftBoId = !!listBo?.length && listBo[0].model.item_type_id;
    }

    setDefaultBoId(draftBoId);
  }, [contentSourceGroups, listBo]);

  useEffect(() => {
    if (!tableBlockSettings.boId && defaultBoId) {
      dispatch(
        updateBlockFieldsSelected({
          dataUpdate: [
            {
              fieldPath: 'settings.boId',
              data: defaultBoId,
            },
          ],
        }),
      );
    }
  }, [tableBlockSettings.boId, defaultBoId, dispatch, updateBlockFieldsSelected]);

  useEffect(() => {
    const tableWrapper = document.querySelector<HTMLTableElement>('.table-block__wrapper');

    if (!tableWrapper) return;

    const tableWrapperWidth = tableWrapper.clientWidth;

    const tableNoneColumns = tableBlockSettings.columns.length === 0;

    if (!!dataTableBO?.header.length && tableNoneColumns) {
      let defaultColumns = [] as TColumnTableBlock[];

      defaultColumns.push({
        ...TABLE_COLUMN_SETTINGS_DEFAULT,
        id: random(6),
        label: dataTableBO?.header[0].itemPropertyDisplay || '',
        value: dataTableBO?.header[0].itemPropertyName || '',
        dataType: dataTableBO?.header[0].dataType || '',
        width: tableWrapperWidth + 'px',
        colType: BO_DIMENSION_TYPE.includes(dataTableBO?.header[0].dataType) ? 'dimension' : 'metric',
      });

      // Add Column Index to default columns
      if (tableBlockSettings.tableBody.settings.showRowNumbers) {
        defaultColumns.unshift({
          ...TABLE_COLUMN_SETTINGS_DEFAULT,
          id: random(6),
          placement: 'center',
          value: '',
          label: '',
          dataType: '',
          colType: 'index',
        });

        defaultColumns = defaultColumns.map(col => ({
          ...col,
          width: tableWrapperWidth / defaultColumns.length + 'px',
        }));
      }

      dispatch(
        updateBlockFieldsSelected({
          dataUpdate: [
            {
              fieldPath: 'settings.columns',
              data: defaultColumns,
            },
            {
              fieldPath: 'settings.sortBy.columnId',
              data: defaultColumns[defaultColumns.length - 1].value,
            },
          ],
          ignoreUndoAction: true,
        }),
      );
    }
  }, [
    dataTableBO?.header,
    dispatch,
    tableBlockSettings.columns.length,
    tableBlockSettings.tableBody.settings.showRowNumbers,
    updateBlockFieldsSelected,
  ]);

  const onChangeTabs = (activeKey: string) => {
    try {
      setState(state => ({
        ...state,
        tabSelected: activeKey,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTabs',
        args: {},
      });
    }
  };

  const renderTab = () => {
    try {
      switch (state.tabSelected) {
        case SIDE_PANEL_TABS.CONTENT.name:
          return (
            <Content
              tableBlockSettings={tableBlockSettings}
              dimensions={dimensions}
              metrics={metrics}
              selectedDimensionCols={selectedDimensionCols}
              selectedMetricCols={selectedMetricCols}
              isLoadingDataTableBo={isLoadingDataTableBo}
              isLoadingGetListBO={isLoadingGetListBo}
              listBO={listBo as any}
            />
          );

        case SIDE_PANEL_TABS.ADVANCED.name:
          return (
            <Advanced
              tableBlockSettings={tableBlockSettings}
              selectedDimensionCols={selectedDimensionCols}
              selectedMetricCols={selectedMetricCols}
              isLoadingDataTableBo={isLoadingDataTableBo}
            />
          );
        default:
          return '';
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderTabContent',
        args: {},
      });
    }
  };

  return (
    <Tabs defaultActiveKey={state.tabSelected} onChange={onChangeTabs} destroyInactiveTabPane>
      {tabs.map(({ name, label }) => (
        <TabPane
          tab={label}
          key={name}
          disabled={label === SIDE_PANEL_TABS.ADVANCED.label && (isLoadingDataTableBo || isLoadingGetListBo)}
        >
          <MobileWarning />
          {renderTab()}
        </TabPane>
      ))}
    </Tabs>
  );
};
