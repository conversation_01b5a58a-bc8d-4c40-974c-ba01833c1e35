// Libraries
import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { cloneDeep } from 'lodash';

// Locales
import { translations } from 'locales/translations';

// Atoms
import { Alert, Divider, Space, Switch, Text } from 'app/components/atoms';

// Molecules
import { ColorSetting, SettingWrapper } from '../../../../molecules';
import { InputNumber, Select, SliderWithInputNumber } from 'app/components/molecules';

// Styled
import { ContentWrapper } from './styled';

// Selectors
import {
  selectBlockSelected,
  selectChildrenBlockSelected,
  selectCurrentBlocks,
  selectCurrentTree,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { removeBlocks } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import { handleError } from 'app/utils/handleError';
import { random } from 'app/utils/common';

// Hooks
import { useDebounce, useUpdateEffect } from 'app/hooks';

// Types
import { TElement } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

// Config
import { BLOCK_SETTING_DEFAULT } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

// Constants
import { BUTTON_POSITION, BUTTON_STYLE, DISPLAY_STYLE, SLIDE_DIRECTION, SLIDE_TRANSITION } from './constants';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/SlideShow/Content/index.tsx';

const Content = () => {
  // Dispatch
  const dispatch = useDispatch();

  // i18n
  const { t } = useTranslation();

  // Selector
  const blockSelected = useSelector(selectBlockSelected) as TElement;
  const slides = useSelector(selectChildrenBlockSelected);
  const currentBlocks = useSelector(selectCurrentBlocks);
  const currentTree = useSelector(selectCurrentTree);
  const { settings } = blockSelected || {};
  const { navigationSettings, navigationStyles } = settings;

  const debounceTotalItems = useDebounce(settings?.totalItems, 1000);

  // Actions
  const { updateBlockFieldsSelected, updateCurrentPageTreeBlocks } = mediaTemplateDesignActions;

  useUpdateEffect(() => {
    const cloneTree = cloneDeep(currentTree);
    const cloneBlocks = cloneDeep(currentBlocks);

    if (debounceTotalItems <= (slides?.length || 0)) {
      for (let i = slides.length - 1; i >= debounceTotalItems; i--) {
        removeBlocks({
          blocks: cloneBlocks,
          tree: cloneTree,
          blockId: slides[i]?.id,
          parentId: blockSelected.id.toString(),
        });
      }
    } else {
      for (let i = 0; i < debounceTotalItems - (slides?.length || 0); i++) {
        const cloneSlide = {
          id: random(20),
          type: BLOCK_SETTING_DEFAULT.SLIDE.type,
          settings: BLOCK_SETTING_DEFAULT.SLIDE as any,
        };
        cloneBlocks[cloneSlide.id] = cloneSlide;
        cloneTree[blockSelected.id] = [...cloneTree[blockSelected.id], cloneSlide.id];
        cloneTree[cloneSlide.id] = [];
      }
    }

    dispatch(
      updateCurrentPageTreeBlocks({
        tree: [
          {
            fieldPath: '',
            data: cloneTree,
          },
        ],
        blocks: [
          {
            fieldPath: '',
            data: cloneBlocks,
          },
        ],
      }),
    );
  }, [debounceTotalItems]);

  const onChangeSlideShowSetting = payload => {
    try {
      dispatch(
        updateBlockFieldsSelected({
          dataUpdate: Object.entries(payload).map(([key, value]) => ({
            fieldPath: `settings.${key}`,
            data: value,
          })),
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const onChangeNavigationSettings = ({ navigationSettings = {}, navigationStyles = {} }) => {
    try {
      dispatch(
        updateBlockFieldsSelected({
          dataUpdate: [
            ...Object.entries(navigationSettings).map(([key, value]) => ({
              fieldPath: `settings.navigationSettings.${key}`,
              data: value,
            })),
            ...Object.entries(navigationStyles).map(([key, value]) => ({
              fieldPath: `settings.navigationStyles.${key}`,
              data: value,
            })),
          ],
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeNavigationSettings',
        args: {},
      });
    }
  };

  if (blockSelected) {
    return (
      <ContentWrapper>
        <Text size="medium" className="ants-font-medium !ants-text-cus-dark ants-mb-2.5">
          {t(translations.slideShow.title)}
        </Text>

        <Space direction="vertical" size={20}>
          <SettingWrapper label={t(translations.totalItems.title)}>
            <InputNumber
              value={settings?.totalItems}
              min={1}
              max={100}
              required
              onChange={value => {
                onChangeSlideShowSetting({ totalItems: value });
              }}
            />
          </SettingWrapper>
          <SettingWrapper label={t(translations.displayItems.title)}>
            <InputNumber
              value={settings?.displayItems}
              min={1}
              max={settings?.totalItems}
              required
              onChange={value => {
                onChangeSlideShowSetting({ displayItems: value });
              }}
            />
          </SettingWrapper>
          <Select
            label={t(translations.displayStyle.title)}
            options={Object.values(DISPLAY_STYLE)}
            value={settings?.displayStyle}
            onChange={value => onChangeSlideShowSetting({ displayStyle: value })}
          />
          <SliderWithInputNumber
            label={t(translations.columnGapPx.title)}
            min={0}
            max={1000}
            value={settings?.columnGap}
            onAfterChange={value => onChangeSlideShowSetting({ columnGap: value })}
          />
          <Divider className="!ants-m-0" dot />
          <Select
            label={t(translations.slideDirection.title)}
            options={Object.values(SLIDE_DIRECTION)}
            value={settings?.slideDirection}
            onChange={value => {
              onChangeSlideShowSetting({
                slideDirection: value,
                navigationSettings: {
                  ...navigationSettings,
                  buttonPosition: BUTTON_POSITION.MIDDLE.value,
                },
              });
            }}
          />
          <Select
            label={t(translations.slideTransition.title)}
            options={Object.values(SLIDE_TRANSITION)}
            value={settings?.slideTransition}
            onChange={value => onChangeSlideShowSetting({ slideTransition: value })}
          />
          <SettingWrapper label={`${t(translations.numberOfNextItem.title)}`}>
            <InputNumber
              value={settings?.skipItems}
              min={1}
              max={settings.displayItems}
              required
              onChange={value => {
                onChangeSlideShowSetting({ skipItems: value });
              }}
            />
          </SettingWrapper>
          <SettingWrapper label={`${t(translations.autoSlide.title)}?`}>
            <Switch
              checked={settings.autoSlide}
              onChange={checked =>
                onChangeSlideShowSetting({
                  autoSlide: checked,
                })
              }
            />
          </SettingWrapper>
          {settings.autoSlide && (
            <SettingWrapper label={`${t(translations.slideDelay.title)}`}>
              <InputNumber
                value={settings?.slideDelay}
                min={0}
                max={100}
                required
                onChange={value => {
                  onChangeSlideShowSetting({ slideDelay: value });
                }}
              />
            </SettingWrapper>
          )}
          <SettingWrapper label={`${t(translations.slideTime.title)}`}>
            <InputNumber
              value={settings?.slideDuration}
              min={0}
              max={100}
              required
              onChange={value => {
                onChangeSlideShowSetting({ slideDuration: value });
              }}
            />
          </SettingWrapper>
          <SettingWrapper label={`${t(translations.slideLoop.title)}?`}>
            <Switch
              checked={settings.slideLoop}
              onChange={checked =>
                onChangeSlideShowSetting({
                  slideLoop: checked,
                })
              }
            />
          </SettingWrapper>
          <Alert type="info" message={<Trans i18nKey={translations.slideShow.info} />} />
        </Space>
        <Divider dot />
        <Text size="medium" className="ants-font-medium !ants-text-cus-dark ants-mb-2.5">
          {t(translations.nextPreviousButton.title)}
        </Text>
        <Space direction="vertical" size={20}>
          <Select
            label={t(translations.buttonPosition.title)}
            value={navigationSettings.buttonPosition}
            options={Object.values(BUTTON_POSITION).filter(position => {
              if ((position as any).mode) {
                return (position as any).mode === settings.slideDirection;
              }

              return true;
            })}
            onChange={value =>
              onChangeNavigationSettings({
                navigationSettings: {
                  buttonPosition: value,
                },
              })
            }
          />
          <div className="ants-grid ants-grid-cols-2">
            <ColorSetting
              vertical
              label={t(translations.buttonIconColor.title)}
              color={navigationStyles.color}
              positionInput="right"
              onChange={color =>
                onChangeNavigationSettings({
                  navigationStyles: {
                    color,
                  },
                })
              }
            />
            <SettingWrapper label={`${t(translations.buttonSize.title)} (px)`} vertical>
              <InputNumber
                value={parseInt(navigationStyles.fontSize)}
                min={12}
                max={256}
                required
                onChange={value =>
                  onChangeNavigationSettings({
                    navigationStyles: {
                      fontSize: `${value}px`,
                    },
                  })
                }
              />
            </SettingWrapper>
          </div>
          <Select
            label={t(translations.buttonShape.title)}
            value={navigationSettings.buttonStyle}
            options={Object.values(BUTTON_STYLE)}
            onChange={value =>
              onChangeNavigationSettings({
                navigationSettings: {
                  buttonStyle: value,
                },
              })
            }
          />
          <div className="ants-grid ants-grid-cols-2">
            <ColorSetting
              vertical
              label={t(translations.bgNormalColor.title)}
              color={navigationStyles.backgroundColor}
              positionInput="right"
              onChange={color =>
                onChangeNavigationSettings({
                  navigationStyles: {
                    backgroundColor: color,
                  },
                })
              }
            />
            <ColorSetting
              vertical
              label={t(translations.bgHoverColor.title)}
              color={navigationSettings.hoverBackgroundColor}
              positionInput="right"
              onChange={color =>
                onChangeNavigationSettings({
                  navigationSettings: {
                    hoverBackgroundColor: color,
                  },
                })
              }
            />
          </div>
        </Space>
      </ContentWrapper>
    );
  }

  return null;
};

export default Content;
