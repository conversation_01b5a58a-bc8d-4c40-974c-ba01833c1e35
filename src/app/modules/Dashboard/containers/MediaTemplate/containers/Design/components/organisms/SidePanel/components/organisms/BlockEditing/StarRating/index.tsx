// Libraries
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Components
import { Content } from './Content';
import { Advanced } from './Advanced';

// Molecules
import { TabPane, Tabs } from 'app/components/molecules';

// Selector
import { selectSidePanel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { handleError } from 'app/utils/handleError';

// Constants
import { SIDE_PANEL_TABS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/constants';
import MobileWarning from '../../../molecules/MobileWarning';

const PATH =
  'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Rating/index.tsx';

const tabs = [
  {
    name: SIDE_PANEL_TABS.CONTENT.name,
    label: SIDE_PANEL_TABS.CONTENT.label,
  },
  {
    name: SIDE_PANEL_TABS.ADVANCED.name,
    label: SIDE_PANEL_TABS.ADVANCED.label,
  },
];

interface RatingProps {}

export const Rating: React.FC<RatingProps> = props => {
  // Dispatch
  const dispatch = useDispatch();

  // Selector
  const { activeTab, activePanel } = useSelector(selectSidePanel);

  // Actions
  const { setSidePanel } = mediaTemplateDesignActions;

  const onChangeTabs = (activeKey: string) => {
    try {
      dispatch(
        setSidePanel({
          activeTab: activeKey,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTabs',
        args: [],
      });
    }
  };

  const renderTabContent = () => {
    try {
      switch (activeTab) {
        case SIDE_PANEL_TABS.CONTENT.name:
          return <Content />;
        case SIDE_PANEL_TABS.ADVANCED.name:
          return <Advanced />;
        default:
          return '';
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderTabContent',
        args: {},
      });
    }
  };

  return (
    <Tabs destroyInactiveTabPane activeKey={activeTab} onChange={onChangeTabs}>
      {tabs.map(({ name, label }) => (
        <TabPane tab={label} key={name}>
          <MobileWarning />
          {renderTabContent()}
        </TabPane>
      ))}
    </Tabs>
  );
};
