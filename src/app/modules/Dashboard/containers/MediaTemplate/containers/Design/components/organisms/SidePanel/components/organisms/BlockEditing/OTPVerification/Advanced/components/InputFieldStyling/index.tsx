import { Space, Text } from 'app/components/atoms';
import { handleError } from 'app/utils/handleError';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { Select, SliderWithUnit } from 'app/components/molecules';
import BackgroundSetting from '../../../../../BackgroundSetting';
import {
  getBackgroundSettings,
  getBackgroundStyles,
  getBorderSettings,
  getBorderStyles,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from '../../../../../../../utils';
import { BorderSettingPopover } from '../../../../../BorderSetting';
import { RoundedCornersSetting } from '../../../../../RoundedCornersSetting';
import { SpacingSetting } from '../../../../../SpacingSetting';
import { WrapperSelectInline } from '../../../Content/components/FormOTP/styled';
import { STYLE_INPUT_FIELD_OPTIONS } from '../../../constants';
import { mapValueToStyleFieldInput } from '../../../utils';
import { FontSettingPopover } from '../../../../../FontSetting';

type TInputFieldStyling = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/OTPVerification/components/InputFieldStyling/index.tsx';

export const InputFieldStyling: React.FC<TInputFieldStyling> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const { inputFieldStyle } = settings;

  const { inputStyles, inputSettings } = inputFieldStyle;
  const onUpdateInputFieldSettings = (settings = {}, styles = {}) => {
    try {
      onChange({
        inputFieldStyle: {
          ...inputFieldStyle,
          inputStyles: {
            ...inputStyles,
            ...styles,
          },
          inputSettings: {
            ...inputSettings,
            ...settings,
          },
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateButtonSettings',
        args: {},
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <WrapperSelectInline>
        <Text className="!ants-text-gray-4">{t(translations.style.title)}</Text>
        <Select
          options={Object.values(STYLE_INPUT_FIELD_OPTIONS)}
          value={inputFieldStyle?.styleValue}
          onChange={value => {
            onChange({
              inputFieldStyle: {
                ...inputFieldStyle,
                styleValue: value,
                inputStyles: {
                  ...inputStyles,
                  width: '80px',
                  height: '80px',
                },
              },
            });
          }}
        />
      </WrapperSelectInline>
      <BackgroundSetting
        label={t(translations.background.title)}
        settings={getBackgroundSettings(inputSettings)}
        styles={getBackgroundStyles(inputStyles)}
        onChange={onUpdateInputFieldSettings}
      />
      <SliderWithUnit
        label={t(translations.width.title)}
        labelClassName="!ants-text-gray-4"
        unit={inputFieldStyle.widthSuffix || '%'}
        min={0}
        max={(inputFieldStyle.widthSuffix || '%') === 'px' ? 1000 : 100}
        value={parseFloat(inputFieldStyle.inputStyles?.width)}
        onAfterChange={value =>
          onChange({
            inputFieldStyle: {
              ...inputFieldStyle,
              inputStyles: { ...inputStyles, width: `${value}${inputFieldStyle.widthSuffix || '%'}` },
            },
          })
        }
        onChangeUnit={value => {
          const width = parseFloat(inputFieldStyle.inputStyles?.width) || 100;
          let newWidth = '';

          newWidth = width + value;

          if (value === '%' && width > 100) {
            newWidth = '100%';
          }

          if (value === 'auto') {
            newWidth = 'auto';
          }

          onChange({
            inputFieldStyle: {
              ...inputFieldStyle,
              inputStyles: { ...inputStyles, width: newWidth },
              widthSuffix: value,
            },
          });
        }}
      />
      <SliderWithUnit
        label={t(translations.height.title)}
        labelClassName="!ants-text-gray-4"
        unit={inputFieldStyle.heightSuffix || '%'}
        min={0}
        max={(inputFieldStyle.heightSuffix || '%') === 'px' ? 1000 : 100}
        value={parseFloat(inputFieldStyle.inputStyles?.height)}
        onAfterChange={value =>
          onChange({
            inputFieldStyle: {
              ...inputFieldStyle,
              inputStyles: { ...inputStyles, height: `${value}${inputFieldStyle.heightSuffix || '%'}` },
            },
          })
        }
        onChangeUnit={value => {
          const height = parseFloat(inputFieldStyle.inputStyles?.height) || 100;
          let newHeight = '';

          newHeight = height + value;

          if (value === '%' && height > 100) {
            newHeight = '100%';
          }

          if (value === 'auto') {
            newHeight = 'auto';
          }

          onChange({
            inputFieldStyle: {
              ...inputFieldStyle,
              inputStyles: { ...inputStyles, height: newHeight },
              heightSuffix: value,
            },
          });
        }}
      />
      <FontSettingPopover styles={inputStyles} settingsStyle={inputSettings} onChange={onUpdateInputFieldSettings} />
      <BorderSettingPopover
        settings={getBorderSettings(inputSettings)}
        styles={getBorderStyles(inputStyles)}
        onChange={onUpdateInputFieldSettings}
      />

      <RoundedCornersSetting
        settings={getRoundedCornersSettings(inputSettings)}
        styles={getRoundedCornersStyles(inputStyles)}
        onChange={onUpdateInputFieldSettings}
      />

      <SpacingSetting
        settings={getSpacingSettings(inputSettings)}
        styles={getSpacingStyles(inputStyles)}
        onChange={onUpdateInputFieldSettings}
      />
    </Space>
  );
};
