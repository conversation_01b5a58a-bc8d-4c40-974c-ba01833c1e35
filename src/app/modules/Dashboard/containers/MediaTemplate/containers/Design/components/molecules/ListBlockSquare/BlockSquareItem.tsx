import { Button, Icon } from 'app/components/atoms';
import React from 'react';
import styled from 'styled-components';
import { Logo, WrapperItem } from './styled';

type TBlockSquareItem = {
  value: any;
  label: string;
  icon?: string;
  imageUrl?: string;
  callback?: (type?: string, value?: any) => void;
  activeId?: number;
};

export const BlockSquareItem: React.FC<TBlockSquareItem> = props => {
  const { value, label, icon, imageUrl, callback = (type?: string, value?: any) => {}, activeId } = props;
  return (
    <WrapperItem title={label} onClick={() => callback(value)} className={activeId === value ? 'active' : ''}>
      {icon ? (
        <div className="add-new-icon">
          <Icon className="icon" size={25} color="#005EB8" type={icon} />
        </div>
      ) : (
        <Logo src={imageUrl} />
      )}
      <div className="provider-title">{label}</div>
      {value !== 'CREATE' && (
        <div className="cover-item">
          <div className="block">
            <Button title="Edit" type="default" onClick={() => callback('EDIT', value)}>
              Edit
            </Button>
            <Button title="Remove" type="default" onClick={() => callback('REMOVE', value)}>
              <Icon type="icon-ants-remove-trash" />
            </Button>
          </div>
        </div>
      )}
    </WrapperItem>
  );
};
