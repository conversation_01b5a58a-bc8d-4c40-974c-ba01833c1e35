import React from 'react';
import Icon from '@antscorp/icons';
import { useTranslation } from 'react-i18next';
import { TFontStyles } from '../../../Countdown/types';
import { FontSetting } from '../../../../FontSetting';
import { SettingWrapper } from '../../../../../molecules';
import { Button, Popover, Space, Checkbox } from 'app/components/atoms';
import { translations } from 'locales/translations';

type FontSettingProps = {
  styles: TFontStyles;
  settingsStyle?: any;
  onChange: (settingsStylesFont: {}, stylesFont: TFontStyles) => void;
  isUseOptional?: boolean;
  className?: string;
};

export const TableHeaderSettings: React.FC<FontSettingProps> = props => {
  // I18next
  const { t } = useTranslation();

  const content = (
    <div className="ants-w-72">
      <Space className="ants-w-100 ants-justify-between ants-mb-4">
        <Checkbox
          checked={props.settingsStyle.showHeader}
          onChange={e =>
            props.onChange(
              {
                ...props.settingsStyle,
                showHeader: e.target.checked,
              },
              props.styles,
            )
          }
        >
          {t(translations.showHeader.title)}
        </Checkbox>
        <Checkbox
          checked={props.settingsStyle.wrapText}
          onChange={e =>
            props.onChange(
              {
                ...props.settingsStyle,
                wrapText: e.target.checked,
              },
              props.styles,
            )
          }
        >
          {t(translations.wrapText.title)}
        </Checkbox>
      </Space>
      <FontSetting {...props} isUseOptional />
    </div>
  );

  return (
    <SettingWrapper label={t(translations.tableHeader.title)} labelClassName="ants-font-bold">
      <Popover placement="bottomRight" content={content} trigger={['click']}>
        <Button icon={<Icon type="icon-ants-edit-2" />} type="text" />
      </Popover>
    </SettingWrapper>
  );
};

export default TableHeaderSettings;
