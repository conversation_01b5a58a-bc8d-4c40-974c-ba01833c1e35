import { InputNumber, Select } from 'app/components/molecules';

type TimeUnit = 'DAY' | 'HOUR';

interface InputDateTimeAgoProps {
  value: string | number;
  timeUnit?: TimeUnit;
  onChangeMultipleValue: ({
    value,
    time_unit,
  }: {
    value?: InputDateTimeAgoProps['value'];
    time_unit?: InputDateTimeAgoProps['timeUnit'];
  }) => void;
  required?: boolean;
}

export const InputDateTimeAgo: React.FC<InputDateTimeAgoProps> = props => {
  const { value, timeUnit, onChangeMultipleValue, required } = props;

  const onChangeTime = val => {
    onChangeMultipleValue({
      value: val,
    });
  };

  const onChangeUnit = (val: TimeUnit) => {
    onChangeMultipleValue({
      time_unit: val,
    });
  };

  return (
    <div className="ants-flex ants-items-center">
      <InputNumber required={required} value={value} onChange={onChangeTime} width="100%" />
      <Select
        className="!ants-ml-4 ants-w-24"
        value={timeUnit}
        onChange={onChangeUnit}
        options={optionsInputDateTimeAgo}
      />
    </div>
  );
};

const optionsInputDateTimeAgo = [
  { value: 'DAY', label: 'Days' },
  { value: 'HOUR', label: 'Hours' },
];
