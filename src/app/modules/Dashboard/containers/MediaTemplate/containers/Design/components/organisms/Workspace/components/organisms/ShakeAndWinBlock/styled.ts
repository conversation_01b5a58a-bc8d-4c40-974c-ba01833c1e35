// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

export const ShakeAndWinBlockWrapper = styled.div`
  ${tw`ants-relative ants-overflow-hidden`}
`;

export const ShakeAndWinContainer = styled.div``;

export const ShakeAndWinBlockEmptyWrapper = styled.div`
  ${tw`ants-w-full ants-h-full ants-flex ants-flex-col ants-items-center ants-py-2.5 ants-border ants-border-dashed ants-transition ants-relative`}
`;
