// Libraries
import React, { ElementType } from 'react';

// Styled
import { IconsWrapper } from './styled';

// Utils
import { random } from 'app/utils/common';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/atoms/Icons/index.tsx';

export type RatingType = 'star' | 'heart';

interface IconRatingProps {
  type: RatingType;
  fill?: string;
  beforeFill?: string;
  isFill?: boolean;
  isHalfFill?: boolean;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  stroke?: string;
  strokeWidth?: string;
}

export const IconRating: React.FC<IconRatingProps> = props => {
  const {
    isFill,
    isHalfFill,
    type,
    fill,
    width = 24,
    height = 24,
    style,
    stroke,
    strokeWidth = 1,
    beforeFill,
    className,
  } = props;

  const randomIdIconDef = random(10);

  return (
    <IconsWrapper>
      {type === 'star' ? (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width={width}
          height={height}
          viewBox="0 0 20 21.852"
          stroke={stroke}
          strokeWidth={strokeWidth}
          className={className}
          style={style}
        >
          {isHalfFill && (
            <defs>
              <linearGradient id={randomIdIconDef}>
                <stop offset="50%" stopColor={fill} />
                <stop offset="50%" stopColor={beforeFill} stopOpacity="1" />
              </linearGradient>
            </defs>
          )}
          <path
            id={type}
            transform="translate(0 1.37)"
            d="M10,0l3.05,6.331L20,7.282l-5.065,4.864,1.245,6.919L10,15.74,3.82,19.065l1.245-6.919L0,7.282l6.95-.951Z"
            fill={isFill ? fill : isHalfFill ? `url(#${randomIdIconDef})` : beforeFill}
          />
        </svg>
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width={width}
          height={height}
          stroke={stroke}
          strokeWidth={strokeWidth}
          className={className}
          style={style}
          viewBox="0 0 20 21"
        >
          {isHalfFill && (
            <defs>
              <linearGradient id={randomIdIconDef}>
                <stop offset="50%" stopColor={fill} />
                <stop offset="50%" stopColor={beforeFill} stopOpacity="1" />
              </linearGradient>
            </defs>
          )}
          <path
            id={type}
            data-name="heart icon"
            d="M8.374,1.93c-5.454.041-10.359,8.4,3.529,17.238a6.525,6.525,0,0,0,.687.429l.257.156.029-.013.028.013.261-.159a6.535,6.535,0,0,0,.685-.426C28.314,9.962,22.391,1.277,16.7,1.968a4.553,4.553,0,0,0-3.82,2.573A4.555,4.555,0,0,0,9.054,1.968,5.311,5.311,0,0,0,8.374,1.93Z"
            transform="translate(-2.876 -0.291)"
            fill={isFill ? fill : isHalfFill ? `url(#${randomIdIconDef})` : beforeFill}
          />
        </svg>
      )}
    </IconsWrapper>
  );
};
