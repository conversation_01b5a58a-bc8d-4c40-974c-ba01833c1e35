// Libraries
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

//Action
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Locales
import { translations } from 'locales/translations';

// Icons
import { WarningIcon } from 'app/components/icons';

// Hook
import { useUserInfo } from 'app/hooks';
import { useGetListPromotionPool } from 'app/queries/PromotionPool';

// Assets
import PlaceholderImage from 'assets/images/placeholder-image.png';

// Atoms
import { Icon, Text, Skeleton } from 'app/components/atoms';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Styled
import { ShakeAndWinBlockWrapper, ShakeAndWinContainer, ShakeAndWinBlockEmptyWrapper } from './styled';

// Constants
import {
  PREFIX_EL_NAME,
  SIDE_PANEL_COLLAPSE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Config
import { APP_CONFIG } from 'constants/appConfig';

// Utils
import { random } from 'app/utils/common';
import { buildPromotionSectionsData, buildPromotionTagData, getRawDynamicData } from '../../../utils';
import { buildMergeTag } from '../../../../../../slice/utils';
import { getDataBOfromDM } from '../../../../SidePanel/components/organisms/AddDynamicContent/utils';

// Selectors
import { selectCSDataOfGroup, selectSidePanel } from '../../../../../../slice/selectors';
import { TRIGGER_OPTIONS } from '../../../../SidePanel/components/organisms/BlockEditing/ShakeAndWin/components/TriggerSetting/constants';

const STANDARD_WIDTH = 690;
const ERROR_POSITION = {
  TOP: 'top',
  CENTER: 'center',
  BOTTOM: 'bottom',
};

interface ShakeAndWinBlockProps extends BlockProps {}

export const ShakeAndWinBlock: React.FC<ShakeAndWinBlockProps> = props => {
  const dispatch = useDispatch();

  // Actions
  const { setData } = mediaTemplateDesignActions;

  // Props
  const { namespace, settings, isPreviewMode, id } = props;
  const { dynamic, sections, triggerSettings, errorMessage } = settings;
  const { previewUrl = {}, altText = {}, linkedUrl = {} } = dynamic;
  const { token, user_id, account_id } = useUserInfo();
  const { reminderNotification, shakeTrigger } = triggerSettings;

  // i18n
  const { t } = useTranslation();

  // Selectors
  const contentSourcesData = useSelector(selectCSDataOfGroup);
  const { data: promotionPools = { rows: [], total: 0 } } = useGetListPromotionPool();
  const sidePanel = useSelector(selectSidePanel);

  // Refs
  const shakeRef = useRef<HTMLDivElement>(null);
  const shakeWrapperRef = useRef<HTMLDivElement>(null);
  const shakeErrorRef = useRef<HTMLDivElement>(null);
  const shakeErrorPlaceholderRef = useRef<HTMLDivElement>(null);
  const isOverSize = useRef(false);
  const wrapperClientRect = shakeWrapperRef.current ? shakeWrapperRef.current.getBoundingClientRect() : null;
  const shakeRect = shakeRef.current ? shakeRef.current.getBoundingClientRect() : null;

  // States
  const [wrapperSize, setWrapperSize] = useState({
    width: wrapperClientRect?.width || STANDARD_WIDTH,
    height: wrapperClientRect?.height || STANDARD_WIDTH,
  });

  isOverSize.current =
    wrapperClientRect && shakeRect
      ? (isOverSize.current = Math.round(wrapperClientRect.width) <= Math.round(shakeRect.width))
      : false;

  const [deltaScale, setDeltaScale] = useState(wrapperSize.height / STANDARD_WIDTH);

  useEffect(() => {
    const wrapperObserver = new ResizeObserver((entrys: ResizeObserverEntry[]) => {
      if (!shakeWrapperRef?.current) {
        return;
      }
      for (let entry of entrys) {
        const cr = entry.contentRect;
        setWrapperSize(cr);
        setDeltaScale(Math.min(cr.width, cr.height) / STANDARD_WIDTH);
      }
    });

    shakeWrapperRef?.current && wrapperObserver.observe(shakeWrapperRef?.current);

    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      shakeWrapperRef?.current && wrapperObserver.unobserve(shakeWrapperRef?.current);
    };
  }, []);

  useEffect(() => {
    if (promotionPools?.total) {
      dispatch(
        setData({
          promotionPool: promotionPools,
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotionPools]);

  let realDetaScale = isOverSize.current ? deltaScale : deltaScale;

  if (shakeRect && wrapperClientRect && STANDARD_WIDTH * realDetaScale > wrapperClientRect?.height) {
    realDetaScale = wrapperClientRect?.height / STANDARD_WIDTH;
  }

  useEffect(() => {
    if (
      shakeErrorRef.current &&
      sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
      sidePanel.blockSelectedId === id
    ) {
      shakeErrorRef.current.style.width = STANDARD_WIDTH * realDetaScale + 'px';
    }

    if (
      shakeErrorPlaceholderRef.current &&
      sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
      sidePanel.blockSelectedId === id
    ) {
      shakeErrorPlaceholderRef.current.style.width = STANDARD_WIDTH * realDetaScale + 'px';
    }
  }, [errorMessage, sidePanel.activePanel, sidePanel.blockSelectedId, id, realDetaScale]);

  // Memoized
  const [dynamicData, dynamicDataPreview, isDynamic, dynamicIndexData, isShakeTriggerSystem] = useMemo(() => {
    const draftData = {
      previewUrl: settings.uploadedImage.previewUrlBefore ? settings.uploadedImage.previewUrlBefore.toString() : '',
      altText: settings.altText,
      linkedUrl: settings.linkedUrl,
    };
    const draftDataPreview = {
      previewUrl: settings.uploadedImage.previewUrlBefore ? settings.uploadedImage.previewUrlBefore.toString() : '',
      altText: settings.altText,
      linkedUrl: settings.linkedUrl,
    };
    let isDynamic = false;
    const dynamicIndexData: number[] = [];

    // Preview url
    if (previewUrl.isDynamic) {
      draftDataPreview.previewUrl = getRawDynamicData({
        dataTableBO: getDataBOfromDM(previewUrl, contentSourcesData.data),
        dynamicItem: previewUrl,
      });

      draftData.previewUrl = isPreviewMode ? buildMergeTag(previewUrl) : draftDataPreview.previewUrl;

      isDynamic = true;
      dynamicIndexData.push(previewUrl.index);
    }

    // Alt text
    if (altText.isDynamic) {
      draftDataPreview.altText = getRawDynamicData({
        dataTableBO: getDataBOfromDM(altText, contentSourcesData.data),
        dynamicItem: altText,
      });

      draftData.altText = isPreviewMode ? buildMergeTag(altText) : draftDataPreview.altText;

      isDynamic = true;
      dynamicIndexData.push(altText.index);
    }

    // Linked url
    if (linkedUrl.isDynamic) {
      draftDataPreview.linkedUrl = getRawDynamicData({
        dataTableBO: getDataBOfromDM(linkedUrl, contentSourcesData.data),
        dynamicItem: linkedUrl,
      });

      draftData.linkedUrl = isPreviewMode ? buildMergeTag(linkedUrl) : draftDataPreview.linkedUrl;

      isDynamic = true;
      dynamicIndexData.push(linkedUrl.index);
    }

    // check shakeTrigger System
    const isShakeTriggerSystem = shakeTrigger === TRIGGER_OPTIONS.SYSTEM.value;

    return [draftData, draftDataPreview, isDynamic, dynamicIndexData, isShakeTriggerSystem];
  }, [previewUrl, altText, contentSourcesData, linkedUrl, settings, isPreviewMode]);

  // Handlers
  const renderImageDynamic = (renderData, { classLinked, relLinked }, isPreview = false) => {
    const proxyImageUrl = isPreview
      ? `${APP_CONFIG.API_HOST}/api/v1/saved-image/external-url/${random(
          8,
        )}?_token=${token}&_user_id=${user_id}&_account_id=${account_id}&imageUrl=${renderData.previewUrl}`
      : '';

    return settings.linkedImage || renderData.linkedUrl ? (
      <a
        className={classLinked}
        href={renderData.linkedUrl}
        {...(settings.linkedTarget === '' ? {} : { target: settings.linkedTarget })}
        {...(relLinked === '' ? {} : { rel: relLinked })}
        target={settings.linkedTarget}
        rel={relLinked}
        style={{
          display: !isPreview ? 'inline' : 'none',
        }}
        {...(isPreview ? { 'data-dynamic-preview': 1 } : { 'data-dynamic-display': 1 })}
      >
        <img
          src={renderData.previewUrl || 'invalid'}
          alt={renderData.altText}
          style={{ ...settings.styles, display: 'inline' }}
          {...(isPreview && { 'data-proxy-image-url': proxyImageUrl })}
          onError={(e: any) => {
            if (!isPreviewMode) {
              e.target.src = PlaceholderImage;
              e.target.dataset.src = renderData.previewUrl || 'invalid';
            }
          }}
        />
      </a>
    ) : (
      <img
        src={renderData.previewUrl || 'invalid'}
        alt={renderData.altText}
        style={{ ...settings.styles, display: !isPreview ? 'inline' : 'none' }}
        // width={settings.dimensions.width}
        // height={settings.dimensions.height}
        {...(isPreview
          ? { 'data-dynamic-preview': 1, 'data-proxy-image-url': proxyImageUrl }
          : { 'data-dynamic-display': 1 })}
        onError={(e: any) => {
          if (!isPreviewMode) {
            e.target.src = PlaceholderImage;
            e.target.dataset.src = renderData.previewUrl || 'invalid';
          }
        }}
      />
    );
  };

  const renderMessageWithPlaceholder = position => {
    return (
      <>
        {renderMessage(position)} {renderMessage(position, false)}
      </>
    );
  };

  const renderMessage = (position, isMainMessage = true) => {
    return position === errorMessage?.position ? (
      <div
        ref={isMainMessage ? shakeErrorRef : shakeErrorPlaceholderRef}
        className={`${
          isMainMessage ? namespace : 'ants-invisible placeholder'
        }-coupon-wheel-error ants-flex ants-justify-center ants-items-center ants-z-1
        ${
          position === ERROR_POSITION.CENTER
            ? 'ants-absolute ants-inset-0'
            : position === ERROR_POSITION.TOP
            ? isMainMessage
              ? 'ants-absolute ants-top-2'
              : 'ants-relative ants-top-2 ants-h-14'
            : isMainMessage
            ? 'ants-absolute ants-bottom-2'
            : 'ants-relative ants-bottom-2 ants-h-14'
        }`}
        style={{
          fontSize: 13,
          display:
            sidePanel.blockSelectedId === id &&
            sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
            !isPreviewMode
              ? 'flex'
              : 'none',
          ...(isOverSize.current
            ? {
                left: 'unset',
                right: 'unset',
              }
            : {
                left: '50%',
                right: 'unset',
                transform: `translateX(calc(-50%))`,
              }),
        }}
      >
        <div
          className="ants-px-15px ants-py-10px ants-bg-gray-4 
            ants-text-cus-primary ants-w-fit ants-h-fit
            ants-flex ants-items-center ants-gap-10px
            ants-rounded ants-relative ants-text-center"
        >
          <WarningIcon
            style={{
              width: 21,
              height: 21,
            }}
          />
          {errorMessage.message}
        </div>
      </div>
    ) : null;
  };

  const renderImage = () => {
    //
    const relLinked = settings.linkedNoFollow ? 'nofollow' : '';
    const classLinked = settings.linkedTracking
      ? `${PREFIX_EL_NAME}-trigger-conversion`
      : `${PREFIX_EL_NAME}-no-conversion`;
    //

    if (settings.uploadedImage.previewUrlBefore || isDynamic) {
      return (
        <>
          {renderImageDynamic(dynamicData, { classLinked, relLinked })}
          {isDynamic && isPreviewMode && renderImageDynamic(dynamicDataPreview, { classLinked, relLinked }, true)}
        </>
      );
    }

    return (
      <ShakeAndWinBlockEmptyWrapper>
        <Icon type="icon-ants-plus-circle" className="ants-text-primary" size={50} />
        <Text className="ants-mt-2.5">{t(translations.clickToAddImage.title)}</Text>
      </ShakeAndWinBlockEmptyWrapper>
    );
  };

  const getPositionNoti = position => {
    if (position === 'center top') {
      return 0;
    } else if (position === 'center center') {
      return '50%';
    }
    return 'unset';
  };

  return (
    <>
      {renderMessageWithPlaceholder(ERROR_POSITION.TOP)}
      {renderMessageWithPlaceholder(ERROR_POSITION.CENTER)}
      <ShakeAndWinBlockWrapper
        id={`${namespace}-${settings.component}--${id}`}
        ref={shakeWrapperRef}
        className={`${namespace}-imge-content ${namespace}-${settings.component}--content`}
        data-wheel={buildPromotionSectionsData(sections, promotionPools)}
        data-tag={buildPromotionTagData(sections, promotionPools)}
        style={{
          ...settings.outerContainerStyles,
          ...(!isPreviewMode && { pointerEvents: 'none' }),
        }}
        {...(isDynamic && {
          [APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_IS_DYNAMIC]: 1,
          [APP_CONFIG.DYNAMIC_ATTRIBUTE.DATA_DYNAMIC_INDEX]: dynamicIndexData.join(','),
        })}
      >
        {previewUrl.isDynamic && contentSourcesData.isLoading ? (
          <>
            <Skeleton.Image className="!ants-w-full" style={{ height: 200 }} />
            <Skeleton.Input className="ants-absolute ants-top-1 ants-left-1 !ants-w-1/2" size="small" active />
          </>
        ) : (
          <ShakeAndWinContainer className="animate__animated animate__fadeIn">
            {renderImage()}
            {(settings.uploadedImage.previewUrlBefore || isDynamic) && (
              <div
                className="ants-absolute ants-left-[50%] ants-text-center reminder-notification"
                style={{
                  transform: 'translateX(-50%)',
                  top: getPositionNoti(
                    settings.notificationStylesSettings && settings.notificationStylesSettings.position,
                  ),
                  bottom:
                    settings.notificationStylesSettings &&
                    settings.notificationStylesSettings.position === 'center bottom'
                      ? '0'
                      : 'unset',
                  ...settings.notificationStyles,
                  ...(!isPreviewMode && { pointerEvents: 'none' }),
                  transition: 'all .3s ease-in-out',
                  display: isShakeTriggerSystem || isPreviewMode ? 'none' : 'block',
                }}
              >
                {reminderNotification}
              </div>
            )}
          </ShakeAndWinContainer>
        )}
      </ShakeAndWinBlockWrapper>
      {renderMessageWithPlaceholder(ERROR_POSITION.BOTTOM)}
    </>
  );
};

export default ShakeAndWinBlock;
