// Libraries
import React, { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import produce from 'immer';

// Queries
import { queryClient } from 'index';
import { QUERY_KEYS } from 'constants/queries';
import { useGetListSourceBO } from 'app/queries/BusinessObject';

// Services
import { getListAttributes, getListEvents } from 'app/services/MediaTemplateDesign/BusinessObject';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Constants
import { getAttrDefault, getEventDefault, getSourceDefault, mapFieldToFieldName } from '../constants';

// Actions
import { mediaTemplateDesignActions } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { handleError } from 'app/utils';

// Types
import { TBlock } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/SyncData/hooks/useDefaultSyncData.ts';

type TDefaultSyncData = {
  blocks: TBlock[];
};

export const useDefaultSyncData = (props: TDefaultSyncData) => {
  const dispatch = useDispatch();

  // Actions
  const { updateBlockFieldsAllViewById, setLoadingSyncData } = mediaTemplateDesignActions;

  // Props
  const { blocks } = props;

  // Queries
  const { data: sourceBO = [] } = useGetListSourceBO({
    staleTime: Infinity,
  });

  const onChangeSyncDataSettings = useCallback(
    (blockId: string, settings = {}) => {
      try {
        dispatch(
          updateBlockFieldsAllViewById({
            blockId,
            dataUpdate: [
              {
                fieldPath: 'settings',
                data: settings,
              },
            ],
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeDefault',
          args: {},
        });
      }
    },
    [dispatch, updateBlockFieldsAllViewById],
  );

  const onChangeLoadingSyncData = useCallback(
    (loading = false) => {
      try {
        dispatch(setLoadingSyncData(loading));
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeLoadingSyncData',
          args: {},
        });
      }
    },
    [dispatch, setLoadingSyncData],
  );

  useDeepCompareEffect(() => {
    let isCancelled = false;
    const run = async () => {
      if (sourceBO?.length === 0 || blocks?.length === 0) return;

      await Promise.all(
        blocks.map(async block => {
          const { syncData, fields } = block.settings;
          const { dataSourceOut, allDataSourceOut, isCouponCode } = mapFieldToFieldName(
            fields,
            syncData?.columnMapping,
            syncData?.isAction,
          );

          // Xác định chưa có data, cần set default
          if (dataSourceOut && !syncData?.isAction && !syncData?.columnMapping?.length) {
            // Default Source
            const sourceDefault = getSourceDefault(sourceBO);
            const eventBO = await queryClient.fetchQuery({
              queryKey: [QUERY_KEYS.GET_LIST_EVENT_BY_SOURCE, sourceDefault.insightPropertyId],
              queryFn: async () => getListEvents({ source_id: sourceDefault.insightPropertyId }),
              staleTime: Infinity,
            });

            // Default Event
            const eventDefault = getEventDefault(eventBO);
            const eventSource = `${eventDefault.eventActionId}:${eventDefault.eventCategoryId}`;
            const attributeBO = await queryClient.fetchQuery({
              queryKey: [QUERY_KEYS.GET_LIST_EVENT_ATTR, sourceDefault.insightPropertyId, eventSource],
              queryFn: () =>
                getListAttributes({
                  eventActionId: eventDefault.eventActionId,
                  eventCategoryId: eventDefault.eventCategoryId,
                  sourceId: sourceDefault.insightPropertyId,
                }),
              staleTime: Infinity,
            });

            // Default Attribute
            const dataAttrDefault = getAttrDefault(dataSourceOut, attributeBO);

            // Stop update when side panel is updated
            if (isCancelled) return;

            // Update Redux Settings
            const settingUpdated = produce(block.settings, draft => {
              draft.syncData = {
                ...draft.syncData,
                source: { ...sourceDefault },
                event: { ...eventDefault },
                columnMapping: [...dataAttrDefault],
                isAction: true,
              };
            });
            onChangeSyncDataSettings(block.id, settingUpdated);
          }
        }),
      );
    };

    run();

    return () => {
      if (sourceBO.length) {
        isCancelled = true;
      }
    };
  }, [blocks.length, sourceBO]);

  useEffect(() => {
    onChangeLoadingSyncData(!!blocks.length);
  }, [blocks.length, onChangeLoadingSyncData]);
};
