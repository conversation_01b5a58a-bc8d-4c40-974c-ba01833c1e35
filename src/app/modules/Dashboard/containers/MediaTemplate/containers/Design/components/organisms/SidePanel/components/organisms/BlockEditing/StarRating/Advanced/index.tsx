// Libraries
import React, { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';

// Translations
import { translations } from 'locales/translations';

// Selectors
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Organisms
import { DisplayCondition } from '../../../DisplayCondition';

// Utils
import { handleError } from 'app/utils/handleError';

//Molecules
import { Collapse } from 'app/components/molecules';
import { Panel } from 'app/components/molecules/Collapse';

// Types
import { BlockStyling } from '../../Common/BlockStyling';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Columns/Advanced/index.tsx';

interface AdvancedProps {}

export const Advanced: React.FC<AdvancedProps> = memo(props => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateBlockSetting } = mediaTemplateDesignActions;

  // Selector
  const blockSelected = useSelector(selectBlockSelected);

  const blockSettings = blockSelected?.settings;

  const {
    displayCondition = {
      condition: '',
      field: '',
      index: 1,
      operator: '',
      dataType: '',
      value: '',
    },
  } = blockSettings?.blockStylesSettings || {};

  const onChangeDisplayCondition = (settings = {}) => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              draft.blockStylesSettings.displayCondition = {
                ...draft.blockStylesSettings.displayCondition,
                ...settings,
              };
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  if (blockSettings) {
    return (
      <Collapse defaultActiveKey={['1']} accordion>
        <Panel header={t(translations.containerStyling.title)} key="1">
          <BlockStyling />
        </Panel>
        <Panel header={t(translations.displayCondition.title)} key="2">
          <DisplayCondition
            displayCondition={displayCondition}
            valueCondition={displayCondition.condition}
            onChange={onChangeDisplayCondition}
          />
        </Panel>
      </Collapse>
    );
  }

  return null;
});
