//Libraries
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Trans, useTranslation } from 'react-i18next';

//styled
import { TextStylingWrapper } from './styled';

//components
import { Alert, Switch, Text } from 'app/components/atoms';

//molecules
import { Select } from 'app/components/molecules';
import { AlignSetting, ColorSetting, SettingWrapper } from '../../molecules';

//constant
import { NUMBER_OF_LINE } from './constants';

// Selectors
import { selectBlockSelected, selectBreakpoint, selectIsMobileMode } from '../../../../../../slice/selectors';

//actions
import { mediaTemplateDesignActions } from '../../../../../../slice';

//Locales
import { translations } from 'locales/translations';
import { getBlockResponsiveSettings } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import { getChangedResponsiveSettingsData } from '../../../utils';

interface TextStylingProps {}

export const TextStyling: React.FC<TextStylingProps> = props => {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);
  const breakpoint = useSelector(selectBreakpoint);
  const isMobileMode = useSelector(selectIsMobileMode);

  const blockSettings = blockSelected?.settings;
  const blockId = blockSelected?.id || '';

  const responsiveSettings = getBlockResponsiveSettings({ breakpoint, settings: blockSettings });

  const { breakpoints } = blockSettings;
  const { selectLineDisplay = '', isEllipsisText = true } = blockSettings?.textStyling || {};
  const { color, textAlign = 'left' } = responsiveSettings.textStyling || {};

  //Actions
  const { updateBlockText } = mediaTemplateDesignActions;

  useEffect(() => {
    if (
      !blockSettings?.textStyling ||
      !blockSettings?.textStyling.isEllipsis ||
      !blockSettings?.textStyling.selectLineDisplay
    ) {
      dispatch(
        updateBlockText({
          blockId,
          dataUpdate: [
            {
              fieldPath: 'settings.textStyling.selectLineDisplay',
              data: selectLineDisplay,
            },
            {
              fieldPath: 'settings.textStyling.isEllipsisText',
              data: isEllipsisText,
            },
          ],
        }),
      );
    }
  }, []);

  const handleChangeTextStyling = (data: any, type: any) => {
    if (blockSelected) {
      dispatch(
        updateBlockText({
          blockId,
          dataUpdate: [
            {
              fieldPath: `settings.textStyling.${type}`,
              data,
            },
          ],
        }),
      );
    }
  };

  const onChangeResponsive = (key: string, value: any) => {
    const dataUpdate = getChangedResponsiveSettingsData({
      breakpoint,
      currentData: {
        textStyling: blockSettings?.textStyling,
      },
      updatedData: {
        textStyling: {
          [key]: value,
        },
      },
      responsiveSettings: breakpoints?.[breakpoint],
    });

    dispatch(updateBlockText({ blockId, dataUpdate }));
  };

  return (
    <TextStylingWrapper>
      <ColorSetting
        label={`${t(translations.color.title)}:`}
        color={color}
        onChange={color => onChangeResponsive('color', color)}
      />

      <AlignSetting
        label={t(translations.align.title)}
        align={textAlign}
        onChange={value => onChangeResponsive('textAlign', value)}
      />

      {!isMobileMode && (
        <>
          <Text type="secondary" className="ants-font-bold">
            {t(translations.textStyling.sidePanel.numberOfRows.title)}
          </Text>
          <Select
            options={Object.values(NUMBER_OF_LINE)}
            value={selectLineDisplay}
            onChange={data => handleChangeTextStyling(data, 'selectLineDisplay')}
          />
          {selectLineDisplay && (
            <SettingWrapper
              label={t(translations.textStyling.sidePanel.ellipsisText.title)}
              className="ants-mt-3 ants-font-bold"
            >
              <Switch checked={isEllipsisText} onChange={data => handleChangeTextStyling(data, 'isEllipsisText')} />
            </SettingWrapper>
          )}
          <Alert className="ants-mt-3" type="info" message={<Trans i18nKey={translations.textStyling.info} />} />
        </>
      )}
    </TextStylingWrapper>
  );
};
