// Libraries
import { AutoComplete } from 'antd';
import { Modal } from 'app/components/molecules';
import styled from 'styled-components';

export const AddPersonalizationWrapper = styled.div``;
export const InputSuggestion = styled(AutoComplete)`
  .ant-select-selector {
    border-right: none !important;
    border-left: none !important;
    border-top: none !important;
    border-bottom: 1px solid #d9d9d9 !important;
  }
`;
export const ModalCustom = styled(Modal)`
  .ant-modal-body {
    overflow: auto;
    max-height: 500px;
  }
`;
