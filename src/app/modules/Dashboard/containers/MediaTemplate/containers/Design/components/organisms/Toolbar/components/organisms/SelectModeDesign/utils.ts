import { isEqual, omit } from 'lodash';
import { LAYOUT_TEMPLATE } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { INIT_PAGE_CODE_MODE, initialState } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

const { FULL_SCREEN, POP_UP, INLINE, SLIDE_IN, FLOATING_BAR } = LAYOUT_TEMPLATE;

export const checkIsChangeSettingsCodeMode = workspace => {
  const { codeModeSettings: initCodeModeSettings } = INIT_PAGE_CODE_MODE;
  const { viewPages, template, settings } = workspace;

  let isChangeCodeModeSettings = !isEqual(initCodeModeSettings, viewPages[0]?.codeModeSettings);

  switch (template.type) {
    case FULL_SCREEN.name:
    case POP_UP.name:
    case INLINE.name: {
      return isChangeCodeModeSettings;
    }

    case SLIDE_IN.name: {
      const { position: currentPosition, positionSettings: currentPositionSettings } =
        viewPages[0]?.settings?.slideClosedContainer || {};

      const isChangePosition =
        currentPosition === 'left' ||
        (currentPosition === 'right'
          ? !!(+currentPositionSettings.right || +currentPositionSettings.bottom)
          : !!(+currentPositionSettings.left || +currentPositionSettings.bottom));

      const isChangeSuffix = currentPositionSettings.positionSuffix !== 'px';

      return isChangeCodeModeSettings || isChangePosition || isChangeSuffix;
    }

    case FLOATING_BAR.name: {
      const { floatingBarPosition, positionSettings } = settings;
      const isChangePosition =
        floatingBarPosition === 'top' ||
        (positionSettings
          ? floatingBarPosition === 'bottom'
            ? !!(+positionSettings.bottom || +positionSettings.left)
            : !!(+positionSettings.top || +positionSettings.left)
          : false);

      const isChangeSuffix = positionSettings ? positionSettings.positionSuffix !== 'px' : false;

      return isChangeCodeModeSettings || isChangePosition || isChangeSuffix;
    }

    default:
      return false;
  }
};

const NO_NEED_COMPARE_KEYS = {
  PRESENT: ['data', 'saveData', 'toolbar', 'exportInfo'],
  WORKSPACE: ['id', 'name', 'template', 'objectiveTypes', 'thumbnail'],
  SIDE_PANEL: ['activePanel', 'activeTab', 'type'],
};

const omitedState = state => {
  const omitData: Record<string, any> = omit(state, NO_NEED_COMPARE_KEYS.PRESENT);
  const currentData = {
    ...omitData,
    workspace: {
      ...omit(omitData.workspace, NO_NEED_COMPARE_KEYS.WORKSPACE),
      settings: omit(omitData.settings, ['positionSettings']),
      viewPages: omitData.workspace.viewPages.map(page => {
        const { tree, settings, ...restPage } = page;
        return {
          ...omit(restPage, ['html', 'thumbnail']),
          settings: {
            ...settings,
            slideClosedContainer: omit(settings.slideClosedContainer, ['positionSettings']),
          },
        };
      }),
    },
    sidePanel: omit(omitData.sidePanel, NO_NEED_COMPARE_KEYS.SIDE_PANEL),
  };

  return currentData;
};

export const checkIsChangeSettingsDesignMode = present => {
  if (!present) return false;

  const { workspace } = present;

  const omitedPresent = omitedState(present);
  const omitedInitState = omitedState(initialState);

  const isChangedPresent = !isEqual(omitedPresent, omitedInitState);

  // console.log({
  //   omitedPresent,
  //   omitedInitState,
  // });

  if ([FLOATING_BAR.name, SLIDE_IN.name].includes(workspace.template.type)) {
    let isHasChangePosition = false;

    switch (workspace.template.type) {
      case FLOATING_BAR.name: {
        const { floatingBarPosition, positionSettings } = workspace?.settings || {};
        const isChangePosition =
          floatingBarPosition === 'top' ||
          (positionSettings
            ? floatingBarPosition === 'bottom'
              ? !!(+positionSettings.bottom || +positionSettings.left)
              : !!(+positionSettings.top || +positionSettings.left)
            : false);

        const isChangeSuffix = positionSettings ? positionSettings.positionSuffix !== 'px' : false;

        isHasChangePosition = isChangePosition || isChangeSuffix;
        break;
      }

      case SLIDE_IN.name: {
        const { viewPages = [] } = workspace || {};

        const isChangePosition = viewPages.some(page => {
          const currentPage = page.settings;

          const { position: currentPosition, positionSettings: currentPositionSettings } =
            currentPage?.slideClosedContainer || {};

          return (
            currentPosition === 'left' ||
            (currentPosition === 'right'
              ? !!(+currentPositionSettings.right || +currentPositionSettings.bottom)
              : !!(+currentPositionSettings.left || +currentPositionSettings.bottom))
          );
        });

        const isChangeSuffix = viewPages.some(page => {
          const currentPage = page.settings;

          const { position: currentPosition, positionSettings: currentPositionSettings } =
            currentPage?.slideClosedContainer || {};

          return currentPositionSettings.positionSuffix !== 'px';
        });

        const isChangeToggleSlide = workspace.settings.slideToggleState;

        isHasChangePosition = isChangePosition || isChangeSuffix || isChangeToggleSlide;
        break;
      }

      default:
        break;
    }

    return isHasChangePosition || isChangedPresent;
  }

  return isChangedPresent;
};
