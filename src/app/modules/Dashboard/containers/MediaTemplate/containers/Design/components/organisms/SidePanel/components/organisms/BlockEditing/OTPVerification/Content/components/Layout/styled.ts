import styled from 'styled-components';
import tw from 'twin.macro';

export const WrapperLayoutForm = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
`;

export const WrapperItem = styled.div`
  ${tw`ants-flex ants-justify-center ants-items-center ants-flex-col ants-cursor-pointer`}
`;

export const LayoutItem = styled.img<{ active: boolean }>`
  width: 145px;
  height: 100px;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  border-color: ${props => (props.active ? '#3060B8' : '')};
`;
