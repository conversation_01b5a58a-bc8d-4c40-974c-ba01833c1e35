// Libraries
import React, { useState } from 'react';

// Molecules
import { Tabs, TabPane } from 'app/components/molecules';

// Constants
import { SIDE_PANEL_TABS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/constants';

// Utils
import { handleError } from 'app/utils/handleError';

// Components
import { Content } from './Content';
import { Advanced } from './Advanced';
import MobileWarning from '../../../molecules/MobileWarning';

interface SurpriseTreasureHuntProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/SurpriseTreasureHunt/index.tsx';

const tabs = [
  {
    name: SIDE_PANEL_TABS.CONTENT.name,
    label: SIDE_PANEL_TABS.CONTENT.label,
  },
  {
    name: SIDE_PANEL_TABS.ADVANCED.name,
    label: SIDE_PANEL_TABS.ADVANCED.label,
  },
];

export const SurpriseTreasureHunt: React.FC<SurpriseTreasureHuntProps> = props => {
  // State
  const [state, setState] = useState({
    tabSelected: SIDE_PANEL_TABS.CONTENT.name,
  });

  // Handlers
  const onChangeTabs = (activeKey: string) => {
    try {
      setState(state => ({
        ...state,
        tabSelected: activeKey,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTabs',
        args: {},
      });
    }
  };

  const renderTabContent = () => {
    try {
      switch (state.tabSelected) {
        case SIDE_PANEL_TABS.CONTENT.name:
          return <Content />;

        case SIDE_PANEL_TABS.ADVANCED.name:
          return <Advanced />;
        default:
          return '';
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderTabContent',
        args: {},
      });
    }
  };

  return (
    <Tabs destroyInactiveTabPane defaultActiveKey={state.tabSelected} onChange={onChangeTabs}>
      {tabs.map(({ name, label }) => (
        <TabPane tab={label} key={name}>
          <MobileWarning />
          {renderTabContent()}
        </TabPane>
      ))}
    </Tabs>
  );
};
