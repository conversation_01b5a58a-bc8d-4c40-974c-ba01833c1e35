// Libraries
import React, { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';

// Translations
import { translations } from 'locales/translations';

// Selectors
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { handleError } from 'app/utils/handleError';
import {
  getBackgroundSettings,
  getBackgroundStyles,
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Atoms
import { Space } from 'app/components/atoms';

// Molecules
import { Collapse, Panel } from 'app/components/molecules/Collapse';
import { SettingWrapper } from '../../../../molecules';
import { Select, UploadImage } from 'app/components/molecules';

// Organisms
import { ContainerStyling } from '../../../ContainerStyling';
import { RoundedCornersSetting } from '../../../RoundedCornersSetting';
import { BorderSettingPopover } from '../../../BorderSetting';
import { BoxShadowSetting } from '../../../BoxShadowSetting';
import { SpacingSetting } from '../../../SpacingSetting';
import BackgroundSetting from '../../../BackgroundSetting';
import { BlockStyling } from '../../Common/BlockStyling';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/SurpriseTreasureHunt/Advanced/index.tsx';

interface AdvancedProps {}

export const Advanced: React.FC<AdvancedProps> = memo(props => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateBlockSetting } = mediaTemplateDesignActions;

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const blockSettings = blockSelected?.settings;

  // Handler Shake Styling
  const onChangeSurpriseTreasureHuntStyling = useCallback(
    (blockStylesSettings = {}, blockStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.stylesSettings = {
                  ...draft.stylesSettings,
                  ...blockStylesSettings,
                };
                draft.styles = {
                  ...draft.styles,
                  ...blockStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeSurpriseTreasureHuntStyling',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeSurpriseTreasureHuntCellStyling = useCallback(
    (blockCellStylesSettings = {}, blockCellStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.cellStylesSettings = {
                  ...draft.cellStylesSettings,
                  ...blockCellStylesSettings,
                };
                draft.cellStyles = {
                  ...draft.styles,
                  ...blockCellStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeSurpriseTreasureHuntCellStyling',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onUpdateBlockSettings = (settings = {}, styles = {}) => {
    try {
      // Callback onChange
      onChangeSurpriseTreasureHuntStyling(
        {
          ...blockSettings.stylesSettings,
          ...settings,
        },
        {
          ...blockSettings.styles,
          ...styles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  const onUpdateCellBlockSettings = (settings = {}, styles = {}) => {
    try {
      // Callback onChange
      onChangeSurpriseTreasureHuntCellStyling(
        {
          ...blockSettings.cellStylesSettings,
          ...settings,
        },
        {
          ...blockSettings.cellStyles,
          ...styles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateCellBlockSettings',
        args: {},
      });
    }
  };

  const onUpdateBackgroundImage = (dataIn: any) => {
    try {
      const { isDataGiftBox = false, url = '' } = dataIn;
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              if (isDataGiftBox) {
                draft.backgroundImageSettings.giftBoxUrl = url;
              } else {
                draft.backgroundImageSettings.cellUrl = url;
              }
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBackgroundImage',
        args: {},
      });
    }
  };

  if (blockSettings) {
    const renderGiftBoxStyling = (): React.ReactNode => (
      <Space size={20} direction="vertical">
        <BackgroundSetting
          label={t(translations.style.title)}
          settings={getBackgroundSettings(blockSettings.stylesSettings)}
          styles={getBackgroundStyles(blockSettings.styles)}
          onChange={onUpdateBlockSettings}
        />
        <SettingWrapper label={t(translations.backgroundImage.title)} />
        <UploadImage
          required
          selectedImage={{ url: blockSettings.backgroundImageSettings.giftBoxUrl }}
          // showImageURL={false}
          onRemoveImage={_value => onUpdateBackgroundImage({ isDataGiftBox: true, url: '' })}
          onChangeImage={value => onUpdateBackgroundImage({ isDataGiftBox: true, url: value.url })}
        />
        <BoxShadowSetting
          settings={getBoxShadowSettings(blockSettings.stylesSettings as any)}
          onChange={(settings, styles) => {
            onUpdateBlockSettings(settings, styles);
          }}
        />
        <BorderSettingPopover
          settings={getBorderSettings(blockSettings.stylesSettings as any)}
          styles={getBorderStyles(blockSettings.styles)}
          onChange={onUpdateBlockSettings}
        />
        <RoundedCornersSetting
          settings={getRoundedCornersSettings(blockSettings.stylesSettings as any)}
          styles={getRoundedCornersStyles(blockSettings.styles)}
          onChange={onUpdateBlockSettings}
        />
        <SpacingSetting
          isPaddingSetting={false}
          settings={getSpacingSettings(blockSettings.stylesSettings as any)}
          styles={getSpacingStyles(blockSettings.styles)}
          onChange={onUpdateBlockSettings}
        />
      </Space>
    );

    const renderCellStyling = (): React.ReactNode => (
      <Space size={20} direction="vertical">
        <BackgroundSetting
          label={t(translations.style.title)}
          settings={getBackgroundSettings(blockSettings.cellStylesSettings)}
          styles={getBackgroundStyles(blockSettings.cellStyles)}
          onChange={onUpdateCellBlockSettings}
        />
        <SettingWrapper label={t(translations.backgroundImage.title)} />
        <UploadImage
          required
          selectedImage={{ url: blockSettings.backgroundImageSettings.cellUrl }}
          // showImageURL={false}
          onRemoveImage={_value => onUpdateBackgroundImage({ isDataGiftBox: false, url: '' })}
          onChangeImage={value => onUpdateBackgroundImage({ isDataGiftBox: false, url: value.url })}
        />
        <BoxShadowSetting
          settings={getBoxShadowSettings(blockSettings.cellStylesSettings as any)}
          onChange={(settings, styles) => {
            onUpdateCellBlockSettings(settings, styles);
          }}
        />
        <BorderSettingPopover
          settings={getBorderSettings(blockSettings.cellStylesSettings as any)}
          styles={getBorderStyles(blockSettings.cellStyles)}
          onChange={onUpdateCellBlockSettings}
        />
        <RoundedCornersSetting
          settings={getRoundedCornersSettings(blockSettings.cellStylesSettings as any)}
          styles={getRoundedCornersStyles(blockSettings.cellStyles)}
          onChange={onUpdateCellBlockSettings}
        />
        <SpacingSetting
          settings={getSpacingSettings(blockSettings.cellStylesSettings as any)}
          styles={getSpacingStyles(blockSettings.cellStyles)}
          onChange={onUpdateCellBlockSettings}
        />
      </Space>
    );

    return (
      <Collapse defaultActiveKey={['gift-box-styling']} accordion>
        <Panel header={t(translations.giftBoxStyling.title)} key="gift-box-styling">
          {renderGiftBoxStyling()}
        </Panel>
        <Panel header={t(translations.cellStyling.title)} key="cell-styling">
          {renderCellStyling()}
        </Panel>
        <Panel header={t(translations.containerStyling.title)} key="container-styling">
          <BlockStyling />
        </Panel>
      </Collapse>
    );
  }

  return null;
});
