// Libraries
import { <PERSON><PERSON><PERSON><PERSON>, UIEvent<PERSON><PERSON><PERSON>, useEffect, useState } from 'react';
import tw from 'twin.macro';
import { keyBy } from 'lodash';
import styled from 'styled-components';
import { TreeSelectProps as AntdTreeSelectProps } from 'antd';
import { useTranslation } from 'react-i18next';

// Atoms
import { Tag } from 'app/components/atoms/Tag';
import { Button, Divider, Text } from 'app/components/atoms';

// Molecules
import { StyledTreeSelect } from '../InputOrSelect';

// Services
import {
  getListInfoMetadata,
  getListSuggestionItems,
  getListSuggestionObjects,
} from 'app/services/MediaTemplateDesign/BusinessObject';

// Translation
import { translations } from 'locales/translations';

// Hooks
import { useDebounce } from 'app/hooks';

// Constants
import { SEGMENT_IDS } from '../../constants';

// Types
import { EventBoFieldMetadata } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { getScopSuggest } from '../../../FilterSetting/utils';
import { buildOptionArchive } from '../../../../../utils';

interface InputSelectMultiProps {
  value: string[];
  itemTypeId: number | null;
  itemTypeName: string | null;
  column: string | null;
  eventMetadata?: EventBoFieldMetadata | any;
  onChange: (val: InputSelectMultiProps['value']) => void;
  isDisable: boolean | null;
  errorMessage: string | null;
  required?: boolean;
  focused?: boolean;
}

type TreeData = {
  value: string;
  title: string;
  key: string;
  isLeaf: boolean;
}[];

export const InputSelectMulti: React.FC<InputSelectMultiProps> = props => {
  // Props
  const { value, itemTypeId, itemTypeName, column, onChange, isDisable, required, focused, errorMessage } = props;

  // State
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [treeData, setTreeData] = useState<TreeData>([]);
  const [page, setPage] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const [isShowCheck, setIsShowCheck] = useState(false);
  const [mapLabel, setMapLabel] = useState({});
  // Debounce
  const debounceSearchValue = useDebounce(searchValue, 500);

  useEffect(() => {
    (async () => {
      setLoading(true);

      const res = await getSuggestions({ pageNum: page, search: debounceSearchValue });

      const newData = {
        ...res,
      };

      setData(newData);
      setTreeData(Object.values(newData));
      setLoading(false);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceSearchValue]);

  useEffect(() => {
    if (column === SEGMENT_IDS) {
      (async () => {
        const res = await getInfoMetadata();

        const newData = {
          ...res,
        };

        setMapLabel(newData);
      })();
    }
  }, [value]);

  const getSuggestions: any = async ({ pageNum, search }) =>
    column === SEGMENT_IDS
      ? await getSuggestionObjects({ pageNum, search })
      : await getSuggestionItems({ pageNum, search });

  const getSuggestionItems = async ({ pageNum, search }) => {
    const { rows } = await getListSuggestionItems({
      limit: 20,
      page: pageNum,
      sd: 'asc',
      search: search,
      decryptFields: [column],
      propertyCode: column,
      itemTypeId,
      itemTypeName,
      // 1 | 3
      scope: getScopSuggest({ itemTypeId }),
      itemPropertyName: column,
      systemDefined: 0,
      isPk: 0,
    });

    const data = keyBy(
      rows.map((item: { id?: string | number; name: string }) => {
        const key = item.id || item.name;
        return {
          value: key,
          title: key,
          key: key,
          isLeaf: true,
        };
      }),
      'value',
    );

    return data;
  };

  const getSuggestionObjects = async ({ pageNum, search }) => {
    const { rows } = await getListSuggestionObjects({
      limit: 20,
      page: pageNum,
      sd: 'asc',
      search: search,
      decryptFields: [column],
      propertyCode: column,
      itemTypeId,
      itemTypeName,
      isLookupLabelId: true,
      feServices: 'suggestionMultilang',
      feKey: `${itemTypeId}-${itemTypeName}-${column}`,
      objectType: 'BO_SEGMENTS',
      isPk: 1,
      objectName: column,
      filters: {
        OR: [
          {
            AND: [
              {
                type: 1,
                column: 'item_type_id',
                data_type: 'number',
                operator: 'equals',
                value: itemTypeId,
              },
            ],
          },
        ],
      },
    });

    const data = keyBy(
      rows.map((item: { id?: string | number; name: string; status: number }) => {
        // const key = item.id || item.name;
        return {
          value: item.id,
          title: item.name,
          key: item.id,
          status: +item.status,
          isLeaf: true,
        };
      }),
      'value',
    );

    return data;
  };

  const getInfoMetadata = async () => {
    const { rows } = await getListInfoMetadata({
      objectType: 'BO_SEGMENTS',
      filters: {
        OR: [
          {
            AND: [
              {
                column: 'segment_id',
                data_type: 'number',
                operator: 'matches',
                value,
              },
            ],
          },
        ],
      },
    });

    const data = rows.reduce((acc, cur) => {
      acc[cur.id] = cur.name;
      return acc;
    }, {});

    return data;
  };

  const onChangeValue = (val: string[]) => {
    onChange(val);
  };

  const onChangeTreeData = (val: TreeData) => {
    setTreeData(val);
  };

  const onLoadMore = async () => {
    setLoadingMore(true);
    const newPage = page + 1;

    const res = await getSuggestions({ pageNum: newPage, search: searchValue });

    const newData = {
      ...data,
      ...res,
    };

    setData(newData);
    setTreeData(Object.values(newData));
    setLoadingMore(false);

    setPage(prev => prev + 1);
  };

  const onScroll: UIEventHandler<HTMLDivElement> = event => {
    if (isShowCheck) {
      return;
    }

    const target = event.target as Element;
    const bottom = target.scrollHeight - target.scrollTop < target.clientHeight + 70;
    if (bottom && !loadingMore) {
      onLoadMore();
    }
  };

  const onSearch = (val: string) => {
    setLoading(true);
    setPage(1);
    setSearchValue(val);
  };

  const onChangeIsShowCheck = () => {
    if (!isShowCheck) {
      const newData = props.value.map(
        name =>
          data[name] || {
            // hard value
            value: name,
            title: name,
            key: name,
            isLeaf: name,
          },
      );
      onChangeTreeData(newData);
    } else {
      onChangeTreeData(Object.values(data));
    }
    setIsShowCheck(prev => !prev);
  };

  const onUncheckAll = () => {
    props.onChange?.([]);
  };

  return (
    <>
      <SelectMultiple
        // open
        required={required}
        focused={focused}
        virtual={false}
        treeData={buildOptionArchive(treeData, value)}
        value={value}
        searchValue={searchValue}
        notFoundContent={
          <div className="ants-flex ants-justify-center ants-items-center ants-h-32">
            {loading ? 'Loading...' : 'No data'}
          </div>
        }
        onSearch={onSearch}
        onChange={onChangeValue}
        onChangeTreeData={onChangeTreeData}
        // data={data}
        loading={loading}
        onScroll={onScroll}
        isShowCheck={isShowCheck}
        onChangeIsShowCheck={onChangeIsShowCheck}
        onUncheckAll={onUncheckAll}
        mapLabel={mapLabel}
      />
      {errorMessage ? (
        <Text color="#ff4d4f" className="ants-ml-2">
          {errorMessage}
        </Text>
      ) : null}
    </>
  );
};

interface SelectMultipleProps extends AntdTreeSelectProps, Omit<SelectMultipleDropdownProps, 'menu'> {
  loading?: boolean;
  onChangeTreeData: (treeData: any) => void;
  // data: {
  //   [key: string]: {
  //     value: string;
  //     title: string;
  //     key: string;
  //     isLeaf: boolean;
  //   };
  // };
  mapLabel: { [key: string | number]: string };
  dropdownExtraContent?: ReactNode;
  required?: boolean;
  focused?: boolean;
}

export const SelectMultiple: React.FC<SelectMultipleProps> = props => {
  const {
    onChangeTreeData,
    // data,
    loading,
    onScroll,
    isShowCheck,
    onChangeIsShowCheck,
    onUncheckAll,
    disabled,
    mapLabel,
    dropdownExtraContent,
    ...restOf
  } = props;

  // I18next
  const { t } = useTranslation();

  return (
    <StyledTreeSelect
      showSearch
      treeCheckable
      maxTagCount="responsive"
      tagRender={v => <Tag {...v}>{mapLabel[v.value] || v.label}</Tag>}
      placeholder={t(translations.selectAnItem.title)}
      dropdownRender={menu =>
        loading ? (
          <div className="ants-flex ants-justify-center ants-items-center ants-h-32">
            <Text color="#666">Loading...</Text>
          </div>
        ) : (
          <SelectMultipleDropdown
            menu={menu}
            isShowCheck={isShowCheck}
            extraContent={dropdownExtraContent}
            onChangeIsShowCheck={onChangeIsShowCheck}
            onUncheckAll={onUncheckAll}
            onScroll={onScroll}
          />
        )
      }
      {...restOf}
    />
  );
};

interface SelectMultipleDropdownProps {
  menu: ReactNode;
  isShowCheck: boolean;
  onChangeIsShowCheck: () => void;
  onUncheckAll: () => void;
  onScroll: UIEventHandler<HTMLDivElement>;
  extraContent?: ReactNode;
}

export const SelectMultipleDropdown: React.FC<SelectMultipleDropdownProps> = props => {
  const { menu, isShowCheck, onChangeIsShowCheck, onUncheckAll, onScroll, extraContent } = props;

  return (
    <StyledDropdown>
      <div className="ants-flex ants-items-center ants-justify-between">
        <Button type="text" onClick={onChangeIsShowCheck}>
          {!isShowCheck ? 'Show checked' : 'Show all'}
        </Button>
        <Button type="text" onClick={onUncheckAll}>
          Uncheck all
        </Button>
      </div>
      <Divider className="!ants-mt-0 !ants-mb-2" />
      <div onScrollCapture={onScroll}>{menu}</div>
      {extraContent}
    </StyledDropdown>
  );
};

export const StyledDropdown = styled.div`
  .ant-select-tree-list {
    .ant-select-tree-title {
      ${tw`ants-text-normal`}
    }
    .ant-select-tree-treenode {
      ${tw`ants-mr-2 ants-ml-2`}
      .ant-select-tree-switcher.ant-select-tree-switcher-noop {
        ${tw`ants-hidden`}
      }
    }
  }
`;
