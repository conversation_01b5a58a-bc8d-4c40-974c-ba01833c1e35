// Libraries
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Organisms
import { ContainerStyling } from '../../../ContainerStyling';

// Selectors
import {
  selectBlockSelected,
  selectBreakpoint,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { tryCatchWrapper } from 'app/utils';
import { useDeepCompareMemo } from 'app/hooks';
import { getBlockResponsiveSettings } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import { getChangedResponsiveSettingsData } from '../../../../../utils';
import { mediaTemplateDesignActions } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

interface BlockStylingProps {}

export const BlockStyling: React.FC<BlockStylingProps> = () => {
  // Hooks
  const dispatch = useDispatch();

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const breakpoint = useSelector(selectBreakpoint);

  // Variables
  const blockSettings = blockSelected?.settings;
  const { blockStyles, blockStylesSettings, blockHoverStyles, blockHoverStylesSettings, breakpoints } = blockSettings;

  // Actions
  const { updateBlockFieldsSelected } = mediaTemplateDesignActions;

  // Memo
  const responsiveSettings = useDeepCompareMemo(() => {
    return getBlockResponsiveSettings({ breakpoint, settings: blockSettings });
  }, [breakpoint, blockSettings]);

  const onChangeContainerStyling = (settings = {}, styles = {}) => {
    if (blockSelected) {
      const dataUpdate = getChangedResponsiveSettingsData({
        breakpoint: breakpoint as any,
        currentData: {
          blockStylesSettings,
          blockStyles,
        },
        updatedData: {
          blockStylesSettings: settings,
          blockStyles: styles,
        },
        responsiveSettings: breakpoints?.[breakpoint],
      });

      dispatch(updateBlockFieldsSelected({ dataUpdate }));
    }
  };

  const onChangeHoverSetting = (settings = {}, styles = {}) => {
    if (blockSelected) {
      const dataUpdate = getChangedResponsiveSettingsData({
        breakpoint: breakpoint as any,
        currentData: {
          blockHoverStyles,
          blockHoverStylesSettings,
        },
        updatedData: {
          blockHoverStyles: styles,
          blockHoverStylesSettings: settings,
        },
        responsiveSettings: breakpoints?.[breakpoint],
      });
      dispatch(updateBlockFieldsSelected({ dataUpdate }));
    }
  };

  return (
    <ContainerStyling
      blockStylesSettings={responsiveSettings.blockStylesSettings}
      blockStyles={responsiveSettings.blockStyles}
      blockHoverStyles={responsiveSettings.blockHoverStyles}
      blockHoverStylesSettings={responsiveSettings.blockHoverStylesSettings}
      onChange={onChangeContainerStyling}
      onChangeHover={onChangeHoverSetting}
    />
  );
};
