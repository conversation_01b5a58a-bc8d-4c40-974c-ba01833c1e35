// Libraries
import React from 'react';

// Molecules
import { Collapse } from 'app/components/molecules';

// Atoms
import { Input, Text, Icon, Divider, Checkbox, Button } from 'app/components/atoms';

// Types
import { TContentSourceGroup, UpdateGroupAction } from './types';

// Components
import Group from './Group';

// Models
import { FallbackBO } from 'app/models';

// Styles
import { StyledCollapsePanel } from './styled';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Translations
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import { translate, translations as localesTranslations } from '@antscorp/antsomi-locales';

export type TProps = {
  groups: TContentSourceGroup[];
  isLoadingListBO: boolean;
  listBO: any;
  onChange?: (contentSources: TContentSourceGroup[]) => void;
  expanded?: string[];
  onDeleteGroup?: (groupId: string) => void;
  onChangeGroup?: (groupId: string, action: UpdateGroupAction) => void;
  onChangeExpand?: (expanded: string[]) => void;
  onChangeExcludeDuplicate?: (isExcludeDuplicate: boolean) => void;
  onAddGroup?: () => void;
  isShowErrorAlert: boolean;
  journeySettings: Record<string, any>;
  listFallbackBO: FallbackBO[];
  isExcludeDuplicate?: boolean;
  // Max quantity of algorithms value
  algorithmQuantityMax?: number;
};

export const MAX_NUM_OF_CONTENT_SOURCE = 2;

const defaultProps = {
  groups: [],
  isShowErrorAlert: false,
  journeySettings: {},
  isLoadingListBO: true,
  listFallbackBO: [],
  listBO: [],
  isExcludeDuplicate: false,
};

function ContentSources(props: TProps = defaultProps) {
  // prettier-ignore
  const { 
    groups,
    isLoadingListBO, 
    listBO, 
    onChangeGroup, 
    onChangeExpand, 
    onChangeExcludeDuplicate,
    onAddGroup,
    expanded, 
    isShowErrorAlert, 
    listFallbackBO, 
    journeySettings,
    algorithmQuantityMax,
    onDeleteGroup,
    isExcludeDuplicate,
  } = props;

  // I18n
  const { t } = useTranslation();

  const groupNames = groups.map(g => g.groupName);

  const handleOnChangeExpand = (value: string | string[]) => {
    if (!onChangeExpand) return;

    if (typeof value === 'string') {
      onChangeExpand([value]);
    } else {
      onChangeExpand(value);
    }
  };

  const handleOnDeleteGroup = (e: React.MouseEvent<HTMLElement, MouseEvent>, groupId: string) => {
    e.stopPropagation();

    if (onDeleteGroup) onDeleteGroup(groupId);
  };

  const handleChangeGroupName = (groupId: TContentSourceGroup['groupId'], name: string) => {
    if (onChangeGroup) {
      onChangeGroup(groupId, {
        type: 'NAME',
        payload: { name },
      });
    }
  };

  const handleChangeExcludeDuplicate = (isExcludeDuplicate: boolean) => {
    if (onChangeExcludeDuplicate) {
      onChangeExcludeDuplicate(isExcludeDuplicate);
    }
  };

  const onClickAddGroup = () => {
    if (onAddGroup) onAddGroup();
  };

  const renderCollapsePanelHeader = (group: TContentSourceGroup) => {
    const hasSimilarGroupName = groupNames.filter(name => group.groupName === name).length > 1;

    const errorSimilarGroupName: Record<string, any> = {};

    if (hasSimilarGroupName) {
      errorSimilarGroupName.status = 'error';
      errorSimilarGroupName.errorMsg = getTranslateMessage(translations.contentSources.errorMessage.groupSameName);
    }

    return (
      <React.Fragment>
        <div className="ants-w-100 ants-flex ants-gap-4 ants-justify-between ants-items-start ants-pr-8">
          <div className="ants-flex-1">
            <Input
              className="ants-font-bold !ants-text-[14px]"
              value={group.groupName}
              onClick={e => e.stopPropagation()}
              onAfterChange={value => handleChangeGroupName(group.groupId, value)}
              required
              status={hasSimilarGroupName ? 'error' : ''}
              {...errorSimilarGroupName}
            />
          </div>
          <Icon
            className="ants-text-primary ants-relative ants-top-[15px] ants-translate-y-[-50%]"
            onClick={e => handleOnDeleteGroup(e, group.groupId)}
            type="icon-ants-outline-delete"
            size={20}
          />
        </div>
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <Collapse className="!ants-bg-white" activeKey={expanded} onChange={handleOnChangeExpand} destroyInactivePanel>
        {groups.map((group, index) => (
          <React.Fragment key={group.groupId}>
            <StyledCollapsePanel header={renderCollapsePanelHeader(group)} key={group.groupId}>
              <Group
                data={group}
                groupNames={groupNames}
                isLoadingSelectBO={isLoadingListBO}
                listBO={listBO}
                onChange={onChangeGroup}
                isShowErrorAlert={isShowErrorAlert}
                journeySettings={journeySettings}
                listFallbackBO={listFallbackBO}
                algorithmQuantityMax={algorithmQuantityMax}
              />
            </StyledCollapsePanel>

            {index < groups.length && <Divider className="!ants-my-2" type="horizontal" dashed />}
          </React.Fragment>
        ))}
      </Collapse>
      {groups.length > 1 && (
        <Checkbox checked={isExcludeDuplicate} onChange={e => handleChangeExcludeDuplicate(e.target.checked)}>
          {translate(localesTranslations.ALL_TEMP_EXCLUDE_DUP_CONTENT)}
        </Checkbox>
      )}
      {groups.length < MAX_NUM_OF_CONTENT_SOURCE && (
        <Button type="text" onClick={() => onClickAddGroup()}>
          + {t(translations.addGroup.title)}
        </Button>
      )}
    </React.Fragment>
  );
}

export default ContentSources;
