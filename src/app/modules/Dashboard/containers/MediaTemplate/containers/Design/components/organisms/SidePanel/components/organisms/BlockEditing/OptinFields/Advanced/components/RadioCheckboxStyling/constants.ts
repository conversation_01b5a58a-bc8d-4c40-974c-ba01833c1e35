import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const INPUT_SIZE = {
  custom: {
    value: 'custom',
    label: 'Custom',
  },
  'x-small-rounded': {
    value: 'x-small-rounded',
    label: 'X Small Rounded',
    styles: {
      paddingTop: '4px',
      paddingRight: '8px',
      paddingBottom: '4px',
      paddingLeft: '8px',
      borderTopLeftRadius: '2px',
      borderTopRightRadius: '2px',
      borderBottomRightRadius: '2px',
      borderBottomLeftRadius: '2px',
      fontSize: '12px',
    },
    settings: {
      borderRadiusStyle: 'custom',
      linkedBorderRadiusInput: true,
    },
  },
  'x-small-squared': {
    value: 'x-small-squared',
    label: 'X Small Squared',
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '4px',
      paddingRight: '8px',
      paddingBottom: '4px',
      paddingLeft: '8px',
      fontSize: '12px',
    },
    settings: {
      borderRadiusStyle: 'none',
    },
  },
  'small-rounded': {
    value: 'small-rounded',
    label: 'Small Rounded',
    styles: {
      borderTopLeftRadius: '2px',
      borderTopRightRadius: '2px',
      borderBottomRightRadius: '2px',
      borderBottomLeftRadius: '2px',
      paddingTop: '5px',
      paddingRight: '10px',
      paddingBottom: '5px',
      paddingLeft: '10px',
      fontSize: '16px',
    },
    settings: {
      borderRadiusStyle: 'custom',
      linkedBorderRadiusInput: true,
    },
  },
  'small-squared': {
    value: 'small-squared',
    label: 'Small Squared',
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '5px',
      paddingRight: '10px',
      paddingBottom: '5px',
      paddingLeft: '10px',
      fontSize: '16px',
    },
    settings: {
      borderRadiusStyle: 'none',
    },
  },
  'medium-rounded': {
    value: 'medium-rounded',
    label: 'Medium Rounded',
    styles: {
      borderTopLeftRadius: '3px',
      borderTopRightRadius: '3px',
      borderBottomRightRadius: '3px',
      borderBottomLeftRadius: '3px',
      paddingTop: '7px',
      paddingRight: '14px',
      paddingBottom: '7px',
      paddingLeft: '14px',
      fontSize: '18px',
    },
    settings: {
      borderRadiusStyle: 'custom',
      linkedBorderRadiusInput: true,
    },
  },
  'medium-squared': {
    value: 'medium-squared',
    label: 'Medium Squared',
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '7px',
      paddingRight: '14px',
      paddingBottom: '7px',
      paddingLeft: '14px',
      fontSize: '18px',
    },
    settings: {
      borderRadiusStyle: 'none',
    },
  },
  'large-rounded': {
    value: 'large-rounded',
    label: 'Large Rounded',
    styles: {
      borderTopLeftRadius: '4px',
      borderTopRightRadius: '4px',
      borderBottomRightRadius: '4px',
      borderBottomLeftRadius: '4px',
      paddingTop: '10px',
      paddingRight: '18px',
      paddingBottom: '10px',
      paddingLeft: '18px',
      fontSize: '22px',
    },
    settings: {
      borderRadiusStyle: 'custom',
      linkedBorderRadiusInput: true,
    },
  },
  'large-squared': {
    value: 'large-squared',
    label: 'Large Squared',
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '10px',
      paddingRight: '18px',
      paddingBottom: '10px',
      paddingLeft: '18px',
      fontSize: '22px',
    },
    settings: {
      borderRadiusStyle: 'none',
    },
  },
  'x-large-rounded': {
    value: 'x-large-rounded',
    label: 'X Large Rounded',
    styles: {
      borderTopLeftRadius: '4px',
      borderTopRightRadius: '4px',
      borderBottomRightRadius: '4px',
      borderBottomLeftRadius: '4px',
      paddingTop: '12px',
      paddingRight: '20px',
      paddingBottom: '12px',
      paddingLeft: '20px',
      fontSize: '24px',
    },
    settings: {
      borderRadiusStyle: 'custom',
      linkedBorderRadiusInput: true,
    },
  },
  'x-large-squared': {
    value: 'x-large-squared',
    label: 'X Large Squared',
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '12px',
      paddingRight: '20px',
      paddingBottom: '12px',
      paddingLeft: '20px',
      fontSize: '24px',
    },
    settings: {
      borderRadiusStyle: 'none',
    },
  },
  '2x-large-rounded': {
    value: '2x-large-rounded',
    label: '2X Large Rounded',
    styles: {
      borderTopLeftRadius: '6px',
      borderTopRightRadius: '6px',
      borderBottomRightRadius: '6px',
      borderBottomLeftRadius: '6px',
      paddingTop: '14px',
      paddingRight: '24px',
      paddingBottom: '14px',
      paddingLeft: '24px',
      fontSize: '28px',
    },
    settings: {
      borderRadiusStyle: 'custom',
      linkedBorderRadiusInput: true,
    },
  },
  '2x-large-squared': {
    value: '2x-large-squared',
    label: '2X Large Squared',
    styles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      paddingTop: '14px',
      paddingRight: '24px',
      paddingBottom: '14px',
      paddingLeft: '24px',
      fontSize: '28px',
    },
    settings: {
      borderRadiusStyle: 'none',
    },
  },
};

export const POSITION_OPTIONS = {
  TOP: {
    value: 'top',
    label: getTranslateMessage(translations.optionPosition.top.title),
  },
  RIGHT: {
    value: 'right',
    label: getTranslateMessage(translations.optionPosition.right.title),
  },
  BOTTOM: {
    value: 'bottom',
    label: getTranslateMessage(translations.optionPosition.bottom.title),
  },
  LEFT: {
    value: 'left',
    label: getTranslateMessage(translations.optionPosition.left.title),
  },
};
