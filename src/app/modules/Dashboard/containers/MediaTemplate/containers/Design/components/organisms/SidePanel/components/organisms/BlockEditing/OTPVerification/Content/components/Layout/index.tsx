import React from 'react';
import { LAYOUT_FORM } from '../../../constants';
import { LayoutItem, WrapperItem, WrapperLayoutForm } from './styled';

type TLayoutForm = {
  onChange: (data: Record<string, any>) => void;
  layout: string;
};

export const LayoutForm: React.FC<TLayoutForm> = props => {
  const { layout, onChange } = props;

  const handleClickItem = form => {
    onChange({ layout: form.value });
  };

  return (
    <WrapperLayoutForm>
      {Object.values(LAYOUT_FORM).map(form => {
        return (
          <WrapperItem onClick={() => handleClickItem(form)}>
            <LayoutItem src={form.image} alt={form.label} active={layout === form.value} />
            <div className="ants-my-1" style={{ fontSize: 12 }}>
              {form.label}
            </div>
          </WrapperItem>
        );
      })}
    </WrapperLayoutForm>
  );
};
