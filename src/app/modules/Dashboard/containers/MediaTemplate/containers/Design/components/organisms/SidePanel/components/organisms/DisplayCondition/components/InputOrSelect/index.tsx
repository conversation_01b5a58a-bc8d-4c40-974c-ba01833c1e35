// Libraries
import { useState, useEffect, ReactNode, useMemo } from 'react';
import styled, { css } from 'styled-components';
import { useTranslation } from 'react-i18next';
import tw from 'twin.macro';
import { AutoComplete, RadioChangeEvent } from 'antd';
import get from 'lodash/get';

// Icons
import Icon from '@antscorp/icons';

// Atoms
import { Tag } from 'app/components/atoms/Tag';
import { Button, Text, Input } from 'app/components/atoms';

// Molecules
import { Modal, Select, RadioGroup, TreeSelect } from 'app/components/molecules';

// Translations
import { translations } from 'locales/translations';

// Style
import { RowSelect } from '../../styled';

// Services
import {
  getListAllEvents,
  getListAttributesEvent,
  getListSourceByEvent,
} from 'app/services/MediaTemplateDesign/BusinessObject';
import Search from 'antd/lib/input/Search';

// Types
import {
  EventBoFieldMetadata,
  SelectOption,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { isCheckStatusAttr } from '../../../../molecules/DynamicSetting/constants';

// Queries
import { useGetListBO } from 'app/queries/BusinessObject';

// Constants
import { isCheckBOArchiveDelete } from '../../../BlockEditing/Settings/Basic/constants';

// Utils
import { flattenTree } from 'app/utils/common';
import { buildOptionAttrArchive, checkStatusAttr } from '../../../../../utils';

export const defaultEventMetadata = {
  insight_property_id: null,
  event_action_id: null,
  event_category_id: null,
  event_tracking_name: null,
  item_type_id: null,
  item_type_name: null,
  item_property_name: null,
  item_property_label: null,
  event_property_syntax: null,
  useBo: true,
};

type Value = string | null;
type ValueType = 'event' | 'normal' | string;
type BOField = {
  label: string;
  value: string;
  dataType: string;
};

interface InputOrSelectProps {
  value: Value;
  eventMetadata: EventBoFieldMetadata;
  valueType: ValueType;
  onChangeMultipleValue: ({
    value,
    event_metadata,
    value_type,
  }: {
    value?: InputOrSelectProps['value'];
    event_metadata?: InputOrSelectProps['eventMetadata'];
    value_type?: InputOrSelectProps['valueType'];
  }) => void;
  dataType: string | null;
  businessObject?: Record<string, string>;
  boFieldOptions?: BOField[];
  useBo?: boolean;
  required?: boolean;
  focused?: boolean;
}

export const InputOrSelect: React.FC<InputOrSelectProps> = props => {
  const {
    value,
    eventMetadata,
    valueType,
    onChangeMultipleValue,
    dataType,
    boFieldOptions,
    businessObject,
    useBo,
    required,
    focused,
  } = props;

  const { t } = useTranslation();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isFocused, setFocused] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');

  const showModal = () => {
    setIsModalVisible(true);
    onChangeMultipleValue({
      value_type: 'event',
    });
  };

  const requiredMsg = useMemo(() => {
    let msg = '';
    const isEmptyValue = !props.value || (Array.isArray(props.value) && !props.value.length);

    if (required && isEmptyValue && isFocused) {
      msg = t(translations.messageError.fieldIsRequired.message);
    }

    return msg;
  }, [props.value, required, isFocused, t]);

  useEffect(() => {
    if (focused) {
      setFocused(focused);
    }
  }, [focused]);

  const handleOk = (val: EventBoFieldMetadata) => {
    setIsModalVisible(false);
    onChangeMultipleValue({
      event_metadata: val,
      value: null,
      value_type: 'event',
    });
  };
  const handleCancel = () => {
    setIsModalVisible(false);
    if (!eventMetadata.useBo && !eventMetadata.item_property_name) {
      onDeselect();
    }
  };

  const onChangeInput = (val: Value) => {
    if (dataType === 'number' && isNaN(val as unknown as number) && val !== '-' && val !== '+') {
      return;
    }

    onChangeMultipleValue({
      value: val,
      value_type: 'normal',
      event_metadata: {
        ...defaultEventMetadata,
        useBo,
      },
    });
  };

  const onDeselect = () => {
    onChangeMultipleValue({
      value_type: 'normal',
      event_metadata: {
        ...defaultEventMetadata,
        useBo,
      },
    });
  };

  const renderInput = (type: ValueType) => {
    let element: ReactNode | null = null;

    if (
      isModalVisible ||
      (type === 'event' && ((eventMetadata.useBo && eventMetadata.field_code_bo) || eventMetadata.item_property_name))
    ) {
      element = (
        <>
          <div
            style={{
              height: 32,
              padding: '4px 12px 4px 4px',
              borderBottom: '1px solid #d9d9d9',
              // opacity: isDisable ? '0.4' : '1',
              // pointerEvents: isDisable ? 'none' : 'auto',
            }}
            className="ants-flex ants-items-center ants-justify-between"
          >
            {(eventMetadata.useBo && eventMetadata.field_code_bo) || eventMetadata.item_property_name ? (
              <>
                <div className="ants-w-100 ants-cursor-pointer" onClick={showModal}>
                  <Tag>
                    {eventMetadata.useBo
                      ? eventMetadata.field_label_bo || eventMetadata.field_code_bo
                      : eventMetadata.item_property_label || eventMetadata.item_property_name}
                  </Tag>
                </div>
                <Icon
                  type="icon-ants-remove"
                  className="ants-cursor-pointer"
                  style={{ fontSize: 10, color: '#222' }}
                  onClick={onDeselect}
                />
              </>
            ) : null}
          </div>

          {!!errorMsg ? (
            <Text color="#ff4d4f" className="ants-ml-2 ants-mt-5px">
              {errorMsg}
            </Text>
          ) : null}
        </>
      );
    } else {
      element = (
        <>
          <StyledSelect
            mode="multiple"
            options={[{ value: '', label: t(translations.orSelectAField.title) }]}
            notFoundContent={null}
            onSelect={showModal}
            className="ants-w-100 !ants-border-t-0 !ants-border-x-0 !ants-placeholder-black"
            onDeselect={onDeselect}
            // value={undefined}
            searchValue={typeof value === 'string' ? value : ''}
            onSearch={onChangeInput}
            placeholder={value || t(translations.inputYourValue.title)}
            $isPlaceholder={!value}
            onBlur={() => {
              if (!focused) {
                setFocused(true);
              }
            }}
            // onBlur={() => setVisible(false)}
          >
            {/* <SelectOption value={null}>Select A Field</SelectOption> */}
          </StyledSelect>
          {requiredMsg ? (
            <Text color="#ff4d4f" className="ants-ml-2 ants-mt-5px">
              {requiredMsg}
            </Text>
          ) : null}
        </>
      );
    }
    return element;
  };

  return (
    <>
      {renderInput(valueType)}

      <ModalSelect
        isModalVisible={isModalVisible}
        eventMetadata={eventMetadata}
        handleOk={handleOk}
        handleCancel={handleCancel}
        businessObject={businessObject}
        boFieldOptions={boFieldOptions}
        defaultIsBo={useBo}
        onError={errorMsg => setErrorMsg(errorMsg)}
      />
    </>
  );
};

interface ModalSelectProps {
  isModalVisible: boolean;
  handleOk: (val: EventBoFieldMetadata) => void;
  handleCancel: () => void;
  eventMetadata: EventBoFieldMetadata;
  businessObject?: Record<string, string>;
  boFieldOptions?: BOField[];
  defaultIsBo?: boolean;
  onError?: (errorMessage: string) => void;
}

export const ModalSelect: React.FC<ModalSelectProps> = props => {
  // Props
  const {
    isModalVisible,
    handleOk,
    handleCancel,
    eventMetadata,
    boFieldOptions,
    businessObject,
    defaultIsBo,
    onError,
  } = props;

  // Translations
  const { t } = useTranslation();

  // Selector
  // const boSettings = useSelector(selectBusinessObjectSettings) || {};

  const { data: businessObjectGet } = useGetListBO();

  // States
  const [, setOpenTreeSelect] = useState(false);
  const [tempValue, setTempValue] = useState({
    ...eventMetadata,
    useBo: eventMetadata.useBo ?? defaultIsBo,
  } as EventBoFieldMetadata);

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [listEvent, setListEvent] = useState<any[]>([]);
  const [listSourceByEvent, setListSourceByEvent] = useState<any[]>([]);
  const [listEventAttr, setListEventAttr] = useState<any[]>([]);
  const [treeEventAttr, setTreeEventAttr] = useState<any[]>([]);
  const [, setSearchValue] = useState('');
  const [loading, setLoading] = useState({
    isLoadingEvent: false,
    isLoadingSource: false,
    isLoadingAttrs: false,
  });

  const attributeValue =
    (tempValue.useBo
      ? tempValue.field_code_bo
      : tempValue.item_type_id
      ? `${tempValue.item_type_id}.${tempValue.item_property_name}`
      : tempValue.item_property_name) || undefined;

  const flatEventAttrs = useMemo(() => {
    return flattenTree(listEventAttr, 'items');
  }, [listEventAttr]);

  const { errorMessage: eventAttrErrorMessage } = checkStatusAttr({
    listAttribute:
      flatEventAttrs.map(({ itemPropertyName, eventPropertyName, status }) => ({
        value: eventPropertyName || itemPropertyName,
        status,
      })) || [],
    field: tempValue.item_property_name || undefined,
  });

  const { errorMessage: boAttrErrorMessage, isDisable } = isCheckStatusAttr(
    businessObjectGet,
    businessObject?.itemTypeId,
    boFieldOptions,
    attributeValue,
  );

  useEffect(() => {
    let errorMsg = '';

    if ((tempValue.useBo ?? defaultIsBo) && boAttrErrorMessage) {
      errorMsg = boAttrErrorMessage;
    }

    if (!(tempValue.useBo ?? defaultIsBo) && eventAttrErrorMessage) {
      errorMsg = eventAttrErrorMessage;
    }

    onError && onError(errorMsg);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventAttrErrorMessage, boAttrErrorMessage, eventMetadata.useBo]);

  useEffect(() => {
    if (isModalVisible) {
      getListEvents();
      setTempValue({
        ...eventMetadata,
        useBo: eventMetadata.useBo ?? defaultIsBo,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalVisible]);

  useEffect(() => {
    if (tempValue.event_action_id && tempValue.event_category_id) {
      getListSources({
        event_action_id: tempValue.event_action_id,
        event_category_id: tempValue.event_category_id,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tempValue.event_action_id, tempValue.event_category_id]);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const { insight_property_id } = tempValue;

    getListAttribute({
      insight_property_id: Array.isArray(insight_property_id) ? insight_property_id.join(',') : insight_property_id,
      event_action_id: tempValue.event_action_id,
      event_category_id: tempValue.event_category_id,
      signal,
    });

    return () => {
      controller.abort();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tempValue.insight_property_id]);

  // Services
  const getListEvents = async () => {
    try {
      const { rows } = await getListAllEvents();
      setListEvent(rows);

      setLoading({
        ...loading,
        isLoadingEvent: true,
      });
    } catch (error) {
    } finally {
      setLoading({
        ...loading,
        isLoadingEvent: false,
      });
    }
  };

  const getListSources = async ({ event_action_id, event_category_id }) => {
    setLoading({
      ...loading,
      isLoadingSource: true,
    });

    const { rows } = await getListSourceByEvent({ eventActionId: event_action_id, eventCategoryId: event_category_id });
    setListSourceByEvent(rows);

    setLoading({
      ...loading,
      isLoadingSource: false,
    });
  };

  const getListAttribute = async ({ insight_property_id, event_action_id, event_category_id, signal }) => {
    if (!insight_property_id) {
      setLoading({
        ...loading,
        isLoadingAttrs: false,
      });
      return;
    }

    setLoading({
      ...loading,
      isLoadingAttrs: true,
    });

    const { rows } = await getListAttributesEvent({
      sourceId: insight_property_id,
      eventActionId: event_action_id,
      eventCategoryId: event_category_id,
      signal, // AbortController
    });

    setListEventAttr(rows);

    setLoading({
      ...loading,
      isLoadingAttrs: false,
    });

    const treeAttrs = rows.map((attr: any) => ({
      value: !!get(attr, 'items.length', 0) ? `sub-${attr.eventPropertyName}` : attr.eventPropertyName,
      title: attr.eventPropertyDisplay,
      isLeaf: attr.items ? attr.items.length === 0 : true,
      selectable: attr.items ? attr.items.length === 0 : true,
      ...(attr.items &&
        attr.items.length && {
          children: attr.items.map((childAttr: any) => ({
            value: `${childAttr.itemTypeId}.${childAttr.itemPropertyName}`,
            title: childAttr.translateLabel,
            status: childAttr.status,
            isLeaf: true,
          })),
        }),
    }));

    setTreeEventAttr(treeAttrs);
  };

  const onChangeFieldType = (e: RadioChangeEvent) => {
    const { value } = e.target;
    if (value) {
      setTempValue({
        ...tempValue,
        useBo: value === 'bo',
      });
    }
  };

  const onChangeEvent = (val: string) => {
    const selected = listEvent.find(item => item.value === val);
    if (selected) {
      setTempValue(prevVal => ({
        ...prevVal,
        event_action_id: selected.eventActionId,
        event_category_id: selected.eventCategoryId,
        event_tracking_name: selected.eventTrackingName,

        // resets
        insight_property_id: null,
        item_type_id: null,
        item_type_name: null,
        item_property_name: null,
        item_property_label: null,
        event_property_syntax: null,
      }));

      setTreeEventAttr([]);
      setListEventAttr([]);
    }
  };

  const onChangeSource = (value: string) => {
    const sourcesSelected = listSourceByEvent.find(source => source.value === value);

    if (!value) {
      setTreeEventAttr([]);
      setListEventAttr([]);
    }

    setTempValue(prevVal => ({
      ...prevVal,
      insight_property_id: sourcesSelected.insightPropertyId,

      // resets
      item_type_id: null,
      item_type_name: null,
      item_property_name: null,
      item_property_label: null,
      event_property_syntax: null,
    }));
  };

  const onChangeAttribute = (val: string) => {
    const [attribute, item] = val.split('.');
    if (!item) {
      const selected = listEventAttr.find(attr => attr.value === attribute);
      if (selected) {
        setTempValue({
          ...tempValue,
          item_type_id: null,
          item_type_name: null,
          item_property_name: selected.eventPropertyName,
          item_property_label: selected.label,
          event_property_syntax: selected.eventPropertySyntax,
          useBo: false,
        });
      }
    } else {
      const selectedAttribute = listEventAttr.find(attr => +attr.itemTypeId === +attribute);
      const selected = selectedAttribute.items.find((attr: any) => attr.itemPropertyName === item);
      if (selected) {
        setTempValue({
          ...tempValue,
          item_type_id: selected.itemTypeId,
          item_type_name: selected.itemTypeName,
          item_property_name: selected.itemPropertyName,
          item_property_label: selected.translateLabel,
          event_property_syntax: selected.eventPropertySyntax,
          useBo: false,
        });
      }
    }
  };

  const onSelect = (item: { isLeaf: boolean; value: string; label: string; isBo: boolean }) => {
    if (item.isBo) {
      setTempValue(prevTemp => ({
        ...prevTemp,
        field_code_bo: item.value,
        field_label_bo: item.label,
        useBo: true,
      }));
    } else {
      if (item.isLeaf) {
        // select item || parents not items
        setSearchValue('');
        onChangeAttribute(item.value);
        onDropdownVisibleChange(false);
      } else {
        // select parents
        if (expandedKeys.includes(item.value)) {
          // close expand
          const newExpandedKeys = expandedKeys.filter(key => key !== item.value);
          onTreeExpand(newExpandedKeys);
        } else {
          // open expand
          onTreeExpand((prev: any) => [...prev, item.value]);
        }
      }
    }
  };

  const onTreeExpand = (val: any) => {
    setExpandedKeys(val);
  };

  const onDropdownVisibleChange = (open: boolean) => {
    setOpenTreeSelect(open);
  };

  const handleDisableSubmit = () => {
    if (tempValue.useBo) {
      return !tempValue.field_code_bo || !!boAttrErrorMessage;
    }

    return !tempValue.item_property_name || !!eventAttrErrorMessage;
  };

  const onCancel = () => {
    setTempValue({
      ...eventMetadata,
      useBo: eventMetadata.useBo ?? defaultIsBo,
    });

    handleCancel && handleCancel();
  };

  return (
    <Modal
      title={t(translations.selectAttributeCapitalize.title)}
      width={545}
      visible={isModalVisible}
      onOk={() => handleOk(tempValue)}
      onCancel={onCancel}
      footer={[
        <Button key="submit" type="primary" onClick={() => handleOk(tempValue)} disabled={handleDisableSubmit()}>
          {t(translations.apply.title)}
        </Button>,
        <Button key="back" onClick={onCancel}>
          {t(translations.cancel.title)}
        </Button>,
      ]}
      destroyOnClose
    >
      {businessObject && boFieldOptions && boFieldOptions.length ? (
        <RadioGroup
          value={tempValue.useBo ? 'bo' : 'event'}
          onChange={onChangeFieldType}
          style={{ gap: 25 }}
          options={[
            {
              value: 'bo',
              label: t(translations.fromCotentSources.title),
            },
            {
              value: 'event',
              label: t(translations.fromEvent.title),
            },
          ]}
        >
          {/* <Space direction="horizontal">
            <Radio value="bo">
              <Text color="var(--text-second-color)" size={13}>
                {t(translations.fromBo.title)}
              </Text>
            </Radio>
            <Radio value="event">
              <Text color="var(--text-second-color)" size={13}>
                {t(translations.fromEvent.title)}
              </Text>
            </Radio>
          </Space> */}
        </RadioGroup>
      ) : null}
      {tempValue.useBo && businessObject && boFieldOptions ? (
        <RowSelect>
          <Text className="ants-text-left">{t(translations.selectContentSource.title)}</Text>
          <Input
            value={
              businessObject
                ? `${businessObject.groupName} (${businessObject.itemTypeDisplay})` ||
                  `${businessObject.groupName} (${businessObject.itemTypeName})`
                : ''
            }
            readOnly
            style={{ borderTop: 'none', borderRight: 'none', borderLeft: 'none', fontSize: 12 }}
            errorArchive={isCheckBOArchiveDelete(businessObjectGet, businessObject?.itemTypeId)}
          />
          <Text className="ants-text-left">{t(translations.selectAttribute.title)}</Text>
          <Select
            value={attributeValue}
            options={boFieldOptions}
            onChange={(value, item) =>
              onSelect({ value, isBo: true, label: (item as SelectOption).label || '', isLeaf: true })
            }
            placeholder={t(translations.selectAnItem.title)}
            showSearch
            errorArchive={boAttrErrorMessage}
            disabled={isDisable}
          />
        </RowSelect>
      ) : (
        <RowSelect>
          <Text className="ants-text-left">{t(translations.selectEvent.title)}</Text>
          <Select
            showSearch
            placeholder="Select an item"
            value={tempValue.event_tracking_name || undefined}
            loading={loading.isLoadingEvent}
            onChange={v => onChangeEvent(v)}
            options={listEvent.map(event => ({ value: event.value, label: event.label }))}
          />
          <Text className="ants-text-left">{t(translations.inAnySourceOf.title)}</Text>
          <Select
            showSearch
            placeholder="Select an item"
            value={tempValue.insight_property_id || undefined}
            loading={loading.isLoadingSource}
            options={listSourceByEvent.map(event => ({ value: event.value, label: event.label }))}
            onChange={v => onChangeSource(v)}
          />
          <Text className="ants-text-left">{t(translations.selectEventAttribute.title)}</Text>
          <TreeSelect
            status={eventAttrErrorMessage ? 'error' : ''}
            errorMsg={eventAttrErrorMessage}
            showSearch
            value={attributeValue || undefined}
            placeholder={t(translations.selectEventAttribute.title)}
            treeData={treeEventAttr.map(attr => ({
              ...attr,
              children: buildOptionAttrArchive(attr.children || [], attributeValue),
            }))}
            loading={loading.isLoadingAttrs}
            onChange={onChangeAttribute}
          />
        </RowSelect>
      )}
    </Modal>
  );
};

export const StyledAutoComplete = styled(AutoComplete)`
  ${tw`ants-w-100`}
  .ant-select-selector {
    border-top: none !important;
    border-right: none !important;
    border-left: none !important;
  }
`;

export const StyledDropdown = styled.div<{ $isSearching: boolean }>`
  .ant-select-tree
    .ant-select-tree-treenode:not(.ant-select-tree .ant-select-tree-treenode-disabled)
    .ant-select-tree-title {
    font-size: var(--text-normal-font-size);
    ${props =>
      props.$isSearching
        ? css`
            font-weight: 500;
          `
        : ``};
  }
  .ant-select-tree-node-content-wrapper {
    ${tw`ants-flex ants-items-center`}
  }
`;

export const StyledSelect = styled(Select)<any>`
  .ant-select-selection-placeholder {
    ${props =>
      !props.$isPlaceholder
        ? css`
            color: rgba(0, 0, 0, 0.85);
            font-size: var(--text-normal-font-size);
          `
        : `
        `}
  }
`;

export const StyledTreeSelect = styled(TreeSelect)`
  font-size: var(--text-normal-font-size);
  width: 100%;

  &:hover {
    .ant-select-selector {
      border-color: #d9d9d9 !important;
    }
  }

  .ant-select-selector {
    border-top: 0 !important;
    border-right: 0 !important;
    border-left: 0 !important;
    border-bottom: 1px solid #d9d9d9;

    // padding-right: 12px;
  }
  .ant-select-selection-overflow-item.ant-select-selection-overflow-item-rest {
    .ant-select-selection-item {
      color: #096dd9;
      background: #e6f7ff;
      border-color: #91d5ff;
    }
  }
`;

export const StyledSearch = styled(Search)`
  .ant-input-group-addon {
    button {
      height: 32px;
    }
  }
`;
