// Libraries
import React, { memo } from 'react';
import { useSelector } from 'react-redux';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Molecules
import BlockWrapper from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/molecules/BlockWrapper';
import { DragBlockHere } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/molecules/DragBlockHere';

// Organisms
import {
  TextBlock,
  ImageBlock,
  IconsBlock,
  ButtonBlock,
  CountdownBlock,
  CouponWheelBlock,
  DividerBlock,
  HtmlBlock,
  OptinFieldsBlock,
  SpacerBlock,
  VideoBlock,
  YesNoBlock,
  SlideShowBlock,
  RatingBlock,
  TableBlock,
  TableBlockExport,
  OTPVerificationBlock,
} from '../index';

// Styled
import { GroupBlockContent, GroupBlockWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';
import { buildDisplayConditionData } from '../../../../SidePanel/components/organisms/DisplayCondition/utils';

// Selectors
import {
  selectChildrenBlockById,
  selectNamespace,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import { STANDARDS_BLOCKS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

interface GroupBlockProps extends BlockProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/organisms/GroupBlock/index.tsx';

const { GROUP } = STANDARDS_BLOCKS;

export const GroupBlock: React.FC<GroupBlockProps> = memo(props => {
  // Props
  const { id, settings, isPreviewMode, rowId, columnId } = props;
  const { groupSettings, groupStyles } = settings;

  // Selectors
  const namespace = useSelector(selectNamespace);
  const elements = useSelector(selectChildrenBlockById(id));

  // Variables
  const isEmpty = !elements?.length;

  // Ref
  const groupBlockRef = React.useRef<HTMLDivElement>(null);

  const renderElement = ({ id, parentId, type, settings, savedBlockId, idx }) => {
    try {
      const elementProps = {
        id,
        parentId,
        type,
        settings,
        idx,
        isPreviewMode,
        namespace,
        rowId,
        columnId,
        elements,
      };

      const settingDC = settings.blockStylesSettings.displayCondition || {};
      const renderContent = () => {
        switch (type) {
          case STANDARDS_BLOCKS.TEXT.name:
            return <TextBlock {...elementProps} />;

          case STANDARDS_BLOCKS.IMAGE.name:
            return <ImageBlock {...elementProps} />;

          case STANDARDS_BLOCKS.BUTTON.name:
            return <ButtonBlock {...elementProps} />;

          case STANDARDS_BLOCKS.COUNT_DOWN.name:
            return <CountdownBlock {...elementProps} />;

          case STANDARDS_BLOCKS.COUPON_WHEEL.name:
            return <CouponWheelBlock {...elementProps} />;

          case STANDARDS_BLOCKS.DIVIDER.name:
            return <DividerBlock {...elementProps} />;

          case STANDARDS_BLOCKS.HTML.name:
            return <HtmlBlock {...elementProps} />;

          case STANDARDS_BLOCKS.ICON.name:
            return <IconsBlock {...elementProps} />;

          case STANDARDS_BLOCKS.OPTIN_FIELDS.name:
            return <OptinFieldsBlock {...elementProps} />;

          case STANDARDS_BLOCKS.SPACER.name:
            return <SpacerBlock {...elementProps} />;

          case STANDARDS_BLOCKS.VIDEO.name:
            return <VideoBlock {...elementProps} />;

          case STANDARDS_BLOCKS.YES_NO.name:
            return <YesNoBlock {...elementProps} />;

          case STANDARDS_BLOCKS.SLIDE_SHOW.name:
            return <SlideShowBlock {...elementProps} />;

          case STANDARDS_BLOCKS.GROUP.name:
            return <GroupBlock {...elementProps} />;

          case STANDARDS_BLOCKS.RATING.name:
            return <RatingBlock {...elementProps} />;

          case STANDARDS_BLOCKS.TABLE.name:
            return isPreviewMode ? <TableBlockExport {...elementProps} /> : <TableBlock {...elementProps} />;

          case STANDARDS_BLOCKS.OTP_VERIFICATION.name:
            return <OTPVerificationBlock {...elementProps} />;

          default:
            break;
        }
      };

      return (
        <BlockWrapper
          {...{
            ...elementProps,
            savedBlockId,
            draggableId: `element-${id}`,
            isPortal: true,
            blockWrapperStyle: {
              width: 'auto',
              height: 'auto',
            },
          }}
          displayConditionData={buildDisplayConditionData(settingDC)}
        >
          {renderContent()}
        </BlockWrapper>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const getGroupBlockStyle = () => {
    let styles = {
      ...groupStyles,
    };
    const flex = groupSettings?.flex;

    try {
      switch (groupStyles.display) {
        case 'flex':
          styles = {
            ...styles,
            flexDirection: flex.direction,
            alignItems: flex.align,
            justifyContent: flex.justify,
            alignContent: flex.alignContent,
            gap: `${flex.gapY} ${flex.gapX}`,
            flexWrap: flex.wrap,
          };
          break;

        default:
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'getGroupBlockStyle',
        args: {},
      });
    } finally {
      return styles;
    }
  };

  return (
    <GroupBlockWrapper ref={groupBlockRef}>
      <GroupBlockContent id={`group-${id}`}>
        {isEmpty ? (
          !isPreviewMode && <DragBlockHere blockId={id} blockType={GROUP.name} />
        ) : (
          <div className="ants-h-full" style={{ ...getGroupBlockStyle() }}>
            {elements.map((element, idx) => {
              const { id: elementId, type, settings, savedBlockId } = element;

              return (
                <React.Fragment key={elementId + idx}>
                  {renderElement({ id: elementId, parentId: id, type, settings, savedBlockId, idx })}
                </React.Fragment>
              );
            })}
          </div>
        )}
      </GroupBlockContent>
    </GroupBlockWrapper>
  );
});
