// Molecules
import { InputNumber as InputNumberAntd } from 'app/components/molecules';

interface InputNumberProps {
  value: string | number;
  onChange: (val: InputNumberProps['value']) => void;
}

export const InputNumber: React.FC<InputNumberProps> = props => {
  const { value, onChange } = props;

  const onChangeValue = val => {
    onChange(val);
  };

  return <InputNumberAntd value={value} onChange={onChangeValue} width="100%" />;
};
