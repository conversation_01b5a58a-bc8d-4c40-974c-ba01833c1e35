import React, { useEffect, useMemo } from 'react';

// Molecusles
import { Space, Text } from 'app/components/atoms';
import { Select } from 'app/components/molecules';
import { buildOptionArchive } from '../../../utils';
import { convertValue, isCheckBOArchiveDelete } from '../../organisms/BlockEditing/Settings/Basic/constants';
import { cloneDeep, get, isEmpty } from 'lodash';
// Utils
import { TRANSLATE_TEXT } from './utils';

// Constants
import { ITEM_TYPE_NAME, ITEM_TYPE_NAME_DISPLAY_LEVEL, PRODUCT_ITEM_TYPE_ID } from '../../../../../../config';

// Types
import { TContentSourceGroup, TRelationship, UpdateGroupAction } from './types';
import { FallbackBO } from 'app/models';

// Components
import { FilterSetting } from '../../organisms/FilterSetting';
import { AlgorithmsSetting } from '../../organisms/AlgorithmsSetting';
import LevelSetting from './components/LevelSetting';

// Queries
import { useGetBODetail } from 'app/queries/BusinessObject/useGetBODetail';
import { LEVEL_OPTIONS } from './components/LevelSetting/constants';
import { useDeepCompareEffect } from 'app/hooks';

type TProps = {
  data: TContentSourceGroup;
  listBO: any[];
  isLoadingSelectBO: boolean;
  onChange?: (groupId: string, action: UpdateGroupAction) => void;
  isShowErrorAlert: boolean;
  journeySettings: Record<string, any>;
  listFallbackBO: FallbackBO[];
  groupNames: string[];
  // Max quantity of algorithms value
  algorithmQuantityMax?: number;
};

const Group = (props: TProps) => {
  // prettier-ignore
  const {
    data, 
    isLoadingSelectBO, 
    listBO, 
    listFallbackBO, 
    isShowErrorAlert, 
    journeySettings,
    algorithmQuantityMax,
    onChange,
  } = props;

  const { itemTypeId, filters, level, itemTypeName } = data;

  const options = useMemo(() => {
    const filterdOptions = buildOptionArchive(listBO, convertValue(listBO, itemTypeId))?.map(bo => ({
      value: bo.value,
      label: bo.label,
    }));

    return filterdOptions;
  }, [listBO, itemTypeId]);

  const handleChange = (value: UpdateGroupAction) => {
    if (onChange) {
      onChange(data.groupId, value);
    }
  };

  // Queries
  const { data: businessObjectDetail } = useGetBODetail(itemTypeId);
  const relationship: TRelationship = get(businessObjectDetail, 'data_upd_methods.RELATIONSHIP', {});

  const listFallbackByBO: FallbackBO[] = useMemo(() => {
    let listFallback = cloneDeep(listFallbackBO);

    listFallback = listFallbackBO.filter(option => {
      if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
        return ['hidden', 'none', 'product_viewed_most_often'].includes(option?.fallbackValue || '');
      }
      return true;
    });

    return listFallback;
  }, [listFallbackBO, itemTypeName]);

  useDeepCompareEffect(() => {
    if (listFallbackByBO && !!listFallbackByBO.length && !!data.fallback) {
      const isFallbackExist = listFallbackByBO.some(fallback => fallback.fallbackValue === data.fallback);

      if (!isFallbackExist) {
        handleChange({
          type: 'FALLBACK',
          payload: {
            fallback:
              itemTypeName === ITEM_TYPE_NAME.ARTICLE
                ? 'product_viewed_most_often'
                : listFallbackByBO[0]?.fallbackValue || '',
          },
        });
      }
    }
  }, [data.fallback, itemTypeName, listFallbackByBO]);

  useDeepCompareEffect(() => {
    if (!isEmpty(relationship)) {
      if (!!relationship?.enabled) {
        if (
          relationship.recommendation_level &&
          !LEVEL_OPTIONS[relationship.recommendation_level]?.find(item => item.value === level)
        ) {
          handleChange({
            type: 'LEVEL',
            payload: { level: get(LEVEL_OPTIONS, `[${relationship.recommendation_level}][0].value`, 'parent') },
          });
        }
      } else {
        handleChange({
          type: 'LEVEL',
          payload: { level: undefined },
        });
      }
    }
  }, [level, relationship]);

  return (
    <Space size={20} direction="vertical">
      <Select
        showSearch
        loading={isLoadingSelectBO}
        label={TRANSLATE_TEXT.selectCsTitle}
        options={options}
        placeholder={TRANSLATE_TEXT.selectAnItem}
        value={convertValue(listBO, itemTypeId)}
        onChange={value =>
          handleChange({
            type: 'BO_TYPE',
            payload: { itemTypeId: value },
          })
        }
        errorArchive={isCheckBOArchiveDelete(listBO, itemTypeId)}
      />

      {itemTypeId === PRODUCT_ITEM_TYPE_ID && relationship && !!relationship?.enabled && (
        <LevelSetting
          value={level || 'parent'}
          levelOption={relationship.recommendation_level}
          onChange={e => {
            handleChange({
              type: 'LEVEL',
              payload: { level: e.target.value },
            });
          }}
        />
      )}

      {itemTypeId && (
        <React.Fragment>
          <FilterSetting
            boId={itemTypeId}
            filters={filters}
            onOKChangeFilter={conditions =>
              handleChange({
                type: 'FILTER',
                payload: { conditions },
              })
            }
          />

          <AlgorithmsSetting
            journeySettings={journeySettings}
            contentSourceGroupId={data.groupId}
            isShowErrorAlert={isShowErrorAlert}
            itemTypeId={itemTypeId}
            data={data.ranking}
            algorithmQuantityMax={algorithmQuantityMax}
            onChange={value =>
              handleChange({
                type: 'ALGORITHMS',
                payload: { ranking: value },
              })
            }
            itemTypeName={itemTypeName}
          />

          {(itemTypeId === PRODUCT_ITEM_TYPE_ID || itemTypeName === ITEM_TYPE_NAME.ARTICLE) && (
            <>
              <Text type="secondary" className="ants-font-bold -ants-mb-3">
                {TRANSLATE_TEXT.fallback}
              </Text>
              <Select
                options={listFallbackByBO?.map(({ fallbackDisplay, fallbackValue }) => ({
                  label: fallbackDisplay,
                  value: fallbackValue,
                }))}
                value={data.fallback}
                onChange={value =>
                  handleChange({
                    type: 'FALLBACK',
                    payload: { fallback: value },
                  })
                }
                required
              />
            </>
          )}
        </React.Fragment>
      )}
    </Space>
  );
};

export default Group;
