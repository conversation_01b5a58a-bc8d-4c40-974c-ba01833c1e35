//Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

//Atoms
import { Input, RequiredLabel } from 'app/components/atoms';

//Molecules
import { InputNumber, Select } from 'app/components/molecules';

//Utils
import { translations } from 'locales/translations';
import { handleError } from 'app/utils/handleError';
import { getInputType, isInputDateTimeAgo, isInputDateTimeBetweenAgo } from '../FilterSetting/utils';
import { checkStatusCollection } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';
import { defaultDCValues, getInputDefaultValue } from './utils';
import { regexCSType, serializeCSSelectedString } from '../AddDynamicContent/utils';

//Constants
import { DISPLAY_CONDITION, SEGMENT_IDS } from './constants';
import { OPERATORS } from '../FilterSetting/constants';

// Common
import { InputBoolean } from '../FilterSetting/components/common/InputBoolean';
import { InputNumberBetween } from '../FilterSetting/components/common/InputNumberBetween';
import { InputCalendarBetween } from '../FilterSetting/components/common/InputCalendarBetween';
import { InputDateTimeAgo } from '../FilterSetting/components/common/InputDateTimeAgo';
import { InputCalendar } from '../FilterSetting/components/common/InputCalendar';
import { InputArray } from '../FilterSetting/components/common/InputArray';
import { InputDateTimeBetweenAgo } from '../FilterSetting/components/common/InputDateTimeBetweenAgo';
import { InputSelectMulti } from './components/InputSelectMulti';
import { InputOrSelect } from './components/InputOrSelect';
import SelectDynamicAttribute from './components/SelectDynamicAttribute';

//services
import { EventMetadata, TOperatorValue } from '../../../../../../types';
import { getListCollectionsBO } from 'app/services/MediaTemplateDesign/BusinessObject';

//Selectors
import {
  selectContentSources,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Queries
import { useGetListBO } from 'app/queries/BusinessObject';
import { useGetListAttributeBO } from 'app/queries/BusinessObject/useGetListAttributeBO';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/DisplayCondition/index.tsx';

type TDisplayConditionOption = {
  value: string;
  label: string;
};

interface DisplayConditionProps {
  displayConditionOptions?: TDisplayConditionOption[];
  displayCondition: Record<string, any>;
  onChange: (settings: Record<string, any>) => void;
  valueCondition: string;
  showIndex?: boolean;
}

export const DisplayCondition: React.FC<DisplayConditionProps> = props => {
  const { displayCondition, displayConditionOptions, onChange, valueCondition = '', showIndex = true } = props;

  // prettier-ignore
  const { 
    attribute, 
    operator, 
    value, 
    event_metadata, 
    value_type, 
    time_unit,
    type
  } = displayCondition;

  const { t } = useTranslation();

  //selectors
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);
  const contentSources = useSelector(selectContentSources);

  const { itemTypeId, itemTypeName, boSettings } = useMemo(() => {
    const result = { itemTypeId: null, itemTypeName: '', boSettings: null };
    if (!regexCSType.test(type)) return result;

    const groupId = serializeCSSelectedString(type).groupId;
    const group = contentSources.groups.find(g => g.groupId === groupId);

    return group
      ? {
          itemTypeId: group.itemTypeId,
          itemTypeName: group.itemTypeName,
          boSettings: group,
        }
      : result;
  }, [type, contentSources]);

  // Queries
  const { data: businessObject } = useGetListBO();
  const { data: listBOAttr } = useGetListAttributeBO<any>({
    itemTypeIds: itemTypeId ? [itemTypeId] : [],
    options: {
      select(data) {
        const { rows = [] } = data || {};

        return (
          rows[0]?.properties.map(item => ({
            ...item,
            value: item.itemPropertyName,
            label: item.translateLabel,
            disabled: parseInt(item.status) === 4,
          })) || []
        );
      },
    },
  });
  const [listCollection, setListCollection] = useState<any[]>([]);

  useEffect(() => {
    (async () => {
      if (itemTypeId === 1) {
        const { rows } = await getListCollectionsBO({ itemTypeId });
        const filters = rows.map((item: any) => ({
          value: item.value,
          id: item.value,
          label: item.label,
          disabled: parseInt(item.model.status) === 4,
          status: parseInt(item.model.status),
        }));
        setListCollection(filters);
      }
    })();
  }, [itemTypeId]);

  const dataDisplayCondition = {
    operator,
    value,
    event_metadata: event_metadata || ({} as EventMetadata),
    value_type: value_type || 'normal',
    time_unit,
  };

  const onChangeDisplayCondition = (settings: any) => {
    try {
      onChange({
        ...displayCondition,
        ...settings,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeDisplayCondition',
        args: {},
      });
    }
  };

  const onChangeSelectedField = (values: any) => {
    const defaultOperator = OPERATORS[values.attribute.dataType][0].value as TOperatorValue;

    const dataUpdate = {
      ...values,
      operator: defaultOperator,
      value: '',
      time_unit: '',
    };

    onChangeDisplayCondition(dataUpdate);
  };

  const onChangeValue = (value: any, key: string = 'string') => {
    onChangeDisplayCondition({ value: value });
  };

  const onChangeMultipleValue = (val: any) => {
    const dataUpdate = {
      ...dataDisplayCondition,
      ...val,
    };
    onChangeDisplayCondition(dataUpdate);
  };

  const renderInputValue = () => {
    const onChangeValueItem = (value: any, key: string = 'value') => {
      onChangeValue(value, key);
    };
    const onChangeMultipleValueItem = (value: any) => {
      onChangeMultipleValue(value);
    };

    let dataInput = value;

    if (dataInput == null) {
      dataInput = operator.includes('matches') ? [] : '';
    }

    const { errorMessage, isDisable } = checkStatusCollection({
      listBO: businessObject as any,
      itemTypeId,
      listCollection,
      field: value,
    });

    const element = getInputType({
      operator,
      dataType: attribute.dataType,
      property: attribute,
      resInputBoolean: (
        <InputBoolean required focused={isShowErrorAlert} value={dataInput} onChange={onChangeValueItem as any} />
      ),
      resInputNumberBetween: <InputNumberBetween value={dataInput} onChange={onChangeValueItem} />,
      resInputCalendarBetween: (
        <InputCalendarBetween required focused={isShowErrorAlert} value={dataInput} onChange={onChangeValueItem} />
      ),
      resInputDateTimeAgo: (
        <InputDateTimeAgo
          required
          value={dataInput}
          timeUnit={time_unit}
          onChangeMultipleValue={onChangeMultipleValueItem}
        />
      ),
      resInputDateTimeBetweenAgo: (
        <InputDateTimeBetweenAgo value={dataInput} timeUnit={time_unit} onChange={onChangeMultipleValue} />
      ),
      resInputCalendar: (
        <InputCalendar
          required
          focused={isShowErrorAlert}
          value={dataInput}
          operator={operator}
          onChange={onChangeValueItem}
        />
      ),
      resInputSelectMulti: (
        <InputSelectMulti
          required
          focused={isShowErrorAlert}
          value={dataInput}
          eventMetadata={event_metadata}
          onChange={onChangeValueItem}
          itemTypeId={itemTypeId}
          itemTypeName={itemTypeName}
          errorMessage={attribute.value === SEGMENT_IDS ? errorMessage : ''}
          isDisable={attribute.value === SEGMENT_IDS && isDisable}
          column={attribute.value}
        />
      ),
      resInputOrSelect: (
        <InputOrSelect
          required
          focused={isShowErrorAlert}
          value={dataInput}
          onChangeMultipleValue={onChangeMultipleValueItem}
          eventMetadata={event_metadata}
          valueType={value_type}
          dataType={attribute.dataType}
          businessObject={(boSettings as Record<string, any>) ?? undefined}
          boFieldOptions={listBOAttr}
          useBo={true}
        />
      ),
      resInputArray: <InputArray required focused={isShowErrorAlert} value={dataInput} onChange={onChangeValueItem} />,
      resInputNumber: (
        <InputNumber required focused={isShowErrorAlert} value={dataInput} onChange={onChangeValueItem} />
      ),
      resDefault: (
        <Input
          required
          focused={isShowErrorAlert}
          value={dataInput}
          onChange={e => onChangeValueItem(e.target.value)}
          placeholder={t(translations.inputYourValue.title)}
        />
      ),
    });
    return element;
  };

  return (
    <>
      <Select
        showSearch
        label={t(translations.displayCondition.condition.title)}
        value={valueCondition}
        options={displayConditionOptions || Object.values(DISPLAY_CONDITION)}
        onChange={value =>
          onChangeDisplayCondition({
            ...defaultDCValues,
            condition: value,
          })
        }
      />
      {valueCondition ? (
        <React.Fragment>
          <SelectDynamicAttribute
            className="ants-mt-4"
            values={displayCondition}
            onOk={onChangeSelectedField}
            showIndex={showIndex}
          />

          {attribute?.value ? (
            <React.Fragment>
              <Select
                labelClassname="ants-mt-2"
                className="ants-mb-4"
                value={displayCondition.operator}
                label={t(translations.displayCondition.operator.title)}
                placeholder={t(translations.selectAnItem.title)}
                options={
                  attribute.dataType === 'string' && attribute.autoSuggestion === 1
                    ? OPERATORS['suggestion']
                    : OPERATORS[attribute.dataType]
                }
                onChange={value =>
                  onChangeDisplayCondition({
                    operator: value,
                    value: getInputDefaultValue({
                      operator: value,
                      dataType: attribute.dataType,
                      property: attribute,
                    }),
                    event_metadata: {},
                    value_type: 'normal',
                    time_unit:
                      isInputDateTimeAgo(value, attribute.dataType) ||
                      isInputDateTimeBetweenAgo(value, attribute.dataType)
                        ? 'DAY'
                        : '',
                  })
                }
              />
              <div>
                {operator && operator !== 'exists' && operator !== 'not_exists' ? (
                  <React.Fragment>
                    <RequiredLabel color="#666">{t(translations.displayCondition.value.title)}</RequiredLabel>
                    <div className="ants-mt-1">{renderInputValue()}</div>
                  </React.Fragment>
                ) : null}
              </div>
            </React.Fragment>
          ) : null}
        </React.Fragment>
      ) : null}
    </>
  );
};
