import { TreeSelect } from 'app/components/molecules';
import styled from 'styled-components';
import tw from 'twin.macro';

export const StyledTreeSelect = styled(TreeSelect)`
  &.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 30px;
  }
`;

export const StyledOptionTitle = styled.div`
  ${tw`ants-pl-3 ants-pr-4 ants-absolute ants-inset-0 `}
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
