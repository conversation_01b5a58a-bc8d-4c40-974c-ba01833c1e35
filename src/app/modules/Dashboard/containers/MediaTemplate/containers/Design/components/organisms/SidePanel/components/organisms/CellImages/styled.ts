import styled from 'styled-components';
import { SwitchLabel } from '../../molecules';
import tw from 'twin.macro';

export const FooterContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  gap: 10px;
`;

export const CellImage = styled.div`
  display: inline-block;
  padding: 10px;
  width: 25%;

  .label-cell {
    color: #000 !important;
  }
`;

export const StyledSwitchLabel = styled(SwitchLabel)`
  ${tw`ants-inline-flex ants-flex-row-reverse ants-mt-5px`}

  .label-switch {
    ${tw`ants-ml-2.5 ants-mt-1`}
  }
`;

export const ListCellImages = styled.div`
  height: 420px;
`;
