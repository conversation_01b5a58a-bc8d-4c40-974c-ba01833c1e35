// Libraries
import { useState, useEffect, ReactNode, useMemo } from 'react';
import styled, { css } from 'styled-components';
import { useTranslation } from 'react-i18next';
import tw from 'twin.macro';
import { AutoComplete, Input, RadioChangeEvent } from 'antd';
import get from 'lodash/get';

// Icons
import Icon from '@antscorp/icons';

// Atoms
import { Tag } from 'app/components/atoms/Tag';
import { Button, Text } from 'app/components/atoms';

// Molecules
import { Modal, Select, RadioGroup, TreeSelect } from 'app/components/molecules';

// Translations
import { translations } from 'locales/translations';

// Queries
import { useGetDynamicContentAttr } from 'app/queries/DynamicContentAttribute';

// Style
import { RowSelect } from '../../../styled';

// Services
import {
  getListAllEvents,
  getListAttributesEvent,
  getListSourceByEvent,
} from 'app/services/MediaTemplateDesign/BusinessObject';
import Search from 'antd/lib/input/Search';

// Types
import {
  CustomerMetadata,
  EventBoFieldMetadata,
  SelectOption,
  VisitorMetadata,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Constants
import {
  DEFAULT_CUSTOMER_META_DATA,
  DEFAULT_EVENT_META_DATA,
  DEFAULT_VISITOR_META_DATA,
  VALUE_TYPE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FilterSetting/constants';

// Selectors
import { useDeepCompareEffect } from 'app/hooks';

// Utils
import { flattenTree } from 'app/utils/common';
import { buildOptionAttrArchive, checkStatusAttr } from '../../../../../../utils';

const { CUSTOMER, EVENT, VISITOR } = VALUE_TYPE;

type Value = string | null | string[];
type ValueType = 'event' | 'normal' | 'customer' | 'visitor' | string;
type BOField = {
  label: string;
  value: string;
  dataType: string;
};

interface InputOrSelectProps {
  value: Value;
  eventMetadata: EventBoFieldMetadata;
  visitorMetadata: VisitorMetadata;
  customerMetadata: CustomerMetadata;
  valueType: ValueType;
  onChangeMultipleValue: ({
    value,
    event_metadata,
    visitor_metadata,
    customer_metadata,
    value_type,
  }: {
    value?: InputOrSelectProps['value'];
    event_metadata?: InputOrSelectProps['eventMetadata'];
    visitor_metadata?: InputOrSelectProps['visitorMetadata'];
    customer_metadata?: InputOrSelectProps['customerMetadata'];
    value_type?: InputOrSelectProps['valueType'];
  }) => void;
  dataType: string | null;
  businessObject?: Record<string, string>;
  boFieldOptions?: BOField[];
  useBo?: boolean;
  hideSelectField?: boolean;
  useArray?: boolean;
}

export const InputOrSelect: React.FC<InputOrSelectProps> = props => {
  const {
    value,
    eventMetadata,
    customerMetadata,
    visitorMetadata,
    valueType,
    onChangeMultipleValue,
    dataType,
    boFieldOptions,
    businessObject,
    useBo,
    hideSelectField,
    useArray,
  } = props;

  const { t } = useTranslation();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  // const [selectedEvent , setSelectedEvent] = useState({value:'',label:''})

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = ({ valueType, metaData }) => {
    const mapMetaData = {
      event_metadata: metaData.event,
      visitor_metadata: metaData.visitor,
      customer_metadata: metaData.customer,
    };

    setIsModalVisible(false);
    onChangeMultipleValue({
      ...mapMetaData,
      value: null,
      value_type: valueType,
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    // if (!eventMetadata.useBo && !eventMetadata.item_property_name && !eventMetadata.ATTRIBUTE) {
    //   onDeselect();
    // }
  };

  const onChangeInput = (val: Value) => {
    if (dataType === 'number' && isNaN(val as unknown as number) && val !== '-' && val !== '+') {
      return;
    }

    const value = useArray ? [val as string] : val;

    onChangeMultipleValue({
      value,
      value_type: 'normal',
      customer_metadata: DEFAULT_CUSTOMER_META_DATA,
      visitor_metadata: DEFAULT_VISITOR_META_DATA,
      event_metadata: {
        ...DEFAULT_EVENT_META_DATA,
        useBo,
      },
    });
  };

  const onDeselect = () => {
    onChangeMultipleValue({
      value_type: 'normal',
      customer_metadata: DEFAULT_CUSTOMER_META_DATA,
      visitor_metadata: DEFAULT_VISITOR_META_DATA,
      event_metadata: {
        ...DEFAULT_EVENT_META_DATA,
        useBo,
      },
    });
  };

  const renderInput = (type: ValueType) => {
    let element: ReactNode | null = null;

    if (
      isModalVisible ||
      (['event', 'visitor', 'customer'].includes(type) &&
        ((eventMetadata.useBo && eventMetadata.field_code_bo) ||
          eventMetadata.item_property_name ||
          customerMetadata.item_property_name ||
          visitorMetadata.item_property_name))
    ) {
      element = (
        <div
          style={{ height: 32, padding: '4px 12px 4px 4px', borderBottom: '1px solid #d9d9d9' }}
          className="ants-flex ants-items-center ants-justify-between"
        >
          <div className="ants-w-100 ants-cursor-pointer" onClick={showModal}>
            <Tag>
              {eventMetadata.useBo
                ? eventMetadata.field_label_bo || eventMetadata.field_code_bo
                : (valueType === EVENT.value &&
                    (eventMetadata.item_property_label || eventMetadata.item_property_name)) ||
                  (valueType === CUSTOMER.value && customerMetadata.item_property_name) ||
                  (valueType === VISITOR.value && visitorMetadata.item_property_name)}
            </Tag>
          </div>
          <Icon
            type="icon-ants-remove"
            className="ants-cursor-pointer"
            style={{ fontSize: 10, color: '#222' }}
            onClick={onDeselect}
          />
        </div>
      );
    } else {
      const renderValue = Array.isArray(value) ? value[0] : value;

      element = (
        <StyledSelect
          mode="multiple"
          options={[{ value: '', label: t(translations.orSelectAField.title) }]}
          notFoundContent={null}
          onSelect={showModal}
          className="ants-w-100 !ants-border-t-0 !ants-border-x-0 !ants-placeholder-black"
          onDeselect={onDeselect}
          autoClearSearchValue={false}
          searchValue={typeof renderValue === 'string' ? renderValue : ''}
          onSearch={onChangeInput}
          placeholder={renderValue || t(translations.inputYourValue.title)}
          $isPlaceholder={!renderValue}
          dropdownStyle={{
            ...(hideSelectField ? { display: 'none' } : {}),
          }}
          // onBlur={() => setVisible(false)}
        >
          {/* <SelectOption value={null}>Select A Field</SelectOption> */}
        </StyledSelect>
      );
    }
    return element;
  };

  return (
    <>
      {renderInput(valueType)}
      {!!errorMsg ? (
        <Text color="#ff4d4f" className="ants-ml-2 ants-mt-5px">
          {errorMsg}
        </Text>
      ) : null}
      <ModalSelect
        isModalVisible={isModalVisible}
        valueType={valueType}
        eventMetadata={eventMetadata}
        customerMetadata={customerMetadata}
        visitorMetadata={visitorMetadata}
        handleOk={handleOk}
        handleCancel={handleCancel}
        businessObject={businessObject}
        boFieldOptions={boFieldOptions}
        defaultIsBo={useBo}
        onError={errorMsg => setErrorMsg(errorMsg)}
      />
    </>
  );
};

interface ModalSelectProps {
  isModalVisible: boolean;
  valueType: ValueType;
  handleOk: ({ valueType, metaData }) => void;
  handleCancel: () => void;
  eventMetadata: EventBoFieldMetadata;
  customerMetadata: CustomerMetadata;
  visitorMetadata: VisitorMetadata;
  businessObject?: Record<string, string>;
  boFieldOptions?: BOField[];
  defaultIsBo?: boolean;
  onError?: (errorMsg: string) => void;
}

export const ModalSelect: React.FC<ModalSelectProps> = props => {
  // Props
  const {
    isModalVisible,
    handleOk,
    handleCancel,
    valueType,
    customerMetadata,
    visitorMetadata,
    eventMetadata,
    boFieldOptions,
    businessObject,
    defaultIsBo,
    onError,
  } = props;

  // Translations
  const { t, i18n } = useTranslation();

  // States
  const [, setOpenTreeSelect] = useState(false);
  const [metaData, setMetaData] = useState({
    customer: customerMetadata,
    visitor: visitorMetadata,
    event: {
      ...eventMetadata,
      useBo: eventMetadata.useBo ?? defaultIsBo,
    },
  });
  const [tempValueType, setTempValueType] = useState(valueType === 'normal' ? 'event' : valueType);

  // Selectors
  const { data: dynamicContentAttributes } = useGetDynamicContentAttr();

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [listEvent, setListEvent] = useState<any[]>([]);
  const [listSourceByEvent, setListSourceByEvent] = useState<any[]>([]);
  const [listEventAttr, setListEventAttr] = useState<any[]>([]);
  const [treeEventAttr, setTreeEventAttr] = useState<any[]>([]);
  const [, setSearchValue] = useState('');
  const [loading, setLoading] = useState({
    isLoadingEvent: false,
    isLoadingSource: false,
    isLoadingAttrs: false,
  });

  // Memo
  const currentMetaData = useMemo(() => {
    return metaData[tempValueType] || {};
  }, [metaData, tempValueType]);

  const flatEventAttrs = useMemo(() => {
    return flattenTree(listEventAttr, 'items');
  }, [listEventAttr]);

  const { errorMessage: eventAttrErrorMessage } = checkStatusAttr({
    listAttribute:
      flatEventAttrs.map(({ itemPropertyName, eventPropertyName, status }) => ({
        value: eventPropertyName || itemPropertyName,
        status,
      })) || [],
    field: currentMetaData.item_property_name || undefined,
  });

  const { errorMessage: boAttrErrorMessage } = checkStatusAttr({
    listAttribute: (dynamicContentAttributes || {})[tempValueType] || [],
    field: currentMetaData.item_property_name || undefined,
  });

  useEffect(() => {
    let errorMsg = '';

    if (valueType === VALUE_TYPE.EVENT.value && eventAttrErrorMessage) {
      errorMsg = eventAttrErrorMessage;
    }

    if ([VALUE_TYPE.CUSTOMER.value, VALUE_TYPE.VISITOR.value].includes(valueType) && boAttrErrorMessage) {
      errorMsg = boAttrErrorMessage;
    }

    onError && onError(errorMsg);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventAttrErrorMessage, boAttrErrorMessage, valueType]);

  useDeepCompareEffect(() => {
    setMetaData({
      customer: customerMetadata,
      visitor: visitorMetadata,
      event: {
        ...eventMetadata,
        useBo: eventMetadata.useBo ?? defaultIsBo,
      },
    });
  }, [customerMetadata, visitorMetadata, eventMetadata]);

  useEffect(() => {
    if (isModalVisible) {
      setTempValueType(valueType === 'normal' ? 'event' : valueType);
    }
  }, [valueType, isModalVisible]);

  useEffect(() => {
    getListEvents();

    setMetaData(metaData => ({
      ...metaData,
      [tempValueType]: {
        ...metaData[tempValueType],
        useBo: eventMetadata.useBo ?? defaultIsBo,
      },
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tempValueType]);

  useEffect(() => {
    if (currentMetaData.event_action_id && currentMetaData.event_category_id) {
      getListSources({
        event_action_id: currentMetaData.event_action_id,
        event_category_id: currentMetaData.event_category_id,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentMetaData.event_action_id, currentMetaData.event_category_id]);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const { insight_property_ids } = currentMetaData;

    getListAttribute({
      insight_property_ids: insight_property_ids,
      event_action_id: currentMetaData.event_action_id,
      event_category_id: currentMetaData.event_category_id,
      signal,
    });

    return () => {
      controller.abort();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentMetaData.insight_property_ids]);

  // Services
  const getListEvents = async () => {
    try {
      setLoading({
        ...loading,
        isLoadingEvent: true,
      });

      const { rows } = await getListAllEvents();
      setListEvent(rows);
    } catch (error) {
    } finally {
      setLoading({
        ...loading,
        isLoadingEvent: false,
      });
    }
  };

  const getListSources = async ({ event_action_id, event_category_id }) => {
    setLoading({
      ...loading,
      isLoadingSource: true,
    });

    const { rows } = await getListSourceByEvent({ eventActionId: event_action_id, eventCategoryId: event_category_id });
    setListSourceByEvent(rows);

    setLoading({
      ...loading,
      isLoadingSource: false,
    });
  };

  const getListAttribute = async ({ insight_property_ids, event_action_id, event_category_id, signal }) => {
    if (!insight_property_ids || (Array.isArray(insight_property_ids) && insight_property_ids.length === 0)) {
      setLoading({
        ...loading,
        isLoadingAttrs: false,
      });
      return;
    }

    setLoading({
      ...loading,
      isLoadingAttrs: true,
    });

    const { rows } = await getListAttributesEvent({
      sourceId: insight_property_ids,
      eventActionId: event_action_id,
      eventCategoryId: event_category_id,
      signal, // AbortController
    });

    setListEventAttr(rows);

    setLoading({
      ...loading,
      isLoadingAttrs: false,
    });

    const treeAttrs = rows.map((attr: any) => ({
      value: attr.eventPropertyName,
      title: attr.eventPropertyDisplay,
      isLeaf: attr.items ? attr.items.length === 0 : true,
      selectable: attr.items ? attr.items.length === 0 : true,
      ...(attr.items &&
        attr.items.length && {
          children: attr.items.map((childAttr: any) => ({
            value: `${childAttr.itemTypeId}.${childAttr.itemPropertyName}`,
            title: childAttr.translateLabel,
            status: childAttr.status,
            isLeaf: true,
          })),
        }),
    }));

    setTreeEventAttr(treeAttrs);
  };

  const onChangeFieldType = (e: RadioChangeEvent) => {
    const { value } = e.target;
    if (value) {
      setMetaData(metaData => ({
        ...metaData,
        event: {
          ...metaData.event,
          useBo: value === 'bo',
        },
      }));
    }
  };

  const onChangeEvent = (val: string) => {
    const selected = listEvent.find(item => item.value === val);
    if (selected) {
      setMetaData(metaData => ({
        ...metaData,
        event: {
          ...metaData.event,
          event_action_id: selected.eventActionId,
          event_category_id: selected.eventCategoryId,
          event_tracking_name: selected.eventTrackingName,

          // resets
          insight_property_ids: null,
          item_type_id: null,
          item_type_name: null,
          item_property_name: null,
          item_property_label: null,
          event_property_syntax: null,
        },
      }));

      setTreeEventAttr([]);
      setListEventAttr([]);
    }
  };

  const onChangeSource = (values: any = []) => {
    const sourcesSelected = listSourceByEvent.filter(source => values.includes(source.value));

    if (values.length === 0) {
      setTreeEventAttr([]);
      setListEventAttr([]);
    }

    setMetaData(metaData => ({
      ...metaData,
      event: {
        ...metaData.event,
        insight_property_ids: sourcesSelected.map(source => source.insightPropertyId),

        // resets
        item_type_id: null,
        item_type_name: null,
        item_property_name: null,
        item_property_label: null,
        event_property_syntax: null,
      },
    }));
  };

  const onChangeAttribute = (val: string) => {
    const [attribute, item] = val.split('.');
    if (!item) {
      const selected = listEventAttr.find(attr => attr.value === attribute);
      if (selected) {
        setMetaData(metaData => ({
          ...metaData,
          event: {
            ...metaData.event,
            item_type_id: null,
            item_type_name: null,
            item_property_name: selected.eventPropertyName,
            item_property_label: selected.label,
            event_property_syntax: selected.eventPropertySyntax,
            useBo: false,
          },
        }));
      }
    } else {
      const selectedAttribute = listEventAttr.find(attr => +attr.itemTypeId === +attribute);
      const selected = selectedAttribute.items.find((attr: any) => attr.itemPropertyName === item);
      if (selected) {
        setMetaData(metaData => ({
          ...metaData,
          event: {
            ...metaData.event,
            item_type_id: selected.itemTypeId,
            item_type_name: selected.itemTypeName,
            item_property_name: selected.itemPropertyName,
            item_property_label: selected.translateLabel,
            event_property_syntax: selected.eventPropertySyntax,
            useBo: false,
          },
        }));
      }
    }
  };

  const onSelect = (item: { isLeaf: boolean; value: string; label: string; isBo: boolean }) => {
    if (item.isBo) {
      setMetaData(metaData => ({
        ...metaData,
        event: {
          ...metaData.event,
          field_code_bo: item.value,
          field_label_bo: item.label,
          useBo: true,
        },
      }));
    } else {
      if (item.isLeaf) {
        // select item || parents not items
        setSearchValue('');
        onChangeAttribute(item.value);
        onDropdownVisibleChange(false);
      } else {
        // select parents
        if (expandedKeys.includes(item.value)) {
          // close expand
          const newExpandedKeys = expandedKeys.filter(key => key !== item.value);
          onTreeExpand(newExpandedKeys);
        } else {
          // open expand
          onTreeExpand((prev: any) => [...prev, item.value]);
        }
      }
    }
  };

  const onTreeExpand = (val: any) => {
    setExpandedKeys(val);
  };

  const onDropdownVisibleChange = (open: boolean) => {
    setOpenTreeSelect(open);
  };

  const handleDisableSubmit = () => {
    switch (tempValueType) {
      case EVENT.value:
        if (metaData.event.useBo) {
          return !metaData.event.useBo;
        }

        return !metaData.event.item_property_name || !!eventAttrErrorMessage;

      default:
        return !currentMetaData.item_property_name || !!boAttrErrorMessage;
    }
  };

  const onChangeDynamicContentAttr = (value: any) => {
    setMetaData(metaData => ({
      ...metaData,
      [tempValueType]: {
        item_type_id: VALUE_TYPE[tempValueType.toUpperCase()]?.id,
        item_type_name: VALUE_TYPE[tempValueType.toUpperCase()]?.name,
        item_property_name: value,
      },
    }));
  };

  const onCancel = () => {
    setMetaData({
      visitor: visitorMetadata,
      customer: customerMetadata,
      event: eventMetadata as any,
    });

    setTempValueType(valueType);

    handleCancel();
  };

  const attributeValue =
    (currentMetaData.useBo
      ? currentMetaData.field_code_bo
      : currentMetaData.item_type_id
      ? `${currentMetaData.item_type_id}.${currentMetaData.item_property_name}`
      : currentMetaData.item_property_name) || undefined;

  return (
    <Modal
      title={t(translations.selectAttributeCapitalize.title)}
      width={545}
      visible={isModalVisible}
      onOk={() =>
        handleOk({
          valueType: tempValueType,
          metaData,
        })
      }
      onCancel={onCancel}
      destroyOnClose
      footer={[
        <Button
          key="submit"
          type="primary"
          onClick={() =>
            handleOk({
              valueType: tempValueType,
              metaData,
            })
          }
          disabled={handleDisableSubmit()}
        >
          {t(translations.apply.title)}
        </Button>,
        <Button key="back" onClick={onCancel}>
          {t(translations.cancel.title)}
        </Button>,
      ]}
    >
      {businessObject && boFieldOptions && boFieldOptions.length ? (
        <RadioGroup
          value={currentMetaData.useBo ? 'bo' : 'event'}
          onChange={onChangeFieldType}
          style={{ gap: 25 }}
          options={[
            {
              value: 'bo',
              label: t(translations.fromBo.title),
            },
            {
              value: 'event',
              label: t(translations.fromEvent.title),
            },
          ]}
        >
          {/* <Space direction="horizontal">
            <Radio value="bo">
              <Text color="var(--text-second-color)" size={13}>
                {t(translations.fromBo.title)}
              </Text>
            </Radio>
            <Radio value="event">
              <Text color="var(--text-second-color)" size={13}>
                {t(translations.fromEvent.title)}
              </Text>
            </Radio>
          </Space> */}
        </RadioGroup>
      ) : null}
      {currentMetaData.useBo && businessObject && boFieldOptions ? (
        <RowSelect>
          <Text className="ants-text-left">{t(translations.selectBo.title)}</Text>
          <Input
            value={businessObject ? businessObject.itemTypeDisplay || businessObject.itemTypeName : ''}
            onChange={() => {}}
            className="ants-pointer-events-none"
            style={{ borderTop: 'none', borderRight: 'none', borderLeft: 'none', fontSize: 12 }}
          />
          <Text className="ants-text-left">{t(translations.selectAttribute.title)}</Text>
          <Select
            value={attributeValue}
            options={boFieldOptions}
            onChange={(value, item) =>
              onSelect({ value, isBo: true, label: (item as SelectOption).label || '', isLeaf: true })
            }
            placeholder={t(translations.selectAnItem.title)}
            showSearch
          />
        </RowSelect>
      ) : (
        <RowSelect>
          <Text className="ants-text-left">{t(translations.dynamicContent.modal.label.contentSource)}</Text>
          <Select
            showSearch
            placeholder="Select an item"
            defaultValue={EVENT.value}
            value={tempValueType || undefined}
            onChange={v => setTempValueType(v)}
            options={Object.values(VALUE_TYPE).map(({ value, label }) => ({ value, label }))}
          />

          {[CUSTOMER.value, VISITOR.value].includes(tempValueType || '') ? (
            <>
              <Text className="ants-text-left">{t(translations.dynamicContent.modal.label.attribute)}</Text>
              <Select
                status={boAttrErrorMessage ? 'error' : ''}
                errorMsg={boAttrErrorMessage}
                showSearch
                placeholder="Select an item"
                options={buildOptionAttrArchive(
                  (dynamicContentAttributes || {})[tempValueType],
                  currentMetaData.item_property_name || undefined,
                )?.map(attribute => ({
                  value: attribute.value,
                  label: get(attribute, `label.${i18n.language.toUpperCase()}`, attribute.defaultLabel),
                }))}
                value={currentMetaData.item_property_name || undefined}
                onChange={onChangeDynamicContentAttr}
              />
            </>
          ) : (
            <>
              <Text className="ants-text-left">{t(translations.selectEvent.title)}</Text>
              <Select
                showSearch
                placeholder="Select an item"
                value={currentMetaData.event_tracking_name || undefined}
                loading={loading.isLoadingEvent}
                onChange={v => onChangeEvent(v)}
                options={listEvent.map(event => ({ value: event.value, label: event.label }))}
              />
              <Text className="ants-text-left">{t(translations.inAnySourceOf.title)}</Text>
              <Select
                mode="multiple"
                showSearch
                placeholder="Select an item"
                value={currentMetaData.insight_property_ids || undefined}
                loading={loading.isLoadingSource}
                options={listSourceByEvent.map(event => ({ value: event.value, label: event.label }))}
                onChange={v => onChangeSource(v)}
              />
              <Text className="ants-text-left">{t(translations.selectEventAttribute.title)}</Text>
              <TreeSelect
                showSearch
                value={attributeValue || undefined}
                placeholder={t(translations.selectEventAttribute.title)}
                treeData={treeEventAttr.map(attr => ({
                  ...attr,
                  children: buildOptionAttrArchive(attr.children || [], attributeValue || undefined),
                }))}
                loading={loading.isLoadingAttrs}
                onChange={onChangeAttribute}
                status={eventAttrErrorMessage ? 'error' : ''}
                errorMsg={eventAttrErrorMessage}
              />
            </>
          )}
        </RowSelect>
      )}
    </Modal>
  );
};

ModalSelect.defaultProps = {
  valueType: EVENT.value,
  customerMetadata: {
    item_type_id: CUSTOMER.id,
    item_type_name: CUSTOMER.name,
    item_property_name: '',
  },
  visitorMetadata: {
    item_type_id: VISITOR.id,
    item_type_name: VISITOR.name,
    item_property_name: '',
  },
};

export const StyledAutoComplete = styled(AutoComplete)`
  ${tw`ants-w-100`}
  .ant-select-selector {
    border-top: none !important;
    border-right: none !important;
    border-left: none !important;
  }
`;

export const StyledDropdown = styled.div<{ $isSearching: boolean }>`
  .ant-select-tree
    .ant-select-tree-treenode:not(.ant-select-tree .ant-select-tree-treenode-disabled)
    .ant-select-tree-title {
    font-size: var(--text-normal-font-size);
    ${props =>
      props.$isSearching
        ? css`
            font-weight: 500;
          `
        : ``};
  }
  .ant-select-tree-node-content-wrapper {
    ${tw`ants-flex ants-items-center`}
  }
`;

export const StyledSelect = styled(Select)<any>`
  .ant-select-selection-placeholder {
    ${props =>
      !props.$isPlaceholder
        ? css`
            color: rgba(0, 0, 0, 0.85);
            font-size: var(--text-normal-font-size);
          `
        : `
        `}
  }
`;

export const StyledTreeSelect = styled(TreeSelect)`
  font-size: var(--text-normal-font-size);
  width: 100%;
  &:hover {
    .ant-select-selector {
      border-color: #d9d9d9 !important;
    }
  }

  .ant-select-selector {
    border-top: 0 !important;
    border-right: 0 !important;
    border-left: 0 !important;
    border-bottom: 1px solid #d9d9d9;

    // padding-right: 12px;
  }
  .ant-select-selection-overflow-item.ant-select-selection-overflow-item-rest {
    .ant-select-selection-item {
      color: #096dd9;
      background: #e6f7ff;
      border-color: #91d5ff;
    }
  }
`;
export const StyledSearch = styled(Search)`
  .ant-input-group-addon {
    button {
      height: 32px;
    }
  }
`;
