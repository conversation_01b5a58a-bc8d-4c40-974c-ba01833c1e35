// libraries
import React, { memo, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';

// components
import { Space } from 'app/components/atoms';
import { Collapse, CollapsePanel, UploadImage } from 'app/components/molecules';
import {
  selectBlockSelected,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import BackgroundSetting from '../../../BackgroundSetting';
import { SettingWrapper } from '../../../../molecules';
import { BoxShadowSetting } from '../../../BoxShadowSetting';
import { BorderSettingPopover } from '../../../BorderSetting';
import { RoundedCornersSetting } from '../../../RoundedCornersSetting';
import { SpacingSetting } from '../../../SpacingSetting';

// actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// translation
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';

// utils
import {
  getBackgroundSettings,
  getBackgroundStyles,
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from '../../../../../utils';
import { handleError } from 'app/utils/handleError';
import { InputFieldStyling } from './components/InputFieldStyling';
import { ResendButtonStyling } from './components/ResendButtonStyling';
import { ButtonStyling } from '../../../ButtonStyling';
import { MessageStyling } from './components/MessageStyling';
import { ContainerStyling } from '../../../ContainerStyling';
import { FontSettingPopover } from '../../../FontSetting';
import { TYPE_TAB_ACTIVE_ADVANCE, TYPE_TAB_ACTIVE_CONTENT } from '../constants';
import { BlockStyling } from '../../Common/BlockStyling';

type TAdvanced = {};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/OTPVerification/Advanced/index.tsx';

export const Advanced: React.FC<TAdvanced> = memo(props => {
  const dispatch = useDispatch();
  const { updateBlockSetting, setData, setSidePanel } = mediaTemplateDesignActions;
  const blockSelected = useSelector(selectBlockSelected);
  const sidePanel = useSelector(selectSidePanel);
  const blockSettings = blockSelected?.settings;

  const { t } = useTranslation();

  useEffect(() => {
    if (sidePanel.keyActivePanel === TYPE_TAB_ACTIVE_CONTENT.TRIGGER_EVENT) {
      dispatch(
        setSidePanel({
          keyActivePanel: TYPE_TAB_ACTIVE_ADVANCE.OTP_FORM_STYLING,
        }),
      );
    }
  }, []);

  const onUpdateBlockSettings = (settings = {}, styles = {}) => {
    try {
      // Callback onChange
      if (blockSelected) {
        const { otpForm } = blockSettings;
        onChangeOTPFormStyling(
          {
            ...otpForm.blockSettings,
            ...settings,
          },
          {
            ...otpForm.blockStyles,
            ...styles,
          },
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  const onChangeOTPFormStyling = useCallback(
    (blockSettings = {}, blockStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.otpForm = {
                  ...draft.otpForm,
                  blockSettings,
                  blockStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeOTPFormStyling',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onUpdateBackgroundImage = imageUrl => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              draft.otpForm = {
                ...draft.otpForm,
                backgroundImageObj: {
                  ...draft.otpForm.backgroundImageObj,
                  ...imageUrl,
                },
              };
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBackgroundImage',
        args: {},
      });
    }
  };

  const onChangeButtonStyling = useCallback(
    (name = '', settings = {}, styles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft[name] = {
                  ...draft[name],
                  buttonSettings: {
                    ...draft[name].buttonSettings,
                    ...settings,
                  },
                  buttonStyles: {
                    ...draft[name].buttonStyles,
                    ...styles,
                  },
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: onChangeButtonStyling.name,
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeHoverSetting = useCallback(
    (name = '', settings = {}, styles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft[name] = {
                  ...draft[name],
                  buttonHoverSettings: {
                    ...draft[name].buttonHoverSettings,
                    ...settings,
                  },
                  buttonHoverStyles: {
                    ...draft[name].buttonHoverStyles,
                    ...styles,
                  },
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: onChangeHoverSetting.name,
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeIconStyling = useCallback(
    (name: string = '', settings: any = {}, styles: any = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft[name] = {
                  ...draft[name],
                  buttonSettings: {
                    ...draft[name].buttonSettings,
                    icon: settings.icon,
                    iconColor: settings.color,
                    iconSize: settings.iconSize,
                    iconAfter: settings.displayIconAfterText,
                    iconSpacing: settings.spacing,
                  },
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const renderOTPFormStyling = (): React.ReactNode => {
    const otpForm = blockSettings.otpForm;
    return (
      <Space size={20} direction="vertical">
        <BackgroundSetting
          label={t(translations.style.title)}
          settings={getBackgroundSettings(otpForm.blockSettings)}
          styles={getBackgroundStyles(otpForm.blockStyles)}
          onChange={onUpdateBlockSettings}
        />
        <SettingWrapper label={t(translations.backgroundImage.title)} />
        <UploadImage
          required
          selectedImage={{ url: otpForm.backgroundImageObj?.previewUrl }}
          // showImageURL={false}
          onRemoveImage={value => onUpdateBackgroundImage({ previewUrl: '' })}
          onChangeImage={value => onUpdateBackgroundImage({ previewUrl: value.url })}
        />
        <FontSettingPopover
          styles={otpForm.blockStyles}
          settingsStyle={otpForm.blockSettings}
          onChange={onUpdateBlockSettings}
        />
        <BoxShadowSetting
          settings={getBoxShadowSettings(otpForm.blockSettings as any)}
          onChange={(settings, styles) => {
            onUpdateBlockSettings(settings, styles);
          }}
        />
        <BorderSettingPopover
          settings={getBorderSettings(otpForm.blockSettings as any)}
          styles={getBorderStyles(otpForm.blockStyles)}
          onChange={onUpdateBlockSettings}
        />
        <RoundedCornersSetting
          settings={getRoundedCornersSettings(otpForm.blockSettings as any)}
          styles={getRoundedCornersStyles(otpForm.blockStyles)}
          onChange={onUpdateBlockSettings}
        />
        <SpacingSetting
          settings={getSpacingSettings(otpForm.blockSettings as any)}
          styles={getSpacingStyles(otpForm.blockStyles)}
          onChange={onUpdateBlockSettings}
        />
      </Space>
    );
  };

  const handleChangeSettings = useCallback(
    (data: Record<string, any>) => {
      try {
        if (blockSelected && data && typeof data === 'object') {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                Object.keys(data).forEach(key => {
                  draft[key] = data[key];
                });
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'handleChangeSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const handleOnchangeTab = (key: number) => {
    const displayMessage = key === TYPE_TAB_ACTIVE_ADVANCE.MESSAGES_STYLING;
    handleChangeSettings({ displayMessage });
    dispatch(
      setSidePanel({
        keyActivePanel: key,
      }),
    );
  };

  return (
    <Collapse
      activeKey={sidePanel?.keyActivePanel || TYPE_TAB_ACTIVE_ADVANCE.CONTAINER_STYLING}
      onChange={(key: string | string[]) => handleOnchangeTab(Number(key))}
      defaultActiveKey={sidePanel?.keyActivePanel || TYPE_TAB_ACTIVE_ADVANCE.CONTAINER_STYLING}
      accordion
    >
      <CollapsePanel header={t(translations.otpFormStyling.title)} key={TYPE_TAB_ACTIVE_ADVANCE.OTP_FORM_STYLING}>
        {renderOTPFormStyling()}
      </CollapsePanel>
      <CollapsePanel header={t(translations.inputFieldStyling.title)} key={TYPE_TAB_ACTIVE_ADVANCE.INPUT_FIELD_STYLING}>
        <InputFieldStyling onChange={handleChangeSettings} settings={blockSettings} />
      </CollapsePanel>
      <CollapsePanel header={t(translations.resendButtonStyling.title)} key={TYPE_TAB_ACTIVE_ADVANCE.RESEND_STYLING}>
        <ButtonStyling
          buttonSettings={blockSettings.resendButton?.buttonSettings}
          buttonStyles={blockSettings.resendButton?.buttonStyles}
          buttonHoverStyles={blockSettings.resendButton?.buttonHoverStyles}
          buttonHoverSettings={blockSettings.resendButton?.buttonHoverSettings}
          onChange={(settings, styles) => onChangeButtonStyling('resendButton', settings, styles)}
          onChangeHover={(settings, styles) => onChangeHoverSetting('resendButton', settings, styles)}
          onChangeIcon={settings => onChangeIconStyling('resendButton', settings)}
        />
      </CollapsePanel>
      <CollapsePanel header={t(translations.verifyButtonStyling.title)} key={TYPE_TAB_ACTIVE_ADVANCE.VERIFY_STYLING}>
        <ButtonStyling
          buttonSettings={blockSettings.verifyButton?.buttonSettings || {}}
          buttonStyles={blockSettings.verifyButton?.buttonStyles}
          buttonHoverStyles={blockSettings.verifyButton?.buttonHoverStyles}
          buttonHoverSettings={blockSettings.verifyButton?.buttonHoverSettings}
          onChange={(settings, styles) => onChangeButtonStyling('verifyButton', settings, styles)}
          onChangeHover={(settings, styles) => onChangeHoverSetting('verifyButton', settings, styles)}
          onChangeIcon={settings => onChangeIconStyling('verifyButton', settings)}
        />
      </CollapsePanel>
      <CollapsePanel header={t(translations.messageStyling.title)} key={TYPE_TAB_ACTIVE_ADVANCE.MESSAGES_STYLING}>
        <MessageStyling onChange={handleChangeSettings} settings={blockSettings} />
      </CollapsePanel>
      <CollapsePanel header={t(translations.containerStyling.title)} key={TYPE_TAB_ACTIVE_ADVANCE.CONTAINER_STYLING}>
        <BlockStyling />
      </CollapsePanel>
    </Collapse>
  );
});
// verifyButton
