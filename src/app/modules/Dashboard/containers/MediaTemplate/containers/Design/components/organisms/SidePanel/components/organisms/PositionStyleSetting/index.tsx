// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Molecules
import { EdgeSetting } from '../../molecules/EdgeSetting';

// Utils
import { handleError } from 'app/utils/handleError';

// Types
import { TPositionSettings } from './types';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/PositionSetting/index.tsx';

interface PositionSettingProps {
  settings: TPositionSettings;
  onChange: (styleSettings: TPositionSettings) => void;
  isHasNegativeValue?: boolean;
}

export const PositionStyleSetting: React.FC<PositionSettingProps> = props => {
  const { settings, onChange = () => {}, isHasNegativeValue = false } = props;

  const { t } = useTranslation();

  const onChangeEdgeSetting = ({ values, linked, unit }) => {
    try {
      onChange({
        ...settings,
        linkedPositionInput: linked,
        positionSuffix: unit,
        top: values[0],
        right: values[1],
        bottom: values[2],
        left: values[3],
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeEdgeSetting',
        args: {},
      });
    }
  };
  return (
    <div>
      <EdgeSetting
        edgeLabelClassName="max-w-min"
        label={t(translations.position.title)}
        unit={settings.positionSuffix}
        linked={settings.linkedPositionInput}
        disabledLinked
        values={[
          settings.top === 'auto' ? 'auto' : settings.top,
          settings.right === 'auto' ? 'auto' : settings.right,
          settings.bottom === 'auto' ? 'auto' : settings.bottom,
          settings.left === 'auto' ? 'auto' : settings.left,
        ]}
        edgeLabels={[
          t(translations.top.title),
          t(translations.right.title),
          t(translations.bottom.title),
          t(translations.left.title),
        ]}
        onChange={onChangeEdgeSetting}
        {...(isHasNegativeValue ? { min: undefined } : {})}
      />
    </div>
  );
};
