// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

export const SurpriseTreasureHuntBlockWrapper = styled.div<{ align: string }>`
  ${tw`ants-relative ants-w-full ants-h-full ants-overflow-visible`}
  display: flex;
  align-items: center;
  justify-content: ${props =>
    props.align === 'center' ? 'center' : props.align === 'right' ? 'flex-end' : 'flex-start'};
`;

export const GiftContainer = styled.div<{ gap: number; width: number; rows: number; columns: number }>`
  display: grid;
  grid-template-columns: ${props => (props.columns ? `repeat(${props.columns}, 1fr)` : `repeat(3, 1fr)`)};
  grid-template-rows: ${props => (props.rows ? `repeat(${props.rows}, 1fr)` : `repeat(3, 1fr)`)};
  width: ${props => props.width}%;
  gap: ${props => props.gap}px;
  overflow: visible;
`;

export const GiftItem = styled.div<{ align: string }>`
  ${tw`ants-w-full ants-h-full`}

  display: flex;
  align-items: center;
  justify-content: ${props =>
    props.align === 'center' ? 'center' : props.align === 'right' ? 'flex-end' : 'flex-start'};
`;

export const ImageWrapper = styled.div`
  ${tw`ants-flex ants-items-center ants-justify-center ants-flex-1 ants-relative`}
`;

export const ImageGiftPreview = styled.img`
  ${tw`ants-w-full ants-h-full ants-object-contain`}
`;
