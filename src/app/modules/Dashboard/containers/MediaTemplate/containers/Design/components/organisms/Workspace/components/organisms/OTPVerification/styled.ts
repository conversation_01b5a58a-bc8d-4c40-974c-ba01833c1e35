import styled from 'styled-components';
import { getDirection } from './utils';

export const WrapperOTPVerificaction = styled.div`
  position: relative;
`;

export const WrapperItem = styled.div`
  display: flex;
`;

export const InputItem = styled.div<{ fullWidth?: boolean }>`
  width: ${props => (props.fullWidth ? '100% !important' : '80px')};
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
  .block-hyphens {
    position: absolute;
    width: 100%;
    height: 10px;
    z-index: 1;
  }
`;

export const ExpireTimeElement = styled.div<{ labelPosition: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-direction: ${props => getDirection(props.labelPosition)};
  font-size: 14px;
`;

export const ContentColumn = styled.div<{ alignContent?: string }>`
  display: flex;
  flex: 1;
  justify-content: ${props => props.alignContent};
`;

export const WrapperNotiMessage = styled.div<{ type: string }>`
  position: absolute;
  width: 100%;
  text-align: center;
  ${({ type }) => (type === 'top' ? { top: '-70px' } : { bottom: '-70px' })}
`;

export const ContentMessage = styled.div``;

export const WrapperInputField = styled.div``;
