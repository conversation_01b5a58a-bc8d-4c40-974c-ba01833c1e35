import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';

// Translations
import { translations } from 'locales/translations';

// Slices
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Molecules
import { Collapse } from 'app/components/molecules';
import { Panel } from 'app/components/molecules/Collapse';

// Organisms
import { ContainerStyling } from '../../../ContainerStyling';
import { DisplayCondition } from '../../../DisplayCondition';

// Utils
import { handleError } from 'app/utils/handleError';

// Constants
import {
  SIDE_PANEL_COLLAPSE,
  SLIDE_DISPLAY_CONDITION_OPTIONS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { BlockStyling } from '../../Common/BlockStyling';

const { SHOW_SLIDE_WHEN, HIDDEN_SLIDE_WHEN } = SLIDE_DISPLAY_CONDITION_OPTIONS;

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/SlideShow/Advanced/index.tsx';

const Advanced = () => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateBlockSetting } = mediaTemplateDesignActions;

  // Selector
  const blockSelected = useSelector(selectBlockSelected);

  const blockSettings = blockSelected?.settings;

  const {
    displayCondition = {
      condition: '',
      field: '',
      index: 1,
      operator: '',
      dataType: '',
      value: '',
    },
  } = blockSettings?.blockStylesSettings || {};

  const onChangeDisplayCondition = (settings = {}) => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              draft.blockStylesSettings.displayCondition = {
                ...draft.blockStylesSettings.displayCondition,
                ...settings,
              };
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  // Handlers
  const onChangeContainerStyling = useCallback(
    (blockStylesSettings = {}, blockStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.blockStylesSettings = {
                  ...draft.blockStylesSettings,
                  ...blockStylesSettings,
                };
                draft.blockStyles = {
                  ...draft.blockStyles,
                  ...blockStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeSlideStyling = useCallback(
    (slideStylesSettings = {}, slideStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.slideStylesSettings = {
                  ...draft.slideStylesSettings,
                  ...slideStylesSettings,
                };
                draft.slideStyles = {
                  ...draft.slideStyles,
                  ...slideStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeSlideStyling',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeContainerHoverSetting = useCallback(
    (settings = {}, styles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.blockHoverStylesSettings = {
                  ...draft.blockHoverStylesSettings,
                  ...settings,
                };
                draft.blockHoverStyles = {
                  ...draft.blockHoverStyles,
                  ...styles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeSlideHoverSetting = (slideHoverStylesSettings = {}, slideHoverStyles = {}) => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              draft.slideHoverStylesSettings = {
                ...draft.slideHoverStylesSettings,
                ...slideHoverStylesSettings,
              };
              draft.slideHoverStyles = {
                ...draft.slideHoverStyles,
                ...slideHoverStyles,
              };
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeSlideHoverSetting',
        args: { slideHoverStylesSettings, slideHoverStyles },
      });
    }
  };

  if (blockSettings) {
    return (
      <Collapse defaultActiveKey={SIDE_PANEL_COLLAPSE.CONTAINER_STYLING} accordion>
        <Panel header={t(translations.containerStyling.title)} key={SIDE_PANEL_COLLAPSE.CONTAINER_STYLING}>
          <BlockStyling />
        </Panel>
        <Panel header={t(translations.slideStyling.title)} key={SIDE_PANEL_COLLAPSE.STYLE_STYLING}>
          <ContainerStyling
            blockStylesSettings={blockSettings.slideStylesSettings}
            blockStyles={blockSettings.slideStyles}
            onChange={onChangeSlideStyling}
            blockHoverStyles={blockSettings.slideHoverStyles}
            blockHoverStylesSettings={blockSettings.slideHoverStylesSettings}
            onChangeHover={onChangeSlideHoverSetting}
          />
        </Panel>
        <Panel header={t(translations.displayCondition.title)} key={SIDE_PANEL_COLLAPSE.DISPLAY_CONDITION}>
          <DisplayCondition
            showIndex={![SHOW_SLIDE_WHEN.value, HIDDEN_SLIDE_WHEN.value].includes(displayCondition.condition)}
            displayConditionOptions={Object.values(SLIDE_DISPLAY_CONDITION_OPTIONS)}
            displayCondition={displayCondition}
            valueCondition={displayCondition.condition}
            onChange={onChangeDisplayCondition}
          />
        </Panel>
      </Collapse>
    );
  }

  return <div>Advanced</div>;
};

export default Advanced;
