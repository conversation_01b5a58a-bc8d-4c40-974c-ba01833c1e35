import React, { useEffect, useState } from 'react';
import { Modal, TabPane, Tabs, UploadImage } from 'app/components/molecules';
import { CellImage, FooterContainer, ListCellImages, StyledSwitchLabel } from './styled';
import { translations } from 'locales/translations';
import { <PERSON>ton, ScrollBox } from 'app/components/atoms';
import { useTranslation } from 'react-i18next';
import { cloneDeep, isEmpty } from 'lodash';
import { SettingWrapper, SwitchLabel } from '../../molecules';
import { ACTION_TYPES_CHANGE } from '../BlockEditing/SurpriseTreasureHunt/Content/constants';

type TCellImages = {
  visible: boolean;
  toggle: (data: boolean) => void;
  settings: Record<string, any>;
  onChangeSurpriseTreasureHunt: (type: string, data: any) => void;
};

const CellImages: React.FC<TCellImages> = props => {
  const { t } = useTranslation();
  const { visible, toggle, settings, onChangeSurpriseTreasureHunt } = props;
  const { cellImages, imageGiftFirstRender } = settings;

  const [data, setData] = useState(cellImages);
  const { applyImageAllBefore, applyImageAllAfter, imageAllUrlBefore, imageAllUrlAfter, imageDynamic = [] } = data;

  useEffect(() => {
    setData(cellImages);
    return () => {
      if (!visible) {
        setData(cellImages);
      }
    };
  }, [cellImages, visible]);

  const onApplyData = () => {
    onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.APPLY_CELL_IMAGES, data);
    toggle(!visible);
  };

  const renderFooter = () => {
    return (
      <FooterContainer>
        <Button type="primary" onClick={onApplyData}>
          {t(translations.apply.title)}
        </Button>
        <Button onClick={() => toggle(!visible)}>{t(translations.close.title)}</Button>
      </FooterContainer>
    );
  };

  const onChangeData = (type: string, value: any) => {
    switch (type) {
      case ACTION_TYPES_CHANGE.CHANGE_IMAGE_DYNAMIC:
        const { imageKey = '', imageUrl = '', isApplyAll = false, isBefore = false } = value;
        let imageDynamicTemp = cloneDeep(data.imageDynamic);

        if (!Array.isArray(imageDynamicTemp)) return;

        const { rows, columns } = settings.metrics;

        if (isApplyAll) {
          const keyUpdate = isBefore ? 'imageAllUrlBefore' : 'imageAllUrlAfter';
          setData(prev => ({
            ...prev,
            [keyUpdate]: imageUrl,
          }));
          break;
        }

        const previewUrlKey = isBefore ? 'previewUrlBefore' : 'previewUrlAfter';
        for (let i = 0; i < rows; i++) {
          for (let j = 0; j < columns; j++) {
            const item = imageDynamicTemp[i][j];
            if (item.key === imageKey) {
              item[previewUrlKey] = imageUrl;
            }
          }
        }
        setData(prev => ({
          ...prev,
          imageDynamic: imageDynamicTemp,
        }));
        break;

      case ACTION_TYPES_CHANGE.IS_APPLY_IMAGE_ALL:
        if (Object.keys(value).length) {
          Object.keys(value).forEach(key => {
            setData(prev => ({
              ...prev,
              [key]: value[key],
            }));
          });
        }
        break;

      default:
        break;
    }
  };

  const renderLisImage = (type: string) => {
    const isBefore = type === 'BEFORE_IMAGE_CELL';

    const dataImageCellWithType = {
      type,
      applyImageAll: isBefore ? applyImageAllBefore : applyImageAllAfter,
      imageAllUrl: isBefore ? imageAllUrlBefore : imageAllUrlAfter,
    };

    const { imageAllUrl, applyImageAll } = dataImageCellWithType;

    if (applyImageAll) {
      return (
        <CellImage className="image-cell">
          <SettingWrapper label={t(translations.cellImage.title)} />
          <UploadImage
            required
            selectedImage={{ url: imageAllUrl || imageGiftFirstRender }}
            // showImageURL={false}
            onRemoveImage={value =>
              onChangeData(ACTION_TYPES_CHANGE.CHANGE_IMAGE_DYNAMIC, {
                imageKey: '',
                imageUrl: '',
                isApplyAll: true,
                isBefore,
              })
            }
            onChangeImage={value =>
              onChangeData(ACTION_TYPES_CHANGE.CHANGE_IMAGE_DYNAMIC, {
                imageKey: '',
                imageUrl: value.url,
                isApplyAll: true,
                isBefore,
              })
            }
          />
        </CellImage>
      );
    }

    if (!isEmpty(imageDynamic)) {
      const flattenImages = imageDynamic.flat();

      return flattenImages.map((image, index) => (
        <CellImage className="image-cell" key={image.key}>
          <SettingWrapper labelClassName="label-cell" label={`${t(translations.image.title)} ${index + 1}`} />
          <UploadImage
            required
            selectedImage={{ url: (isBefore ? image.previewUrlBefore : image.previewUrlAfter) || imageGiftFirstRender }}
            // showImageURL={false}
            onRemoveImage={value =>
              onChangeData(ACTION_TYPES_CHANGE.CHANGE_IMAGE_DYNAMIC, {
                imageKey: image.key,
                imageUrl: '',
                isBefore,
              })
            }
            onChangeImage={value =>
              onChangeData(ACTION_TYPES_CHANGE.CHANGE_IMAGE_DYNAMIC, {
                imageKey: image.key,
                imageUrl: value.url,
                isBefore,
              })
            }
          />
        </CellImage>
      ));
    }
  };
  return (
    <Modal
      visible={visible}
      title={'Cell Images'}
      onCancel={() => toggle(!visible)}
      centered
      width={1347}
      style={{ height: 751 }}
      footer={renderFooter()}
    >
      <Tabs defaultActiveKey="1">
        <TabPane tab={<div className="ants-flex ants-gap-2 ants-items-center">Cell Image before open</div>} key="1">
          <StyledSwitchLabel
            label={t(translations.applyImageAll.title)}
            checked={data.applyImageAllBefore}
            className="--color-label"
            onChange={checked =>
              onChangeData(ACTION_TYPES_CHANGE.IS_APPLY_IMAGE_ALL, {
                applyImageAllBefore: checked,
              })
            }
          />
          <ListCellImages>{renderLisImage('BEFORE_IMAGE_CELL')}</ListCellImages>
        </TabPane>
        <TabPane tab={<div className="ants-flex ants-gap-2 ants-items-center">Cell Image after open</div>} key="2">
          <StyledSwitchLabel
            label={t(translations.applyImageAll.title)}
            checked={data.applyImageAllAfter}
            className="--color-label"
            onChange={checked =>
              onChangeData(ACTION_TYPES_CHANGE.IS_APPLY_IMAGE_ALL, {
                applyImageAllAfter: checked,
              })
            }
          />
          <ListCellImages>{renderLisImage('AFTER_IMAGE_CELL')}</ListCellImages>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default CellImages;

CellImages.defaultProps = {
  visible: false,
  toggle: () => {},
  onChangeSurpriseTreasureHunt: () => {},
};
