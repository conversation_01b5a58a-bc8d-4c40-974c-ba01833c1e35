// Libraries
import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Select as SelectAnt } from 'antd';
import { useSelector } from 'react-redux';

// Translations
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';

// Molecules
import { InputNumber, RadioGroup, Select } from 'app/components/molecules';
import { SettingWrapper } from '../../../../../molecules';

// Utils
import { handleError } from 'app/utils/handleError';
import { getObjSafely } from 'app/utils/common';

// Atoms
import { Input, Space, Text } from 'app/components/atoms';

// Contants
import { BY_OPTIONS, BY_OPTIONS_PAGES, SHAKE_TYPE_OPTIONS, TRIGGER_OPTIONS } from './constants';

// Styles
import { WrapperBlockTime, WrapperUnitTime } from './styled';

// Selectors
import {
  selectBlockSelected,
  selectCurrentViewPage,
  selectNamespace,
  selectViewPageSelected,
  selectViewPages,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Custom hooks
import { useDebounce, useUpdateEffect } from 'app/hooks';

// Types
import { TElement } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

type TSettings = {
  [key: string]: any;
  shakeTrigger: string;
  by: string;
  timeToShake: string;
  shakeType: string;
  reminderNotification: string;
};

type TOptions = {
  label: string;
  value: string | null;
  id?: string;
  itemSelected?: string;
};

interface TriggerSettingProps {
  label?: string;
  settings?: TSettings;
  onChange?: (settings: TSettings, keyChange?: string) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/TriggerSetting/index.tsx';

export const TriggerSetting: React.FC<TriggerSettingProps> = props => {
  const { label = '', settings, onChange = () => {} } = props;

  const { Option } = SelectAnt;

  // Selectors
  const currentViewPages = useSelector(selectCurrentViewPage);
  const pageSelected = useSelector(selectViewPageSelected);
  const viewPages = useSelector(selectViewPages);
  const currentBlocks = getObjSafely(() => currentViewPages?.blocks);
  const namespace = useSelector(selectNamespace);
  const blockSelected = useSelector(selectBlockSelected) as TElement;
  const { settings: settingsSelector } = blockSelected;
  const { uploadedImage } = settingsSelector;
  // State
  const [reminderNotification, setReminderNotification] = useState('');
  const reminderNotificationDebounce = useDebounce(reminderNotification, 500);

  // I18n
  const { t } = useTranslation();

  useUpdateEffect(() => {
    onChangeSetting({ referral: '' }, 'referral');
  }, [settings?.by]);

  useEffect(() => {
    setReminderNotification(settings?.reminderNotification || '');
  }, [settings?.reminderNotification]);

  useUpdateEffect(() => {
    onChangeSetting({ reminderNotification: reminderNotificationDebounce }, 'reminderNotification');
  }, [reminderNotificationDebounce]);

  // memos
  const isGifImageMemo = useMemo(() => {
    if (uploadedImage.previewUrlBefore && uploadedImage.previewUrlBefore.endsWith('.gif')) {
      return true;
    }
    return false;
  }, [uploadedImage.previewUrlBefore]);

  const memoOptionsReferral = useMemo(() => {
    const options: TOptions[] = [];
    try {
      if (settings?.by === 'button') {
        if (currentBlocks) {
          Object.keys(currentBlocks) &&
            Object.keys(currentBlocks).forEach(key => {
              if (currentBlocks[key].type === settings?.by) {
                const id = `#${namespace}-${getObjSafely(() => currentBlocks[key].settings.component)}--${getObjSafely(
                  () => currentBlocks[key].id,
                )}`;
                options.push({
                  id,
                  value: id,
                  label: getObjSafely(() => currentBlocks[key].settings.buttonSettings.buttonValue),
                });
              }
            });
        }
      } else if (settings?.by === 'page') {
        viewPages.forEach(page => {
          if (page.id !== pageSelected) {
            options.push({
              value: `${getObjSafely(() => page.id)}`,
              label: getObjSafely(() => page.title),
              id: getObjSafely(() => page.id),
            });
          }
        });
      }
      return options;
    } catch (error) {
      return options;
    }
  }, [currentBlocks, pageSelected, settings?.by, viewPages]);

  useEffect(() => {
    if (['button', 'page'].includes(settings?.by || '')) {
      if (!settings?.referral) {
        handleOnchangeReferral(memoOptionsReferral.length ? memoOptionsReferral[0].value : null);
      } else if (!memoOptionsReferral.some(item => item.id === settings?.referral?.id)) {
        handleOnchangeReferral(null);
      }
    }
  }, [memoOptionsReferral]);

  useEffect(() => {
    if (isGifImageMemo && settings?.by !== 'page') {
      onChangeSetting({ by: BY_OPTIONS_PAGES.PAGE.value }, 'by');
    }
  }, [isGifImageMemo]);

  const handleItemHover = (id: string | undefined) => {
    // debugger;
    // Xử lý khi mục được hover
    if (id) {
      const btnElemnt: HTMLElement | null = document.querySelector(id);
      if (btnElemnt) {
        btnElemnt.style.outline = '2px solid #005fb8';
        btnElemnt.style.zIndex = '1';
      }
    }
  };

  const handleItemHoverEnd = (id: string | undefined) => {
    // Xử lý khi hover kết thúc
    if (id) {
      const btnElemnt: HTMLElement | null = document.querySelector(id);
      if (btnElemnt) {
        btnElemnt.style.outline = 'none';
        btnElemnt.style.zIndex = '';
      }
    }
  };

  // Handlers
  const onChangeSetting = (params, keyChange = '') => {
    try {
      onChange &&
        onChange(
          {
            ...settings,
            ...params,
          },
          keyChange,
        );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeSetting',
        args: {},
      });
    }
  };

  const handleOnchangeReferral = value => {
    if (!value) {
      onChangeSetting({ referral: '' }, 'referral');
    }

    const itemSelected = memoOptionsReferral.find(item => item.value === value);
    if (itemSelected) {
      onChangeSetting({ referral: itemSelected }, 'referral');
    }
  };

  return (
    <Space size={20} direction="vertical">
      <SettingWrapper label={label}>
        <RadioGroup
          value={settings?.shakeTrigger}
          options={Object.values(TRIGGER_OPTIONS)}
          onChange={e => onChangeSetting({ shakeTrigger: e.target.value }, 'shakeTrigger')}
        />
      </SettingWrapper>
      {settings?.shakeTrigger === 'system' && (
        <Fragment>
          <SettingWrapper label={`${t(translations.shakeBy.title)}:`}>
            <Select
              className="ants-w-[112px]"
              options={isGifImageMemo ? Object.values(BY_OPTIONS_PAGES) : Object.values(BY_OPTIONS)}
              value={settings?.by}
              onChange={value => onChangeSetting({ by: value }, 'by')}
              placeholder={t(translations.selectAField.title)}
            />
          </SettingWrapper>
          {(settings?.by === 'button' || settings?.by === 'page') && (
            <Select
              label={t(translations.referral.title)}
              value={settings?.referral?.value}
              onChange={value => handleOnchangeReferral(value)}
            >
              {memoOptionsReferral.map(option => {
                return (
                  <Option
                    key={option.id}
                    value={option.value}
                    label={option.label}
                    className={settings?.referral?.value === option.value ? 'ant-select-item-option-selected' : ''}
                    onMouseEnter={() => handleItemHover(option?.id)}
                    onMouseLeave={() => handleItemHoverEnd(option?.id)}
                  >
                    {option.label}
                  </Option>
                );
              })}
            </Select>
          )}
        </Fragment>
      )}

      {/* <WrapperBlockTime>
        <Text>{t(translations.timeToShake.title)}</Text>
        <WrapperUnitTime>
          <InputNumber
            value={Number(settings?.timeToShake)}
            required
            min={0}
            // max={88888888}
            onChange={value => onChangeSetting({ timeToShake: value }, 'timeToShake')}
          />
          <Text key="">{t(translations.seconds.title)}</Text>
        </WrapperUnitTime>
      </WrapperBlockTime> */}

      {settings?.shakeTrigger === 'user_action' && (
        <WrapperBlockTime>
          <Text>{t(translations.timeToDelay.title)}</Text>
          <WrapperUnitTime>
            <InputNumber
              value={Number(settings?.timeToDelay)}
              required
              min={0}
              // max={88888888}
              onChange={value => onChangeSetting({ timeToDelay: value }, 'timeToDelay')}
            />
            <Text key="">{t(translations.seconds.unit)}</Text>
          </WrapperUnitTime>
        </WrapperBlockTime>
      )}

      {settings?.shakeTrigger === 'user_action' && (
        <Space size={5} direction="vertical">
          <Text className="!ants-text-gray-4">{t(translations.reminderNotification.title)}</Text>
          <Input
            placeholder={t(translations.reminderNotification.placeholder)}
            value={settings?.reminderNotification}
            onChange={e => setReminderNotification(e.target.value)}
          />
        </Space>
      )}
    </Space>
  );
};
