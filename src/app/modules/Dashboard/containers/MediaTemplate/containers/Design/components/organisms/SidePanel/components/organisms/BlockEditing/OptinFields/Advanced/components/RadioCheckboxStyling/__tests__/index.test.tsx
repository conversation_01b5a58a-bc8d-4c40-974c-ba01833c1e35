import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { RadioCheckboxStyling } from '../index';

describe('RadioCheckboxStyling', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component correctly', () => {
    const blockSettings = {
      // Provide mock data for blockSettings if needed
    };
    const radioCheckboxStylesSettings = {
      // Provide mock data for radioCheckboxStylesSettings if needed
    };
    const radioCheckboxStyles = {
      // Provide mock data for radioCheckboxStyles if needed
    };

    const { getByLabelText, getByText } = render(
      <RadioCheckboxStyling
        blockSettings={blockSettings}
        radioCheckboxStylesSettings={radioCheckboxStylesSettings}
        radioCheckboxStyles={radioCheckboxStyles}
        onChange={mockOnChange}
      />,
    );

    // Assert that the necessary elements are rendered
    expect(getByLabelText('Field Alignment')).toBeInTheDocument();
    expect(getByText('Option Label Gap')).toBeInTheDocument();
    expect(getByText('Line Height Gap')).toBeInTheDocument();
    expect(getByText('Column Spacing')).toBeInTheDocument();
  });

  it('calls the onChange callback with the updated settings', () => {
    const blockSettings = {
      // Provide mock data for blockSettings if needed
    };
    const radioCheckboxStylesSettings = {
      // Provide mock data for radioCheckboxStylesSettings if needed
    };
    const radioCheckboxStyles = {
      // Provide mock data for radioCheckboxStyles if needed
    };

    const { getByLabelText } = render(
      <RadioCheckboxStyling
        blockSettings={blockSettings}
        radioCheckboxStylesSettings={radioCheckboxStylesSettings}
        radioCheckboxStyles={radioCheckboxStyles}
        onChange={mockOnChange}
      />,
    );

    // Simulate a change in the position select input
    fireEvent.change(getByLabelText('Field Alignment'), { target: { value: 'left' } });

    // Assert that the onChange callback is called with the updated settings
    expect(mockOnChange).toHaveBeenCalledWith(
      {
        position: 'left',
      },
      radioCheckboxStyles,
    );
  });
});
