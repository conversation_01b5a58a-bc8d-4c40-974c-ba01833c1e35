// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Button, Icon, Popover } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../molecules/SettingWrapper';
import { EdgeSetting } from '../../molecules/EdgeSetting';

// Config
import { SPACING_SETTINGS_DEFAULT, SPACING_STYLES_DEFAULT } from './config';

// Types
import { TPositionSettings, TPositionStyles } from './types';

// Utils
import { handleError } from 'app/utils/handleError';
import { getNumberFromString } from 'app/utils/common';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/SpacingSetting/index.tsx';

interface PositionSettingProps {
  settings: TPositionSettings;
  styles: TPositionStyles;
  onChange?: (settings: TPositionSettings, styles: TPositionStyles) => void;
}

interface PositionSettingPopoverProps extends PositionSettingProps {}

export const PositionSetting: React.FC<PositionSettingProps> = memo(props => {
  // Props
  const { settings, styles, onChange } = props;

  // I18n
  const { t } = useTranslation();

  return (
    <SettingWrapper label={t(translations.position.title)} labelClassName="ants-font-bold">
      <PositionSettingPopover settings={settings} styles={styles} onChange={onChange} />
    </SettingWrapper>
  );
});

export const PositionSettingPopover: React.FC<PositionSettingPopoverProps> = props => {
  // Props
  const { settings, styles, onChange = () => {} } = props;

  // I18n
  const { t } = useTranslation();

  // Handlers
  const onChangePositionSetting = ({ values, unit, linked }) => {
    try {
      const positionStyles = {
        top: `${values[0]}${unit}`,
        right: `${values[1]}${unit}`,
        bottom: `${values[2]}${unit}`,
        left: `${values[3]}${unit}`,
      };

      // Callback
      onChange(
        {
          ...settings,
          linkedPositionInput: linked,
          positionSuffix: unit,
        },
        {
          ...styles,
          ...positionStyles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangePositionSetting',
        args: { values, unit, linked },
      });
    }
  };

  const content = (
    <div className="ants-flex ants-flex-col ants-space-y-5 ants-w-72">
      <EdgeSetting
        label={t(translations.position.title)}
        linked={settings.linkedPositionInput}
        unit={settings.positionSuffix}
        minValue={-9999}
        maxValue={9999}
        values={[
          getNumberFromString(styles.top),
          getNumberFromString(styles.right),
          getNumberFromString(styles.bottom),
          getNumberFromString(styles.left),
        ]}
        onChange={onChangePositionSetting}
      />
    </div>
  );

  return (
    <Popover placement="bottomRight" content={content} trigger={['click']}>
      <Button icon={<Icon type="icon-ants-edit-2" />} type="text" />
    </Popover>
  );
};

PositionSetting.defaultProps = {
  settings: SPACING_SETTINGS_DEFAULT,
  styles: SPACING_STYLES_DEFAULT,
};
