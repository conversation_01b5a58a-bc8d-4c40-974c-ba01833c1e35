import { Input, Space, Text } from 'app/components/atoms';
import { translations } from 'locales/translations';
import _ from 'lodash';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Select } from 'app/components/molecules';
import { POSITION_MESSAGE_OPTION } from '../../../constants';
import { WrapperSelectInline } from '../FormOTP/styled';

type TMessages = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

export const Messages: React.FC<TMessages> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const { messages } = settings;
  const handleOnChangeMessage = (firstKey: string, data: Record<string, any>) => {
    const newData = _.cloneDeep(messages);
    if (typeof data === 'object') {
      Object.keys(data).forEach(key => {
        newData[firstKey][key] = data[key];
      });
    }
    onChange({ messages: newData });
  };

  return (
    <Space size={20} direction="vertical">
      {/* Failed verification */}
      <Text>{t(translations.failedVerification.title)}</Text>
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">
          {t(translations.message.title)}
          <span style={{ color: 'red' }}> *</span>
        </Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={messages?.failedVerification.content}
          onAfterChange={value =>
            handleOnChangeMessage('failedVerification', {
              content: value,
            })
          }
        />
      </Space>
      <WrapperSelectInline>
        <Text className="!ants-text-gray-4">{t(translations.position.title)}</Text>
        <Select
          options={Object.values(POSITION_MESSAGE_OPTION)}
          value={messages?.failedVerification.position}
          onChange={value => {
            handleOnChangeMessage('failedVerification', {
              position: value,
            });
          }}
        />
      </WrapperSelectInline>

      {/* Resent OTP */}
      <Text>{t(translations.resentOTP.title)}</Text>
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">
          {t(translations.message.title)}
          <span style={{ color: 'red' }}> *</span>
        </Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={messages?.resentOTP.content}
          onAfterChange={value =>
            handleOnChangeMessage('resentOTP', {
              content: value,
            })
          }
        />
      </Space>
      <WrapperSelectInline>
        <Text className="!ants-text-gray-4">{t(translations.position.title)}</Text>
        <Select
          options={Object.values(POSITION_MESSAGE_OPTION)}
          value={messages?.resentOTP.position}
          onChange={value => {
            handleOnChangeMessage('resentOTP', {
              position: value,
            });
          }}
        />
      </WrapperSelectInline>
    </Space>
  );
};
