// Libraries
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import get from 'lodash/get';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Molecules
import { Select, TreeSelect } from 'app/components/molecules';

// Locales
import { translations } from 'locales/translations';

// Styled
import { SelectEventAttributeWrapper, StyledSelect } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';
import { flattenTree } from 'app/utils/common';
import { buildOptionAttrArchive, checkStatusAttr } from '../../../utils';

// Services
import {
  getListAllEvents,
  getListAttributesEvent,
  getListSourceByEvent,
} from 'app/services/MediaTemplateDesign/BusinessObject';

// Types
import { EventMetadata } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Slices
import {
  selectIsShowErrorAlert,
  selectJourneySettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

interface SelectEventAttributeProps {
  eventMetadata: EventMetadata;
  isShowErrorAlert: boolean;
  journeySettings: Record<string, any>;
  onChange: (eventMetaData: EventMetadata, ignoreUndoAction?: boolean) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/SelectEventAttribute/index.tsx';

export const SelectEventAttribute: React.FC<SelectEventAttributeProps> = memo(props => {
  // I18n
  const { t } = useTranslation();

  // Props
  const { eventMetadata, journeySettings, isShowErrorAlert, onChange } = props;

  // Selectors
  const { triggerEvent } = journeySettings || {};

  // State
  const [listEvent, setListEvent] = useState<any[]>([]);
  const [listSourceByEvent, setListSourceByEvent] = useState<any[]>([]);
  const [treeEventAttrs, setTreeEventAttrs] = useState<any[]>([]);
  const [loading, setLoading] = useState({
    isLoadingEvent: false,
    isLoadingSource: false,
    isLoadingAttrs: false,
  });
  const [eventAttrs, setEventAttrs] = useState<any[]>([]);

  const flatEventAttrs = useMemo(() => {
    return flattenTree(eventAttrs, 'items');
  }, [eventAttrs]);

  const memoizedEventAttributeValue = useMemo(() => {
    const attributeValue = eventMetadata.item_type_id
      ? `${eventMetadata.item_type_id}.${eventMetadata.item_property_name}`
      : eventMetadata.item_property_name || undefined;

    return attributeValue;
  }, [eventMetadata.item_property_name, eventMetadata.item_type_id]);

  const { errorMessage } = checkStatusAttr({
    listAttribute:
      flatEventAttrs.map(({ itemPropertyName, eventPropertyName, status }) => ({
        value: eventPropertyName || itemPropertyName,
        status,
      })) || [],
    field: eventMetadata.item_property_name || undefined,
  });

  // Use Effect
  useEffect(() => {
    getListEvents();
  }, []);

  // Handle set trigger event when didn't chose event and trigger event is exists
  useDeepCompareEffect(() => {
    const { eventActionId, eventCategoryId, insightPropertyIds = [] } = triggerEvent || {};

    let draftEventMetadata = { ...eventMetadata };

    if (!!eventActionId && !!eventCategoryId && listEvent.length && !eventMetadata.event_action_id) {
      const defaultEvent = listEvent.find(
        event => +event.eventActionId === +eventActionId && +event.eventCategoryId === +eventCategoryId,
      );

      if (defaultEvent) {
        draftEventMetadata.event_action_id = defaultEvent.eventActionId;
        draftEventMetadata.event_category_id = defaultEvent.eventCategoryId;
        draftEventMetadata.event_tracking_name = defaultEvent.eventTrackingName;
      }

      if (get(insightPropertyIds, 'length', 0) && !get(eventMetadata, 'insight_property_id.length', 0)) {
        draftEventMetadata.insight_property_id = insightPropertyIds;
      }

      onChange(draftEventMetadata, true);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [triggerEvent, listEvent, eventMetadata]);

  useEffect(() => {
    if (eventMetadata.event_action_id && eventMetadata.event_category_id) {
      getListSources({
        event_action_id: eventMetadata.event_action_id,
        event_category_id: eventMetadata.event_category_id,
      });
    }
  }, [eventMetadata.event_action_id, eventMetadata.event_category_id]);

  useEffect(() => {
    if (typeof eventMetadata.insight_property_id === 'object' && !eventMetadata.insight_property_id?.length) {
      return;
    }

    if (!eventMetadata.insight_property_id) {
      return;
    }

    getListAttributes({
      insight_property_id: Array.isArray(eventMetadata.insight_property_id)
        ? eventMetadata.insight_property_id.join(',')
        : eventMetadata.insight_property_id,
      event_action_id: eventMetadata.event_action_id,
      event_category_id: eventMetadata.event_category_id,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventMetadata.insight_property_id]);

  // Handlers
  const getListEvents = async () => {
    try {
      setLoading(loading => ({
        ...loading,
        isLoadingEvent: true,
      }));

      const { rows } = await getListAllEvents();

      setListEvent(rows);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'getListEvents',
        args: {},
      });
    } finally {
      setLoading(loading => ({
        ...loading,
        isLoadingEvent: false,
      }));
    }
  };

  const getListSources = async ({ event_action_id, event_category_id }) => {
    try {
      setLoading(loading => ({
        ...loading,
        isLoadingSource: true,
      }));

      const { rows } = await getListSourceByEvent({
        eventActionId: event_action_id,
        eventCategoryId: event_category_id,
      });

      setListSourceByEvent(rows);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'getListSources',
        args: { event_action_id, event_category_id },
      });
    } finally {
      setLoading(loading => ({
        ...loading,
        isLoadingSource: false,
      }));
    }
  };

  const getListAttributes = async ({ insight_property_id, event_action_id, event_category_id }) => {
    try {
      setLoading(loading => ({
        ...loading,
        isLoadingAttrs: true,
      }));

      const { rows } = await getListAttributesEvent({
        sourceId: insight_property_id,
        eventActionId: event_action_id,
        eventCategoryId: event_category_id,
      });

      setEventAttrs(rows);

      const treeAttrs = rows.map((attr: any) => ({
        value: !!get(attr, 'items.length', 0) ? `sub-${attr.eventPropertyName}` : attr.eventPropertyName,
        title: attr.eventPropertyDisplay,
        isLeaf: attr.items ? get(attr, 'items.length', 0) === 0 : true,
        selectable: attr.items ? get(attr, 'items.length', 0) === 0 : true,
        ...(attr.items &&
          get(attr, 'items.length', 0) && {
            children: attr.items.map((childAttr: any) => ({
              value: `${childAttr.itemTypeId}.${childAttr.itemPropertyName}`,
              title: childAttr.translateLabel,
              status: childAttr.status,
              isLeaf: true,
            })),
          }),
      }));

      setTreeEventAttrs(treeAttrs);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'getListAttributes',
        args: { insight_property_id, event_action_id, event_category_id },
      });
    } finally {
      setLoading(loading => ({
        ...loading,
        isLoadingAttrs: false,
      }));
    }
  };

  const onChangeEvent = (value: any) => {
    try {
      const eventSelected = listEvent.find(event => event.value === value);

      onChange({
        ...eventMetadata,
        event_action_id: eventSelected.eventActionId,
        event_category_id: eventSelected.eventCategoryId,
        event_tracking_name: eventSelected.eventTrackingName,

        // resets
        insight_property_id: null,
        item_type_id: null,
        item_type_name: null,
        item_property_name: null,
        item_property_label: null,
        event_property_syntax: null,
      });

      setEventAttrs([]);
      setTreeEventAttrs([]);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeEvent',
        args: { value },
      });
    }
  };

  const onChangeSource = (values: any = []) => {
    try {
      const sourcesSelected = listSourceByEvent.filter(source => values.includes(source.value));

      if (values.length === 0) {
        setTreeEventAttrs([]);
        setEventAttrs([]);
      }

      onChange({
        ...eventMetadata,
        insight_property_id: sourcesSelected.map(source => source.insightPropertyId),

        // resets
        item_type_id: null,
        item_type_name: null,
        item_property_name: null,
        item_property_label: null,
        event_property_syntax: null,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeSource',
        args: { values },
      });
    }
  };

  const onChangeEventAttribute = (value: any) => {
    try {
      const [attribute, item] = value.split('.');

      if (!item) {
        const selected = eventAttrs.find(attr => attr.value === attribute);
        if (selected) {
          onChange({
            ...eventMetadata,
            item_type_id: null,
            item_type_name: null,
            item_property_name: selected.eventPropertyName,
            item_property_label: selected.label,
            event_property_syntax: selected.eventPropertySyntax,
          });
        }
      } else {
        const selectedAttribute = eventAttrs.find(attr => +attr.itemTypeId === +attribute);
        const selected = selectedAttribute.items.find((attr: any) => attr.itemPropertyName === item);
        if (selected) {
          onChange({
            ...eventMetadata,
            item_type_id: selected.itemTypeId,
            item_type_name: selected.itemTypeName,
            item_property_name: selected.itemPropertyName,
            item_property_label: selected.translateLabel,
            event_property_syntax: selected.eventPropertySyntax,
          });
        }
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeEventAttribute',
        args: {},
      });
    }
  };

  return (
    <SelectEventAttributeWrapper>
      <Select
        showSearch
        required
        focused={isShowErrorAlert}
        value={eventMetadata.event_tracking_name || undefined}
        loading={loading.isLoadingEvent}
        placeholder={t(translations.selectEvent.title)}
        options={listEvent.map(({ value, label }) => ({ value, label }))}
        onChange={onChangeEvent}
      />
      <StyledSelect
        showSearch
        required
        focused={isShowErrorAlert}
        value={eventMetadata.insight_property_id || undefined}
        mode="multiple"
        allowClear
        maxTagTextLength={18}
        loading={loading.isLoadingSource}
        placeholder={t(translations.inAnySourceOf.title)}
        options={listSourceByEvent.map(({ value, label }) => ({ value, label }))}
        onChange={onChangeSource}
      />
      <TreeSelect
        showSearch
        required
        focused={isShowErrorAlert}
        value={memoizedEventAttributeValue || undefined}
        placeholder={t(translations.selectEventAttribute.title)}
        treeData={treeEventAttrs.map(attr => ({
          ...attr,
          children: buildOptionAttrArchive(attr.children || [], memoizedEventAttributeValue),
        }))}
        loading={loading.isLoadingAttrs}
        onChange={onChangeEventAttribute}
        status={errorMessage ? 'error' : ''}
        errorMsg={errorMessage}
      />
    </SelectEventAttributeWrapper>
  );
});
