// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Atoms
import { Button, Icon, Popover } from 'app/components/atoms';

// Molecules
import { RadioGroup, Select, SliderWithInputNumber } from 'app/components/molecules';
import { SettingWrapper } from '../../molecules';

// Styled
import { TAnimationSetting } from './types';

// Utils
import { getTranslateMessage } from 'utils/messages';
import { ANIMATION_ITERATION_STYLE, ANIMATION_TYPE } from './constants';
import { handleError } from 'app/utils/handleError';

interface AnimationSettingProps {
  label?: string;
  settings: TAnimationSetting;
  onChange: (settings: TAnimationSetting) => void;
}

interface AnimationSettingPopoverProps extends AnimationSettingProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/AnimationSetting/index.tsx';

export const AnimationSetting: React.FC<AnimationSettingProps> = props => {
  // Props
  const { label, settings, onChange } = props;

  return (
    <SettingWrapper label={label || ''} labelClassName="ants-font-bold">
      <AnimationSettingPopover settings={settings} onChange={onChange} />
    </SettingWrapper>
  );
};

export const AnimationSettingPopover: React.FC<AnimationSettingPopoverProps> = props => {
  // Props
  const { settings, onChange } = props;
  const {
    animationType = 'none',
    animationDuration = 1000,
    animationDelay = 0,
    animationIterationStyle = 'infinite',
    animationIterationCount = 1,
  } = settings;

  // I18n
  const { t } = useTranslation();

  const onChangeAnimationSetting = payload => {
    try {
      onChange({
        ...settings,
        ...payload,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeAnimationSetting',
        args: {},
      });
    }
  };

  const content = (
    <div className="ants-flex ants-flex-col ants-space-y-5 ants-w-72">
      <Select
        showSearch
        value={animationType}
        label={t(translations.animationType.title)}
        options={Object.values(ANIMATION_TYPE)}
        onChange={value =>
          onChangeAnimationSetting({
            animationType: value,
          })
        }
        onClick={e => {
          e.stopPropagation();
        }}
      />
      {animationType !== ANIMATION_TYPE.NONE.value && (
        <>
          <SliderWithInputNumber
            min={0}
            max={10000}
            step={50}
            label={`${t(translations.animationDuration.title)} (ms)`}
            value={animationDuration}
            onAfterChange={value =>
              onChangeAnimationSetting({
                animationDuration: value,
              })
            }
          />
          <SliderWithInputNumber
            min={0}
            max={10000}
            step={50}
            label={`${t(translations.animationDelay.title)} (ms)`}
            value={animationDelay}
            onAfterChange={value =>
              onChangeAnimationSetting({
                animationDelay: value,
              })
            }
          />
          <SettingWrapper label={t(translations.animationIterationStyle.title)}>
            <RadioGroup
              options={Object.values(ANIMATION_ITERATION_STYLE)}
              value={animationIterationStyle}
              onChange={e =>
                onChangeAnimationSetting({
                  animationIterationStyle: e.target.value,
                })
              }
            />
          </SettingWrapper>
          {animationIterationStyle === ANIMATION_ITERATION_STYLE.FINITE.value && (
            <SliderWithInputNumber
              min={1}
              max={100}
              step={1}
              label={`${t(translations.animationIterationCount.title)}`}
              value={animationIterationCount}
              onAfterChange={value =>
                onChangeAnimationSetting({
                  animationIterationCount: value,
                })
              }
            />
          )}
        </>
      )}
    </div>
  );

  return (
    <Popover placement="bottomRight" content={content} trigger={['click']}>
      <Button icon={<Icon type="icon-ants-edit-2" />} type="text" />
    </Popover>
  );
};

AnimationSetting.defaultProps = {
  label: getTranslateMessage(translations.animation.title),
  settings: {
    animationType: 'none',
    animationDelay: 0,
    animationDuration: 1000,
    animationIterationStyle: 'infinite',
    animationIterationCount: 1,
  },
};
