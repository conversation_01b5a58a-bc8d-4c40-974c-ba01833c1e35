// Locales
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const dateFormatFE = 'DD/MM/YYYY';
export const dateFormatAPI = 'YYYY-MM-DD HH:mm:ss';
export const defaultDateFormatAPI = 'YYYY-MM-DD 00:00:00';
export const endDateFormat = 'YYYY-MM-DD 23:59:59';

export const OPERATORS_OPTION = {
  GREATER_THAN: {
    label: 'is greater than',
    value: 'greater_than',
  },
  GREATER_THAN_EQUAL: {
    label: 'is greater than or equal',
    value: 'greater_than_equal',
  },
  LESS_THAN: {
    label: 'is less than',
    value: 'less_than',
  },
  LESS_THAN_EQUAL: {
    label: 'is less than or equal',
    value: 'less_than_equal',
  },
  NOT_EQUALS: {
    label: 'is not equal',
    value: 'not_equals',
  },
  EQUALS: {
    label: 'is equal',
    value: 'equals',
  },
  BETWEEN: {
    label: 'is between',
    value: 'between',
  },
  EXISTS: {
    label: 'exists',
    value: 'exists',
  },
  NOT_EXISTS: {
    label: 'does not exist',
    value: 'not_exists',
  },
  CONTAINS: {
    label: 'contains',
    value: 'contains',
  },
  DOESNT_CONTAIN: {
    label: 'does not contain',
    value: 'doesnt_contain',
  },
  START_WITH: {
    label: 'starts with',
    value: 'start_with',
  },
  NOT_START_WITH: {
    label: "doesn't start with",
    value: 'not_start_with',
  },
  END_WITH: {
    label: 'ends with',
    value: 'end_with',
  },
  NOT_END_WITH: {
    label: "doesn't end with",
    value: 'not_end_with',
  },
  BEFORE_DATE: {
    label: 'is before date',
    value: 'before_date',
  },
  AFTER_DATE: {
    label: 'is after date',
    value: 'after_date',
  },
  EQUAL_TIME_AGO: {
    label: 'is equal time ago',
    value: 'equal_time_ago',
  },
  NOT_EQUAL_TIME_AGO: {
    label: "isn't equal time ago",
    value: 'not_equal_time_ago',
  },
  BEFORE_TIME_AGO: {
    label: 'is before time ago',
    value: 'before_time_ago',
  },
  AFTER_TIME_AGO: {
    label: 'is after time ago',
    value: 'after_time_ago',
  },
  BETWEEN_TIME_AGO: {
    label: 'is between time ago',
    value: 'between_time_ago',
  },
  MATCHES: {
    label: 'matches any of',
    value: 'matches',
  },
  NOT_MATCHES: {
    label: "doesn't match any of",
    value: 'not_matches',
  },
  INCLUDES: {
    label: 'includes',
    value: 'includes',
  },
  DOESNT_INCLUDE: {
    label: 'does not include',
    value: 'doesnt_include',
  },
};

export const DATA_TYPES = {
  STRING: 'string',
  NUMBER: 'number',
  DATETIME: 'datetime',
  ARRAY: 'array',
  BOOLEAN: 'boolean',
  OBJECT: 'object',
  TEXT: 'text',
  ARRAY_STRING: 'array_string',
  ARRAY_NUMBER: 'array_number',
  ARRAY_DATETIME: 'array_datetime',
  ARRAY_TEXT: 'array_text',
  SUGGESTION: 'suggestion',
  DATE: 'date',
  OBJECT_ID: 'object_id',
  UNIQUE: 'unique',
};

export const OPERATORS = {
  [DATA_TYPES.STRING]: [
    OPERATORS_OPTION.CONTAINS,
    OPERATORS_OPTION.DOESNT_CONTAIN,
    OPERATORS_OPTION.START_WITH,
    OPERATORS_OPTION.NOT_START_WITH,
    OPERATORS_OPTION.END_WITH,
    OPERATORS_OPTION.NOT_END_WITH,
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.NUMBER]: [
    OPERATORS_OPTION.GREATER_THAN,
    OPERATORS_OPTION.GREATER_THAN_EQUAL,
    OPERATORS_OPTION.LESS_THAN,
    OPERATORS_OPTION.LESS_THAN_EQUAL,
    OPERATORS_OPTION.BETWEEN,
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.DATETIME]: [
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.BEFORE_DATE,
    OPERATORS_OPTION.AFTER_DATE,
    OPERATORS_OPTION.BETWEEN,
    OPERATORS_OPTION.EQUAL_TIME_AGO,
    OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
    OPERATORS_OPTION.BEFORE_TIME_AGO,
    OPERATORS_OPTION.AFTER_TIME_AGO,
    OPERATORS_OPTION.BETWEEN_TIME_AGO,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],

  [DATA_TYPES.ARRAY]: [
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.BOOLEAN]: [
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.OBJECT]: [OPERATORS_OPTION.EXISTS, OPERATORS_OPTION.NOT_EXISTS],
  [DATA_TYPES.TEXT]: [
    OPERATORS_OPTION.CONTAINS,
    OPERATORS_OPTION.DOESNT_CONTAIN,
    OPERATORS_OPTION.START_WITH,
    OPERATORS_OPTION.NOT_START_WITH,
    OPERATORS_OPTION.END_WITH,
    OPERATORS_OPTION.NOT_END_WITH,
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.ARRAY_STRING]: [
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.ARRAY_NUMBER]: [
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.ARRAY_DATETIME]: [
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.ARRAY_TEXT]: [
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.SUGGESTION]: [
    OPERATORS_OPTION.CONTAINS,
    OPERATORS_OPTION.DOESNT_CONTAIN,
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.START_WITH,
    OPERATORS_OPTION.NOT_START_WITH,
    OPERATORS_OPTION.END_WITH,
    OPERATORS_OPTION.NOT_END_WITH,
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.DATE]: [
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.BEFORE_DATE,
    OPERATORS_OPTION.AFTER_DATE,
    OPERATORS_OPTION.EQUAL_TIME_AGO,
    OPERATORS_OPTION.NOT_EQUAL_TIME_AGO,
    OPERATORS_OPTION.BEFORE_TIME_AGO,
    OPERATORS_OPTION.AFTER_TIME_AGO,
    OPERATORS_OPTION.BETWEEN_TIME_AGO,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.OBJECT_ID]: [
    OPERATORS_OPTION.MATCHES,
    OPERATORS_OPTION.NOT_MATCHES,
    OPERATORS_OPTION.EQUALS,
    OPERATORS_OPTION.NOT_EQUALS,
    OPERATORS_OPTION.EXISTS,
    OPERATORS_OPTION.NOT_EXISTS,
  ],
  [DATA_TYPES.UNIQUE]: [OPERATORS_OPTION.EQUALS, OPERATORS_OPTION.NOT_EQUALS],
};

export const SEGMENT_IDS = 'segment_ids';

export const VALUE_TYPE = {
  EVENT: {
    index: 2,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.eventAttr),
    value: 'event',
  },
  CUSTOMER: {
    index: 1,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.customerAttr),
    value: 'customer',
    name: 'customers',
    id: -1003,
  },

  VISITOR: {
    index: 0,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.visitorAttr),
    value: 'visitor',
    name: 'visitors',
    id: -1007,
  },
};

export const DEFAULT_CUSTOMER_META_DATA = {
  item_type_id: VALUE_TYPE.CUSTOMER.id,
  item_type_name: VALUE_TYPE.CUSTOMER.name,
  item_property_name: '',
};

export const DEFAULT_VISITOR_META_DATA = {
  item_type_id: VALUE_TYPE.VISITOR.id,
  item_type_name: VALUE_TYPE.VISITOR.name,
  item_property_name: '',
};

export const DEFAULT_EVENT_META_DATA = {
  insight_property_id: null,
  insight_property_ids: null,
  event_action_id: null,
  event_category_id: null,
  event_tracking_name: null,
  item_type_id: null,
  item_type_name: null,
  item_property_name: null,
  item_property_label: null,
  event_property_syntax: null,
  useBo: true,
};
