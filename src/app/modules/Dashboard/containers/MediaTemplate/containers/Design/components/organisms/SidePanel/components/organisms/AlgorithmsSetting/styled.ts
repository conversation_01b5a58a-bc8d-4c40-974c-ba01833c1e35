// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

// Molecules
import { Select, Tabs } from 'app/components/molecules';

export const AlgorithmsSettingWrapper = styled.div``;

export const StyledTabs = styled(Tabs)`
  margin-left: -8px;
  margin-top: 8px !important;
  width: calc(100% + 16px);

  & .ant-tabs-nav {
    margin-bottom: 16px !important;
  }

  .ant-tabs-nav-list {
    height: 36px;
  }
  .ant-tabs-tab-btn {
    font-size: var(--text-normal-font-size);
  }
`;

export const AlgorithmWrapper = styled.div`
  ${tw`ants-grid ants-items-center ants-px-2 ants-py-1 ants-justify-end`};
  margin-top: 0;
  grid-template-columns: 128px 147px 28px;
  .ant-select-selector {
    border-top: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;
  }
`;

export const StyledSelect = styled(Select)`
  .ant-select-selector {
    ${tw`ants-h-auto`}
  }
`;
