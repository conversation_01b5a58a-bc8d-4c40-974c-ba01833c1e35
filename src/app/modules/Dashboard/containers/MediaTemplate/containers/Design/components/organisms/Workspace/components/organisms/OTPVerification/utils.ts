import { LABEL_POSITION } from './constants';

export const getDirection = (position: string) => {
  let direction = '';
  switch (position) {
    case LABEL_POSITION.LEFT:
      direction = 'row';
      break;
    case LABEL_POSITION.RIGHT:
      direction = 'row-reverse';
      break;

    case LABEL_POSITION.TOP:
      direction = 'column';
      break;
    case LABEL_POSITION.BOTTOM:
      direction = 'column-reverse';
      break;

    default:
      break;
  }

  return direction;
};

export const getStyleHyphens = inputStyles => {
  const {
    borderBottomWidth,
    borderLeftWidth,
    borderRightWidth,
    borderTopWidth,
    borderBottomLeftRadius,
    borderBottomRightRadius,
    borderTopLeftRadius,
    borderTopRightRadius,
    borderStyle,
    borderColor,
    ...restInputStyle
  } = inputStyles;

  const styleHyphens = {
    borderBottomWidth,
    borderLeftWidth,
    borderRightWidth,
    borderTopWidth,
    borderBottomLeftRadius,
    borderBottomRightRadius,
    borderTopLeftRadius,
    borderTopRightRadius,
    borderStyle,
    borderColor,
  };

  return { styleHyphens, restInputStyle };
};
