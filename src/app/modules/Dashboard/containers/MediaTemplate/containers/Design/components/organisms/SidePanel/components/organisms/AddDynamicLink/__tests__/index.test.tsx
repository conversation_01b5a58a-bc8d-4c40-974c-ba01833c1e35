import * as React from 'react';
import { render } from '@testing-library/react';

import { AddDynamicLink } from '..';

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: str => str,
      i18n: {
        changeLanguage: () => new Promise(() => {}),
      },
    };
  },
}));

describe('<AddDynamicLink  />', () => {
  it('should match snapshot', () => {
    const loadingIndicator = render(<AddDynamicLink />);
    expect(loadingIndicator.container.firstChild).toMatchSnapshot();
  });
});
