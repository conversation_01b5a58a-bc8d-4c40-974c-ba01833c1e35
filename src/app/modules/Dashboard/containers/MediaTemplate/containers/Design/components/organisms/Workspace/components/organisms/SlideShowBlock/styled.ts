// Libraries
import React from 'react';
import styled, { css } from 'styled-components';
import { Swiper } from 'swiper/react';
import tw from 'twin.macro';

// Types
import { TSlideDirection } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { JSToCSS } from 'app/utils/dom';

interface SlideBlockProps {}

interface SlideShowBlockWrapperProps {
  navigationStyles: React.CSSProperties;
  navigationSettings: Record<string, any>;
  slideDirection: TSlideDirection;
  isPreviewMode: boolean | undefined;
}

interface SwiperSlideContentProps {
  hoverStyle?: React.CSSProperties;
}

export const SlideShowBlockWrapper = styled.div<SlideShowBlockWrapperProps>`
  ${tw`ants-relative -webkit-text-size-adjust[none]`}

  .custom-button-disabled {
    ${tw`ants-cursor-pointer ants-opacity-50`}
  }

  .custom-button-next {
    ${({ slideDirection }) => {
      if (slideDirection === 'vertical') {
        return css`
          top: auto;
          bottom: 0px;
        `;
      }

      return css`
        left: auto;
        right: 0px;
      `;
    }}
  }

  .custom-button-prev {
    ${({ slideDirection }) => {
      if (slideDirection === 'vertical') {
        return css`
          top: 0px;
          bottom: auto;
        `;
      }

      return css`
        left: 0px;
        right: auto;
      `;
    }}
  }

  .custom-button-prev,
  .custom-button-next {
    ${({ navigationStyles, navigationSettings, slideDirection }) => css`
      z-index: 10000;
      position: absolute;
      ${slideDirection === 'vertical' && `left: 50%;`}
      ${slideDirection !== 'vertical' && `top: 50%;`}
      transform: ${slideDirection === 'vertical' ? 'translateX(-50%)' : 'translateY(-50%)'};
      padding: 10px;
      background-color: ${navigationStyles.backgroundColor};
      color: ${navigationStyles.color};
      aspect-ratio: 1 / 1;
      width: unset;
      border-radius: ${navigationSettings.buttonStyle === 'circle' ? '50%' : '0'};
      transition: all 300ms ease-in-out;

      &:not(.custom-button-disabled):hover {
        background-color: ${navigationSettings.hoverBackgroundColor};
      }
    `}
  }

  // Custom next and previous button position
  ${({ navigationSettings, navigationStyles, slideDirection }) => {
    const { buttonPosition } = navigationSettings;
    const fontSize: any = navigationStyles.fontSize;

    switch (buttonPosition) {
      case 'top-left':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;

            ${slideDirection === 'vertical'
              ? `left: -${parseInt(fontSize) - 5}px;`
              : `top: -${parseInt(fontSize) - 5}px;`}
          }

          .custom-button-next {
            ${slideDirection === 'vertical'
              ? `
                top: ${parseInt(fontSize) + 15}px;
                bottom: auto;
              `
              : `
                right: auto;
                left: ${parseInt(fontSize) + 15}px;
              `}
          }
        `;

      case 'center-left':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;
            left: -${parseInt(fontSize) - 5}px;
          }

          .custom-button-prev {
            bottom: calc(50% + 2.5px);
            top: auto;
          }

          .custom-button-next {
            top: calc(50% + 2.5px);
            bottom: auto;
          }
        `;

      case 'top-center':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;
            top: -${parseInt(fontSize) - 5}px;
          }

          .custom-button-prev {
            right: calc(50% + 2.5px);
            left: auto;
          }

          .custom-button-next {
            left: calc(50% + 2.5px);
            right: auto;
          }
        `;

      case 'top-right':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;

            ${slideDirection === 'vertical'
              ? `right: -${parseInt(fontSize) * 2 + 5}px; left: auto;`
              : `top: -${parseInt(fontSize) - 5}px;`}
          }

          .custom-button-prev {
            ${slideDirection === 'vertical'
              ? `
                top: 0px;
                bottom: auto;
              `
              : `
                left: auto;
                right: ${parseInt(fontSize) + 15}px;
              `}
          }

          .custom-button-next {
            ${slideDirection === 'vertical' &&
            `
              top: ${parseInt(fontSize) + 15}px;
              bottom: auto;
            `}
          }
        `;

      case 'center-right':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;
            left: auto;
            right: -${parseInt(fontSize) * 2 + 5}px;
          }

          .custom-button-prev {
            bottom: calc(50% + 2.5px);
            top: auto;
          }

          .custom-button-next {
            top: calc(50% + 2.5px);
            bottom: auto;
          }
        `;

      case 'bottom-left':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;

            ${slideDirection === 'vertical'
              ? `
                top: auto;
                bottom: ${parseInt(fontSize) + 15}px;
                left: -${parseInt(fontSize) - 5}px;
              `
              : `
                top: auto;
                bottom: -${parseInt(fontSize) * 2 + 5}px;
              `}
          }

          .custom-button-next {
            ${slideDirection === 'vertical'
              ? `
                top: auto;
                bottom: 0;
              `
              : `
                right: auto;
                left: ${parseInt(fontSize) + 15}px;
              `}
          }
        `;

      case 'bottom-center':
        return css`
          .custom-button-prev,
          .custom-button-next {
            top: auto;
            padding: 5px;
            bottom: -${parseInt(fontSize) * 2 + 5}px;
          }

          .custom-button-prev {
            right: calc(50% + 2.5px);
            left: auto;
          }

          .custom-button-next {
            left: calc(50% + 2.5px);
            right: auto;
          }
        `;

      case 'bottom-right':
        return css`
          .custom-button-prev,
          .custom-button-next {
            padding: 5px;

            ${slideDirection === 'vertical'
              ? `
                top: auto;
                left: auto;
                bottom: 0;
                right: -${parseInt(fontSize) * 2 + 5}px;
              `
              : `
                top: auto;
                bottom: -${parseInt(fontSize) * 2 + 5}px;
              `}
          }

          .custom-button-prev {
            ${slideDirection === 'vertical'
              ? `
                top: auto;
                bottom: ${parseInt(fontSize) + 15}px;
              `
              : `
                left: auto;
                right: ${parseInt(fontSize) + 15}px;
              `}
          }
        `;

      default:
        return css`
          .custom-button-prev {
            ${slideDirection === 'vertical'
              ? `
              top: 0px;
              bottom: auto;
              `
              : ``}
          }
        `;
    }
  }}

  .swiper-slide {
    height: fit-content !important;
  }

  ${({ isPreviewMode }) => {
    if (!isPreviewMode) {
      return `
        .swiper-slide:hover {
          z-index: 10;
        }
      `;
    }
  }}
`;

export const SwiperSlideContent = styled.div<SwiperSlideContentProps>`
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0s;

  &:hover {
    ${p => {
      return p.hoverStyle ? JSToCSS(p.hoverStyle, true) : '';
    }}
  }
`;

export const StyledSlide = styled(Swiper)`
  z-index: unset;
`;

export const SlideEmptyWrapper = styled.div`
  ${tw`
    ants-relative ants-flex ants-items-center ants-justify-center ants-w-full ants-h-full ants-space-x-2 ants-bg-blue-second-2
    ants-border ants-border-dashed ants-border-gray 
  `}
`;

export const StyledSlideBlock = styled.div<SlideBlockProps>`
  ${tw`ants-cursor-pointer ants-h-auto ants-p-0`}
`;
