// Libraries
import { JSToCSS } from 'app/utils/dom';
import { Resizable } from 're-resizable';
import styled, { CSSProperties } from 'styled-components';
import tw from 'twin.macro';

// Types
import { TTableSettings } from '../../../../../../types';

type TableHeadCellProps = {
  resizeable?: boolean;
};

type TableRowProps = {
  color?: string;
};

type TCellResizeableProps = {
  hoverstyle?: CSSProperties;
};

export const CellContentWrapper = styled(Resizable)<TCellResizeableProps>`
  word-break: break-all;

  &:hover {
    ${p => {
      return p.hoverstyle ? JSToCSS(p.hoverstyle, true) : '';
    }}
  }
`;

export const TableHeadCell = styled.th<TableHeadCellProps>`
  font-weight: inherit;
`;

export const TableRow = styled.tr<TableRowProps>``;

export const Table = styled.table<TTableSettings['table']['settings']>`
  tbody tr {
    :nth-child(odd) {
      ${p =>
        p.oddRowColor && {
          backgroundColor: p.oddRowColor,
        }}
    }

    :nth-child(even) {
      ${p =>
        p.evenRowColor && {
          backgroundColor: p.evenRowColor,
        }}
    }
  }

  th,
  td {
    ${p =>
      p.cellBorderColor && {
        border: `1px solid ${p.cellBorderColor} !important`,
      }}
  }

  &.design-mode {
    tr {
      th:not(:last-child),
      td:not(:last-child) {
        border-right: 2px dotted #90ccf9 !important;
      }
    }
  }
`;

export const TableBody = styled.tbody<TTableSettings['tableBody']['settings']>`
  td {
    ${p =>
      !p.wrapText && {
        wordBreak: 'keep-all',
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        maxWidth: '0em',
      }}
  }
`;

export const TableBlockWrapper = styled.div`
  ${tw`ants-w-full ants-h-full`}
`;

export const TableExportWrapper = styled.div`
  .cell--nowrap {
    white-space: nowrap !important;
  }
`;
