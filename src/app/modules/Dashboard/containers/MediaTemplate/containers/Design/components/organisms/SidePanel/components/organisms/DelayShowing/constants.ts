// Translations
import { translations } from 'locales/translations';

// Utils
import { getTranslateMessage } from 'utils/messages';

export const DELAY_SHOWING = {
  PERCENTED_SCROLL: {
    value: 'percented_scroll',
    label: getTranslateMessage(translations.percentScroll.title),
  },
  VIEW_PAGE: {
    value: 'viewpage',
    label: getTranslateMessage(translations.viewPage.title),
  },
};
export const UNIT = {
  PERCENT: {
    value: 'percent',
    label: getTranslateMessage(translations.percent.title),
  },
  SECOND: {
    value: 'second',
    label: getTranslateMessage(translations.second.title),
  },
};
