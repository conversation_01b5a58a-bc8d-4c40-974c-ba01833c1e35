// Libraries
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import get from 'lodash/get';
import cloneDeep from 'lodash/cloneDeep';
import uniq from 'lodash/uniq';
import { CSSProperties } from 'styled-components';
import Tree, {
  mutateTree,
  moveItemOnTree,
  type RenderItemParams,
  type TreeData,
  type ItemId,
  type TreeSourcePosition,
  type TreeDestinationPosition,
} from '@atlaskit/tree';
import classNames from 'classnames';
import produce from 'immer';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Icons
import { WarningIcon } from 'app/components/icons';
import { ErrorIcon } from 'app/components/icons';

// Atoms
import { Icon, Text, Tooltip, Input } from 'app/components/atoms';
import { Typography } from '@antscorp/antsomi-ui';

// Molecules
import { Modal } from 'app/components/molecules';
import { SetIndexButton } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SetIndexButton';
import { SetAutoIndexButton } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SetAutoIndexButton';

// Selectors
import {
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectCurrentViewPage,
  selectIsShowErrorAlert,
  selectIsShowWarningAlert,
  selectJourneySettings,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Styled
import { ElementItem, AddComponent, ComponentsWrapper, OptionIcon, TreeComponentWrapper, InputWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

// Translations
import { translations } from 'locales/translations';

// Constants
import {
  SIDE_PANEL_TYPE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { SIDE_PANEL_TABS } from '../../../../SidePanel/constants';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/index';

// Utils
import { getAllChildrenBlockId } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import { reorder } from 'app/utils/common';

const { COLUMN, SLIDE_SHOW, BUTTON, IMAGE, VIDEO, YES_NO, OPTIN_FIELDS, RATING, TEXT } = STANDARDS_BLOCKS;

interface LayersProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/LeftSidePanel/components/organisms/Layers/index.tsx';

type ShowIconOptions = {
  hide: boolean;
  clone: boolean;
  remove: boolean;
  setIndex?: boolean;
  setAutoIndex?: boolean;
};

export const Layers: React.FC<LayersProps> = props => {
  const dispatch = useDispatch();

  // Selectors
  const currentViewPage = useSelector(selectCurrentViewPage);
  const sidePanel = useSelector(selectSidePanel);
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);
  const isShowWarningAlert = useSelector(selectIsShowWarningAlert);
  const { blocks = {}, tree = {} } = currentViewPage || {};
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);

  const { blockSelectedId } = sidePanel;

  // Actions
  const { updateBlockById, setSidePanel, removeBlock, cloneBlock, updateBlockFieldsById, updateCurrentPageTreeBlocks } =
    mediaTemplateDesignActions;

  // I18n
  const { t } = useTranslation();

  // State
  const [treeMenuData, setTreeMenuData] = useState<TreeData>({
    rootId: 'root',
    items: {},
  });
  const [blockEditing, setBlockEditing] = useState('');
  const [newBlockName, setNewBlockName] = useState<any>(null);

  const getIsShowAutoIndexError = ({ blockId }) => {
    let listDynamicIndex: number[] = [];
    let draftTree = cloneDeep(tree);

    // Exclude Slide show block
    draftTree[blockId] = draftTree[blockId]?.filter(blockId => blocks[blockId]?.type !== SLIDE_SHOW.name);

    const childrenBlockIds = getAllChildrenBlockId({ tree: draftTree, blockId });

    childrenBlockIds.forEach(childrenBlockId => {
      if (childrenBlockId !== blockId) {
        const childrenBlock = blocks[childrenBlockId] || {};
        const blockSettings = cloneDeep(childrenBlock.settings) || {};

        if (!!get(blockSettings, 'blockStylesSettings.displayCondition.condition', '')) {
          listDynamicIndex.push(get(blockSettings, 'blockStylesSettings.displayCondition.index', 1));
        }

        // Set auto index for Button, Yes/No, Image, Video, Rating, Text Block
        switch (childrenBlock.type) {
          case BUTTON.name:
          case RATING.name:
          case VIDEO.name:
          case OPTIN_FIELDS.name:
          case IMAGE.name:
            let { dynamic } = blockSettings;

            Object.keys({ ...dynamic }).forEach((key: string) => {
              if (dynamic[key]?.isDynamic && dynamic[key]?.index != null && !!dynamic[key]?.attribute?.value) {
                listDynamicIndex.push(dynamic[key].index);
              }
            });

            break;
          case YES_NO.name:
            let { yesDynamic, noDynamic } = blockSettings;

            Object.keys({ ...yesDynamic }).forEach((key: string) => {
              if (yesDynamic[key]?.isDynamic && yesDynamic[key]?.index != null && !!yesDynamic[key]?.attribute?.value) {
                listDynamicIndex.push(yesDynamic[key].index);
              }
            });

            Object.keys({ ...noDynamic }).forEach((key: string) => {
              if (noDynamic[key]?.isDynamic && noDynamic[key]?.index != null && !!noDynamic[key]?.attribute?.value) {
                listDynamicIndex.push(noDynamic[key].index);
              }
            });
            break;
          case TEXT.name:
            const dynamicData = get(blockSettings, 'dynamic.data', {});
            const linkData = get(blockSettings, 'link.data', {});

            Object.keys({ ...dynamicData }).forEach((key: any) => {
              if (dynamicData[key] && dynamicData[key]?.index != null && !!dynamicData[key]?.attribute?.value) {
                listDynamicIndex.push(dynamicData[key].index);
              }
            });

            Object.keys({ ...linkData }).forEach((key: any) => {
              if (linkData[key] && linkData[key]?.index != null && !!linkData[key]?.attribute?.value) {
                listDynamicIndex.push(linkData[key].index);
              }
            });

            break;
          default:
            break;
        }
      }
    });

    return uniq(listDynamicIndex).length > 1;
  };

  const renderSetAutoIndexOption = ({ blockId }) => {
    // Check Block is Existed
    if (!tree[blockId]) {
      return null;
    }

    const isShowAutoIndexError = getIsShowAutoIndexError({ blockId });
    let warningNameBlock = '';

    switch (blocks[blockId].type) {
      case 'col':
        warningNameBlock = 'column';
        break;
      case 'slide':
        warningNameBlock = 'slide';
        break;
      default:
        break;
    }

    return (
      <SetAutoIndexButton color="#666" hoverColor="var(--primary-color)" blockId={blockId}>
        <Tooltip
          title={t(translations.setIndex[isShowAutoIndexError ? 'warningTooltips' : 'setAutoIndex'], {
            name: warningNameBlock,
          })}
        >
          <span className="ants-absolute ants-inset-0"></span>
        </Tooltip>
        {isShowAutoIndexError && (
          <div className="ants-absolute ants-top-[-7px] ants-right-[-7px]">
            <WarningIcon
              style={{
                width: 14,
                height: 10,
              }}
            />
          </div>
        )}
      </SetAutoIndexButton>
    );
  };

  const renderItemLabel = (
    element: Record<string, any>,
    label?: string,
    showIconOptions: ShowIconOptions = {
      hide: true,
      clone: true,
      remove: true,
      setIndex: false,
      setAutoIndex: false,
    },
    parentId = '',
    errorInfo = {
      isShowError: false,
      isSelfError: false,
    },
    warningInfo = {
      isShowWarning: false,
      isSelfWarning: false,
    },
  ) => {
    const { id, type } = element;
    const hidden = get(element, 'settings.blockStylesSettings.hidden', false);

    if (!label) {
      label = Object.values(STANDARDS_BLOCKS).find(({ name }) => name === type)?.label;
    }

    const handleDoubleClick = () => {
      setBlockEditing(id);
    };

    const handleChangeLabel = (e: React.ChangeEvent<HTMLInputElement>) => {
      setNewBlockName(e.target.value);
    };

    const handleKeyDown = e => {
      if (e.key === 'Enter') {
        handleBlur();
      }
    };

    return (
      <ElementItem
        className="ants-group ants-w-full ants-overflow-hidden"
        key={id}
        onClick={e => {
          if (blockEditing === id) {
            e.stopPropagation();
            e.preventDefault();
          }
          onClickElementItem(element);
        }}
      >
        {blockEditing === id ? (
          <InputWrapper>
            <Input
              autoFocus
              value={newBlockName !== null ? newBlockName : label}
              style={{ backgroundColor: 'transparent', padding: 0, height: 'auto' }}
              onChange={handleChangeLabel}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              maxLength={50}
              // disableUndo
            />
          </InputWrapper>
        ) : (
          <>
            <Typography.Text
              onDoubleClick={handleDoubleClick}
              ellipsis={{ tooltip: label }}
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                flex: 1,
                minWidth: 0,
                color: 'inherit',
              }}
              {...(errorInfo.isShowError && errorInfo.isSelfError && { color: '#ef3340' })}
            >
              {label}
            </Typography.Text>

            <div className="ants-flex ants-items-center ants-gap-2">
              <div
                className="ants-items-center ants-space-x-2 ants-hidden group-hover:ants-flex"
                onClick={e => e.stopPropagation()}
              >
                {showIconOptions.setIndex && (
                  <SetIndexButton color="#666" hoverColor="var(--primary-color)" blockId={id}>
                    <Tooltip title={t(translations.setIndex.title)}>
                      <span className="ants-absolute ants-inset-0"></span>
                    </Tooltip>
                  </SetIndexButton>
                )}

                {showIconOptions.setAutoIndex && renderSetAutoIndexOption({ blockId: id })}

                {showIconOptions.hide && (
                  <Tooltip title={t(!hidden ? translations.hide.title : translations.show.title)}>
                    <span>
                      <OptionIcon
                        type={hidden ? 'icon-ants-visibility-off' : 'icon-ants-visibility'}
                        size={18}
                        onClick={() => onClickHideElement(element)}
                      />
                    </span>
                  </Tooltip>
                )}

                {showIconOptions.clone && (
                  <Tooltip title={t(translations.duplicate.title)}>
                    <span>
                      <OptionIcon
                        type="icon-ants-material-outline-content-copy"
                        size={16}
                        onClick={() => onClickCLoneElement(element, parentId)}
                      />
                    </span>
                  </Tooltip>
                )}

                {showIconOptions.remove && (
                  <Tooltip title={t(translations.delete.title)}>
                    <span>
                      <OptionIcon
                        type="icon-ants-remove-trash"
                        size={16}
                        onClick={() => onClickRemoveElement(element, parentId)}
                      />
                    </span>
                  </Tooltip>
                )}
              </div>

              {errorInfo.isShowError && errorInfo.isSelfError ? (
                <Tooltip title={errors[id]?.[0]}>
                  <ErrorIcon />
                </Tooltip>
              ) : errorInfo.isShowError ? (
                <ErrorIcon />
              ) : warningInfo.isShowWarning && warningInfo.isSelfWarning ? (
                <Tooltip title={warnings[id]?.[0]}>
                  <WarningIcon
                    style={{
                      width: 18,
                      height: 14,
                    }}
                  />
                </Tooltip>
              ) : warningInfo.isShowWarning ? (
                <WarningIcon
                  style={{
                    width: 18,
                    height: 14,
                  }}
                />
              ) : (
                <></>
              )}
            </div>
          </>
        )}
      </ElementItem>
    );
  };

  const getIcon = (type?: string) => {
    return Object.values(STANDARDS_BLOCKS).find(({ name }) => name === type)?.icon;
  };

  const getErrorInfo = ({ blockId = '' }) => {
    const childrenBlockIds = tree[blockId] || [];

    if (!!errors[blockId]?.length && isShowErrorAlert) {
      return {
        isShowError: true,
        isSelfError: true,
      };
    }

    if (childrenBlockIds.length) {
      const blockErrors = childrenBlockIds.map(blockId => getErrorInfo({ blockId })) || [];

      return {
        isShowError: blockErrors.some(({ isShowError }) => !!isShowError),
        isSelfError: false,
      };
    }

    // This function will return 2 params:
    // 1 - isShowError: Block is has error or not
    // 2 - isSelfError: Is this error of current block or of child block
    return {
      isShowError: false,
      isSelfError: false,
    };
  };

  const getWarningInfo = ({ blockId = '' }) => {
    const childrenBlockIds = tree[blockId] || [];
    if (!!warnings[blockId]?.length && isShowWarningAlert) {
      return {
        isShowWarning: true,
        isSelfWarning: true,
      };
    }
    if (childrenBlockIds.length) {
      const blockWarnings = childrenBlockIds.map(blockId => getWarningInfo({ blockId })) || [];
      return {
        isShowWarning: blockWarnings.some(({ isShowWarning }) => !!isShowWarning),
        isSelfWarning: false,
      };
    }
    // This function will return 2 params:
    // 1 - isShowWarning: Block is has warning or not
    // 2 - isSelfWarning: Is this warning of current block or of child block
    return {
      isShowWarning: false,
      isSelfWarning: false,
    };
  };

  const recursiveTreeData = ({ blockId = '', parentId = 'root', index = 0 }) => {
    try {
      const block = blocks[blockId];
      const childrenBlockIds = tree[blockId] || [];
      const treeData = {};

      let label = blocks[blockId]?.settings?.label || '';
      let icon = getIcon(block?.type);
      let style: CSSProperties = {};
      let showIconOptions = {
        hide: true,
        clone: true,
        remove: true,
        setIndex: false,
        setAutoIndex: false,
      };

      const errorInfo = getErrorInfo({ blockId });

      const warningInfo = getWarningInfo({ blockId });

      switch (block?.type) {
        case COLUMN.name:
          label = label || `Row ${index + 1}`;
          icon = getIcon(COLUMN.name);
          style = { fontSize: 14 };
          showIconOptions.setIndex = true;
          break;
        case SLIDE_SHOW.name:
          showIconOptions.setIndex = true;
          break;
        case 'col':
          label = label || `Column ${index + 1}`;
          icon = 'icon-ants-folder';
          showIconOptions.remove = tree[parentId]?.length > 1;
          showIconOptions.setAutoIndex = true;
          break;
        case 'slide':
          label = label || `Slide ${index + 1}`;
          icon = 'icon-ants-folder';
          showIconOptions.remove = tree[parentId]?.length > 1;
          showIconOptions.clone = !!tree[block.id]?.length && tree[parentId]?.some(childId => !tree[childId]?.length);
          showIconOptions.setAutoIndex = true;
          break;
        default:
          // showIconOptions.existedLink = checkBlockExistedLink(block);
          break;
      }

      treeData[blockId] = {
        id: blockId,
        children: tree[blockId] ? tree[blockId] : [],
        hasChildren: !!tree[blockId]?.length,
        isExpanded: get(treeMenuData, `items[${blockId}].isExpanded`, false),
        isChildrenLoading: false,
        data: {
          icon,
          style,
          label,
          errorInfo,
          warningInfo,
          parentId,
          block: blocks[blockId],
          showIconOptions,
        },
      };

      if (childrenBlockIds.length) {
        childrenBlockIds.forEach((childBlockId, idx) => {
          Object.assign(treeData, recursiveTreeData({ blockId: childBlockId, parentId: blockId, index: idx }));
        });
      }

      return treeData;
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'recursiveBlockItems',
        args: { blockId, parentId },
      });

      return {};
    }
  };

  // Effects
  useDeepCompareEffect(() => {
    const draftTree = {
      rootId: 'root',
      items: {},
    };

    if (tree['root']) {
      draftTree.items['root'] = {
        id: 'root',
        children: tree['root'],
        hasChildren: true,
        isExpanded: true,
        isChildrenLoading: false,
        data: {},
      };

      (tree['root'] || []).forEach((blockId, index) => {
        Object.assign(draftTree.items, recursiveTreeData({ blockId, parentId: 'root', index }));
      });
    }

    setTreeMenuData(draftTree);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tree, blocks, errors, isShowErrorAlert, warnings, isShowWarningAlert, blockEditing]);

  // Handlers
  const onClickElementItem = (element: Record<string, any>) => {
    try {
      if (blockEditing && element.id !== blockEditing) {
        handleBlur();
      }

      if (!element.type || ['slide', 'col'].includes(element.type)) {
        return;
      }

      dispatch(
        setSidePanel({
          type: element.type,
          blockSelectedId: element.id,
          activeTab: SIDE_PANEL_TABS.CONTENT.name,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickElementItem',
        args: {},
      });
    }
  };

  const handleBlur = () => {
    const newName =
      newBlockName?.trim() === ''
        ? Object.values(STANDARDS_BLOCKS).find(({ name }) => name === blocks[blockEditing]?.type)?.label
        : newBlockName?.trim() || blocks[blockEditing]?.settings?.label || '';

    dispatch(
      updateBlockById({
        blockId: blockEditing,
        dataUpdate: produce(blocks[blockEditing], draft => {
          draft.settings.label = newName;
        }),
      }),
    );
    setTimeout(() => {
      setBlockEditing('');
      setNewBlockName(null);
    }, 0);
  };

  const onClickRemoveElement = (element: Record<string, any>, parentId: string) => {
    try {
      Modal.confirm({
        title: t(translations.confirmDeletionBlock.title),
        icon: null,
        centered: true,
        content: t(translations.confirmDeletionBlock.description),
        onOk() {
          if (element.type === 'col') {
            dispatch(
              setSidePanel({
                type: COLUMN.name,
                blockSelectedId: parentId,
                activeTab: SIDE_PANEL_TABS.CONTENT.name,
              }),
            );
          } else {
            if (sidePanel.blockSelectedId === element.id) {
              dispatch(
                setSidePanel({
                  blockSelectedId: '',
                  type: SIDE_PANEL_TYPE.BLOCKS.name,
                }),
              );
            }
          }

          setTimeout(() => {
            dispatch(
              removeBlock({
                parentBlockId: parentId,
                blockId: element.id,
                blockType: element.type,
              }),
            );
          });
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickRemoveBlock',
        args: {},
      });
    }
  };

  const onClickCLoneElement = (element: Record<string, any>, parentId: string) => {
    try {
      if (element.type === 'col') {
        dispatch(
          setSidePanel({
            type: COLUMN.name,
            blockSelectedId: parentId,
            activeTab: SIDE_PANEL_TABS.CONTENT.name,
          }),
        );
      }

      setTimeout(() => {
        dispatch(
          cloneBlock({
            blockId: element.id,
            parentBlockId: parentId,
            blockType: element.type,
          }),
        );
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickCLoneBlock',
        args: {},
      });
    }
  };

  const onClickHideElement = (element: Record<string, any>) => {
    try {
      dispatch(
        updateBlockFieldsById({
          blockId: element.id,
          blockType: element.type,
          dataUpdate: [
            {
              fieldPath: 'settings.blockStylesSettings.hidden',
              data: !element.settings.blockStylesSettings.hidden,
            },
          ],
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickHideBlock',
        args: {},
      });
    }
  };

  const onClickAddComponent = () => {
    try {
      dispatch(
        setSidePanel({
          blockSelectedId: '',
          type: SIDE_PANEL_TYPE.BLOCKS.name,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickAddComponent',
        args: {},
      });
    }
  };

  const renderItem = ({ item, provided, snapshot }: RenderItemParams) => {
    const { data, isExpanded, id, hasChildren } = item || {};
    const { block, label, showIconOptions, parentId, errorInfo = {}, warningInfo = {}, icon } = data || {};

    return (
      <TreeComponentWrapper
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        className={classNames({
          '!ants-bg-[#deeffe] !ants-font-bold !ants-text-primary':
            (blockSelectedId.toString() === block.id && blockSelectedId.toString() !== blockEditing) ||
            snapshot.isDragging,
        })}
      >
        <div className="ants-flex ants-items-center ants-w-full ants-gap-2 ants-px-4">
          <Icon
            type={icon}
            size={18}
            style={{
              ...(errorInfo.isShowError && errorInfo.isSelfError && { color: '#ef3340' }),
            }}
          />
          {renderItemLabel(block, label, showIconOptions, parentId, errorInfo, warningInfo)}
          {hasChildren && (
            <div
              style={{
                minWidth: '20px',
                minHeight: '38px',
                alignItems: 'center',
                flexShrink: 0,
                display: 'flex',
                justifyContent: 'center',
                cursor: 'default',
              }}
              onClick={() => setTreeMenuData(mutateTree(treeMenuData, id, { isExpanded: !isExpanded }))}
            >
              <Icon
                type="icon-ants-angle-right"
                className="ants-transition-transform"
                size={10}
                style={{
                  transform: `rotate(${isExpanded ? '-90' : '90'}deg)`,
                }}
              />
            </div>
          )}
        </div>
      </TreeComponentWrapper>
    );
  };

  const onExpandTreeMenu = (itemId: ItemId) => {
    try {
      setTreeMenuData(mutateTree(treeMenuData, itemId, { isExpanded: true }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onExpand',
        args: {},
      });
    }
  };

  const onCollapseTreeMenu = (itemId: ItemId) => {
    try {
      setTreeMenuData(mutateTree(treeMenuData, itemId, { isExpanded: false }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const onDragEndTreeMenu = (source: TreeSourcePosition, destination?: TreeDestinationPosition) => {
    try {
      if (!destination) {
        return;
      }

      let isShouldUpdateTree = false;

      const draftTree = cloneDeep(tree);

      const { parentId: sourceParentId, index: sourceIdx } = source;
      const { parentId: desParentId, index: desIdx } = destination;

      const parentSourceBlock = blocks[sourceParentId];
      const parentDesBlock = blocks[desParentId];

      // If sort in same parentId
      if (sourceParentId === desParentId) {
        draftTree[sourceParentId] = reorder(draftTree[sourceParentId], sourceIdx, desIdx || 0);

        isShouldUpdateTree = true;
      } else {
        // Disable drop some case
        if (
          [COLUMN.name, SLIDE_SHOW.name].includes(parentSourceBlock?.type) ||
          [COLUMN.name, SLIDE_SHOW.name].includes(parentDesBlock?.type) ||
          [desParentId, sourceParentId].includes('root')
        ) {
          return;
        }

        const sourceBlockId = draftTree[sourceParentId][sourceIdx];

        // Is drag into column,slide,group
        if (desIdx != null) {
          draftTree[desParentId].splice(desIdx, 0, sourceBlockId);
          draftTree[sourceParentId].splice(sourceIdx, 1);

          isShouldUpdateTree = true;
        } else {
          // Check drag over group, col, slide block
          if (['group', 'col', 'slide'].includes(parentDesBlock.type)) {
            draftTree[desParentId].splice(0, 0, sourceBlockId);
            draftTree[sourceParentId].splice(sourceIdx, 1);

            isShouldUpdateTree = true;
          }
        }
      }

      if (isShouldUpdateTree) {
        setTreeMenuData(treeMenuData => moveItemOnTree(treeMenuData, source, destination));

        dispatch(
          updateCurrentPageTreeBlocks({
            tree: [
              {
                fieldPath: '',
                data: draftTree,
              },
            ],
          }),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onDragEndTreeMenu',
        args: {},
      });
    }
  };

  return (
    <ComponentsWrapper>
      {Object.keys({ ...blocks }).length ? (
        <Tree
          tree={treeMenuData}
          renderItem={renderItem}
          offsetPerLevel={14}
          isDragEnabled
          isNestingEnabled
          onExpand={onExpandTreeMenu}
          onCollapse={onCollapseTreeMenu}
          onDragEnd={onDragEndTreeMenu}
        />
      ) : (
        <AddComponent onClick={onClickAddComponent}>
          <Icon type="icon-ants-plus-circle" />
          <Text className="!ants-font-normal ants-mt-1">{t(translations.addComponent.title)}</Text>
        </AddComponent>
      )}
    </ComponentsWrapper>
  );
};
