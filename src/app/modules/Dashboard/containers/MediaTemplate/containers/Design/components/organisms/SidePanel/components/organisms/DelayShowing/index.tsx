// Libraries
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { handleError } from 'app/utils/handleError';
import classNames from 'classnames';
import produce from 'immer';
import isEqual from 'react-fast-compare';
import xor from 'lodash/xor';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Button, Divider, Icon, Switch, Text, Checkbox, Popover } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../molecules';
import { InputNumber } from 'app/components/molecules';

// Constants
import { DELAY_SHOWING, UNIT } from './constants';

// Styled
import { WrapperFlex } from './styled';

interface DelayShowingProps {
  delayShowing: Record<string, any>;
  onChange: (payload: any, ignoreUndoAction?: boolean) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/DelayShowing/index.tsx';

export const DelayShowing: React.FC<DelayShowingProps> = props => {
  const { t } = useTranslation();

  // Props
  const { delayShowing, onChange = () => {} } = props;
  const { enable, conditions = [] } = delayShowing || {};

  // State
  const [selectedCondition, setSelectedCondition] = useState<string[]>([]);
  const [isVisibleAddCondition, setVisibleAddCondition] = useState(false);

  // Effects
  useDeepCompareEffect(() => {
    const draftConditions = conditions.map(condition => condition.type);

    if (!isEqual(draftConditions, selectedCondition)) {
      setSelectedCondition(draftConditions);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conditions]);

  // Handlers
  const onChangeDelayShowing = (payload: Record<string, any>, ignoreUndoAction?: boolean) => {
    onChange(
      {
        ...delayShowing,
        ...payload,
      },
      ignoreUndoAction,
    );
  };

  const onChangeCondition = (condition: any, value: any, index) => {
    try {
      const newListCondition = produce(conditions, (draft: any) => {
        draft[index] = {
          ...draft[index],
          ...value,
        };
      });

      onChangeDelayShowing({
        conditions: newListCondition,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeCondition',
        args: {},
      });
    }
  };

  const onApplySelect = () => {
    setVisibleAddCondition(false);

    const draftConditions = selectedCondition.map(type => ({
      type,
      value: delayShowing.conditions.find(condition => condition.type === type)?.value || 100,
    }));

    onChangeDelayShowing({
      conditions: draftConditions,
    });
  };

  const onRemoveCondition = (index: number) => {
    try {
      let draftConditions = [...delayShowing.conditions];
      draftConditions.splice(index, 1);

      setSelectedCondition(draftConditions.map(({ type }) => type));

      onChangeDelayShowing({
        conditions: draftConditions,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onRemoveCondition',
        args: {},
      });
    }
  };

  const onChangeCheckedCondition = useCallback(
    val => {
      setSelectedCondition(xor(selectedCondition, [val]));
    },
    [selectedCondition],
  );

  const onVisibleChange = (visible: boolean) => {
    try {
      const selected = conditions.map(condition => condition.type) || [];
      setSelectedCondition(selected);

      setVisibleAddCondition(visible);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onVisibleChange',
        args: {},
      });
    }
  };

  return (
    <>
      <SettingWrapper label={`${t(translations.delayShowing.title)}`}>
        <Switch
          checked={enable}
          onChange={checked =>
            onChangeDelayShowing({
              enable: checked,
            })
          }
        />
      </SettingWrapper>

      {enable === true && (
        <>
          {conditions.map((condition, index) => {
            let min = 0,
              max: any = undefined,
              unit: any = '';

            switch (condition.type) {
              case DELAY_SHOWING.PERCENTED_SCROLL.value:
                min = 1;
                max = 100;
                unit = UNIT.PERCENT.label;
                break;

              case DELAY_SHOWING.VIEW_PAGE.value:
                min = 0;
                max = 180;
                unit = UNIT.SECOND.label;

                break;
              default:
                break;
            }

            return (
              <WrapperFlex className="!ants-p-0 !ants-py-3">
                <Text type="secondary" className="ants-text-right ants-mr-2">
                  {Object.values(DELAY_SHOWING).find(({ value }) => value === condition.type)?.label}
                </Text>

                <InputNumber
                  value={condition.value}
                  width={90}
                  onChange={value => onChangeCondition(condition, { value: value }, index)}
                  min={min}
                  max={max}
                  required
                />
                <div className={classNames('ants-items-center ')}>
                  <Text color="var(--primary-color)" className="ants-text-left ants-ml-2 ants-font-bold ">
                    {unit}
                  </Text>
                </div>
                {conditions?.length > 1 && (
                  <Button
                    onClick={() => onRemoveCondition(index)}
                    type="text"
                    icon={<Icon type="icon-ants-remove-slim" size={15} />}
                    className="ants-justify-self-center"
                  />
                )}
              </WrapperFlex>
            );
          })}

          {conditions.length < Object.keys(DELAY_SHOWING).length && (
            <Popover
              visible={conditions.length >= 2 ? false : isVisibleAddCondition}
              placement="bottomLeft"
              overlayClassName="no-inner-padding"
              overlayStyle={{
                width: '300px',
              }}
              onVisibleChange={onVisibleChange}
              trigger="click"
              content={
                <div>
                  {Object.values(DELAY_SHOWING).map(({ value, label }, index) => {
                    return (
                      <React.Fragment key={value}>
                        <Checkbox
                          className=" !ants-ml-5 !ants-mt-3"
                          checked={selectedCondition.includes(value)}
                          onChange={() => onChangeCheckedCondition(value)}
                        >
                          {label}
                        </Checkbox>
                        <br />
                      </React.Fragment>
                    );
                  })}
                  <Divider className="!ants-my-2" />

                  <div className="!ants-flex !ants-pb-3">
                    <Button
                      type="primary"
                      className="!ants-ml-2"
                      disabled={selectedCondition.length < 1}
                      onClick={onApplySelect}
                    >
                      {t(translations.apply.title)}
                    </Button>

                    <Button onClick={() => onVisibleChange(false)} className="!ants-ml-2">
                      {t(translations.cancel.title)}
                    </Button>
                  </div>
                </div>
              }
            >
              <Button className="!ants-mt-2" type="text">
                + {t(translations.conditions.title)}
              </Button>
            </Popover>
          )}
        </>
      )}
    </>
  );
};
