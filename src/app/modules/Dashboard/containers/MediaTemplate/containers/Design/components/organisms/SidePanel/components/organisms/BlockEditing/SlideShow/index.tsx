// Libraries
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Molecules
import { TabPane, Tabs } from 'app/components/molecules';

// Components
import Content from './Content';
import Advanced from './Advanced';

// Constants
import { SIDE_PANEL_TABS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/constants';

// Slice
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { selectSidePanel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Utils
import { handleError } from 'app/utils/handleError';
import MobileWarning from '../../../molecules/MobileWarning';

interface SlideShowProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/SlideShow/index.tsx';

const tabs = [
  {
    name: SIDE_PANEL_TABS.CONTENT.name,
    label: SIDE_PANEL_TABS.CONTENT.label,
  },
  {
    name: SIDE_PANEL_TABS.ADVANCED.name,
    label: SIDE_PANEL_TABS.ADVANCED.label,
  },
];

export const SlideShow: React.FC<SlideShowProps> = props => {
  // Dispatch
  const dispatch = useDispatch();

  // Actions
  const { setSidePanel } = mediaTemplateDesignActions;

  // Selector
  const { activeTab } = useSelector(selectSidePanel);

  const onChangeTabs = (activeKey: string) => {
    try {
      dispatch(
        setSidePanel({
          activeTab: activeKey,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTabs',
        args: {},
      });
    }
  };

  const renderTabContent = () => {
    try {
      switch (activeTab) {
        case SIDE_PANEL_TABS.CONTENT.name:
          return <Content />;

        case SIDE_PANEL_TABS.ADVANCED.name:
          return <Advanced />;
        default:
          return '';
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderTabContent',
        args: {},
      });
    }
  };

  return (
    <Tabs destroyInactiveTabPane activeKey={activeTab} onChange={onChangeTabs}>
      {tabs.map(({ name, label }) => (
        <TabPane tab={label} key={name}>
          <MobileWarning />
          {renderTabContent()}
        </TabPane>
      ))}
    </Tabs>
  );
};
