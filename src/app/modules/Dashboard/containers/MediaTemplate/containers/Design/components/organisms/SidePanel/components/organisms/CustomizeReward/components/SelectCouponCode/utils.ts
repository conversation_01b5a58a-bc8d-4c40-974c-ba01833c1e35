export const isPoolValue = (value: string = ''): boolean => {
  return new RegExp(/^pool@@[\w_]+$/).test(value);
};

export const serializePoolValue = (value: string) => {
  let poolValue = '';

  if (isPoolValue(value)) {
    poolValue = value.split('@@')[1] ?? '';
  }

  return poolValue;
};

export const serializeAttrValue = (value: string) => {
  let poolValue = '';
  let attrValue = '';

  if (isAttrValue(value)) {
    const [poolTerm = '', attrTerm = ''] = value.split('::');

    poolValue = poolTerm.split('@@')[1] ?? '';
    attrValue = attrTerm.split('@@')[1] ?? '';
  }

  return { poolValue, attrValue };
};

export const isAttrValue = (value: string = ''): boolean => {
  return new RegExp(/^pool@@[\w_]+::attr@@[\w_]+$/).test(value);
};
