import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TextEditorRef, TextEditorJSONContent } from '@antscorp/antsomi-ui';
import {
  selectBlockById,
  selectBlockSelectedId,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import {
  BlockProps,
  TDynamicTextSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { compareDomStr } from 'app/utils';
import isEqual from 'react-fast-compare';
import { getSmartTagIdsFromJSON, getTextBlockHeightWithNumOfLines, genTextEditorId } from '../utils';
import { TUpdateBlockTextPayload } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';
import { handleDynamicIdsUpdate } from './useDynamicText';
import { handleLinkDataUpdate } from './useLinkText';

interface UseTextEditorManagementProps {
  id: string;
  type: string;
  settings: BlockProps['settings'];
  textBlockSettings: TDynamicTextSettings;
  contentForPreview?: boolean;
}

export const useTextEditorManagement = ({
  id,
  type,
  settings,
  textBlockSettings,
  contentForPreview,
}: UseTextEditorManagementProps) => {
  const dispatch = useDispatch();
  const { updateBlockText } = mediaTemplateDesignActions;

  const editorId = genTextEditorId(contentForPreview ? `${id}_preview` : id);
  const editorRef = useRef<TextEditorRef>(null);
  const [processing, setProcessing] = useState<boolean>(false);
  const [previewStyle, setPreviewStyle] = useState<React.CSSProperties>();

  const textBlock = useSelector(selectBlockById(id, type));
  const blockSelectedId = useSelector(selectBlockSelectedId);

  const { data: dataDynamic = {} } = textBlockSettings.dynamic || {};
  const { data: dataLink = {} } = textBlockSettings.link || {};
  const dataDynamicIds = Object.keys(dataDynamic);
  const dataLinkIds = Object.keys(dataLink);

  // Blur editor if not selected
  useEffect(() => {
    if (!editorRef.current || !blockSelectedId) return;
    if (blockSelectedId !== id) {
      editorRef.current?.blur();
    }
  }, [id, blockSelectedId]);

  // Handle preview style for line clamping and ellipsis
  useEffect(() => {
    const { selectLineDisplay, isEllipsisText } = settings.textStyling || {};
    if (selectLineDisplay) {
      const textHeight = getTextBlockHeightWithNumOfLines({
        numOfLine: selectLineDisplay,
        editorId,
      });

      if (isEllipsisText) {
        setPreviewStyle({
          WebkitLineClamp: selectLineDisplay,
          WebkitBoxOrient: 'vertical',
          display: '-webkit-box',
          overflow: 'hidden',
        });
      } else if (textHeight) {
        setPreviewStyle({
          maxHeight: textHeight,
          overflow: 'hidden',
        });
      }
      return;
    }
    setPreviewStyle(undefined);
  }, [editorId, settings.textStyling]);

  const onChangeText = useCallback(
    ({ html, json }: { html: string; json: TextEditorJSONContent }, source = 'api') => {
      if (processing || !textBlock) return;
      if (compareDomStr(html, textBlock?.settings.rawHTML)) return;

      const updatePayload: TUpdateBlockTextPayload['dataUpdate'] = [
        {
          fieldPath: 'settings.rawHTML',
          data: html,
        },
      ];

      const newDynamicIds = getSmartTagIdsFromJSON(json);

      // Use imported function
      const dynamicUpdate = handleDynamicIdsUpdate(dataDynamicIds, newDynamicIds, dataDynamic);

      if (dynamicUpdate) {
        updatePayload.push(dynamicUpdate);
      }

      // Use imported function
      const newLinkData = handleLinkDataUpdate(dataLink, json, dataLinkIds);

      if (!isEqual(newLinkData, dataLink)) {
        updatePayload.push({
          fieldPath: 'settings.link.data',
          data: newLinkData,
        });
      }

      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: updatePayload,
          ignoreUndoAction: source !== 'user',
        }),
      );
    },
    [processing, textBlock, dataDynamicIds, dataDynamic, dataLink, dataLinkIds, dispatch, id, updateBlockText],
  );

  return {
    editorId,
    editorRef,
    processing,
    previewStyle,
    onChangeText,
    setProcessing,
  };
};
