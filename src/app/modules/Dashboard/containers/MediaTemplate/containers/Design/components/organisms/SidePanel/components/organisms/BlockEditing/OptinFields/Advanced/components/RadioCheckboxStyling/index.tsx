// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';
import get from 'lodash/get';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Space } from 'app/components/atoms';

// Types
import { TRowSettings, TSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { handleError } from 'app/utils/handleError';

// Molecules
import { Select, SliderWithInputNumber } from 'app/components/molecules';

// Constants
import { INPUT_SIZE, POSITION_OPTIONS } from './constants';

// Organisms
import { SettingWrapper } from '../../../../../../molecules';
import { FontSettingPopover } from '../../../../../FontSetting';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/LabelStying/index.tsx';

type TStylesSettings = {
  gapSuffix?: string;
  position?: 'left' | 'top' | 'right' | 'bottom' | undefined;
  optionLabelGap?: string;
  lineHeightGap?: string;
  columnSpacing?: string;
};

interface RadioCheckboxStylingProps {
  blockSettings: TSettings<TRowSettings>;
  radioCheckboxStylesSettings: TStylesSettings;
  radioCheckboxStyles: React.CSSProperties;
  onChange: (styleSettings: TStylesSettings, styles: React.CSSProperties, displayInline?: boolean) => void;
}

export const RadioCheckboxStyling: React.FC<RadioCheckboxStylingProps> = props => {
  const { radioCheckboxStyles, radioCheckboxStylesSettings, blockSettings, onChange } = props;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { t, i18n } = useTranslation();

  // Handlers
  const onUpdateSettings = (newLabelStylesSettings = {}, newLabelStyles = {}) => {
    try {
      // Callback onChange
      onChange(
        {
          ...radioCheckboxStylesSettings,
          ...newLabelStylesSettings,
        },
        {
          ...radioCheckboxStyles,
          ...newLabelStyles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onUpdateSettings.name,
        args: {},
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <SettingWrapper label={t(translations.optionPosition.title)}>
        <Select
          className="ants-w-[112px]"
          options={Object.values(POSITION_OPTIONS)}
          value={radioCheckboxStylesSettings?.position}
          onChange={value =>
            onUpdateSettings({
              position: value,
            })
          }
        />
      </SettingWrapper>
      <FontSettingPopover
        styles={blockSettings.radioCheckboxStyles}
        settingsStyle={blockSettings.radioCheckboxStylesSettings}
        onChange={(settings, styles) => {
          onUpdateSettings(
            {
              ...settings,
              ...(styles.fontSize !== get(blockSettings, 'styles.fontSize', '16px') && {
                inputSize: INPUT_SIZE.custom.value,
              }),
            },
            styles,
          );
        }}
      />
      <SliderWithInputNumber
        label={t(translations.optionLabelGap.title)}
        labelClassName="!ants-text-gray-4"
        min={0}
        max={100}
        value={Number(radioCheckboxStylesSettings.optionLabelGap)}
        onAfterChange={value => {
          onUpdateSettings({
            optionLabelGap: value,
          });
        }}
      />
      <SliderWithInputNumber
        label={t(translations.lineHeightGap.title)}
        labelClassName="!ants-text-gray-4"
        min={0}
        max={100}
        value={Number(radioCheckboxStylesSettings.lineHeightGap)}
        onAfterChange={value => {
          onUpdateSettings({
            lineHeightGap: value,
          });
        }}
      />
      <SliderWithInputNumber
        label={t(translations.columnSpacing.title)}
        labelClassName="!ants-text-gray-4"
        min={0}
        max={100}
        value={Number(radioCheckboxStylesSettings.columnSpacing)}
        onAfterChange={value => {
          onUpdateSettings({
            columnSpacing: value,
          });
        }}
      />
    </Space>
  );
};
