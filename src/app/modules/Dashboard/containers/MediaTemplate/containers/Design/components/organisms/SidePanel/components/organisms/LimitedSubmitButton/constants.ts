// Locales
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const LIMIT_ACTION = {
  UNLIMITED_FREQUENCY: {
    value: 'unlimited',
    label: getTranslateMessage(translations.titleLimitedSubmit.type.unlimited.title),
  },
  LIMITED_FREQUENCY: {
    value: 'limited',
    label: getTranslateMessage(translations.titleLimitedSubmit.type.limited.title),
  },

  CAPPING_LEVEL: {
    VARIANT: {
      value: 'variant',
      label: getTranslateMessage(translations.cappingLevel.level.variant.title),
    },
    CAMPAIGN: {
      value: 'campaign',
      label: getTranslateMessage(translations.cappingLevel.level.campaign.title),
    },
    JOURNEY: {
      value: 'journey',
      label: getTranslateMessage(translations.cappingLevel.level.journey.title),
    },
  },

  LIMIT_MESS_POSITION: {
    value: 'position',
    label: getTranslateMessage(translations.limMessPosition.title),
  },
  POSITION: {
    BOTTOM: {
      value: 'bottom',
      label: getTranslateMessage(translations.bottom.title),
    },
    TOP: {
      value: 'top',
      label: getTranslateMessage(translations.top.title),
    },
  },
  TIME_UNIT: {
    HOUR: {
      value: 'hour',
      label: getTranslateMessage(translations.hour.title),
    },
    DAY: {
      value: 'day',
      label: getTranslateMessage(translations.day.title),
    },
    WEEK: {
      value: 'week',
      label: getTranslateMessage(translations.week.title),
    },
    MONTH: {
      value: 'month',
      label: getTranslateMessage(translations.month.title),
    },
    LIFETIME: {
      value: 'lifetime',
      label: getTranslateMessage(translations.lifeTime.title),
    },
  },
};

export const DEFAULT_CONDITION = {
  value: 1,
  timeUnit: '',
  field: '',
  errorMessage: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
};

export const checkDisableOption = ({ currentOption, options, primaryKey, compareKey, value }) => {
  let isDisable = false;

  if (options && options.length) {
    const matchedOptions = options.filter(option => option[primaryKey] === value) || [];
    if (primaryKey === 'timeUnit') {
    }
    return matchedOptions.some(matchedOption => matchedOption[compareKey] === currentOption[compareKey]);
  }

  return isDisable;
};
