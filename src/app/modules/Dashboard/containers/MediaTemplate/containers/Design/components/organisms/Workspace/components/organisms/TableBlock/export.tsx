// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import isNumber from 'lodash/isNumber';

// Types
import { BlockProps, TTableSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Queries
import { useGetDataTableBO } from 'app/queries/BusinessObject';

// Utils
import {
  parseAlignItemsToVerticalAlign,
  sortListDataTableBOByPropertyName,
  getTableMissingDataLabelDisplay,
} from '../../../utils';

// Styled
import { TableExportWrapper } from './styled';

interface TableBlockProps extends BlockProps {}

export const TableBlock = (props: TableBlockProps) => {
  const { namespace, id } = props;

  const settings = props.settings as TTableSettings;
  const { table, tableHeader, tableBody, columns } = settings;

  const { data: dataTableBO } = useGetDataTableBO({
    itemTypeId: settings.boId,
  });
  const [columnsWidth, setColumnsWidth] = useState<number[]>([]);

  const sortedData = useMemo(
    () => sortListDataTableBOByPropertyName(dataTableBO?.data ?? [], settings.sortBy.columnId, settings.sortBy.order),
    [settings.sortBy, dataTableBO],
  );

  useEffect(() => {
    const totalWidth = columns.reduce((acc, { width }) => {
      return acc + parseFloat(width);
    }, 0);

    setColumnsWidth(columns.map(column => (parseFloat(column.width) / totalWidth) * 100));
  }, [columns]);

  const tableHeaderContent = () => {
    if (!!columnsWidth.length) {
      return (
        <thead style={tableHeader.styles}>
          <tr
            style={{
              backgroundColor: table.settings.headerBackground,
            }}
          >
            {columns.map((colHead, colIndex) => (
              <th
                key={colHead.id}
                className={!tableHeader.settings.wrapText ? 'cell--nowrap' : ''}
                style={{
                  padding: table.settings.cellPadding,
                  border: `1px solid ${table.settings.cellBorderColor}`,
                  boxSizing: 'border-box',
                  wordBreak: 'break-word',
                  fontWeight: 'inherit',
                  textAlign: colHead.placement,
                  verticalAlign: parseAlignItemsToVerticalAlign(table.settings.cellAlignItems),
                  width: columnsWidth[colIndex] + '%',
                  ...(!tableHeader.settings.wrapText && {
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                  }),
                }}
              >
                {colHead.label}
              </th>
            ))}
          </tr>
        </thead>
      );
    }

    return <></>;
  };

  // Render tabel body for preview
  const tableBodyPreview = useMemo(() => {
    const rowBody: React.ReactNode[] = [];
    const cellMissingDisplay = getTableMissingDataLabelDisplay(tableBody.settings.showMissingDataType);

    if (!sortedData.length || !columns.length) return <></>;

    for (let rowIndex = 0; rowIndex < table.settings.showTop && rowIndex < sortedData.length; rowIndex++) {
      rowBody.push(
        <tr
          key={`row-${rowIndex + 1}`}
          style={{
            background: (rowIndex + 1) % 2 === 0 ? table.settings.evenRowColor : table.settings.oddRowColor,
          }}
        >
          {columns.map(col => {
            let cellText = sortedData[rowIndex][col.value];

            if (col.colType === 'index') cellText = rowIndex + 1;

            if (!isNumber(cellText) && !cellText) {
              cellText = cellMissingDisplay;
            }

            return (
              <td
                key={col.id}
                className={!tableBody.settings.wrapText ? 'cell--nowrap' : ''}
                style={{
                  padding: table.settings.cellPadding,
                  border: `1px solid ${table.settings.cellBorderColor}`,
                  boxSizing: 'border-box',
                  verticalAlign: parseAlignItemsToVerticalAlign(table.settings.cellAlignItems),
                  wordBreak: 'break-all',
                  textAlign: col.placement,
                  ...(!tableBody.settings.wrapText && {
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                  }),
                }}
                // ref={node => {
                //   if (node && tableBody.settings.wrapText) {
                //     node.style.setProperty('white-space', 'nowrap', 'important');
                //   }
                // }}
              >
                {cellText}
              </td>
            );
          })}
        </tr>,
      );
    }

    return <tbody style={tableBody.styles}>{rowBody}</tbody>;
  }, [columns, table.settings, tableBody, sortedData]);

  // Render Table body for delivery
  const tableBodyDelivery = useMemo(() => {
    const showColIndex = tableBody.settings.showRowNumbers;
    const cellMissingDisplay = getTableMissingDataLabelDisplay(tableBody.settings.showMissingDataType);

    // Data table merge tag use for capture place render delivery
    return (
      <tbody style={tableBody.styles}>
        <tr data-table-merge-tag="1">
          <td>{`#BEGIN_FOR_TABLE_${id}#`}</td>
        </tr>
        <tr data-ats-row-style>
          {columns.map(col => (
            <td
              key={col.id}
              className={!tableBody.settings.wrapText ? 'cell--nowrap' : ''}
              style={{
                padding: table.settings.cellPadding,
                border: `1px solid ${table.settings.cellBorderColor}`,
                boxSizing: 'border-box',
                wordBreak: 'break-all',
                verticalAlign: parseAlignItemsToVerticalAlign(table.settings.cellAlignItems),
                textAlign: col.placement,
                ...(!tableBody.settings.wrapText && {
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                }),
              }}
            >
              {showColIndex && col.colType === 'index'
                ? '#{item.ats_row_index}'
                : `#{item.${col.value} || "${cellMissingDisplay}"}`}
            </td>
          ))}
        </tr>
        <tr data-table-merge-tag="1">
          <td>{`#END_FOR_TABLE_${id}#`}</td>
        </tr>
      </tbody>
    );
  }, [
    columns,
    tableBody,
    id,
    table.settings.cellAlignItems,
    table.settings.cellBorderColor,
    table.settings.cellPadding,
  ]);

  const renderTableLayoutWithDynamicBody = (tableBodyType: 'preview' | 'delivery') => {
    return (
      <div
        className={`${namespace}-te-content ${namespace}-${settings.component}--content table-block__wrapper`}
        {...(tableBodyType === 'preview' ? { 'data-dynamic-preview': 1 } : { 'data-dynamic-display': 1 })}
      >
        <table
          style={{
            ...table.styles,
            tableLayout: 'fixed',
          }}
        >
          {tableHeader.settings.showHeader && tableHeaderContent()}

          {tableBodyType === 'preview' && tableBodyPreview}
          {tableBodyType === 'delivery' && tableBodyDelivery}
        </table>
      </div>
    );
  };

  return (
    <TableExportWrapper data-is-dynamic>
      {renderTableLayoutWithDynamicBody('preview')}
      {renderTableLayoutWithDynamicBody('delivery')}
    </TableExportWrapper>
  );
};
