// Libraries
import classNames from 'classnames';
import { ReactNode, useMemo, useRef, useState } from 'react';
import { useDragDropManager, useDrop } from 'react-dnd';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Icon } from 'app/components/atoms';

// Hooks
import { useDeepCompareMemo } from 'app/hooks';

// Constants
import {
  BLOCK_ITEM,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Slices
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { selectParentBlockById } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Styled
import { DropBlockAreaStyled } from './styled';

interface BlockDropAreaProps {
  blockType: string;
  blockId: string;
  idx?: number;
  children?: ReactNode;
}

const { COLUMN, SLIDE_SHOW, GROUP } = STANDARDS_BLOCKS;

export const BlockDropArea: React.FC<BlockDropAreaProps> = props => {
  // Dispatch
  const dispatch = useDispatch();

  // Props
  const { blockType, blockId, idx = 0, children } = props;

  // State
  const [showDropArea, setShowDropArea] = useState({
    top: false,
    bottom: false,
  });

  // Ref
  const dropWrapperRef = useRef<any>(null);

  // React dnd
  const dragDropManager = useDragDropManager();
  const dragItem = dragDropManager.getMonitor().getItem();

  // Selector
  const parentBlock = useSelector(selectParentBlockById(blockId));

  // Actions
  const { addBlock, reorderBlock } = mediaTemplateDesignActions;

  // Memo
  const isDisableDrop = useDeepCompareMemo(() => {
    const { blockType: dragBlockType } = dragItem || {};

    // If drag block id equal drop block id then cancel
    if (dragItem?.blockId === blockId) {
      return true;
    }

    switch (blockType) {
      case COLUMN.name:
        if (dragBlockType !== blockType) {
          return true;
        }
        break;

      default:
        // Disable drag column block into column block
        if (dragBlockType === COLUMN.name) {
          return true;
        }

        // Disable drag SlideShow block into Slide block
        if (parentBlock.type === 'slide' && [SLIDE_SHOW.name].includes(dragBlockType)) {
          return true;
        }

        // Disable drag SlideShow block into Group block
        if (parentBlock.type === GROUP.name && [SLIDE_SHOW.name].includes(dragBlockType)) {
          return true;
        }
        break;
    }

    return false;
  }, [dragItem]);

  const isShowBothDropArea = useMemo(() => {
    switch (blockType) {
      case SLIDE_SHOW.name:
      case GROUP.name:
        return true;

      default:
        return false;
    }
  }, [blockType]);

  // React DND
  const [{ isOverDropTopArea }, dropTop] = useDrop(() => ({
    // The type (or types) to accept - strings or symbols
    accept: [BLOCK_ITEM],
    // Props to collect,
    collect: monitor => {
      return {
        isOverDropTopArea: monitor.isOver(),
        canDrop: monitor.canDrop(),
      };
    },
    drop(item: any, monitor) {
      updatePositionBlock({ item, dropIndex: idx });
    },
  }));

  const [{ isOverDropBottomArea }, dropBottom] = useDrop(() => ({
    // The type (or types) to accept - strings or symbols
    accept: [BLOCK_ITEM],
    // Props to collect,
    collect: monitor => {
      return {
        isOverDropBottomArea: monitor.isOver(),
        canDrop: monitor.canDrop(),
      };
    },
    drop(item: any) {
      updatePositionBlock({ item, dropIndex: idx + 1 });
    },
  }));

  const [{ isOverDropWrapper }, dropWrapper] = useDrop(() => ({
    // The type (or types) to accept - strings or symbols
    accept: [BLOCK_ITEM],
    // Props to collect,
    collect: monitor => {
      return {
        isOverDropWrapper: monitor.isOver(),
        canDrop: monitor.canDrop(),
      };
    },
    hover(item, monitor) {
      if (!dropWrapperRef.current) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = dropWrapperRef.current?.getBoundingClientRect();
      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      if (!clientOffset?.y) return;

      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      setShowDropArea({
        top: hoverClientY < hoverMiddleY,
        bottom: hoverClientY > hoverMiddleY,
      });
    },
  }));

  // Handlers
  const updatePositionBlock = ({ item, dropIndex }) => {
    const { isAddBlock, savedBlockId, blockType, blockId, index } = item || {};

    if (isAddBlock) {
      dispatch(
        addBlock({
          dragBlockType: blockType,
          dropBlockId: parentBlock.id,
          dropIndex: dropIndex,
          savedBlockId,
        }),
      );
    } else {
      dispatch(
        reorderBlock({
          source: {
            id: blockId,
            index,
          },
          destination: {
            id: parentBlock.id || 'root',
            index: dropIndex,
          },
        }),
      );
    }
  };

  const renderDropBlockArea = ({ ref, isOver, isShow = true }) => {
    return (
      <DropBlockAreaStyled
        ref={ref}
        role={'drop-block-area'}
        className={classNames({
          '!ants-h-9 !ants-border !ants-m-2 !ants-px-2 !ants-opacity-100':
            (isOverDropWrapper || isOver) && !isDisableDrop && isShow,
          '!ants-bg-[#e6f4ff] !ants-border !ants-border-[#91caff] !ants-opacity-100': isOver,
        })}
      >
        {isOverDropWrapper || isOver ? <Icon type="icon-ants-plus-square-outlined" size={18} /> : null}
      </DropBlockAreaStyled>
    );
  };

  return (
    <div
      ref={ref => {
        dropWrapper(ref);

        dropWrapperRef.current = ref;
      }}
    >
      {idx === 0 &&
        renderDropBlockArea({
          ref: dropTop,
          isOver: isOverDropTopArea,
          isShow: isShowBothDropArea ? true : showDropArea.top,
        })}
      {children}
      {renderDropBlockArea({
        ref: dropBottom,
        isOver: isOverDropBottomArea,
        isShow: idx === 0 && !isShowBothDropArea ? showDropArea.bottom : true,
      })}
    </div>
  );
};
