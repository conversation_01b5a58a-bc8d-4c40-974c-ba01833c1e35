// Libraries
import { useContext, useMemo } from 'react';
import moment, { Moment } from 'moment';
import { useTranslation } from 'react-i18next';

// Molecules
import { DatePicker } from 'app/components/molecules/DatePicker';

// Constants
import { dateFormatAPI, dateFormatFE, defaultDateFormatAPI, endDateFormat, OPERATORS_OPTION } from '../../../constants';

// Types
import { TOperatorValue } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { getValidMomentDate } from '../../../utils';

// Locales
import { translations } from 'locales/translations';

// Context
import { FilterContext } from '../../../context';

interface InputCalendarProps {
  value: string | number | null;
  required?: boolean;
  focused?: boolean;
  operator?: TOperatorValue;
  onChange: (v: InputCalendarProps['value']) => void;
}

export const InputCalendar: React.FC<InputCalendarProps> = props => {
  const { value, operator, required, focused, onChange } = props;

  const { t } = useTranslation();

  // Context
  const { isFormatDateTimeMilliseconds } = useContext(FilterContext);

  // Memo
  const isAfterDate = useMemo(() => {
    return operator === OPERATORS_OPTION.AFTER_DATE.value;
  }, [operator]);

  const onChangeValue = (_: Moment | null, dateString: string) => {
    const momentDate = getValidMomentDate(moment(dateString));
    let newValue: string | number | null = null;

    switch (isFormatDateTimeMilliseconds) {
      case true:
        newValue = (isAfterDate ? momentDate?.endOf('day').valueOf() : momentDate?.startOf('day').valueOf()) || null;

        break;
      default:
        newValue = momentDate?.format(isAfterDate ? endDateFormat : defaultDateFormatAPI) || null;
        break;
    }

    onChange(newValue);
  };

  return (
    <DatePicker
      focused={focused}
      required={required}
      format={dateFormatFE}
      placeholder={t(translations.selectDate.title)}
      value={getValidMomentDate(moment(value, !isFormatDateTimeMilliseconds ? dateFormatAPI : ''))}
      onChange={onChangeValue}
      className="!ants-border-t-0 !ants-border-x-0 ants-w-100"
    />
  );
};
