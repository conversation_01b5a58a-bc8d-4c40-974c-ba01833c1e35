// Translations
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';
import { BO_STATUS, COLLECTION_STATUS } from '../../../../../../constants';

export const RANKING_TYPE = {
  ALGORITHMS: {
    value: 'algorithms',
    label: getTranslateMessage(translations.algorithms.title),
  },
  CUSTOM: {
    value: 'custom',
    label: getTranslateMessage(translations.custom.title),
  },
};

export const ALGORITHM_TABS = {
  ALGORITHMS: {
    value: 'algorithms',
    label: getTranslateMessage(translations.algorithms.title),
  },
  FILTERS: {
    value: 'filters',
    label: getTranslateMessage(translations.excluded.title),
  },
};

export const OPERATOR_OPTIONS = {
  GREATER_THAN: {
    label: 'greater than',
    value: 'greater_than',
  },
  LESS_THAN: {
    label: 'less than',
    value: 'less_than',
  },
  GREATER_THAN_EQUAL: {
    label: 'greater than or equal',
    value: 'greater_than_equal',
  },
  LESS_THAN_EQUAL: {
    label: 'less than or equal',
    value: 'less_than_equal',
  },
  GREATER_THAN_RANGE: {
    label: 'greater than in range',
    value: 'greater_than_range',
  },
  LESS_THAN_RANGE: {
    label: 'less than in range',
    value: 'less_than_range',
  },
  GREATER_THAN_EQUAL_RANGE: {
    label: 'greater than or equal in range',
    value: 'greater_than_equal_range',
  },
  LESS_THAN_EQUAL_RANGE: {
    label: 'less than or equal in range',
    value: 'less_than_equal_range',
  },
  ABSOLUTE_DISTANCE: {
    label: 'absolute distance',
    value: 'absolute_distance',
  },
  ABSOLUTE_DISTANCE_RANGE: {
    label: 'absolute distance in range',
    value: 'absolute_distance_range',
  },
};

export const COMPARE_TYPE = {
  VALUE: {
    label: 'value',
    value: 'value',
  },
  PERCENTAGE: {
    label: '%',
    value: 'percentage',
  },
};

export const DEFAULT_DISTANCE_ALGORITHM = {
  by: null,
  operator: 'less_than',
  compareType: 'value',
  compareValue: 0,
  rangeType: ['value', 'value'],
  rangeValue: [0, 0],
  with: {
    event_action_id: null,
    event_category_id: null,
    event_tracking_name: null,
    insight_property_id: [],
    item_type_id: null,
    item_type_name: null,
    item_property_name: null,
    item_property_label: null,
    event_property_syntax: null,
  },
};

export const DEFAULT_TRENDING_ALGORITHM = {
  by: 'count_total',
  in: '15_m',
  sort_by: 'publish_date',
  sort_order: 'desc',
  compareType: 'value',
  compareValue: 0,
  rangeType: ['value', 'value'],
  rangeValue: [0, 0],
  operator: 'greater_than',
};

export const SORT_OPTIONS = {
  ORDER: {
    value: 'order',
    label: getTranslateMessage(translations.order.title),
  },
  MIX: {
    value: 'mix',
    label: getTranslateMessage(translations.mix.title),
  },
};

export const SIMILAR_CONTENT_OPTIONS = [
  {
    value: 'title',
    label: getTranslateMessage(translations.title.title),
  },
  {
    value: 'keywords',
    label: getTranslateMessage(translations.keywords.title),
  },
  {
    value: 'name',
    label: getTranslateMessage(translations.name.title),
  },
  {
    value: 'tags',
    label: getTranslateMessage(translations.tags.title),
  },
];

export const CATEGORY_OPTIONS = [
  { value: 'main_category', label: 'Main Category' },
  { value: 'category_level_1', label: 'L1' },
  { value: 'category_level_2', label: 'L2' },
];

export const INTEREST_OPTIONS = [
  {
    value: 'wishlist',
    label: 'Wishlist',
  },
  {
    value: 'last_decrease_sell_price_date',
    label: 'Decrease the selling price',
  },
  {
    value: 'last_new_release_date',
    label: 'New realease',
  },
  {
    value: 'last_back_in_stock_date',
    label: 'Back in stock',
  },
];

export const SORT_TYPE_OPTIONS = [
  {
    value: 'desc',
    label: 'Descending',
  },
  {
    value: 'asc',
    label: 'Ascending',
  },
  {
    value: 'random',
    label: 'Random',
  },
];

export const MAX_RETARGET_SEARCH_BY = 3;
export const isCheckStatusCollection = (listBO, itemTypeId, listCollection, field) => {
  let errorMessageCollection = '';
  let isDisableCollection = false;
  const selected = listBO?.find(bo => bo.value === itemTypeId);
  const selectedAttr = listCollection?.find(bo =>
    Array.isArray(field) ? field.includes(bo.value) : bo.value === field,
  );

  //TH1 : BO is Archive or Delete
  if (itemTypeId) {
    if (selected) {
      if (BO_STATUS[selected.model.status]) {
        errorMessageCollection = BO_STATUS[selected.model.status].errorMessage;
        isDisableCollection = true;
      } else if (selectedAttr) {
        if (COLLECTION_STATUS[selectedAttr.status]) {
          errorMessageCollection = COLLECTION_STATUS[selectedAttr.status].errorMessage;
        }
      } else {
        if (Array.isArray(field)) {
          errorMessageCollection = field.length ? COLLECTION_STATUS.DELETE.errorMessage : '';
        } else {
          errorMessageCollection = field ? COLLECTION_STATUS.DELETE.errorMessage : '';
        }
      }
    } else {
      errorMessageCollection = BO_STATUS.DELETE.errorMessage;
      isDisableCollection = true;
    }
  }

  return { errorMessageCollection, isDisableCollection };
};

export const ARTICLE_TREND_BY_OPTIONS = [
  { value: 'count_total', label: 'Count total' },
  { value: 'compare_with_before', label: 'Compare with before' },
];

export const ARTICLE_TREND_IN_OPTIONS = [
  { value: '5_m', label: '5 minutes' },
  { value: '15_m', label: '15 minutes' },
  { value: '30_m', label: '30 minutes' },
  { value: '60_m', label: '60 minutes' },
  { value: '1_d', label: 'A day' },
  { value: '1_w', label: 'A week' },
];

export const ARTICLE_TREND_SORT_BY_OPTIONS = [
  { value: 'publish_date', label: 'Publish date' },
  { value: 'count_view', label: 'Count view' },
];

export const EXCLUDE_ARTICLE_FILTER = ['seen_products'];

export const QUANTITY_MAX_BY_ALGO_TYPE = {
  frequently_seen_together: 10,
  frequently_bought_together: 10,
  frequently_cart_together: 10,
};
