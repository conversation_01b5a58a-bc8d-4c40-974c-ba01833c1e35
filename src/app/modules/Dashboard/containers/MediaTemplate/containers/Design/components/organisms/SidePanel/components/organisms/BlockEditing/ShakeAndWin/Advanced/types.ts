export type TStylesSettings = {
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  boxShadowBlur: string;
  boxShadowColor: string;
  boxShadowHorizontal: string;
  boxShadowInset: boolean;
  boxShadowSpread: string;
  boxShadowStyle: string;
  boxShadowVertical: string;
  linkedBorderRadiusInput: boolean;
  linkedBorderWidthInput: boolean;
  position?: string;
  positionSuffix?: string;
  linkedMarginInput: boolean;
  marginSuffix: string;
  backgroundColor: string;
  backgroundColorStyle: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  backgroundSize?: string;
  gradientType: string;
  radialPosition: string;
  linearAngle: number;
  customId: string;
  customClass: string;
  columnGap?: number;
  inputSize?: string;
  hidden?: boolean;
  gapX?: string;
  gapY?: string;
  gapSuffix?: string;
  linkedGapInput?: boolean;
  gradients: Array<{
    gradientColor: string;
    gradientColorLocation: number;
  }>;
  heightSuffix?: string;
  displayCondition: {
    condition: string;
    operator: string;
    value: any;
    type: string;
    attribute: Record<string, any>;
    [key: string]: any;
  };
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  linkedPositionInput?: boolean;
};
