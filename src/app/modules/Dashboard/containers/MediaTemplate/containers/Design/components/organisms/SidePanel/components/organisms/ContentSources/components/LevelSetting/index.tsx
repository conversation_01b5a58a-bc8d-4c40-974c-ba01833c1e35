// Libs
import React from 'react';
import { RadioGroupContextProps } from 'antd/lib/radio';

// Molecules
import { RadioGroup } from 'app/components/molecules';
import { SettingWrapper } from '../../../../molecules';
import { useTranslation } from 'react-i18next';

// Constants
import { LEVEL_OPTIONS } from './constants';
import { translations } from 'locales/translations';

export interface ILevelSettingProps extends RadioGroupContextProps {
  levelOption: string;
}

const LevelSetting = (props: ILevelSettingProps) => {
  const { t } = useTranslation();
  const { onChange, levelOption, value } = props;

  return (
    <SettingWrapper
      label={t(translations.level.title)}
      className="!ants-grid ants-grid-cols-3 ants-gap-4"
      labelClassName="ants-font-bold ants-col-span-1"
      childrenClassName="ants-col-span-2"
    >
      <RadioGroup
        options={LEVEL_OPTIONS[levelOption]}
        defaultValue={value}
        value={value}
        className="!ants-grid ants-grid-cols-2 ants-gap-12"
        onChange={onChange}
      />
    </SettingWrapper>
  );
};

export default React.memo(LevelSetting);
