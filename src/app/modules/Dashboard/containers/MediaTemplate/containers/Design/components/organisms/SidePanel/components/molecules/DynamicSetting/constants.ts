/* eslint-disable @typescript-eslint/no-unused-vars */
import { translations } from 'locales/translations';
import { includes } from 'lodash';
import { getTranslateMessage } from 'utils/messages';
import { ATTRIBUTE_STATUS, BO_STATUS } from '../../../../../../constants';

export const DYNAMIC_OPTIONS = {
  STATIC: {
    value: 'static',
    label: getTranslateMessage(translations.static.title),
  },
  DYNAMIC: {
    value: 'dynamic',
    label: getTranslateMessage(translations.dynamic.title),
  },
};
export const isCheckAttrArchiveDelete = (listBO, itemTypeId) => {
  let errorMessage = '';
  if (itemTypeId) {
    const selected = listBO?.find(bo => bo.value === itemTypeId);
    if (selected) {
      if (ATTRIBUTE_STATUS[selected.model.status]) {
        errorMessage = ATTRIBUTE_STATUS[selected.model.status].errorMessage;
      }
    } else {
      errorMessage = ATTRIBUTE_STATUS.DELETE.errorMessage;
    }
  }
  return errorMessage;
};
export const isCheckStatusAttr = (listBO, itemTypeId, listAttribute, field) => {
  let errorMessage = '';
  let isDisable = false;

  const isEmptyField = !field || (Array.isArray(field) && !field.length);

  // If field is empty then return empty errorMessage
  if (isEmptyField || !listAttribute?.length) {
    return {
      errorMessage: '',
      isDisable: false,
    };
  }

  const selected = listBO?.find(bo => bo.value === itemTypeId);
  const selectedAttr = listAttribute?.find(bo =>
    Array.isArray(field) ? field.includes(bo.value) : bo.value === field,
  );
  //TH1 : BO is Archive or Delete
  if (itemTypeId) {
    if (selected) {
      if (BO_STATUS[selected.model.status]) {
        errorMessage = BO_STATUS[selected.model.status].errorMessage;
        isDisable = true;
      } else if (selectedAttr) {
        if (ATTRIBUTE_STATUS[selectedAttr.status]) {
          errorMessage = ATTRIBUTE_STATUS[selectedAttr.status].errorMessage;
        }
      } else {
        errorMessage = field ? ATTRIBUTE_STATUS.DELETE.errorMessage : '';
      }
    } else {
      errorMessage = BO_STATUS.DELETE.errorMessage;
      isDisable = true;
    }
  }
  return { errorMessage, isDisable };
};
