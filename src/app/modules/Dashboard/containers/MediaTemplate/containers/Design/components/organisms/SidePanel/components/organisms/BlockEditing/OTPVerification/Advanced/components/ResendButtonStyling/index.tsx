import { Space, Text } from 'app/components/atoms';
import { handleError } from 'app/utils/handleError';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import BackgroundSetting from '../../../../../BackgroundSetting';
import {
  getBackgroundSettings,
  getBackgroundStyles,
  getBorderSettings,
  getBorderStyles,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from '../../../../../../../utils';
import { BorderSettingPopover } from '../../../../../BorderSetting';
import { RoundedCornersSetting } from '../../../../../RoundedCornersSetting';
import { SpacingSetting } from '../../../../../SpacingSetting';

type TResendButtonStyling = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/OTPVerification/Advanced/components/ResendButtonStyling/index.tsx';

export const ResendButtonStyling: React.FC<TResendButtonStyling> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const { resendButton } = settings;

  const { buttonStyles, buttonSettings } = resendButton;
  const onUpdateInputFieldSettings = (settings = {}, styles = {}) => {
    try {
      onChange({
        resendButton: {
          ...resendButton,
          buttonStyles: {
            ...buttonStyles,
            ...styles,
          },
          buttonSettings: {
            ...buttonSettings,
            ...settings,
          },
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateButtonSettings',
        args: {},
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <BackgroundSetting
        label={t(translations.background.title)}
        settings={getBackgroundSettings(buttonSettings)}
        styles={getBackgroundStyles(buttonStyles)}
        onChange={onUpdateInputFieldSettings}
      />
      <BorderSettingPopover
        settings={getBorderSettings(buttonSettings)}
        styles={getBorderStyles(buttonStyles)}
        onChange={onUpdateInputFieldSettings}
      />

      <RoundedCornersSetting
        settings={getRoundedCornersSettings(buttonSettings)}
        styles={getRoundedCornersStyles(buttonStyles)}
        onChange={onUpdateInputFieldSettings}
      />

      <SpacingSetting
        settings={getSpacingSettings(buttonSettings)}
        styles={getSpacingStyles(buttonStyles)}
        onChange={onUpdateInputFieldSettings}
      />
    </Space>
  );
};
