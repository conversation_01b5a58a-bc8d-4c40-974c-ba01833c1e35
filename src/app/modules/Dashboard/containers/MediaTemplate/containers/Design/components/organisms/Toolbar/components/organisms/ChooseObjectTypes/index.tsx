import React, { useState } from 'react';
import { WrapperCheckbox, WrapperChooseObjectTypes } from './styles';
import { Form } from 'app/components/molecules';
import { Checkbox, Text } from 'app/components/atoms';
import { t } from 'i18next';
import { translations } from 'locales/translations';
import { OBJECT_TYPES } from './constants';

interface IChooseObjectTypes {
  isShowSubTitle?: boolean;
  data: number[];
  onChangeObjectives?: (data: any) => void;
}
export const ChooseObjectTypes: React.FC<IChooseObjectTypes> = props => {
  const { isShowSubTitle = false, onChangeObjectives = () => {}, data } = props;
  const objectTypesOption = Object.values(OBJECT_TYPES);
  return (
    <WrapperChooseObjectTypes>
      {isShowSubTitle && <span className="sub-title">Objective</span>}
      <WrapperCheckbox>
        <Checkbox.Group
          className="ants-grid ants-grid-cols-2"
          style={{ gridGap: 10, padding: '5px 10px' }}
          value={data}
          options={objectTypesOption.map(deviceType => ({
            ...deviceType,
            label: (
              <div className="ants-relative ants-top-[2px] ants-flex ants-items-center ants-space-x-1">
                <Text className="ants-whitespace-nowrap">{deviceType.label}</Text>
              </div>
            ),
          }))}
          onChange={(checkedValues: any) => {
            if (checkedValues.length) {
              onChangeObjectives(checkedValues);
            }
          }}
        />
      </WrapperCheckbox>
    </WrapperChooseObjectTypes>
  );
};
