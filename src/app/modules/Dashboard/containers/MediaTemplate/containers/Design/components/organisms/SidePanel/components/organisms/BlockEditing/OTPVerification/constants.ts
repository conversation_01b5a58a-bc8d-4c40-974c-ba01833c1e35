import popularImg from 'assets/images/otp/popular.png';
import neatImg from 'assets/images/otp/neat.png';
import spreadImg from 'assets/images/otp/spread.png';

export const LAYOUT_FORM = {
  POPULAR: {
    label: 'Popular',
    value: 'popular',
    image: popularImg,
  },
  NEAT: {
    label: 'Neat',
    value: 'neat',
    image: neatImg,
  },
  SPREAD: {
    label: 'Spread',
    value: 'spread',
    image: spreadImg,
  },
};

export const LABEL_POSITION_OPTIONS = {
  TOP: {
    label: 'Top',
    value: 'top',
  },
  BOTTOM: {
    label: 'Bottom',
    value: 'bottom',
  },
  LEFT: {
    label: 'Left',
    value: 'left',
  },
  RIGHT: {
    label: 'Right',
    value: 'right',
  },
};

export const POSITION_MESSAGE_OPTION = {
  TOP: {
    label: 'Top',
    value: 'top',
  },
  BOTTOM: {
    label: 'Bottom',
    value: 'bottom',
  },
};
export const VALUE_STYLE_INPUT_FIELD = {
  WHOLE_BOX: 'whole-box',
  SEPARATE_BOX: 'separate-box',
  HYPHENS: 'hyphens',
};

export const STYLE_INPUT_FIELD_OPTIONS = {
  WHOLE_BOX: {
    value: VALUE_STYLE_INPUT_FIELD.WHOLE_BOX,
    label: 'Whole Box',
  },
  SEPARATE_BOX: {
    value: VALUE_STYLE_INPUT_FIELD.SEPARATE_BOX,
    label: 'Separate box',
  },
  // HYPHENS: {
  //   value: VALUE_STYLE_INPUT_FIELD.HYPHENS,
  //   label: 'Hyphens',
  // },
};

export const TYPE_TAB_ACTIVE_CONTENT = {
  LAYOUT: 1,
  OTP_FORM: 2,
  RESEND_BUTTON: 3,
  VERIFY_BUTTON: 4,
  MESSAGES: 5,
  TRIGGER_EVENT: 6,
};

export const TYPE_TAB_ACTIVE_ADVANCE = {
  CONTAINER_STYLING: 1,
  OTP_FORM_STYLING: 2,
  RESEND_STYLING: 3,
  VERIFY_STYLING: 4,
  MESSAGES_STYLING: 5,
  INPUT_FIELD_STYLING: 7,
};

export const RESEND_BUTTON_SPREAD_FORM_STYLE = {
  key: 'resendButton',
  description: "Didn't receive OTP?",
  width: 120,
  suffix: 'px',
  align: 'left',
  buttonSize: 'xsmall',
  buttonStyles: {
    background: 'ffffff',
    fontWeight: 500,
    fontSize: '14px',
    color: '#3A72B6',
    descriptionColor: '#000000',
    width: '120px',
    fontFamily: 'Roboto',
    borderStyle: 'solid',
    paddingTop: '7px',
    paddingBottom: '7px',
    paddingRight: '14px',
    paddingLeft: '14px',
    marginTop: '20px',
    marginBottom: '20px',
    marginRight: '0px',
    marginLeft: '5px',
    borderColor: '#005EB8',
    borderTopWidth: '1px',
    borderLeftWidth: '1px',
    borderRightWidth: '1px',
    borderBottomWidth: '1px',
    borderTopLeftRadius: '3px',
    borderTopRightRadius: '3px',
    borderBottomLeftRadius: '3px',
    borderBottomRightRadius: '3px',
  },
  buttonSettings: {
    buttonValue: 'Resend',
    backgroundColor: 'ffffff',
    fontWeight: 500,
    fontSize: '14px',
    color: '#3A72B6',
    descriptionColor: '#000000',
    width: '120px',
    fontFamily: 'Roboto',
    borderRadiusStyle: 'slightly_round',
  },
};

export const VERIFY_BUTTON_SPREAD_FORM_STYLE = {
  key: 'verifyButton',
  widthSuffix: 'px',
  buttonSize: 'medium',
  textAlign: 'right',
  buttonStyles: {
    width: '120px',
    background: '#005fb8',
    fontFamily: 'Roboto',
    fontWeight: 500,
    color: '#fff',
    borderTopLeftRadius: '5px',
    borderTopRightRadius: '5px',
    borderBottomLeftRadius: '5px',
    borderBottomRightRadius: '5px',
    marginTop: '20px',
    marginBottom: '20px',
    marginRight: '5px',
    marginLeft: '0px',
    textAlign: 'right',
  },
  buttonSettings: {
    buttonValue: 'Verify',
    width: '120px',
    backgroundColor: '#005fb8',
    fontFamily: 'Roboto',
    fontWeight: 500,
    color: '#fff',
    borderTopLeftRadius: '5px',
    borderTopRightRadius: '5px',
    borderBottomLeftRadius: '5px',
    borderBottomRightRadius: '5px',
    widthSuffix: 'px',
  },
};
