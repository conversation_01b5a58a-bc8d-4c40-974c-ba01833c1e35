// Libraries
import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

// Components
import TableHeaderSettings from '../components/TableHeaderSettings';
import TableColorSettings from '../components/TableColorSettings';
import { FontSettingPopover } from '../../../FontSetting';
import TableBodySettings from '../components/TableBodySettinngs';
// import TableFooterSettings from '../components/TableFooterSettings';
import { BorderSettingPopover } from '../../../BorderSetting';
import { RoundedCornersSetting } from '../../../RoundedCornersSetting';
import { SpacingSetting } from '../../../SpacingSetting';
import ColumnSettings from '../components/ColumnSettings';
import SelectMissingDataShowType from '../components/MissingData';

// Locales
import { translations } from 'locales/translations';

// Action
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Types
import {
  TColumnTableBlock,
  TTableSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { CHANGE_ADVANCED_TYPE } from '../constants';

// Atom
import { Space, Text } from 'app/components/atoms';

// Organisms
import BackgroundSetting from '../../../BackgroundSetting';
import { ItemsAlignSetting } from '../../../../molecules';

// Molecules
import { Collapse, CollapsePanel } from 'app/components/molecules';

// Config, Constants
import { TABLE_COLUMN_SETTINGS_DEFAULT } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';
import {
  ITEMS_ALIGN_TYPE,
  SIDE_PANEL_COLLAPSE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Utils
import {
  getBackgroundSettings,
  getBackgroundStyles,
  getBorderSettings,
  getBorderStyles,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';
import { handleError } from 'app/utils/handleError';
import { random } from 'app/utils/common';
import { DisplayCondition } from '../../../DisplayCondition';

type TableAddvanedProps = {
  tableBlockSettings: TTableSettings;
  isLoadingDataTableBo: boolean;
  selectedMetricCols: TColumnTableBlock[];
  selectedDimensionCols: TColumnTableBlock[];
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Table/Advanced/index.tsx';

const Advanced = (props: TableAddvanedProps) => {
  const dispatch = useDispatch();
  const { tableBlockSettings, selectedMetricCols, selectedDimensionCols } = props;

  const { updateBlockFieldsSelected } = mediaTemplateDesignActions;

  const { t } = useTranslation();

  const {
    displayCondition = {
      condition: '',
      field: '',
      index: 1,
      operator: '',
      dataType: '',
      value: '',
    },
  } = tableBlockSettings.blockStylesSettings || {};

  const changeTableAdvanced = useCallback(
    (type: string | undefined, value?: any, options?: any) => {
      try {
        const tableBlock = document.querySelector<HTMLTableElement>('table');

        if (!tableBlock) return;

        const tableBlockWidth = tableBlock.offsetWidth;

        let dataUpdate = [] as { fieldPath: string; data: any }[];

        switch (type) {
          case CHANGE_ADVANCED_TYPE.BLOCK_SPACING:
          case CHANGE_ADVANCED_TYPE.BLOCK_CORNER:
          case CHANGE_ADVANCED_TYPE.BLOCK_BORDER:
          case CHANGE_ADVANCED_TYPE.BLOCK_BACKGROUND: {
            const { settings, styles } = value;

            dataUpdate.push(
              {
                fieldPath: 'settings.blockStylesSettings',
                data: {
                  ...tableBlockSettings.blockStylesSettings,
                  ...settings,
                },
              },
              {
                fieldPath: 'settings.blockStyles',
                data: {
                  ...tableBlockSettings.blockStyles,
                  ...styles,
                },
              },
              {
                fieldPath: 'settings.blockHoverStylesSettings',
                data: {
                  ...tableBlockSettings.blockHoverStylesSettings,
                  ...settings,
                },
              },
              {
                fieldPath: 'settings.blockHoverStyles',
                data: {
                  ...tableBlockSettings.blockHoverStyles,
                  ...styles,
                },
              },
            );
            break;
          }
          case CHANGE_ADVANCED_TYPE.SHOW_MISS_DATA: {
            dataUpdate = [
              {
                fieldPath: 'settings.tableBody.settings.showMissingDataType',
                data: value,
              },
            ];
            break;
          }
          case CHANGE_ADVANCED_TYPE.BLOCK_ALIGN: {
            dataUpdate = [
              {
                fieldPath: 'settings.table.settings.cellAlignItems',
                data: value,
              },
            ];
            break;
          }
          case CHANGE_ADVANCED_TYPE.TABLE_HEADER: {
            dataUpdate.push({
              fieldPath: 'settings.tableHeader',
              data: value,
            });
            break;
          }
          case CHANGE_ADVANCED_TYPE.TABLE_SETTINGS: {
            dataUpdate = [
              {
                fieldPath: 'settings.table.settings',
                data: value,
              },
            ];
            break;
          }
          case CHANGE_ADVANCED_TYPE.TABLE_BODY: {
            const newSettings = value.settings as TTableSettings['tableBody']['settings'];

            const showRowNumber = tableBlockSettings.tableBody.settings.showRowNumbers;

            if (newSettings.showRowNumbers !== showRowNumber) {
              const draftCols = [...tableBlockSettings.columns];

              if (newSettings.showRowNumbers) {
                draftCols.unshift({
                  ...TABLE_COLUMN_SETTINGS_DEFAULT,
                  placement: 'center',
                  id: random(6),
                  value: '',
                  label: '',
                  dataType: '',
                  colType: 'index',
                });
              } else {
                draftCols.shift();
              }

              dataUpdate.push({
                fieldPath: 'settings.columns',
                data: draftCols.map(col => ({
                  ...col,
                  width: tableBlockWidth / draftCols.length + 'px',
                })),
              });
            }

            dataUpdate.push({
              fieldPath: 'settings.tableBody',
              data: value,
            });

            break;
          }
          case CHANGE_ADVANCED_TYPE.TABLE_FOOTER: {
            dataUpdate = [
              {
                fieldPath: 'settings.tableFooter',
                data: value,
              },
            ];
            break;
          }
          case CHANGE_ADVANCED_TYPE.UPDATE_COL: {
            const { colId, updatedCol } = value;
            const colIdx = tableBlockSettings.columns.findIndex(({ id }) => id === colId);

            if (colIdx !== -1) {
              dataUpdate = [
                {
                  fieldPath: `settings.columns[${colIdx}]`,
                  data: updatedCol,
                },
              ];
            }
            break;
          }

          default:
            return;
        }

        dispatch(
          updateBlockFieldsSelected({
            dataUpdate,
            ...options,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeImageBlock',
          args: {},
        });
      }
    },
    [dispatch, tableBlockSettings, updateBlockFieldsSelected],
  );

  const onChangeDisplayCondition = (settings = {}) => {
    try {
      dispatch(
        updateBlockFieldsSelected({
          dataUpdate: [
            {
              fieldPath: 'settings.blockStylesSettings.displayCondition',
              data: settings,
            },
          ],
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeDisplayCondition',
        args: { settings },
      });
    }
  };

  return (
    <Collapse defaultActiveKey={'table-addvance-styling'} accordion>
      <CollapsePanel header={t(translations.tableStyling.title)} key="table-addvance-styling">
        <Space size={20} direction="vertical">
          {/* Block background */}
          <BackgroundSetting
            label={t(translations.style.title)}
            settings={getBackgroundSettings(tableBlockSettings.blockStylesSettings)}
            styles={getBackgroundStyles(tableBlockSettings.blockStyles)}
            onChange={(settings, styles) =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.BLOCK_BACKGROUND, {
                settings,
                styles,
              })
            }
          />

          {/* Item align (cell vertical-align) */}
          <ItemsAlignSetting
            excluded={[ITEMS_ALIGN_TYPE.STRETCH, ITEMS_ALIGN_TYPE.BASELINE]}
            label={t(translations.itemAlign.title)}
            value={tableBlockSettings.table.settings.cellAlignItems}
            onChange={value => changeTableAdvanced(CHANGE_ADVANCED_TYPE.BLOCK_ALIGN, value)}
          />

          {/* Table header */}
          <TableHeaderSettings
            styles={tableBlockSettings.tableHeader.styles as any}
            settingsStyle={tableBlockSettings.tableHeader.settings}
            onChange={(settings, styles) =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.TABLE_HEADER, {
                settings,
                styles,
              } as TTableSettings['tableHeader'])
            }
          />

          {/* Table colors */}
          <TableColorSettings
            tableSettings={tableBlockSettings.table.settings}
            onChange={newColorsTableSettings =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.TABLE_SETTINGS, {
                ...tableBlockSettings.table.settings,
                ...newColorsTableSettings,
              } as TTableSettings['table']['settings'])
            }
          />

          {/* Table labels (Table body font) */}
          <FontSettingPopover
            label={t(translations.tableLabels.title)}
            styles={tableBlockSettings.tableBody.styles as any}
            onChange={(_settingsStyleFont, stylesFont) => {
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.TABLE_BODY, {
                ...tableBlockSettings.tableBody,
                styles: {
                  ...tableBlockSettings.tableBody.styles,
                  ...stylesFont,
                },
              } as TTableSettings['tableBody']);
            }}
          />

          {/* Table body */}
          <TableBodySettings
            tableBody={tableBlockSettings.tableBody}
            onChange={updatedTableBody => changeTableAdvanced(CHANGE_ADVANCED_TYPE.TABLE_BODY, updatedTableBody)}
          />

          {/* Table footer */}
          {/* <TableFooterSettings */}
          {/*   tableFooter={tableBlockSettings.tableFooter} */}
          {/*   onChange={updatedTableFooter => changeTableAdvanced(CHANGE_ADVANCED_TYPE.TABLE_FOOTER, updatedTableFooter)} */}
          {/* /> */}

          {/* Table block border */}
          <BorderSettingPopover
            placement="topRight"
            settings={getBorderSettings(tableBlockSettings.blockStylesSettings)}
            styles={getBorderStyles(tableBlockSettings.blockStyles)}
            onChange={(settings, styles) =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.BLOCK_BORDER, {
                settings,
                styles,
              })
            }
          />

          {/* Table block rounded corners */}
          <RoundedCornersSetting
            placement="topRight"
            settings={getRoundedCornersSettings(tableBlockSettings.blockStylesSettings)}
            styles={getRoundedCornersStyles(tableBlockSettings.blockStyles)}
            onChange={(settings, styles) =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.BLOCK_CORNER, {
                settings,
                styles,
              })
            }
          />

          {/* Table block spacing */}
          <SpacingSetting
            placement="topRight"
            isMarginSetting={false}
            settings={getSpacingSettings(tableBlockSettings.blockStylesSettings)}
            styles={getSpacingStyles(tableBlockSettings.blockStyles)}
            onChange={(settings, styles) =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.BLOCK_SPACING, {
                settings,
                styles,
              })
            }
          />

          {/* Missing Data */}
          <SelectMissingDataShowType
            tableBodySettings={tableBlockSettings.tableBody.settings}
            onChange={showMissingDataType =>
              changeTableAdvanced(CHANGE_ADVANCED_TYPE.SHOW_MISS_DATA, showMissingDataType)
            }
          />
        </Space>
      </CollapsePanel>

      {/* Dimension Col Styling */}
      <CollapsePanel header={t(translations.dimensionStyling.title)} key="dimennsion-styling">
        {!selectedDimensionCols.length && <Text>{t(translations.noDimensionSelected.title)}</Text>}

        <Space direction="vertical" size={16}>
          {selectedDimensionCols.map((col, index) => (
            <div key={col.id} className="ants-flex ants-flex-col ants-gap-3">
              <Text bold>
                {t(translations.column.title)} #{index + 1}
              </Text>
              <ColumnSettings
                showFormatType={false}
                column={col}
                onChangeSettings={updatedCol =>
                  changeTableAdvanced(CHANGE_ADVANCED_TYPE.UPDATE_COL, {
                    colId: col.id,
                    updatedCol,
                  })
                }
              />
            </div>
          ))}
        </Space>
      </CollapsePanel>

      {/* Metric Col Styling */}
      <CollapsePanel header={t(translations.metricStyling.title)} key="metrics-styling">
        {!selectedMetricCols.length && <Text>{t(translations.noMetricSelected.title)}</Text>}

        <Space direction="vertical" size={16}>
          {selectedMetricCols.map((col, index) => (
            <div key={col.id} className="ants-flex ants-flex-col ants-gap-3">
              <Text bold>
                {t(translations.column.title)} #{index + 1}
              </Text>
              <ColumnSettings
                column={col}
                onChangeSettings={updatedCol =>
                  changeTableAdvanced(CHANGE_ADVANCED_TYPE.UPDATE_COL, {
                    colId: col.id,
                    updatedCol,
                  })
                }
              />
            </div>
          ))}
        </Space>
      </CollapsePanel>

      <CollapsePanel header={t(translations.displayCondition.title)} key={SIDE_PANEL_COLLAPSE.DISPLAY_CONDITION}>
        <DisplayCondition
          displayCondition={displayCondition}
          valueCondition={displayCondition.condition}
          onChange={onChangeDisplayCondition}
        />
      </CollapsePanel>
    </Collapse>
  );
};

export default Advanced;
