// Translations
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const IMAGE_SIZE = {
  UNSET: {
    value: 'unset',
    label: getTranslateMessage(translations.unset.title),
  },
  CONTAIN: {
    value: 'contain',
    label: getTranslateMessage(translations.contain.title),
  },
  COVER: {
    value: 'cover',
    label: getTranslateMessage(translations.cover.title),
  },
};

export const IMAGE_TO_SHAKE = {
  USE_IMAGE_BEFORE: {
    value: 'use-image-before',
    label: 'Use Image Before Shake',
  },
  OTHER: {
    value: 'other-image',
    label: 'Other Image',
  },
};
