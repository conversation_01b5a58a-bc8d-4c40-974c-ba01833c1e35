// Libraries
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
// import { TObjectTemplate } from '@antscorp/antsomi-ui/es/models/ObjectTemplate';
import { GET_LIST_TYPE, OBJECT_TYPES, PUBLIC_LEVEL } from '@antscorp/antsomi-ui/es/constants';
import {
  TemplateSaveAsModal,
  TemplateValueOptions,
  camelCaseToSnakeCase,
  message,
  snakeCaseToCamelCase,
  useGetObjectTemplateDetail,
  // useGetObjectTemplateList,
  usePersistTemplate,
  useTemplateSave,
  // useValidateTemplateName,
} from '@antscorp/antsomi-ui';

// Locales
import { translations } from 'locales/translations';

// Utils
import { handleError } from 'app/utils/handleError';

// Slices
import {
  selectExportInfo,
  selectIsEmbed,
  /* selectCategoriesGoal, */ selectToolbar,
  selectWorkspace,
  selectPromotionPool,
} from '../../../slice/selectors';
import { getTemplateSetting } from '../../../slice/utils';
import createSagaAction, { ACTIONS } from '../../../slice/sagaActions';
import { mediaTemplateDesignActions } from '../../../slice';
import { getTranslateMessage } from 'utils/messages';
import { useExternalServiceAuth } from 'app/hooks/useExternalServiceAuth';
import { cloneDeep, isEmpty, omit } from 'lodash';
import { initAccessInfoDefault } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/utils';
import { useDeepCompareEffect, useDeepCompareMemo, useUserInfoV2 } from 'app/hooks';
import { MENU_PERMISSION } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/constants';
import { permissionServices } from 'app/services/Permission';
import { ObjectAccessInfo } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/types';
import { handleUploadThumbnail } from '../../../utils';
import { TObjectTemplate } from '@antscorp/antsomi-ui/es/models/ObjectTemplate';
import isEqual from 'react-fast-compare';

interface SaveAsButtonProps {
  saveAsGallery?: boolean;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SaveAsButton/index.tsx';

// let timeoutValidator: any = null;

export const SaveAsButton: React.FC<SaveAsButtonProps> = props => {
  const { saveAsGallery } = props;
  // Hooks
  const dispatch = useDispatch();
  const [messageApi, contextHolder] = message.useMessage();
  const serviceAuth = useExternalServiceAuth();

  const { t } = useTranslation();
  const userInfo = useUserInfoV2();

  // Selectors
  const workspace = useSelector(selectWorkspace);
  const promotionPool = useSelector(selectPromotionPool);
  const { isOpenSaveAs } = useSelector(selectToolbar);
  const isEmbed = useSelector(selectIsEmbed);
  // const objectiveTypes = useSelector(selectCategoriesGoal);
  const exportInfo = useSelector(selectExportInfo);

  // NOTE: hot fix when use 2 SaveAsButton at the same time in dropdown items
  const [openModal, setOpenModal] = useState(false);
  const [isLoadingUploadThumbnail, setIsLoadingUploadThumbnail] = useState<boolean>(false);
  const [tempThumbnails, setTempThumbnails] = useState<any[]>([]);

  // Actions
  const { setToolbar, updateObjectiveTypes, setViewPagesThumbnail } = mediaTemplateDesignActions;

  const [selectedTemplate, setSelectedTemplate] = useState<string>();

  const { data: objectTemplateDetail } = useGetObjectTemplateDetail({
    args: {
      auth: serviceAuth,
      params: {
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: saveAsGallery ? PUBLIC_LEVEL.PUBLIC : PUBLIC_LEVEL.RESTRICTED,
        template_id: selectedTemplate || workspace.id,
      },
    },
  });

  // Memo
  const shareAccessInfo: ObjectAccessInfo = useDeepCompareMemo(() => {
    const { shareAccess } = objectTemplateDetail || {};

    if (!isEmpty(shareAccess)) {
      return snakeCaseToCamelCase(shareAccess || {}, true);
    }

    return initAccessInfoDefault(userInfo || {});
  }, [objectTemplateDetail]);

  const defaultThumbnail = useDeepCompareMemo(() => {
    const thumbnailIdx = workspace.viewPages
      .filter(page => page.settings?.isActive)
      .map(item => item.thumbnail)
      .findIndex(item => item === workspace.thumbnail);

    return thumbnailIdx === -1 ? workspace.defaultThumbnailIndex || 0 : thumbnailIdx;
  }, [workspace]);

  const templateSaveDefaultValue: Partial<TemplateValueOptions> = useDeepCompareMemo(() => {
    const defaultValue: Partial<TemplateValueOptions> = {
      accessInfo: shareAccessInfo,
      categories: workspace.categories,
      defaultThumbnail,
      description: workspace.description,
      templateName: {
        id: +workspace.id,
        label: undefined,
      },
      saveOption: saveAsGallery || isEmbed ? 'save-new' : 'save-exist',
    };

    return defaultValue;
  }, [workspace, shareAccessInfo]);

  const {
    categoryItems,
    templateItems,
    value: templateValue,
    onChange: onChangeTemplateValue,
    searchNameProps,
    setValue,
    form,
  } = useTemplateSave({
    service: serviceAuth,
    config: {
      getListType: GET_LIST_TYPE.OWNER,
      objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
      publicLevel: saveAsGallery ? PUBLIC_LEVEL.PUBLIC : PUBLIC_LEVEL.RESTRICTED,
      limitListPerPage: 20,
    },
    defaultValue: {
      defaultThumbnail,
      categories: (workspace.categories as any) || {},
      saveOption: 'save-new',
      templateName: { id: +workspace.id, label: undefined },
      description: objectTemplateDetail?.description || '',
      accessInfo: saveAsGallery
        ? undefined
        : !isEmpty(objectTemplateDetail?.shareAccess || {})
        ? snakeCaseToCamelCase(
            {
              ...objectTemplateDetail?.shareAccess,
              owner_id: objectTemplateDetail?.shareAccess?.owner_id || userInfo?.user_id,
            } || {},
            true,
          )
        : initAccessInfoDefault(userInfo!),
    },
  });

  const { saveOption } = templateValue;

  // const { data: galleryTemplateData } = useGetObjectTemplateList({
  //   args: {
  //     auth: serviceAuth,
  //     params: {
  //       get_list_type: GET_LIST_TYPE.OWNER,
  //       object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
  //       public_level: PUBLIC_LEVEL.PUBLIC,
  //       // TODO: Temporary get all, will make lazy load
  //       limit: 1000,
  //     },
  //   },
  // });
  // const { entities: galleryTemplates } = (galleryTemplateData || {}).pages?.[0] || {};
  // const { mutateAsync: validateTemplateName } = useValidateTemplateName({});

  // Mutations
  const { mutateAsync: persistTemplate, isLoading: isLoadingPersistTemplate } = usePersistTemplate({
    options: {
      // onSuccess: (_, variables) => {
      //   const { persistType } = variables;
      //   messageApi.success(
      //     getTranslateMessage(
      //       translations.saveAsGallery[persistType === 'create' ? 'saveAsNewGallery' : 'saveAsExistingGallery'].success,
      //     ),
      //   );
      // },
      // onError: (_, variables) => {
      //   const { persistType } = variables;
      //   messageApi.error(
      //     getTranslateMessage(
      //       translations.saveAsGallery[persistType === 'create' ? 'saveAsNewGallery' : 'saveAsExistingGallery'].error,
      //     ),
      //   );
      // },
    },
  });

  useEffect(() => {
    const newThumbnails = workspace.viewPages
      .flatMap(item => (item.settings.isActive ? item.thumbnail : []))
      .filter(Boolean);

    if (!isEqual(newThumbnails, tempThumbnails)) {
      setTempThumbnails(newThumbnails);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workspace.viewPages]);

  useEffect(() => {
    // if (workspace?.objectiveTypes) dispatch(updateObjectiveTypes(workspace?.objectiveTypes));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workspace?.objectiveTypes]);

  useDeepCompareEffect(() => {
    if (!!templateSaveDefaultValue) {
      onChangeTemplateValue(templateSaveDefaultValue);
    }
  }, [omit(templateSaveDefaultValue, 'accessInfo')]);

  useDeepCompareEffect(() => {
    if (saveOption === 'save-exist') {
      // Set timeout for get updated value
      const currentShareAccess = snakeCaseToCamelCase(
        objectTemplateDetail?.shareAccess || {},
        true,
      ) as ObjectAccessInfo;

      setValue(prev => ({
        ...prev,
        description: objectTemplateDetail?.description,
        accessInfo: !isEmpty(currentShareAccess) ? currentShareAccess : initAccessInfoDefault(userInfo || {}),
      }));
    }
  }, [objectTemplateDetail, saveOption]);

  // Check if user is create new template then reset accessInfo to current user
  useDeepCompareEffect(() => {
    if (saveOption === 'save-new') {
      onChangeTemplateValue({
        ...templateValue,
        description: templateValue?.description || workspace?.description || '',
        accessInfo: initAccessInfoDefault(userInfo || {}),
      });
    }
  }, [saveOption]);
  // Use Effect
  // useEffect(() => {
  //   if (isOpenSaveAs) {
  //     form.setFieldsValue({
  //       existsGalleryId: (galleryTemplates || [])[0]?.id,
  //     });
  //     setObjectiveTypesTemp(prev => ({
  //       ...prev,
  //       list: objectiveTypes,
  //     }));
  //   } else {
  //     form.resetFields();
  //   }
  // }, [isOpenSaveAs, galleryTemplates, form, objectiveTypes]);

  // Handlers
  // const validatorGalleryName = (_rule: any, value: any) => {
  //   try {
  //     if (!value) {
  //       return Promise.reject(Error(FORM_VALIDATE.DUPLICATED.message()));
  //     }

  //     const promise = new Promise((resolve, reject) => {
  //       if (timeoutValidator) {
  //         clearTimeout(timeoutValidator);
  //       }

  //       timeoutValidator = setTimeout(() => {
  //         validateTemplateName({
  //           auth: serviceAuth,
  //           params: {
  //             object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
  //             public_level: PUBLIC_LEVEL.PUBLIC,
  //             template_name: value,
  //           },
  //         }).then(response => {
  //           const { existed } = response;

  //           if (existed) {
  //             reject(Error(FORM_VALIDATE.DUPLICATED.message()));
  //           } else {
  //             resolve(true);
  //           }
  //         });
  //       }, 500);
  //     });

  //     return promise;
  //   } catch (error) {
  //     handleError(error, {
  //       path: PATH,
  //       name: 'validatorGalleryName',
  //       args: {},
  //     });
  //   }
  // };

  // const onSubmitForm = async (values: TInitialValues) => {
  //   try {
  //     const { saveAsType, existsGalleryId, newGalleryName } = values || {};

  //     const { template, deviceType } = workspace;

  //     const data: Partial<TObjectTemplate> = {
  //       ...(saveAsType === ESaveAsType.SAVE_AS_NEW && { template_name: newGalleryName }),
  //       ...(saveAsType === ESaveAsType.SAVE_AS_EXISTS && { template_id: +`${existsGalleryId}` }),
  //       template_type: template?.id,
  //       device_type: deviceType,
  //       template_setting: getTemplateSetting(workspace),
  //       properties: {
  //         ...workspace,
  //         isInitial: true,
  //       },
  //       object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
  //       public_level: PUBLIC_LEVEL.PUBLIC,
  //       thumbnail: workspace.thumbnail,
  //       // objective_type: workspace.objectiveTypes,
  //       goal: workspace.categories.goal,
  //     };

  //     await persistTemplate({
  //       persistType: saveAsType === ESaveAsType.SAVE_AS_NEW ? 'create' : 'update',
  //       params: {
  //         auth: serviceAuth,
  //         data,
  //       },
  //     });

  //     toggleModal();
  //   } catch (error) {
  //     handleError(error, {
  //       path: PATH,
  //       name: 'onSubmitForm',
  //       args: {},
  //     });
  //   }
  // };

  useDeepCompareEffect(() => {
    if (saveOption === 'save-exist') {
      setValue(prev => ({
        ...prev,
        description: objectTemplateDetail?.description,
      }));
    }
  }, [objectTemplateDetail, saveOption]);

  const toggleModal = () => {
    try {
      dispatch(
        setToolbar({
          isOpenSaveAs: !isOpenSaveAs,
        }),
      );
      setOpenModal(!openModal);
      // setModalType(undefined);
      // dispatch(updateObjectiveTypes(objectiveTypesTemp.list));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'toggleModal',
        args: {},
      });
    }
  };

  const handleCreateTemplate = async (value: TemplateValueOptions) => {
    const { templateName, categories, defaultThumbnail, accessInfo, description, saveOption } = value;
    const { deviceType } = workspace;
    const persistType = saveOption === 'save-new' ? 'create' : 'update';
    const isCreateNew = saveOption === 'save-new';

    // const thumbnail = workspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
    //   defaultThumbnail || 0
    // ];
    let thumbnail = '';

    try {
      let cloneWorkspace = cloneDeep(workspace);

      // Handle Update new thumbnails
      cloneWorkspace.viewPages
        .filter(viewPage => viewPage.settings.isActive)
        .forEach((viewPage, idx) => {
          viewPage.thumbnail = tempThumbnails[idx] || '';
        });

      let updateData: Partial<TObjectTemplate> = {
        ...(isCreateNew ? { template_name: templateName?.label?.trim() } : { template_id: +templateName?.id! }),
        properties: {},
        template_setting: getTemplateSetting(workspace, { promotionPool }),
        template_type: workspace.template.id,
        device_type: deviceType,
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: saveAsGallery ? PUBLIC_LEVEL.PUBLIC : PUBLIC_LEVEL.RESTRICTED,
        description,
        thumbnail: '',
        ...(saveAsGallery ? {} : { share_access: camelCaseToSnakeCase(accessInfo!, true) }),
        ...categories,
      };

      if (!isCreateNew) {
        setIsLoadingUploadThumbnail(true);

        // Handle upload images
        cloneWorkspace = await handleUploadThumbnail({ templateId: +templateName?.id!, workspace: cloneWorkspace });

        setIsLoadingUploadThumbnail(false);

        // Set thumbnail
        thumbnail =
          cloneWorkspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
            defaultThumbnail || 0
          ] || '';

        updateData = {
          ...updateData,
          properties: {
            ...cloneWorkspace,
            viewPages: cloneWorkspace.viewPages,
            categories: categories,
            defaultThumbnailIndex: defaultThumbnail,
            thumbnail,
            ...(persistType === 'create' ? { isInitial: true } : {}),
          },
          thumbnail,
        };
      }

      const { id: templateId } = await persistTemplate({
        persistType,
        params: {
          auth: serviceAuth,
          data: updateData,
        },
      });

      if (templateId && isCreateNew) {
        setIsLoadingUploadThumbnail(true);

        cloneWorkspace = await handleUploadThumbnail({ templateId: templateId, workspace: cloneWorkspace });

        setIsLoadingUploadThumbnail(false);

        // Set thumbnail
        thumbnail =
          cloneWorkspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
            defaultThumbnail || 0
          ] || '';

        await persistTemplate({
          persistType: 'update',
          params: {
            auth: serviceAuth,
            data: {
              template_id: templateId,
              properties: {
                ...cloneWorkspace,
                viewPages: cloneWorkspace.viewPages,
                categories: categories,
                defaultThumbnailIndex: defaultThumbnail,
                thumbnail,
                ...(persistType === 'create' ? { isInitial: true } : {}),
              },
              thumbnail,
            },
          },
        });
      }

      messageApi.success(
        t(
          isCreateNew
            ? translations.createTemplate.notification.createSuccess
            : translations.updateTemplate.notification.updateSuccess,
        ),
      );

      toggleModal();

      onChangeTemplateValue(templateSaveDefaultValue);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleCreateTemplate',
        args: { templateValue },
      });
    }
  };

  const onClickSaveAs = () => {
    setOpenModal(true);

    try {
      const { exportPages } = exportInfo;
      if (exportPages?.length) {
        dispatch(createSagaAction(ACTIONS.SAVE_MEDIA_TEMPLATE, { isSaveAs: true }));
      } else {
        dispatch(
          setToolbar({
            isOpenSaveAs: true,
          }),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSaveAs',
        args: {},
      });
    }
  };

  return (
    <>
      <button onClick={onClickSaveAs}>
        {saveAsGallery ? t(translations.saveAs.galleryTemplate) : t(translations.saveAs.myTemplate)}
      </button>
      {/* {isShowChooseObjective ? (
        <button
          style={{ textAlign: 'left', fontSize: 12 }}
          className="ants-border-none ants-outline-none ants-w-100 ants-h-full"
          onClick={onClickSaveAs}
        >
          {t(translations.templateSave.templateGallery)}
        </button>
      ) : (
        <Button type="primary" onClick={onClickSaveAs}>
          {t(translations.cloneMediaTemplate.saveAs.title)}
        </Button>
      )} */}

      <TemplateSaveAsModal
        key="my-template"
        destroyOnClose
        title={saveAsGallery ? t(translations.templateSave.saveTemplateGallery) : t(translations.saveAs.fullTitle)}
        open={isOpenSaveAs && openModal}
        onCancel={() => {
          toggleModal();
          setTempThumbnails(workspace.viewPages.flatMap(item => (item.settings.isActive ? item.thumbnail : [])));
        }}
        okButtonProps={{
          loading: isLoadingPersistTemplate || isLoadingUploadThumbnail,
        }}
        cancelButtonProps={{
          disabled: isLoadingPersistTemplate || isLoadingUploadThumbnail,
        }}
        onOk={(e, value) => value && handleCreateTemplate(value)}
        templateProps={{
          form,
          value: templateValue,
          onChange: onChangeTemplateValue,
          onEvent: event => {
            if (event.templateName && event.templateName.id) setSelectedTemplate(event.templateName.id.toString());
          },
          imageReview: {
            thumbnails: tempThumbnails,
            skeleton: true,
          },
          onSaveThumbnail(data) {
            const { thumbnails } = data;
            setTempThumbnails(thumbnails);
          },
          templateNames: templateItems,
          categories: categoryItems,
          omitCategories: ['device_type', 'template_type'],
          shareAccess: {
            show: !saveAsGallery,
            ...(saveAsGallery
              ? {}
              : {
                  show: true,
                  userId: +userInfo?.user_id!,
                  userPermission: {
                    edit: MENU_PERMISSION.CREATED_BY_USER,
                    view: MENU_PERMISSION.CREATED_BY_USER,
                  },
                  placeholder: 'Add people',
                  generalAccessSettings: {
                    publicOnlyWith: true,
                  },
                  allowAddUser: true,
                  getUserInfo: email => permissionServices.getUserInfo(email, userInfo?.user_id!) as any,
                }),
          },
          templateNamesOptions: {
            ...searchNameProps,
          },
        }}
      />
      {contextHolder}
    </>
  );
};
