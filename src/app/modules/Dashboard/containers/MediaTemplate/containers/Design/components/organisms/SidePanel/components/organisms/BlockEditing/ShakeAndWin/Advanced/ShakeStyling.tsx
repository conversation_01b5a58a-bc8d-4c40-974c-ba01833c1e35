// Libraries
import React, { Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Atoms
import { Space, Switch, Text } from 'app/components/atoms';

// Molecules
import { SliderWithInputNumber } from 'app/components/molecules/SliderWithInputNumber/';

// Organisms
import { RoundedCornersSetting } from '../../../RoundedCornersSetting';
import { BorderSettingPopover } from '../../../BorderSetting';
import { BoxShadowSetting } from '../../../BoxShadowSetting';
import { SpacingSetting } from '../../../SpacingSetting';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Selectors
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Types
import { TStylesSettings } from './types';

// Utils
import { handleError } from 'app/utils/handleError';
// Modules
import {
  getBackgroundSettings,
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';
// Organisms
import { SettingWrapper } from '../../../../molecules';
import BackgroundSetting from '../../../BackgroundSetting';
import { Select } from 'app/components/molecules';
import { FontSettingPopover } from '../../../FontSetting';

// Constant
import { POSITION_OPTIONS } from './constant';
import { BLOCK_SETTING_DEFAULT } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Image/Advanced/ShakeStyling.tsx';

interface ShakeStylingProps {
  blockStylesSettings: TStylesSettings;
  blockStyles: React.CSSProperties;
  notificationStylesSettings;
  notificationStyles;
  onChange: (blockStylesSettings: TStylesSettings, blockStyles: React.CSSProperties) => void;
  onChangeNotificationStyling: (
    newNotificationStylesSettings: TStylesSettings,
    newNotificationStyles: React.CSSProperties,
  ) => void;
}

export const ShakeStyling: React.FC<ShakeStylingProps> = props => {
  // Dispatch
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Props
  const {
    blockStyles,
    blockStylesSettings,
    onChange,
    notificationStylesSettings,
    notificationStyles,
    onChangeNotificationStyling,
  } = props;

  // Actions
  const { updateBlock } = mediaTemplateDesignActions;

  // Selectors
  const blockSelected = useSelector(selectBlockSelected);

  const onUpdateBlockSettings = (settings = {}, styles = {}) => {
    try {
      // Callback onChange
      onChange(
        {
          ...blockStylesSettings,
          ...settings,
        },
        {
          ...blockStyles,
          ...styles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateBlockSettings',
        args: {},
      });
    }
  };

  const onUpdateNotiSettings = (settings: any = {}, styles = {}) => {
    try {
      // Callback onChange
      let newSettings = settings;
      let newStyles = styles;

      if (settings?.isDefaultNotification) {
        newSettings = BLOCK_SETTING_DEFAULT.SHAKE_AND_WIN.notificationStylesSettings;
        newStyles = BLOCK_SETTING_DEFAULT.SHAKE_AND_WIN.notificationStyles;
      }
      onChangeNotificationStyling(
        {
          ...notificationStylesSettings,
          ...newSettings,
        },
        {
          ...notificationStyles,
          ...newStyles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateNotiSettings',
        args: {},
      });
    }
  };

  if (blockSelected) {
    // Handlers
    const onChangeOpacity = (value: number) => {
      try {
        dispatch(
          updateBlock(
            produce(blockSelected, draft => {
              draft.settings.styles.opacity = value / 100;
            }),
          ),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeOpacity',
          args: {},
        });
      }
    };

    const valueOpacity = (blockStyles.opacity as number) * 100;

    return (
      <Space size={20} direction="vertical">
        <BoxShadowSetting
          settings={getBoxShadowSettings(blockStylesSettings as any)}
          onChange={(settings, styles) => {
            onUpdateBlockSettings(settings, styles);
          }}
        />
        <BorderSettingPopover
          settings={getBorderSettings(blockStylesSettings as any)}
          styles={getBorderStyles(blockStyles)}
          onChange={onUpdateBlockSettings}
        />
        <RoundedCornersSetting
          settings={getRoundedCornersSettings(blockStylesSettings as any)}
          styles={getRoundedCornersStyles(blockStyles)}
          onChange={onUpdateBlockSettings}
        />
        <SpacingSetting
          settings={getSpacingSettings(blockStylesSettings as any)}
          styles={getSpacingStyles(blockStyles)}
          onChange={onUpdateBlockSettings}
        />
        <SliderWithInputNumber
          label={t(translations.opacity.title)}
          labelClassName="!ants-text-gray-4"
          min={0}
          max={100}
          value={valueOpacity}
          onAfterChange={value => onChangeOpacity(value)}
        />
        <SettingWrapper label={t(translations.defaultReminderNotification.title)} labelClassName="!ants-text-gray-4">
          <Switch
            checked={notificationStylesSettings?.isDefaultNotification}
            onChange={checked => onUpdateNotiSettings({ isDefaultNotification: checked })}
          ></Switch>
        </SettingWrapper>
        {!notificationStylesSettings?.isDefaultNotification && (
          <Fragment>
            <Text color="#333" bold>
              {t(translations.reminderNotificationStyling.title)}
            </Text>
            <BackgroundSetting
              label={t(translations.style.title)}
              settings={getBackgroundSettings(notificationStylesSettings as any)}
              styles={{
                background: blockStyles.background as string,
              }}
              onChange={onUpdateNotiSettings}
            />
            <Select
              label={t(translations.position.title)}
              value={notificationStylesSettings?.position}
              options={Object.values(POSITION_OPTIONS)}
              onChange={value => onUpdateNotiSettings({ position: value })}
            />
            <FontSettingPopover
              styles={notificationStyles}
              settingsStyle={notificationStylesSettings}
              onChange={onUpdateNotiSettings}
            />

            <BoxShadowSetting
              settings={getBoxShadowSettings(notificationStylesSettings)}
              onChange={onUpdateNotiSettings}
            />

            <BorderSettingPopover
              settings={getBorderSettings(notificationStylesSettings)}
              styles={getBorderStyles(notificationStyles)}
              onChange={onUpdateNotiSettings}
            />

            <RoundedCornersSetting
              settings={getRoundedCornersSettings(notificationStylesSettings)}
              styles={getRoundedCornersStyles(notificationStyles)}
              onChange={onUpdateNotiSettings}
            />

            <SpacingSetting
              settings={getSpacingSettings(notificationStylesSettings)}
              styles={getSpacingStyles(notificationStyles)}
              onChange={onUpdateNotiSettings}
            />
          </Fragment>
        )}
      </Space>
    );
  }

  return null;
};
