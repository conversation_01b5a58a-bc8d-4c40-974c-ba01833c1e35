// Libraries
import React, { memo, useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep, get, keyBy } from 'lodash';

// Atoms
import { Button, Icon, Input, Text } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../molecules';
import { Modal, Select } from 'app/components/molecules';

// Common
import { InputBoolean } from './components/common/InputBoolean';
import { InputNumberBetween } from './components/common/InputNumberBetween';
import { InputDateTimeAgo } from './components/common/InputDateTimeAgo';
import { InputCalendar } from './components/common/InputCalendar';
import { InputSelectMulti } from './components/common/InputSelectMulti';
import { InputOrSelect } from './components/common/InputOrSelect';
import { InputArray } from './components/common/InputArray';
import { InputNumber } from './components/common/InputNumber';
import { InputCalendarBetween } from './components/common/InputCalendarBetween';

//Queries
import { useGetListBO } from 'app/queries/BusinessObject';
import { useGetListAttributeBO } from 'app/queries/BusinessObject/useGetListAttributeBO';

// Styled
import { FilterSettingWrapper, RowCondition, FilterBlockWrapper } from './styled';
import { OPERATORS, SEGMENT_IDS } from './constants';

// Translations
import { translations } from 'locales/translations';

// Utils
import {
  getInputDefaultValue,
  getInputType,
  isInputDateTimeAgo,
  isInputDateTimeBetweenAgo,
  validateFilters,
} from './utils';
import { handleError } from 'app/utils/handleError';
import {
  buildOptionAttrArchive,
  checkStatusCollection,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';

// Services
import { getListCollectionsBO } from 'app/services/MediaTemplateDesign/BusinessObject';
import {
  CustomerMetadata,
  EventMetadata,
  TFilter,
  TFilters,
  TOperatorValue,
  VisitorMetadata,
} from '../../../../../../types';
import { isCheckStatusAttr } from '../../molecules/DynamicSetting/constants';
import { FilterContext } from './context';
import { InputDateTimeBetweenAgo } from './components/common/InputDateTimeBetweenAgo';

// Constants
import { OPERATORS_OPTION } from './constants';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FilterSetting/index.tsx';

interface FilterSettingProps {
  boId: number;
  filters: TFilters;
  isFormatDateTimeMilliseconds?: boolean;
  onOKChangeFilter?: (conditions: TFilters) => void;
}

export const FilterSetting: React.FC<FilterSettingProps> = memo(props => {
  // Translations
  const { t } = useTranslation();

  const { filters, boId: itemTypeId, isFormatDateTimeMilliseconds } = props;

  //Queries
  const { data: listBO } = useGetListBO();
  const { data: listAttr } = useGetListAttributeBO<any>({
    itemTypeIds: [itemTypeId],
    options: {
      select(data) {
        const { rows = [] } = data || {};

        return rows[0]?.properties || [];
      },
    },
  });

  // Memo
  const itemTypeName = listBO?.find(({ id }) => id === itemTypeId)?.name;

  // Variables
  const filterContextValue = {
    itemTypeId,
    itemTypeName,
    isFormatDateTimeMilliseconds,
  };

  // States
  const [conditions, setConditions] = useState(filters);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [errors, setErrors] = useState<any>({
    OR: [],
    isError: false,
  });

  const error = {
    column: true,
    operator: true,
    value: true,
  };

  let and = {
    AND: [
      {
        operator: null,
        data_type: null,
        column: null,
        condition_type: 'comp_attr',
        value_type: 'normal',
        value: null,
        extend: null,
      },
    ],
  };

  useEffect(() => {
    if (isModalVisible) {
      // Neu filter co OR la [] thì bổ sung thêm object and vào Array của OR để khi mở filter lên sẽ tự động render ra hàng filter có sẵn đầu tiên
      // Tuy nhien payload vẫn là OR : []
      if (!filters.OR.length) {
        setErrors({ ...errors, OR: [...errors.OR, { AND: [error] }] });
        setConditions({ ...filters, OR: [and as any] });
      } else {
        setConditions(filters);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalVisible]);

  const onChangeFilter = (newFilters: TFilters) => {
    try {
      setConditions(newFilters);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onChangeFilter.name,
        args: {},
      });
    }
  };

  const onChangeAttr = (item: TFilter, orIndex: number, andIndex: number) => {
    const newConditions = cloneDeep(conditions);
    const property = listAttr.find(attr => attr.itemPropertyName === item.column);

    if (property) {
      const { itemPropertyName, dataType } = property;
      const defaultOperator = OPERATORS[dataType][0].value as TOperatorValue;
      const defaultValue = getInputDefaultValue({
        operator: defaultOperator,
        dataType,
        property,
      });

      newConditions.OR[orIndex].AND[andIndex] = {
        ...newConditions.OR[orIndex].AND[andIndex],
        column: itemPropertyName,
        data_type: dataType,
        operator: defaultOperator,
        value: defaultValue,
        extend: [],
      };
      onChangeFilter(newConditions);
      onResetError(orIndex, andIndex);
    }
  };

  const onChangeOperator = (item: TFilter, orIndex: number, andIndex: number) => {
    const newConditions = cloneDeep(conditions);
    const property = listAttr.find(attr => attr.itemPropertyName === item.column) || {};
    if (property) {
      const defaultValue = getInputDefaultValue({
        operator: item.operator,
        dataType: item.data_type,
        property,
      });

      if (
        isInputDateTimeAgo(item.operator, item.data_type) ||
        isInputDateTimeBetweenAgo(item.operator, item.data_type)
      ) {
        newConditions.OR[orIndex].AND[andIndex].operator = item.operator;
        newConditions.OR[orIndex].AND[andIndex].value = defaultValue.value;
        newConditions.OR[orIndex].AND[andIndex].time_unit = defaultValue.time_unit;
      } else {
        newConditions.OR[orIndex].AND[andIndex].operator = item.operator;
        newConditions.OR[orIndex].AND[andIndex].value = defaultValue;
      }

      onChangeFilter(newConditions);
      onResetError(orIndex, andIndex);
    }
  };

  const onChangeValue = (value: any, orIndex: number, andIndex: number, key: string = 'value', key2?: string) => {
    const newConditions = cloneDeep(conditions);
    newConditions.OR[orIndex].AND[andIndex] = { ...newConditions.OR[orIndex].AND[andIndex], [key]: value };
    switch (key) {
      case 'extend':
        const valueTmp = newConditions.OR[orIndex].AND[andIndex].value.concat(value);
        newConditions.OR[orIndex].AND[andIndex] = { ...newConditions.OR[orIndex].AND[andIndex], value: valueTmp };
        break;
      case 'value':
        if (newConditions.OR[orIndex].AND[andIndex].extend.length > 0) {
          const valueTmp = newConditions.OR[orIndex].AND[andIndex].value.concat(
            newConditions.OR[orIndex].AND[andIndex].extend,
          );
          newConditions.OR[orIndex].AND[andIndex] = { ...newConditions.OR[orIndex].AND[andIndex], value: valueTmp };
        }
        break;
      default:
        break;
    }
    onChangeFilter(newConditions);
    onResetError(orIndex, andIndex);
  };

  const onChangeMultipleValue = (value: any, orIndex: number, andIndex: number) => {
    const newConditions = cloneDeep(conditions);

    newConditions.OR[orIndex].AND[andIndex] = { ...newConditions.OR[orIndex].AND[andIndex], ...value };
    onChangeFilter(newConditions);
    onResetError(orIndex, andIndex);
  };

  const onResetError = (orIndex: number, andIndex: number) => {
    const newErrors = { ...errors };

    if (newErrors.OR[orIndex] && newErrors.OR[orIndex].AND[andIndex]) {
      const error = {
        column: true,
        operator: true,
        value: true,
        extend: true,
      };
      newErrors.OR[orIndex].AND[andIndex] = error;
      setErrors(newErrors);
    }
  };

  const onAddAnd = (orIndex: number) => {
    const newConditions = cloneDeep(conditions);

    newConditions.OR[orIndex].AND.push({
      value: null,
      operator: null,
      data_type: null,
      condition_type: 'comp_attr',
      column: null,
      value_type: 'normal',
    });
    onChangeFilter(newConditions);
    const andError = cloneDeep(errors);
    andError.OR[orIndex].AND.push(error);
    setErrors(andError);
  };

  const onAddOr = () => {
    const newConditions = cloneDeep(conditions);

    newConditions.OR.push({
      AND: [
        {
          value: null,
          operator: null,
          data_type: null,
          // itemTypeId: null,
          condition_type: 'comp_attr',
          column: null,
          value_type: 'normal',
        },
      ],
    });
    setErrors({ ...errors, OR: [...errors.OR, { AND: [error] }] });
    onChangeFilter(newConditions);
  };

  const onRemoveCondition = (orIndex: number, andIndex: number) => {
    const newConditions = cloneDeep(conditions);

    if (newConditions.OR.length === 1 && newConditions.OR[0].AND.length === 1) {
      onChangeFilter({ ...newConditions, OR: [and as any] });
    } else {
      if (newConditions.OR[orIndex].AND.length <= 1) {
        // removeOr
        newConditions.OR.splice(orIndex, 1);
        const orError = [...errors.OR];
        orError.splice(orIndex, 1);
        setErrors({ ...errors, OR: orError });
      } else {
        // removeAnd
        newConditions.OR[orIndex].AND.splice(andIndex, 1);
        const andErrorData = cloneDeep(errors);

        if (get(andErrorData, `OR[${orIndex}].AND`, null)) {
          andErrorData.OR[orIndex].AND.splice(andIndex, 1);
          setErrors(andErrorData);
        }
      }
      onChangeFilter(newConditions);
    }
  };
  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    const errs = validateFilters(conditions);
    if (errs.isError) {
      setErrors(errs);
    } else {
      setIsModalVisible(false);

      props.onOKChangeFilter && props.onOKChangeFilter(conditions);

      if (conditions.OR.length === 1) {
        // xử lý dữ liệu khi vào store khi ấn Apply nếu OR không có filter nào thì sẽ là OR : []
        if (!conditions.OR[0].AND[0].column) {
          let dataFilter = { OR: [] };
          setIsModalVisible(false);
          props.onOKChangeFilter && props.onOKChangeFilter(dataFilter);
        } else {
          setIsModalVisible(false);
          props.onOKChangeFilter && props.onOKChangeFilter(conditions);
        }
      } else {
        setIsModalVisible(false);
        props.onOKChangeFilter && props.onOKChangeFilter(conditions);
      }
      setErrors({ OR: [], isError: false });
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setErrors({ OR: [], isError: false });
  };

  const numberOfFilters = filters.OR.flat()
    .map(({ AND }) => AND)
    .flat()
    .filter(({ column }) => !!column).length;

  return (
    <FilterContext.Provider value={filterContextValue}>
      <FilterSettingWrapper>
        <SettingWrapper
          label={`${t(translations.filter.title)} ${numberOfFilters ? `(${numberOfFilters})` : ''}`}
          labelClassName="ants-font-bold"
        >
          <Button icon={<Icon type="icon-ants-edit-2" />} type="text" onClick={showModal} />
          <FilterSettingModal
            isModalVisible={isModalVisible}
            handleOk={handleOk}
            handleCancel={handleCancel}
            listAttr={listAttr}
            listBO={listBO as any}
            conditions={conditions}
            onChangeAttr={onChangeAttr}
            onChangeOperator={onChangeOperator}
            onChangeValue={onChangeValue}
            onChangeMultipleValue={onChangeMultipleValue}
            onAddAnd={onAddAnd}
            onAddOr={onAddOr}
            onRemoveCondition={onRemoveCondition}
            errors={errors}
          />
        </SettingWrapper>
      </FilterSettingWrapper>
    </FilterContext.Provider>
  );
});

interface FilterSettingModalProps extends FilterBlockProps {
  isModalVisible: boolean;
  handleOk: any;
  handleCancel: any;
}
export const FilterSettingModal: React.FC<FilterSettingModalProps> = props => {
  const {
    isModalVisible,
    handleOk,
    handleCancel,
    listAttr,
    conditions,
    onChangeAttr,
    onChangeOperator,
    onChangeValue,
    onAddAnd,
    onAddOr,
    onRemoveCondition,
    onChangeMultipleValue,
    errors,
    listBO,
  } = props;
  const { t } = useTranslation();

  return (
    <Modal
      destroyOnClose
      title={t(translations.filter.title)}
      visible={isModalVisible}
      width={1200}
      bodyStyle={{
        maxHeight: '70vh',
        overflow: 'auto',
      }}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={[
        <Button key="submit" type="primary" onClick={handleOk}>
          {t(translations.apply.title)}
        </Button>,
        <Button key="back" onClick={handleCancel}>
          {t(translations.cancel.title)}
        </Button>,
      ]}
    >
      <FilterBlock
        listBO={listBO}
        listAttr={listAttr}
        conditions={conditions}
        onChangeAttr={onChangeAttr}
        onChangeOperator={onChangeOperator}
        onChangeValue={onChangeValue}
        onAddAnd={onAddAnd}
        onAddOr={onAddOr}
        onRemoveCondition={onRemoveCondition}
        onChangeMultipleValue={onChangeMultipleValue}
        errors={errors}
      />
    </Modal>
  );
};

interface FilterBlockProps {
  listAttr: any[];
  listBO: any[];
  conditions: TFilters;
  onChangeAttr: (item: TFilter, orIndex: number, andIndex: number) => void;
  onChangeOperator: (item: TFilter, orIndex: number, andIndex: number) => void;
  onChangeValue: (value: any, orIndex: number, andIndex: number, key: string, key2?: string) => void;
  onAddAnd: (orIndex: number) => void;
  onAddOr: () => void;
  onRemoveCondition: (orIndex: number, andIndex: number) => void;
  onChangeMultipleValue: (value: any, orIndex: number, andIndex: number) => void;
  errors: any;
}
export const FilterBlock: React.FC<FilterBlockProps> = props => {
  const {
    listAttr,
    conditions,
    onChangeAttr,
    onChangeOperator,
    onChangeValue,
    onAddAnd,
    onAddOr,
    onRemoveCondition,
    onChangeMultipleValue,
    listBO,
    errors,
  } = props;

  const { itemTypeId, itemTypeName } = useContext(FilterContext);

  // Translation
  const { t } = useTranslation();

  const optionsAttr = useMemo(
    () =>
      listAttr?.map(attr => ({
        value: attr.itemPropertyName,
        label: attr.itemPropertyDisplay,
        disabled: parseInt(attr.status) === 4,
        status: parseInt(attr.status),
      })),
    [listAttr],
  );

  const [listCollection, setListCollection] = useState<any[]>([]);

  useEffect(() => {
    if (itemTypeId === 1) {
      getListCollections();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemTypeId]);

  const getListCollections = async () => {
    const { rows } = await getListCollectionsBO({ itemTypeId });
    const filters = rows.map(item => ({
      value: item.value,
      id: item.value,
      label: item.label,
      disabled: parseInt(item.model.status) === 4,
      status: parseInt(item.model.status),
    }));
    setListCollection(filters);
  };

  const mapListAttr = useMemo(() => {
    return keyBy(listAttr, 'itemPropertyName');
  }, [listAttr]);

  const renderInputValue = (item: TFilter, orIndex: number, andIndex: number) => {
    const {
      column,
      value,
      data_type,
      operator,
      value_type,
      event_metadata = {} as EventMetadata,
      customer_metadata = {} as CustomerMetadata,
      visitor_metadata = {} as VisitorMetadata,
      time_unit,
      extend,
    } = item;

    const isMatchOperator = [OPERATORS_OPTION.MATCHES.value, OPERATORS_OPTION.NOT_MATCHES.value].includes(
      operator || '',
    );

    const onChangeValueItem = (val: any, key: string = 'value', key2?: string) => {
      onChangeValue(val, orIndex, andIndex, key, key2);
    };
    const onChangeMultipleValueItem = (val: any) => {
      onChangeMultipleValue(val, orIndex, andIndex);
    };

    const property = listAttr.find(item => item.itemPropertyName === column);

    const { errorMessage } = checkStatusCollection({
      listBO,
      itemTypeId: itemTypeId || 0,
      listCollection,
      field: value,
      checkDisable: true,
    });

    const element = getInputType({
      operator,
      dataType: data_type,
      property,
      resInputBoolean: <InputBoolean value={value} onChange={onChangeValueItem as any} />,
      resInputNumberBetween: <InputNumberBetween value={value} onChange={onChangeValueItem} />,
      resInputCalendarBetween: <InputCalendarBetween value={value} onChange={onChangeValueItem} />,
      resInputDateTimeBetweenAgo: (
        <InputDateTimeBetweenAgo
          value={value}
          timeUnit={time_unit}
          onChange={value => onChangeMultipleValueItem(value)}
        />
      ),
      resInputDateTimeAgo: (
        <InputDateTimeAgo
          value={value}
          timeUnit={time_unit}
          onChangeMultipleValue={value =>
            onChangeMultipleValueItem({
              ...value,
              ...(value.value !== undefined && {
                value: String(value.value),
              }),
            })
          }
        />
      ),
      resInputCalendar: <InputCalendar value={value} operator={operator} onChange={onChangeValueItem} />,
      resInputSelectMulti: (
        <InputSelectMulti
          value={value}
          extend={extend}
          eventMetadata={event_metadata}
          customerMetadata={customer_metadata}
          visitorMetadata={visitor_metadata}
          valueType={value_type}
          onChange={onChangeValueItem}
          onChangeMultipleValue={onChangeMultipleValueItem}
          itemTypeId={itemTypeId}
          itemTypeName={itemTypeName}
          errorMessage={column === SEGMENT_IDS ? errorMessage : ''}
          // isDisable={isDisableCollection}
          column={column}
        />
      ),
      resInputOrSelect: (
        <InputOrSelect
          value={value}
          onChangeMultipleValue={onChangeMultipleValueItem}
          eventMetadata={event_metadata}
          customerMetadata={customer_metadata}
          visitorMetadata={visitor_metadata}
          valueType={value_type}
          dataType={data_type}
          useArray={isMatchOperator}
        />
      ),
      resInputArray: <InputArray value={value} onChange={onChangeValueItem} />,
      resInputNumber: <InputNumber value={value} onChange={onChangeValueItem} />,
      resDefault: (
        <Input
          value={value}
          onChange={e => onChangeValueItem(e.target.value)}
          placeholder={t(translations.inputYourValue.title)}
        />
      ),
    });
    return element;
  };
  return (
    <div>
      {conditions.OR.map((or, orIndex) => {
        return (
          <React.Fragment key={orIndex}>
            {orIndex !== 0 ? <Text>{t(translations.or.title).toUpperCase()}</Text> : null}
            <FilterBlockWrapper>
              {or.AND.map((and, andIndex) => {
                const { errorMessage, isDisable } = isCheckStatusAttr(listBO, itemTypeId, optionsAttr, and.column);
                return (
                  <div key={`${and.column}-${andIndex}`} className="ants-p-5">
                    <RowCondition>
                      <div>
                        <Select
                          showSearch
                          disabled={isDisable}
                          options={buildOptionAttrArchive(optionsAttr, and.column)}
                          value={and.column}
                          onChange={value => onChangeAttr({ ...and, column: value }, orIndex, andIndex)}
                          placeholder={t(translations.selectAnItem.title)}
                          status={
                            // Nếu chỉ có 1 OR và 1 AND thì là không filter nên sẽ ko báo lỗi required
                            !and.column && !get(errors, `OR[${orIndex}].AND[${andIndex}].column`, true)
                              ? conditions.OR.length === 1 && conditions.OR[0].AND.length === 1
                                ? undefined
                                : 'error'
                              : undefined
                          }
                          errorArchive={errorMessage}
                          errorMsg={t(translations.thisFieldIsRequired.title)}
                        />
                      </div>
                      <div>
                        {and.column ? (
                          <Select
                            showSearch
                            options={
                              and.data_type === 'string' && mapListAttr[and.column]?.autoSuggestion === 1
                                ? OPERATORS['suggestion']
                                : OPERATORS[and.data_type!]
                            }
                            value={and.operator}
                            onChange={value => onChangeOperator({ ...and, operator: value }, orIndex, andIndex)}
                            placeholder={t(translations.selectAnItem.title)}
                            status={
                              !and.operator && !get(errors, `OR[${orIndex}].AND[${andIndex}].operator`, true)
                                ? 'error'
                                : undefined
                            }
                            errorMsg={t(translations.thisFieldIsRequired.title)}
                          />
                        ) : null}
                      </div>
                      <div>
                        {and.operator ? (
                          <>
                            <div className="ants-mt-0.5">{renderInputValue(and, orIndex, andIndex)}</div>
                            {!and.value &&
                            !get(errors, `OR[${orIndex}].AND[${andIndex}].value`, true) &&
                            !get(errors, `OR[${orIndex}].AND[${andIndex}].extend`, true) ? (
                              <Text color="#ff4d4f" className="ants-ml-2">
                                {t(translations.valueFilterEmpty.title)}
                              </Text>
                            ) : null}
                          </>
                        ) : null}
                      </div>
                      <div>
                        {andIndex === or.AND.length - 1 ? (
                          <Button
                            onClick={() => onAddAnd(orIndex)}
                            type="default"
                            className="ants-mt-1 ants-rounded-full ants-border-primary ants-w-16 ants-justify-self-center"
                            disabled={or.AND.length >= 10}
                          >
                            + {t(translations.and.title).toUpperCase()}
                          </Button>
                        ) : null}
                      </div>
                      <div>
                        {conditions.OR.length && conditions.OR[0].AND.length > 0 ? (
                          // Khi chỉ có 1 filter. nếu column có giá trị thì sẽ hiện thị nút xóa để xóa tất các giá trị đc chọn ở hàng filter đó
                          // Sau khi data của filter đó đc xóa đi thì sẽ ẩn đi nút xóa
                          conditions.OR.length === 1 &&
                          conditions.OR[0].AND.length === 1 &&
                          !conditions.OR[0].AND[0].column ? null : (
                            <Button
                              onClick={() => onRemoveCondition(orIndex, andIndex)}
                              type="text"
                              icon={<Icon type="icon-ants-remove-slim" size={15} />}
                              className="ants-mt-0.5 ants-justify-self-end"
                            />
                          )
                        ) : null}
                      </div>
                    </RowCondition>
                    {+mapListAttr[and.column!]?.status === 2 ? (
                      <Text color="#f3a303" className="ants-ml-2">
                        {t(translations.attrDisabled.description)}
                      </Text>
                    ) : null}
                  </div>
                );
              })}
            </FilterBlockWrapper>
          </React.Fragment>
        );
      })}
      <div>
        <Button
          onClick={onAddOr}
          type="default"
          className="ants-rounded-full ants-border-primary ants-w-16 ants-justify-self-center"
          disabled={conditions.OR.length >= 10}
        >
          + {t(translations.or.title).toUpperCase()}
        </Button>
      </div>
    </div>
  );
};
