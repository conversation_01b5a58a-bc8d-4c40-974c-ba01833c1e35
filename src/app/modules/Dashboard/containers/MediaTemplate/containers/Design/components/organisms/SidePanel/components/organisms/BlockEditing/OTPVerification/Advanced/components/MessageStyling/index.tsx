import { Divider, Space, Text } from 'app/components/atoms';
import { handleError } from 'app/utils/handleError';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { InputNumber, Select, SliderWithUnit } from 'app/components/molecules';
import BackgroundSetting from '../../../../../BackgroundSetting';
import {
  getBackgroundSettings,
  getBackgroundStyles,
  getBorderSettings,
  getBorderStyles,
  getBoxShadowSettings,
  getRoundedCornersSettings,
  getRoundedCornersStyles,
  getSpacingSettings,
  getSpacingStyles,
} from '../../../../../../../utils';
import { BorderSettingPopover } from '../../../../../BorderSetting';
import { RoundedCornersSetting } from '../../../../../RoundedCornersSetting';
import { SpacingSetting } from '../../../../../SpacingSetting';
import { FontSettingPopover } from '../../../../../FontSetting';
import { BoxShadowSetting } from '../../../../../BoxShadowSetting';
import { SettingWrapper } from '../../../../../../molecules';

type TMessageStyling = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/OTPVerification/Advanced/components/MessageStyling/index.tsx';

export const MessageStyling: React.FC<TMessageStyling> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const { messages } = settings;

  const onUpdateMessageStyleSettings = (settings = {}, styles = {}, type = '') => {
    try {
      onChange({
        messages: {
          ...messages,
          [type]: {
            ...messages[type],
            blockStyles: {
              ...messages[type].blockStyles,
              ...styles,
            },
            blockSettings: {
              ...messages[type].blockSettings,
              ...settings,
            },
          },
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onUpdateMessageStyleSettings',
        args: {},
      });
    }
  };

  const renderMessageStyling = (type: string) => {
    const configType = messages[type];
    return (
      <Space size={10} direction="vertical">
        <SettingWrapper label={t(translations.displayIn.title)}>
          <div className="ants-flex ants-items-center ants-gap-1">
            <InputNumber
              required
              value={configType?.displayIn}
              onChange={value =>
                onChange({
                  messages: {
                    ...messages,
                    [type]: {
                      ...messages[type],
                      displayIn: value,
                    },
                  },
                })
              }
            />
            <span style={{ fontSize: 11 }}>seconds</span>
          </div>
        </SettingWrapper>
        <BackgroundSetting
          label={t(translations.background.title)}
          settings={getBackgroundSettings(configType?.blockSettings)}
          styles={getBackgroundStyles(configType?.blockStyles)}
          onChange={(settings, styles) => onUpdateMessageStyleSettings(settings, styles, type)}
        />
        <FontSettingPopover
          styles={configType?.blockStyles}
          settingsStyle={configType?.blockSettings}
          onChange={(settings, styles) => onUpdateMessageStyleSettings(settings, styles, type)}
        />
        <BoxShadowSetting
          settings={getBoxShadowSettings(configType?.blockSettings)}
          onChange={(settings, styles) => onUpdateMessageStyleSettings(settings, styles, type)}
        />

        <BorderSettingPopover
          settings={getBorderSettings(configType?.blockSettings)}
          styles={getBorderStyles(configType?.blockStyles)}
          onChange={(settings, styles) => onUpdateMessageStyleSettings(settings, styles, type)}
        />

        <RoundedCornersSetting
          settings={getRoundedCornersSettings(configType?.blockSettings)}
          styles={getRoundedCornersStyles(configType?.blockStyles)}
          onChange={(settings, styles) => onUpdateMessageStyleSettings(settings, styles, type)}
        />

        <SpacingSetting
          settings={getSpacingSettings(configType?.blockSettings)}
          styles={getSpacingStyles(configType?.blockStyles)}
          onChange={(settings, styles) => onUpdateMessageStyleSettings(settings, styles, type)}
        />
      </Space>
    );
  };

  return (
    <Space size={5} direction="vertical">
      <Text size={14} bold>
        {t(translations.failedVerification.title)}
      </Text>
      {renderMessageStyling('failedVerification')}
      <Divider className="!ants-my-0" />
      <Text size={14} bold>
        {t(translations.resentOTP.title)}
      </Text>
      {renderMessageStyling('resentOTP')}
    </Space>
  );
};
