// Libraries
import React, { memo, ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { cloneDeep, get } from 'lodash';
import { useTranslation } from 'react-i18next';

// Icons
import { PrefixAutoIndex } from 'app/components/icons';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { Modal, ModalProps } from 'app/components/molecules/Modal';
import { Form, InputNumber } from 'app/components/molecules';

// Utils
import { handleError } from 'app/utils/handleError';
import { getAllChildrenBlockId } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';

// Slice
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import {
  selectCurrentBlocks,
  selectCurrentTree,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import { STANDARDS_BLOCKS } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Locales
import { translations } from 'locales/translations';

// Styled
import { StyledBoxIndex, StyledIndexButton } from './styled';

const { SLIDE_SHOW, BUTTON, IMAGE, VIDEO, YES_NO, OPTIN_FIELDS, RATING, TEXT } = STANDARDS_BLOCKS;

interface SetAutoIndexButtonProps {
  color?: string;
  hoverColor?: string;
  blockId: string;
  children?: ReactNode;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SetAutoIndexButton/index.tsx';

export const SetAutoIndexButton: React.FC<SetAutoIndexButtonProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // Actions
  const { updateCurrentPageTreeBlocks } = mediaTemplateDesignActions;

  // Props
  const { children, blockId, color, hoverColor } = props;

  // State
  const [indexModal, setIndexModal] = useState({
    visible: false,
  });

  // Selectors
  const blocks = useSelector(selectCurrentBlocks);
  const tree = useSelector(selectCurrentTree);

  const handleSetIndex = ({ index: autoIndex }) => {
    try {
      let dataUpdateBlocks: any[] = [];

      let draftTree = cloneDeep(tree);

      // Exclude Slide show block
      draftTree[blockId] = draftTree[blockId].filter(blockId => blocks[blockId]?.type !== SLIDE_SHOW.name);

      const childrenBlockIds = getAllChildrenBlockId({ tree: draftTree, blockId });

      childrenBlockIds.forEach(childrenBlockId => {
        if (childrenBlockId !== blockId) {
          const childrenBlock = blocks[childrenBlockId] || {};
          const blockSettings = cloneDeep(childrenBlock.settings) || {};

          // Set auto index for display condition
          if (!!get(blockSettings, 'blockStylesSettings.displayCondition.condition', '')) {
            dataUpdateBlocks.push({
              fieldPath: `${childrenBlock.id}.settings.blockStylesSettings.displayCondition.index`,
              data: autoIndex,
            });
          }

          // Set auto index for Button, Yes/No, Image, Video, Rating, Text Block
          switch (childrenBlock.type) {
            case BUTTON.name:
            case RATING.name:
            case VIDEO.name:
            case OPTIN_FIELDS.name:
            case IMAGE.name:
              let { dynamic } = blockSettings;

              Object.keys({ ...dynamic }).forEach((key: string) => {
                if (dynamic[key]?.isDynamic && dynamic[key]?.index != null) {
                  dynamic[key].index = autoIndex;
                }
              });

              dataUpdateBlocks.push({
                fieldPath: `${childrenBlock.id}.settings.dynamic`,
                data: dynamic,
              });

              break;
            case YES_NO.name:
              let { yesDynamic, noDynamic } = blockSettings;

              Object.keys({ ...yesDynamic }).forEach((key: string) => {
                if (yesDynamic[key]?.isDynamic && yesDynamic[key]?.index != null) {
                  yesDynamic[key].index = autoIndex;
                }
              });

              Object.keys({ ...noDynamic }).forEach((key: string) => {
                if (noDynamic[key]?.isDynamic && noDynamic[key]?.index != null) {
                  noDynamic[key].index = autoIndex;
                }
              });

              dataUpdateBlocks = dataUpdateBlocks.concat([
                {
                  fieldPath: `${childrenBlock.id}.settings.yesDynamic`,
                  data: yesDynamic,
                },
                {
                  fieldPath: `${childrenBlock.id}.settings.noDynamic`,
                  data: noDynamic,
                },
              ]);

              break;
            case TEXT.name:
              const dynamicData = get(blockSettings, 'dynamic.data', {});
              const linkData = get(blockSettings, 'link.data', {});

              Object.keys({ ...dynamicData }).forEach((key: any) => {
                const { index } = dynamicData[key];
                if (dynamicData[key] && index != null) {
                  dynamicData[key].index = autoIndex;
                }
              });

              Object.keys({ ...linkData }).forEach((key: any) => {
                const { index } = linkData[key];
                if (linkData[key] && index != null) {
                  linkData[key].index = autoIndex;
                }
              });

              dataUpdateBlocks = dataUpdateBlocks.concat([
                {
                  fieldPath: `${childrenBlock.id}.settings.dynamic.data`,
                  data: dynamicData,
                },
                {
                  fieldPath: `${childrenBlock.id}.settings.link.data`,
                  data: linkData,
                },
              ]);

              break;
            default:
              break;
          }
        }
      });

      // Update defaultIndex of block
      dataUpdateBlocks.push({
        fieldPath: `${blockId}.settings.defaultIndex`,
        data: autoIndex,
      });

      dispatch(
        updateCurrentPageTreeBlocks({
          blocks: dataUpdateBlocks,
        }),
      );

      setIndexModal(indexModal => ({
        ...indexModal,
        visible: false,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleSetIndex',
        args: {},
      });
    }
  };

  return (
    <>
      <StyledIndexButton
        color={color}
        hoverColor={hoverColor}
        onClick={() => setIndexModal(indexModal => ({ ...indexModal, visible: true }))}
      >
        <div>
          <PrefixAutoIndex />
        </div>
        <StyledBoxIndex>
          <Text size={10} style={{ fontWeight: 'bold' }}>
            {blocks[blockId]?.settings.defaultIndex || 1}
          </Text>
        </StyledBoxIndex>
        {children}
      </StyledIndexButton>

      <SetAutoIndexModal
        defaultIndex={blocks[blockId]?.settings.defaultIndex || 1}
        visible={indexModal.visible}
        onSummit={({ index }) => handleSetIndex({ index })}
        onCancel={() => setIndexModal(indexModal => ({ ...indexModal, visible: false }))}
      />
    </>
  );
});

interface SetAutoIndexModalProps extends ModalProps {
  defaultIndex: number;
  onSummit: (values: any) => void;
}

export const SetAutoIndexModal: React.FC<SetAutoIndexModalProps> = memo(props => {
  // Props
  const { onSummit, onCancel, defaultIndex, ...restOf } = props;

  // Form
  const [form] = Form.useForm();

  // I18n
  const { t } = useTranslation();

  // Effects
  useEffect(() => {
    if (props.visible) {
      form.setFieldsValue({
        index: defaultIndex,
      });
    }
  }, [form, defaultIndex, props.visible]);

  // Handlers
  const onFinishForm = values => {
    try {
      onSummit && onSummit(values);

      form.resetFields();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onFinishForm',
        args: {},
      });
    }
  };

  return (
    <Modal
      {...restOf}
      width={300}
      destroyOnClose
      title={t(translations.setIndex.setAutoIndex)}
      onOk={form.submit}
      onCancel={e => {
        onCancel && onCancel(e);

        form.resetFields();
      }}
    >
      <Form
        form={form}
        name="basic"
        labelCol={{ span: 0 }}
        wrapperCol={{ span: 16 }}
        initialValues={{ index: 1 }}
        onValuesChange={(_changedValues, values: any) => {
          const { index } = values || {};

          form.setFieldsValue({
            index: Math.floor(index),
          });
        }}
        onFinish={onFinishForm}
        // onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <div className="ants-flex ants-items-center ants-gap-2">
          <Form.Item
            label={t(translations.index.title)}
            name="index"
            style={{
              alignItems: 'center',
            }}
          >
            <InputNumber required min={1} max={90} className="ants-ml-2.5" />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
});

SetAutoIndexModal.defaultProps = {
  defaultIndex: 1,
};
