// Locales
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const SEGMENT_IDS = 'segment_ids';

export const DISPLAY_CONDITION = {
  NONE: {
    label: 'None',
    value: '',
  },
  SHOW_WHEN: {
    label: 'Show when',
    value: 'show_when',
  },
  HIDDEN_WHEN: {
    label: 'Hidden when',
    value: 'hidden_when',
  },
};

export const VALUE_TYPE = {
  EVENT: {
    index: 2,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.eventAttr),
    value: 'event',
  },
  CUSTOMER: {
    index: 1,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.customerAttr),
    value: 'customer',
    name: 'customers',
    id: -1003,
  },

  VISITOR: {
    index: 0,
    label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.visitorAttr),
    value: 'visitor',
    name: 'visitors',
    id: -1007,
  },
};

export const DEFAULT_CUSTOMER_META_DATA = {
  item_type_id: VALUE_TYPE.CUSTOMER.id,
  item_type_name: VALUE_TYPE.CUSTOMER.name,
  item_property_name: '',
};

export const DEFAULT_VISITOR_META_DATA = {
  item_type_id: VALUE_TYPE.VISITOR.id,
  item_type_name: VALUE_TYPE.VISITOR.name,
  item_property_name: '',
};

export const DEFAULT_EVENT_META_DATA = {
  insight_property_id: null,
  insight_property_ids: null,
  event_action_id: null,
  event_category_id: null,
  event_tracking_name: null,
  item_type_id: null,
  item_type_name: null,
  item_property_name: null,
  item_property_label: null,
  event_property_syntax: null,
  useBo: true,
};
