import { TNumberFormatSettings } from './components/FormatNumber';
import Currencies from './currencies.json';
import currencyFormatter from 'currency-formatter';
import dayjs from 'dayjs';
import { TDatetimeFormatSetting } from './components/FormatDatetime';
import { translations } from 'locales/translations';

// Constants
import {
  ATTRIBUTE_STATUS,
  DYNAMIC_CONTENT_SETTING_KEY,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { LabeledTreeValue, LabeledValue } from '.';
import { getDataTableBOQuery } from 'app/queries/BusinessObject';
import { queryClient } from 'index';
import { QUERY_KEYS } from 'constants/queries';
import { TDataTableBO } from 'app/services/ThirdParty/TableBO';
import { get, has, set } from 'lodash';
import { i18n } from 'i18next';
import { ListAttribute } from 'app/models/BusinesObject';
import { TDataType } from '../../../../../../types';
import { getTranslateMessage } from 'utils/messages';

export function formatDatatime() {
  return 'datetime formated';
}

export function formatCompactDF(num: number, digits: number): string {
  const lookup = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'K' },
    { value: 1e6, symbol: 'M' },
    { value: 1e9, symbol: 'G' },
    { value: 1e12, symbol: 'T' },
    { value: 1e15, symbol: 'P' },
    { value: 1e18, symbol: 'E' },
  ];
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  var item = lookup
    .slice()
    .reverse()
    .find(function (item) {
      if (num >= 0) {
        return num >= item.value;
      } else {
        return num <= item.value * -1;
      }
    });
  return item ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol : '0';
}

export function formatNumberDF(
  number: number,
  fType: 'number' | 'percentage' | 'currency',
  fOptions: Omit<TNumberFormatSettings, 'type'> = {
    grouping: ',',
    decimal: '.',
    decimalPlaces: 0,
    isCompact: false,
    currencyCode: 'USD',
    prefixType: 'code',
  },
): string {
  const { grouping, decimal, decimalPlaces, isCompact, currencyCode, prefixType } = fOptions;

  let result = {
    textNumberFormated: String(number),
    hasSymbol: false,
    symbol: '',
    symbolOnLeft: false,
  };

  if (isCompact) {
    // 1234.56 => 1.23K
    const lookup = [
      { value: 1, symbol: '' },
      { value: 1e3, symbol: 'K' },
      { value: 1e6, symbol: 'M' },
      { value: 1e9, symbol: 'G' },
      { value: 1e12, symbol: 'T' },
      { value: 1e15, symbol: 'P' },
      { value: 1e18, symbol: 'E' },
    ];

    const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
    let item = lookup
      .slice()
      .reverse()
      .find(function (item) {
        if (number >= 0) {
          return number >= item.value;
        } else {
          return number <= item.value * -1;
        }
      });

    let formatedCompactNumber: string;
    if (item) {
      const compactNumber = (number / item.value).toFixed(decimalPlaces).replace(rx, '$1');
      formatedCompactNumber =
        currencyFormatter.format(Number(compactNumber), {
          decimal,
          precision: decimalPlaces,
        }) + item.symbol;
    } else {
      formatedCompactNumber = '0';
    }

    // 1.23K => 1.23K | 1.23% | 1.23K USD | $1.23K

    result.textNumberFormated = formatedCompactNumber;
  } else {
    result.textNumberFormated = currencyFormatter.format(number, {
      thousand: grouping === 'none' ? '' : grouping,
      decimal: decimal,
      precision: decimalPlaces,
    });
  }

  switch (fType) {
    case 'percentage':
      result.hasSymbol = true;
      result.symbol = '%';
      break;
    case 'currency': {
      result.hasSymbol = true;

      switch (prefixType) {
        case 'symbol': {
          const currency = Currencies[currencyCode];

          if (currency) {
            result.symbol = currency.symbol;
            result.symbolOnLeft = currency.symbolOnLeft;
          }
          break;
        }

        case 'code': {
          result.symbol = ` ${currencyCode}`;
          break;
        }
      }
      break;
    }
  }

  let html = result.textNumberFormated;

  if (result.hasSymbol) {
    if (result.symbolOnLeft) {
      html = `${result.symbol}${html}`;
    } else {
      html = `${html}${result.symbol}`;
    }
  }

  return html;
}

export function getStringFormatDateDF(formatSettings: TDatetimeFormatSetting): string {
  const { hasDateFormat, hasTimeFormat, timeParseFormat, dateParseFormat, timeParseOption, dateParseOption } =
    formatSettings;

  let dateFormat = '';
  let timeFormat = '';

  if (hasDateFormat) {
    switch (dateParseFormat) {
      case 'MM/DD/YYYY':
        if (dateParseOption === 'short') dateFormat = 'MM/DD/YYYY';
        if (dateParseOption === 'medium') dateFormat = 'MMM DD, YYYY';
        if (dateParseOption === 'long') dateFormat = 'MMMM DD, YYYY';
        break;
      case 'DD/MM/YYYY':
        if (dateParseOption === 'short') dateFormat = 'DD/MM/YYYY';
        if (dateParseOption === 'medium') dateFormat = 'DD MMM YYYY';
        if (dateParseOption === 'long') dateFormat = 'DD MMMM YYYY';
        break;
      case 'YYYY/MM/DD':
        if (dateParseOption === 'short') dateFormat = 'YYYY/MM/DD';
        if (dateParseOption === 'medium') dateFormat = 'YYYY, MMM DD';
        if (dateParseOption === 'long') dateFormat = 'YYYY, MMMM DD';
        break;
      default:
        break;
    }
  }

  if (hasTimeFormat) {
    if (timeParseFormat === '12hour') {
      if (timeParseOption === 'short') timeFormat = 'h:mm A';
      if (timeParseOption === 'medium') timeFormat = 'h:mm:ss A';
      if (timeParseOption === 'long') timeFormat = 'h:mm:ss A [GMT]';
    } else {
      if (timeParseOption === 'short') timeFormat = 'H:mm';
      if (timeParseOption === 'medium') timeFormat = 'H:mm:ss';
      if (timeParseOption === 'long') timeFormat = 'H:mm:ss [GMT]';
    }
  }

  return dateFormat + ' ' + timeFormat;
}

export function formatDatetimeDF(date: string | number | dayjs.Dayjs | Date, formatSettings: TDatetimeFormatSetting) {
  let formatedDate = '';

  const timezone = Math.round(dayjs(date).utcOffset() / 60);
  const prefix = timezone > 0 ? '+' : '';

  if (dayjs(date).isValid()) {
    formatedDate = dayjs(date)
      .locale(formatSettings.language || 'en')
      .format(getStringFormatDateDF(formatSettings));

    return formatedDate.replace(/\W*(GMT)\W*/, ` GMT ${prefix}${timezone}`)?.trim();
  }

  return date.toString()?.trim();
}

export function getAvailableAttrs(listAttribute: any[]) {
  return listAttribute?.filter(({ status }) => !ATTRIBUTE_STATUS[+status]) || [];
}

export const regexCSType = new RegExp(/^content-source::.*/);

export const serilizeBOAttr = (data: any, additionalAttrProperties?: string[]): LabeledValue[] => {
  const { rows = [] } = data || {};

  return (
    rows[0]?.properties.map((item: any) => {
      const temp = {
        value: item.itemPropertyName,
        label: item.translateLabel,
        status: item.status,
        disabled: parseInt(item.status) === 4,
        dataType: item.dataType,
      };

      additionalAttrProperties?.forEach(propName => {
        if (has(item, propName)) {
          temp[propName] = item[propName];
        }
      });

      return temp;
    }) || []
  );
};

export const serilizeDynamicContentAttr = (attrs: any[] = [], i18n: i18n, additionalAttrProperties?: string[]) =>
  attrs.map(attr => {
    const temp = {
      label:
        attr.label[i18n.language.toUpperCase()] || attr.label[get(attr, 'label.DEFAULT_LANG')] || attr.defaultLabel,
      value: attr.value,
      dataType: attr.dataType,
      disabled: parseInt(attr.status) === 4,
      status: attr.status,
    };

    additionalAttrProperties?.forEach(propName => {
      if (has(attr, propName)) temp[propName] = attr[propName];
    });

    return temp;
  });

export const serilizeEventAttr = (
  attrs: any[] = [],
  i18n: i18n,
  additionalAttrProperties?: string[],
): LabeledTreeValue[] => {
  return attrs.map(row => {
    const event = new ListAttribute(row);

    const temp = {
      value: `${event.id}`,
      itemTypeId: event.itemTypeId,
      propertyName: event.eventPropertyName,
      label: event.eventPropertyDisplay,
      status: 1,
      dataType: event.dataType as TDataType,
      type: event.propertyType,
      children: [],
    };

    const tempChildren = event.items?.map(child => {
      const tempChildrenItem = {
        value: `${event.id}.${child.itemPropertyName}`,
        itemTypeId: child.itemTypeId,
        itemTypeName: event.itemTypeName,
        propertyName: child.itemPropertyName,
        eventPropertySyntax: child.eventPropertySyntax,
        label:
          child.propertyDisplayMultilang[i18n.language] ||
          child.propertyDisplayMultilang[get(child, "propertyDisplayMultilang['DEFAULT_LANG']")] ||
          child.itemPropertyDisplay,
        children: [],
        status: +child.status,
        type: +child.type,
        dataType: child.dataType as TDataType,
      };

      additionalAttrProperties?.forEach(propName => {
        if (has(child, propName)) set(tempChildrenItem, propName, child[propName]);
      });

      return tempChildrenItem;
    });

    additionalAttrProperties?.forEach(propName => {
      if (has(event, propName)) set(temp, propName, event[propName]);
    });

    set(temp, 'children', tempChildren);

    return temp;
  });
};

export const serilizePromotionPool = (data: any, additionalAttrProperties?: string[]): LabeledValue[] =>
  data.rows.map((pool: any) => {
    const temp = {
      label: pool.name,
      value: pool.code,
    };

    additionalAttrProperties?.forEach(propName => {
      if (has(pool, propName)) temp[propName] = pool[propName];
    });

    return temp;
  });

export const serializeCSSelectedString = (value: string = '') => {
  const [contentSourceKey = '', groupId = '', itemTypeId] = value.split('::');

  return {
    contentSourceKey,
    groupId,
    itemTypeId: Number(itemTypeId),
  };
};

export const asyncGetDataBOFromDMDetail = async (data: { [key: string]: any }): Promise<Record<string, any>[]> => {
  if (regexCSType.test(data[DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE])) {
    const { itemTypeId } = serializeCSSelectedString(data.type);

    const { data: resData } = await getDataTableBOQuery(itemTypeId);

    return resData.data;
  }

  return [];
};

export const getDataBOfromDM = (
  data: { [key: string]: any },
  csData: Map<string, Record<string, any>[]>,
): Record<string, any>[] => {
  if (regexCSType.test(data[DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE])) {
    const { groupId } = serializeCSSelectedString(data.type);

    return csData.get(groupId) ?? [];
  }

  return [];
};

export const transformDateFormat = (dateParseFormat, dateParseOption) => {
  let dateFormat = '';

  switch (dateParseFormat) {
    case 'MM/DD/YYYY':
      if (dateParseOption === 'short') dateFormat = 'MM/DD/YYYY';
      if (dateParseOption === 'medium') dateFormat = 'MMM DD, YYYY';
      if (dateParseOption === 'long') dateFormat = 'MMMM DD, YYYY';
      break;
    case 'DD/MM/YYYY':
      if (dateParseOption === 'short') dateFormat = 'DD/MM/YYYY';
      if (dateParseOption === 'medium') dateFormat = 'DD MMM YYYY';
      if (dateParseOption === 'long') dateFormat = 'DD MMMM YYYY';
      break;
    case 'YYYY/MM/DD':
      if (dateParseOption === 'short') dateFormat = 'YYYY/MM/DD';
      if (dateParseOption === 'medium') dateFormat = 'YYYY, MMM DD';
      if (dateParseOption === 'long') dateFormat = 'YYYY, MMMM DD';
      break;
    default:
      break;
  }

  return dateFormat;
};
export const CUSTOM_TYPE = {
  index: 0,
  label: getTranslateMessage(translations.dynamicContent.modal.dynamicContentType.custom),
  value: `custom`,
};
