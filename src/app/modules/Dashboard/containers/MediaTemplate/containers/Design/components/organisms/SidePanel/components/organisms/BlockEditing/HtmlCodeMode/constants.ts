import { LAYOUT_TEMPLATE } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

const { FLOATING_BAR, SLIDE_IN } = LAYOUT_TEMPLATE;

export const MAP_POSITION_BY_DISPLAY_TYPE = {
  [FLOATING_BAR.name]: [
    {
      value: 'top',
      label: 'Top',
      icon: 'multicolor-icon-position-top',
    },
    {
      value: 'bottom',
      label: 'Bottom',
      icon: 'multicolor-icon-position-bottom',
    },
  ],
  [SLIDE_IN.name]: [
    {
      value: 'left',
      label: 'Left',
      icon: 'multicolor-icon-position-left-bottom',
    },
    {
      value: 'right',
      label: 'Right',
      icon: 'multicolor-icon-position-right-bottom',
    },
  ],
};

export const TYPE_CHANGE = {
  SIDE: 'SIDE',
  POSITION: 'POSITION',
};
