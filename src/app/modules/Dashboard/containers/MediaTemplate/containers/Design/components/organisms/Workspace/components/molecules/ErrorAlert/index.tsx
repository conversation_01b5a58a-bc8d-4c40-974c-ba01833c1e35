// Libraries
import React from 'react';

// Atoms
import { Icon, Text } from 'app/components/atoms';

// Icons
import { WarningIcon } from 'app/components/icons';

// Styled
import { StyledErrorAlert } from './styled';

interface ErrorAlertProps {
  visible: boolean;
  onClose?: () => void;
  content?: React.ReactNode;
}

export const ErrorAlert: React.FC<ErrorAlertProps> = props => {
  // Props
  const { visible, content, onClose } = props;

  return (
    <StyledErrorAlert
      className={`
      ${visible ? '--visible' : ''}
    `}
      onClick={e => {
        e.stopPropagation();
      }}
    >
      <div className="ants-flex ants-gap-2.5">
        <WarningIcon
          style={{
            width: 18,
            height: 14,
            marginTop: 2,
          }}
        />
        <Text color="#fff">{content}</Text>
      </div>
      <Icon
        type="icon-ants-remove-light"
        size={14}
        style={{ color: '#fff', cursor: 'pointer', marginTop: 2 }}
        onClick={onClose}
      />
    </StyledErrorAlert>
  );
};

ErrorAlert.defaultProps = {
  visible: false,
  onClose: () => {},
  content: null,
};
