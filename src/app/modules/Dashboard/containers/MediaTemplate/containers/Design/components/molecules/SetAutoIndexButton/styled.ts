import styled, { css } from 'styled-components';
import tw from 'twin.macro';

export const StyledBoxIndex = styled.div`
  ${tw`ants-flex ants-items-center ants-justify-center 
    ants-h-4
    ants-px-[2px]
    ants-border-[2px]
    ants-rounded-r-[2.5px]
    ants-rounded-bl-[2.5px]
  `}
  min-width: 15px;
`;

export const StyledIndexButton = styled.div<{
  color?: string;
  hoverColor?: string;
}>`
  ${tw`ants-relative ants-flex`}

  ${p => {
    return css`
      * {
        color: ${p.color};
        border-color: ${p.color};
      }

      &:hover {
        * {
          color: ${p.hoverColor} !important;
          border-color: ${p.hoverColor} !important;
        }
      }
    `;
  }}
`;
