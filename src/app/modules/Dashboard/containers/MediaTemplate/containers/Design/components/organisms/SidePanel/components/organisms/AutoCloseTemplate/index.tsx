// Libraries
import React, { memo } from 'react';
import isEqual from 'react-fast-compare';
import { useTranslation } from 'react-i18next';

// Types
import { TAutoCloseTemplate } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Atoms
import { Space, Switch, Text } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../molecules';
import { InputNumber, Select } from 'app/components/molecules';

// Translations
import { translations } from 'locales/translations';

// Constants
import { AT_OPTIONS } from './constants';

// Utils
import { handleError } from 'app/utils/handleError';

interface TAutoCloseTemplateProps {
  setting: TAutoCloseTemplate;
  onChange: (setting: TAutoCloseTemplate) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/AutoCloseTemplate/index.tsx';

export const AutoCloseTemplate: React.FC<TAutoCloseTemplateProps> = memo(
  props => {
    // Translations
    const { t } = useTranslation();

    // Props
    const { setting, onChange } = props;
    const { after, at, enable } = setting || {};

    // Handlers
    const onChangeAutoCloseTemplate = (payload: Partial<TAutoCloseTemplate>) => {
      try {
        onChange && onChange({ ...setting, ...payload });
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: '',
          args: {},
        });
      }
    };

    return (
      <Space direction="vertical" size={20}>
        <SettingWrapper label={t(translations.autoClose.title)}>
          <Switch checked={enable} onChange={enable => onChangeAutoCloseTemplate({ enable })} />
        </SettingWrapper>

        {enable && (
          <>
            <SettingWrapper label={t(translations.at.title)}>
              <Select
                style={{ width: 100 }}
                options={Object.values({ ...AT_OPTIONS }).map(({ name, label }) => ({ value: name, label }))}
                value={at}
                onChange={at => onChangeAutoCloseTemplate({ at })}
              />
            </SettingWrapper>

            <SettingWrapper label={t(translations.after.title)}>
              <Space>
                <InputNumber
                  min={10}
                  max={600}
                  value={after}
                  onChange={(after: any) => onChangeAutoCloseTemplate({ after })}
                />
                <Text>{t(translations.seconds.title)}</Text>
              </Space>
            </SettingWrapper>
          </>
        )}
      </Space>
    );
  },
  (prevProps, nextProps) => isEqual(prevProps.setting, nextProps.setting),
);
