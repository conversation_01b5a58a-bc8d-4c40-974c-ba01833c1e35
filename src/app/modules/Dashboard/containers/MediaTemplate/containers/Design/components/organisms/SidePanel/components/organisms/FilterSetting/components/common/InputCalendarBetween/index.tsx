// Libraries
import { useContext } from 'react';
import moment, { Moment } from 'moment';
import { useTranslation } from 'react-i18next';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { DatePicker } from 'app/components/molecules/DatePicker';

// Constants
import { dateFormatAPI, dateFormatFE, defaultDateFormatAPI } from '../../../constants';

// Utils
import { getValidMomentDate } from '../../../utils';

// Translations
import { translations } from 'locales/translations';

// Filter context
import { FilterContext } from '../../../context';

interface InputCalendarBetweenProps {
  value: string;
  required?: boolean;
  focused?: boolean;
  onChange: (v: InputCalendarBetweenProps['value']) => void;
}
export const InputCalendarBetween: React.FC<InputCalendarBetweenProps> = props => {
  const { t } = useTranslation();
  const { required, focused, value, onChange } = props;

  // Context
  const { isFormatDateTimeMilliseconds } = useContext(FilterContext);

  const [from, to] = value
    .split(isFormatDateTimeMilliseconds ? ' AND ' : 'AND')
    .map(val => (isFormatDateTimeMilliseconds ? +val : val));

  const onChangeFrom = (_: Moment | null, dateString: string) => {
    const momentDate = getValidMomentDate(moment(dateString));

    const newDate = isFormatDateTimeMilliseconds
      ? momentDate?.startOf('day').valueOf() || null
      : momentDate?.format(defaultDateFormatAPI) || null;

    const newValue = isFormatDateTimeMilliseconds ? `${newDate} AND ${to}` : `${newDate}AND${to}`;
    onChange(newValue);
  };

  const onChangeTo = (_: Moment | null, dateString: string) => {
    const momentDate = getValidMomentDate(moment(dateString));

    const newDate = isFormatDateTimeMilliseconds
      ? momentDate?.endOf('day').valueOf() || null
      : momentDate?.format('YYYY-MM-DD 23:59:59') || null;

    const newValue = isFormatDateTimeMilliseconds ? `${from} AND ${newDate}` : `${from}AND${newDate}`;
    onChange(newValue);
  };

  return (
    <div className="ants-flex ants-items-center">
      <DatePicker
        focused={focused}
        required={required}
        format={dateFormatFE}
        value={getValidMomentDate(moment(from, !isFormatDateTimeMilliseconds ? dateFormatAPI : ''))}
        onChange={onChangeFrom}
        className="!ants-border-t-0 !ants-border-x-0"
      />
      <Text className="ants-mx-4">{t(translations.and.title)}</Text>
      <DatePicker
        focused={focused}
        required={required}
        format={dateFormatFE}
        value={getValidMomentDate(moment(to, !isFormatDateTimeMilliseconds ? dateFormatAPI : ''))}
        onChange={onChangeTo}
        className="!ants-border-t-0 !ants-border-x-0"
      />
    </div>
  );
};
