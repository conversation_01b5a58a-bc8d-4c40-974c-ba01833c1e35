import { TTableSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { SettingWrapper } from '../../../../../molecules';
import { Button, Popover, Space, Checkbox } from 'app/components/atoms';
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import Icon from '@antscorp/icons';

type TableBodySettingsProps = {
  tableBody: TTableSettings['tableBody'];
  onChange: (settings: TTableSettings['tableBody']) => void;
};

const TableBodySettings = (props: TableBodySettingsProps) => {
  const {
    tableBody: { styles, settings },
  } = props;

  const { t } = useTranslation();

  const content = (
    <Space className="ants-w-100 ants-justify-between">
      <Checkbox
        checked={settings.showRowNumbers}
        onChange={e =>
          props.onChange({
            styles,
            settings: {
              ...settings,
              showRowNumbers: e.target.checked,
            },
          })
        }
      >
        {t(translations.rowNumbers.title)}
      </Checkbox>

      <Checkbox
        checked={settings.wrapText}
        onChange={e =>
          props.onChange({
            styles,
            settings: {
              ...settings,
              wrapText: e.target.checked,
            },
          })
        }
      >
        {t(translations.wrapText.title)}
      </Checkbox>
    </Space>
  );

  return (
    <SettingWrapper label={t(translations.tableBody.title)} labelClassName="ants-font-bold">
      <Popover placement="topRight" content={content} trigger={['click']}>
        <Button icon={<Icon type="icon-ants-edit-2" />} type="text" />
      </Popover>
    </SettingWrapper>
  );
};

export default TableBodySettings;
