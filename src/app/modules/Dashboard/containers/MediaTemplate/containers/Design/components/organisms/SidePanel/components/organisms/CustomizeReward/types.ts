import { TLimitSpinning } from './components/LimitSpinning';

export type TSectionValidate = {
  id: string;
  key: keyof TWheelItem;
  msg: string;
  status: 'warning' | 'error';
};

export interface TWheelItem {
  sectionId: string;
  label: string;
  backgroundColor: string;
  couponCode: string;
  canWin: boolean;
  pool: boolean;
  winChance: number;
  saved: boolean;
  couponCodeAttr: string;
  internalCode: string;
  limitSpinning: TLimitSpinning;
  cappingLevel: 'journey' | 'campaign' | 'variant' | null;
  frequency: 'this hour' | 'this day' | 'this week' | 'this month' | 'lifetime' | null;
  validateInfo: Omit<TSectionValidate, 'index'>[];
}
