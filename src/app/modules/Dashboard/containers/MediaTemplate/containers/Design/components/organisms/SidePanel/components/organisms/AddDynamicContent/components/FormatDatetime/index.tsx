// Libraries
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// Locales
import { translations } from 'locales/translations';

// Atoms
import { Checkbox, Radio, Space } from 'app/components/atoms';

// Molecules
import { Select, RadioGroup } from 'app/components/molecules';

// Utils
import customParseFormat from 'dayjs/plugin/customParseFormat';

// Utils
import { transformDateFormat } from '../../utils';

dayjs.extend(customParseFormat);

export type TDatetimeFormatSetting = {
  hasDateFormat: boolean;
  hasTimeFormat: boolean;
  dateParseFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY/MM/DD';
  timeParseFormat: '12hour' | '24hour' | string;
  dateParseOption: 'short' | 'medium' | 'long' | undefined;
  timeParseOption: 'short' | 'medium' | 'long' | undefined;
  language?: string;
  type?: 'datetime';
};

export type FormatDatetimeProps = {
  onSettingsChange?: (settingValues: TDatetimeFormatSetting) => void;
  dynamicContentType?: string;
} & TDatetimeFormatSetting;

export const dateExample = dayjs('2020-04-15 18:23:11');

const FormatDatetime = ({
  hasDateFormat,
  hasTimeFormat,
  dateParseFormat,
  timeParseFormat,
  dateParseOption,
  timeParseOption,
  onSettingsChange,
  dynamicContentType,
}: FormatDatetimeProps) => {
  const {
    t,
    i18n: { language: portalLanguage },
  } = useTranslation();

  const dfSettings = {
    hasDateFormat,
    hasTimeFormat,
    dateParseFormat,
    timeParseFormat,
    dateParseOption,
    timeParseOption,
  };

  const datetimeEx = dayjs(dateExample).locale(portalLanguage);

  const timezone = Math.round(dayjs(dateExample).utcOffset() / 60);
  const prefix = timezone > 0 ? '+' : '';
  const gmtString = `GMT ${prefix}${timezone}`;

  const handleChange = (updatedSettings: TDatetimeFormatSetting) => {
    if (onSettingsChange) {
      onSettingsChange({
        ...updatedSettings,
        type: 'datetime',
        language: portalLanguage,
      });
    }
  };
  const labelHours = dynamicContentType !== 'custom' ? 'HH:MM:SS' : '';
  return (
    <div>
      <div className="ants-flex ants-items-center ants-gap-4">
        <Checkbox
          checked={hasDateFormat}
          disabled={hasDateFormat && !hasTimeFormat}
          onChange={e =>
            handleChange({
              ...dfSettings,
              hasDateFormat: e.target.checked,
            })
          }
        >
          {t(translations.dynamicContent.modal.label.datimeDisplayFormat.dateDF)}
        </Checkbox>

        <Select
          className="ants-w-[190px]"
          disabled={!hasDateFormat}
          defaultValue={dateParseFormat}
          options={[
            {
              label: `MM/DD/YYYY ${labelHours}`,
              value: 'MM/DD/YYYY',
            },
            {
              label: `DD/MM/YYYY ${labelHours}`,
              value: 'DD/MM/YYYY',
            },
            {
              label: `YYYY/MM/DD ${labelHours}`,
              value: 'YYYY/MM/DD',
            },
          ]}
          onChange={value =>
            handleChange({
              ...dfSettings,
              dateParseFormat: value,
            })
          }
        />
      </div>

      <div className="ants-mt-2">
        <RadioGroup
          disabled={!hasDateFormat}
          defaultValue={dateParseOption}
          onChange={e =>
            handleChange({
              ...dfSettings,
              dateParseOption: e.target.value,
            })
          }
        >
          <Space direction="vertical" size={4} className="ants-ml-5">
            <Radio value="short">
              {t(translations.dynamicContent.modal.label.datimeDisplayFormat.short)}
              {' - '}
              {datetimeEx.format(transformDateFormat(dateParseFormat, 'short'))}
            </Radio>
            <Radio value="medium">
              {t(translations.dynamicContent.modal.label.datimeDisplayFormat.medium)}
              {' - '}
              {datetimeEx.format(transformDateFormat(dateParseFormat, 'medium'))}
            </Radio>
            <Radio value="long">
              {t(translations.dynamicContent.modal.label.datimeDisplayFormat.long)}
              {' - '}
              {datetimeEx.format(transformDateFormat(dateParseFormat, 'long'))}
            </Radio>
          </Space>
        </RadioGroup>
      </div>

      <div className="ants-flex ants-items-center ants-gap-4 ants-mt-4">
        <Checkbox
          checked={hasTimeFormat}
          disabled={hasTimeFormat && !hasDateFormat}
          onChange={e =>
            handleChange({
              ...dfSettings,
              hasTimeFormat: e.target.checked,
            })
          }
        >
          {t(translations.dynamicContent.modal.label.datimeDisplayFormat.timeDF)}
        </Checkbox>
        <Select
          className="ants-w-[190px]"
          disabled={!hasTimeFormat}
          defaultValue={timeParseFormat}
          options={[
            {
              label: 'AM/PM',
              value: '12hour',
            },
            {
              label: t(translations.dynamicContent.modal.label.datimeDisplayFormat.use24hour),
              value: '24hour',
            },
          ]}
          onChange={value =>
            handleChange({
              ...dfSettings,
              timeParseFormat: value,
            })
          }
        />
      </div>

      <div className="ants-mt-2">
        <RadioGroup
          defaultValue={timeParseOption}
          disabled={!hasTimeFormat}
          onChange={e =>
            handleChange({
              ...dfSettings,
              timeParseOption: e.target.value,
            })
          }
        >
          <Space direction="vertical" size={4} className="ants-ml-5">
            <Radio value="short">
              {t(translations.dynamicContent.modal.label.datimeDisplayFormat.short)}
              {' - '}
              {datetimeEx.format(timeParseFormat === '12hour' ? 'h:mm A' : 'H:mm')}
            </Radio>
            <Radio value="medium">
              {t(translations.dynamicContent.modal.label.datimeDisplayFormat.medium)}
              {' - '}
              {datetimeEx.format(timeParseFormat === '12hour' ? 'h:mm:ss A' : 'H:mm:ss')}
            </Radio>
            <Radio value="long">
              {t(translations.dynamicContent.modal.label.datimeDisplayFormat.long)}
              {' - '}
              {datetimeEx.format(timeParseFormat === '12hour' ? `h:mm:ss A [${gmtString}]` : `H:mm:ss [${gmtString}]`)}
            </Radio>
          </Space>
        </RadioGroup>
      </div>
    </div>
  );
};

export default FormatDatetime;
