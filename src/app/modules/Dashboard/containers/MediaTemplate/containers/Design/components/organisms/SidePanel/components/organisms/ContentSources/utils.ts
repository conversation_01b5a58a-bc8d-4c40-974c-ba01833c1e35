import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';
import { TContentSourceGroup } from './types';
import { random } from 'app/utils/common';
import { FILTERS_DEFAULT, PRODUCT_RANKING_DEFAULT } from '../../../../../../config';
import { FALLBACK_SELECTION } from '../../organisms/BlockEditing/Settings/Basic/constants';

export const initGroup = (groupName: string): TContentSourceGroup => ({
  groupId: `csg${random(5)}`,
  groupName: groupName,
  itemTypeId: null,
  itemTypeName: null,
  itemTypeDisplay: null,
  filters: FILTERS_DEFAULT,
  ranking: PRODUCT_RANKING_DEFAULT,
  fallback: FALLBACK_SELECTION.HIDDEN.value,
  level: undefined,
  data: [],
});

export const TRANSLATE_TEXT = {
  group: getTranslateMessage(translations.group.title, 'Group'),
  selectCsTitle: getTranslateMessage(translations.selectContentSource.title, 'Content source'),
  selectAnItem: getTranslateMessage(translations.selectAnItem.title, 'Select an item'),
  addGroup: getTranslateMessage(translations.addGroup.title, 'Add Group'),
  fallback: getTranslateMessage(translations.fallback.title, 'Fallback'),
  groupName: getTranslateMessage(translations.groupName.title, 'Group Name'),
};
