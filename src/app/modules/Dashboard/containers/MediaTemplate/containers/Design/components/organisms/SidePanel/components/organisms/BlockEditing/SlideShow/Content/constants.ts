// Locales
import { translations } from 'locales/translations';

// Utils
import { getTranslateMessage } from 'utils/messages';

export const SLIDE_DIRECTION = {
  HORIZONTAL: {
    value: 'horizontal',
    label: getTranslateMessage(translations.horizontal.title),
  },
  VERTICAL: {
    value: 'vertical',
    label: getTranslateMessage(translations.vertical.title),
  },
};

export const DISPLAY_STYLE = {
  FULL: {
    value: 'full',
    label: getTranslateMessage(translations.full.title),
  },
  HALF: {
    value: 'half',
    label: getTranslateMessage(translations.half.title),
  },
};

export const SLIDE_TRANSITION = {
  SLIDE: {
    value: 'slide',
    label: getTranslateMessage(translations.slide.title),
  },
  FADE: {
    value: 'fade',
    label: getTranslateMessage(translations.fade.title),
  },
  CUBE: {
    value: 'cube',
    label: getTranslateMessage(translations.cube.title),
  },
  FLIP: {
    value: 'flip',
    label: getTranslateMessage(translations.flip.title),
  },
  CARD: {
    value: 'cards',
    label: getTranslateMessage(translations.cards.title),
  },
  COVERFLOW: {
    value: 'coverflow',
    label: getTranslateMessage(translations.coverflow.title),
  },
  CREATIVE: {
    value: 'creative',
    label: getTranslateMessage(translations.creative.title),
  },
};

export const BUTTON_POSITION = {
  MIDDLE: {
    value: 'middle',
    label: getTranslateMessage(translations.middle.title),
  },
  TOP_LEFT: {
    value: 'top-left',
    label: getTranslateMessage(translations.topLeft.title),
  },
  CENTER_LEFT: {
    value: 'center-left',
    label: getTranslateMessage(translations.centerLeft.title),
    mode: 'vertical',
  },
  BOTTOM_LEFT_VERTICAL: {
    value: 'bottom-left',
    label: getTranslateMessage(translations.bottomLeft.title),
    mode: 'vertical',
  },
  TOP_CENTER: {
    value: 'top-center',
    label: getTranslateMessage(translations.topCenter.title),
    mode: 'horizontal',
  },
  TOP_RIGHT: {
    value: 'top-right',
    label: getTranslateMessage(translations.topRight.title),
  },
  CENTER_RIGHT: {
    value: 'center-right',
    label: getTranslateMessage(translations.centerRight.title),
    mode: 'vertical',
  },
  BOTTOM_LEFT: {
    value: 'bottom-left',
    label: getTranslateMessage(translations.bottomLeft.title),
    mode: 'horizontal',
  },
  BOTTOM_CENTER: {
    value: 'bottom-center',
    label: getTranslateMessage(translations.bottomCenter.title),
    mode: 'horizontal',
  },
  BOTTOM_RIGHT: {
    value: 'bottom-right',
    label: getTranslateMessage(translations.bottomRight.title),
  },
};

export const BUTTON_STYLE = {
  CIRCLE: {
    value: 'circle',
    label: getTranslateMessage(translations.circle.title),
  },
  SQUARE: {
    value: 'square',
    label: getTranslateMessage(translations.square.title),
  },
};
