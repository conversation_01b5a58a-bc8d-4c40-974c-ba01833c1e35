import * as React from 'react';
import { render } from '@testing-library/react';

import { AlgorithmsSetting } from '..';

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: str => str,
      i18n: {
        changeLanguage: () => new Promise(() => {}),
      },
    };
  },
}));

describe('<AlgorithmsSetting  />', () => {
  it('should match snapshot', () => {
    const loadingIndicator = render(<AlgorithmsSetting />);
    expect(loadingIndicator.container.firstChild).toMatchSnapshot();
  });
});
