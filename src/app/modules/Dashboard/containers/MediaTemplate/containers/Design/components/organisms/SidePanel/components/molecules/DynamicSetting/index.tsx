// Libraries
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { get } from 'lodash';
import { Tooltip } from '@antscorp/antsomi-ui';

// Atoms
import { Button, Divider, Icon, Space, Text } from 'app/components/atoms';
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Molecules
import { SettingWrapper } from '../SettingWrapper';
import { RadioGroup } from 'app/components/molecules';

// Organisms
import { AddDynamicContent } from '../../organisms/AddDynamicContent';

import { DYNAMIC_OPTIONS } from './constants';

// Styled
import { DynamicSettingWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

// Locales
import { translations } from 'locales/translations';

// Selectors
import {
  selectBlockSelected,
  selectJourneySettings,
  selectContentSources,
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectWorkspaceErrors,
  selectWorkspaceWarnings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

type TSettings = {
  [key: string]: any;
  isDynamic: boolean;
  index?: number;
};

interface DynamicSettingProps {
  label?: string;
  settings?: TSettings;
  renderStatic?: () => React.ReactNode;
  renderDynamic?: () => React.ReactNode;
  onChange?: (settings: TSettings, keyChange?: string) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/DynamicSetting/index.tsx';

export const DynamicSetting: React.FC<DynamicSettingProps> = props => {
  // Props
  const { label = '', settings, renderStatic, renderDynamic, onChange = () => {} } = props;
  const { isDynamic, attribute, index } = settings || ({} as any);

  // State
  const [isOpenDynamicContent, setOpenDynamicContent] = useState(false);

  // Selector
  const blockSelected = useSelector(selectBlockSelected) || {};
  const journeySettings = useSelector(selectJourneySettings);
  const contentSources = useSelector(selectContentSources);
  const workspaceErrors = useSelector(selectWorkspaceErrors);
  const workspaceWarnings = useSelector(selectWorkspaceWarnings);
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);

  // I18n
  const { t } = useTranslation();

  // Handlers
  const onChangeSetting = (params, keyChange = '') => {
    try {
      onChange &&
        onChange(
          {
            ...settings,
            ...params,
          },
          keyChange,
        );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeSetting',
        args: {},
      });
    }
  };

  const onOkDynamicContent = (values: Record<string, any>) => {
    try {
      // Check if values.index is not exists then set null
      onChangeSetting({ ...values, index: values.index != null ? values.index : null }, 'dynamicContent');

      setOpenDynamicContent(false);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onOkDynamicContent',
        args: { values },
      });
    }
  };

  const renderDynamicContent = () => {
    try {
      const { label } = attribute;

      return (
        <>
          <SettingWrapper label={label} labelClassName="!ants-text-black !ants-font-medium">
            <Space>
              {/* Show Icon Error or Warning */}
              {errors[blockSelected.id] && workspaceErrors?.promotionPool?.[settings?.pool] ? (
                <Tooltip title={workspaceErrors?.promotionPool?.[settings?.pool]}>
                  <ErrorIcon />
                </Tooltip>
              ) : warnings[blockSelected.id] && workspaceWarnings?.promotionPool?.[settings?.pool] ? (
                <Tooltip title={workspaceWarnings?.promotionPool?.[settings?.pool]}>
                  <WarningIcon />
                </Tooltip>
              ) : null}

              {/* Show Icon Error Attribute */}
              {errors[blockSelected.id] && workspaceErrors?.attributes?.[attribute.label] && (
                <Tooltip title={workspaceErrors?.attributes?.[attribute.label]}>
                  <ErrorIcon />
                </Tooltip>
              )}

              <Button type="text" icon={<Icon type="icon-ants-edit-2" />} onClick={() => setOpenDynamicContent(true)} />
              <Button
                type="text"
                icon={<Icon type="icon-ants-remove-trash" size={18} />}
                onClick={() =>
                  onChange({
                    isDynamic,
                  })
                }
              />
            </Space>
          </SettingWrapper>
          {index != null ? (
            <Text className="ants-font-medium">
              {t(translations.index.title)}: {index}
            </Text>
          ) : null}
          <Divider type="horizontal" dashed className="!ants-m-0" />
        </>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const renderContent = () => {
    try {
      switch (isDynamic) {
        case true:
          return (
            <Space size={10} direction="vertical">
              {!!attribute ? (
                renderDynamicContent()
              ) : (
                <Button block onClick={() => setOpenDynamicContent(true)}>
                  + {t(translations.dynamicContent.sidePanel.addDynamicContent)}
                </Button>
              )}

              <AddDynamicContent
                journeySettings={journeySettings}
                contentSources={contentSources}
                defaultData={settings?.attribute ? settings : {}}
                visible={isOpenDynamicContent}
                defaultDynamicIndex={get(blockSelected, 'settings.defaultDynamicIndex', 1)}
                showDisplayFormat={false}
                onCancel={() => setOpenDynamicContent(false)}
                onOk={onOkDynamicContent}
              />

              {renderDynamic && renderDynamic()}
            </Space>
          );

        case false:
          return renderStatic && renderStatic();

        default:
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderContent',
        args: {},
      });
    }
  };

  return (
    <DynamicSettingWrapper>
      <SettingWrapper label={label}>
        <RadioGroup
          value={isDynamic ? DYNAMIC_OPTIONS.DYNAMIC.value : DYNAMIC_OPTIONS.STATIC.value}
          options={Object.values(DYNAMIC_OPTIONS)}
          onChange={e => onChangeSetting({ isDynamic: e.target.value === DYNAMIC_OPTIONS.DYNAMIC.value }, 'isDynamic')}
        />
      </SettingWrapper>
      <div className="ants-mt-15px">{renderContent()}</div>
    </DynamicSettingWrapper>
  );
};

DynamicSetting.defaultProps = {
  label: '',
  settings: {
    isDynamic: false,
    field: '',
    index: 1,
  },
};
