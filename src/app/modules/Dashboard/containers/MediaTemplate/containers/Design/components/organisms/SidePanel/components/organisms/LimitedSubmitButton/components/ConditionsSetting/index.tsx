// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import produce from 'immer';
import { cloneDeep, get, set } from 'lodash';

// Locales
import { translations } from 'locales/translations';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';

// Atoms
import { Button, Icon, Text } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules';
import { Modal } from 'app/components/molecules/Modal';
import { Select } from 'app/components/molecules';

// Components
import SelectMultiple from './components/SelectMultiple';

// Utils
import { handleError } from 'app/utils/handleError';
import { validateFilters } from './utils';

// Types
import {
  TVerifiedSubmitConditions,
  TVerifiedSubmitConditionsAnd,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Styled
import { ConditionBlockWrapper, RowCondition } from './styled';

// Constants
import { HASH, OPERATORS_OPTION } from 'constants/variables';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/LimitedSubmitButton/components/ConditionsSetting/index.tsx';

interface ConditionsSettingProps {
  conditions: TVerifiedSubmitConditions;
  formFields: Array<any>;
  onChange: (conditions: TVerifiedSubmitConditions) => void;
}

type TState = {
  visible: boolean;
  conditions: TVerifiedSubmitConditions;
  errors: {
    OR: any[];
    isError: boolean;
  };
};

const MAX_OR_CONDITIONS = 3;

const defaultAndCondition: TVerifiedSubmitConditionsAnd = {
  field: null,
  hash: HASH.NONE.value,
  operator: OPERATORS_OPTION.MATCHES.value,
  valueType: 'normal',
  value: null,
  filters: { OR: [{ AND: [] }] },
};

const defaultState = {
  visible: false,
  conditions: { OR: [{ AND: [defaultAndCondition] }] },
  errors: { OR: [], isError: false },
};

const ConditionsSetting: React.FC<ConditionsSettingProps> = props => {
  // I18n
  const { t } = useTranslation();

  // Props
  const { conditions, formFields, onChange } = props;

  // State
  const [state, setState] = useState<TState>(defaultState);

  // Memo
  const numberOfAndConditions = useMemo(() => {
    return conditions.OR.map(({ AND }) => AND)
      ?.flat()
      ?.filter(({ field }) => !!field)?.length;
  }, [conditions]);

  const isDisableAddOrBtn = useMemo(() => {
    return state.conditions.OR?.length >= MAX_OR_CONDITIONS;
  }, [state.conditions]);

  // Effects
  useDeepCompareEffect(() => {
    if (state.visible) {
      setState(state => ({ ...state, conditions }));

      const errors = validateFilters({ filters: conditions, formFields });

      if (errors.isError) {
        setState(state => ({ ...state, errors }));
      }
    }
  }, [conditions, formFields, state.visible]);

  // Handlers
  const toggleModal = (visible: boolean) => {
    try {
      setState(modalState => ({
        ...modalState,
        visible: visible != null ? visible : modalState.visible,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'toggleModal',
        args: {},
      });
    }
  };

  const onAddRowCondition = ({ value }) => {
    try {
      setState(state => ({
        ...state,
        conditions: { OR: [{ AND: [{ ...defaultAndCondition, field: value }] }] },
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderRowCondition',
        args: { value },
      });
    }
  };

  const onResetError = (orIndex: number, andIndex: number) => {
    const newErrors = cloneDeep(state.errors);

    if (newErrors.OR[orIndex] && newErrors.OR[orIndex].AND[andIndex]) {
      const error = {
        field: '',
        operator: '',
        value: '',
      };

      newErrors.OR[orIndex].AND[andIndex] = error;
      setState(state => ({ ...state, errors: newErrors }));
    }
  };

  const onChangeCondition = ({ params, orIdx, andIdx }) => {
    try {
      setState(state => ({
        ...state,
        conditions: produce(state.conditions, draft => {
          switch (true) {
            case params.hasOwnProperty('operator'): {
              draft.OR[orIdx]?.AND?.forEach(andCondition => {
                andCondition.operator = params.operator;
              });
              break;
            }
            default: {
              set(draft, `OR[${orIdx}].AND[${andIdx}]`, {
                ...get(draft, `OR[${orIdx}].AND[${andIdx}]`, {}),
                ...params,
              });
              break;
            }
          }
        }),
      }));

      onResetError(orIdx, andIdx);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderRowCondition',
        args: { params, orIdx, andIdx },
      });
    }
  };

  const onRemoveCondition = ({ orIdx, andIdx }) => {
    try {
      setState(state => ({
        ...state,
        conditions: produce(state.conditions, draft => {
          // Check andCondition is a first condition in first rowCondition then just change field to null to keep at least 1 andCondition.
          if (
            orIdx === 0 &&
            andIdx === 0 &&
            state.conditions.OR[orIdx]?.AND?.length === 1 &&
            state.conditions.OR.length === 1
          ) {
            draft.OR[orIdx].AND[andIdx] = defaultAndCondition;
            return;
          }

          const andConditions = cloneDeep(draft.OR[orIdx].AND);

          andConditions.splice(andIdx, 1);

          if (andConditions.length) {
            draft.OR[orIdx].AND = andConditions;
          } else {
            draft.OR.splice(orIdx, 1);
          }
        }),
        errors: produce(state.errors, draft => {
          draft.OR[orIdx]?.AND?.splice(andIdx, 1);
        }),
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onRemoveCondition',
        args: { orIdx, andIdx },
      });
    }
  };

  const onAddAndCondition = ({ orIdx }) => {
    try {
      setState(state => ({
        ...state,
        conditions: produce(state.conditions, draft => {
          draft.OR[orIdx].AND.push({ ...defaultAndCondition, operator: state.conditions.OR[orIdx]?.AND[0]?.operator });
        }),
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onAddAndCondition',
        args: { orIdx },
      });
    }
  };

  const renderRowCondition = ({
    condition,
    orIdx,
    andIdx,
  }: {
    condition: TVerifiedSubmitConditionsAnd;
    orIdx?: number;
    andIdx?: number;
  }) => {
    const { field, hash, operator, value, filters, valueType } = condition || {};
    const isShowAddAndBtn = andIdx === state.conditions.OR[orIdx || 0]?.AND?.length - 1;
    const selectedContentSourceId = get(
      state.conditions.OR[orIdx || 0]?.AND.find(({ value }) => !!value?.attribute),
      'value.itemTypeId',
      null,
    );

    // Error
    const fieldNotExistErrorMsg =
      field && !formFields?.find(formField => formField.value === field) ? t(translations.fieldNotExist.title) : '';
    const fieldErrorMsg = get(state.errors, `OR[${orIdx}].AND[${andIdx}].field`, '');
    const valueErrorMsg = get(state.errors, `OR[${orIdx}].AND[${andIdx}].value`, '');

    let isShowCloseBtn = true;

    if (
      orIdx === 0 &&
      andIdx === 0 &&
      state.conditions.OR[orIdx]?.AND?.length === 1 &&
      state.conditions.OR.length === 1 &&
      !field
    ) {
      isShowCloseBtn = false;
    }

    try {
      return (
        <RowCondition>
          <Select
            value={field}
            options={formFields}
            placeholder={t(translations.selectAnItem.title)}
            onChange={value =>
              condition ? onChangeCondition({ params: { field: value }, orIdx, andIdx }) : onAddRowCondition({ value })
            }
            status={fieldErrorMsg || fieldNotExistErrorMsg ? 'error' : undefined}
            errorMsg={fieldErrorMsg || fieldNotExistErrorMsg}
          />
          {!!condition?.field && (
            <>
              <Select
                value={hash}
                defaultValue={HASH.NONE.value}
                options={Object.values({ ...HASH })}
                onChange={value => onChangeCondition({ params: { hash: value }, orIdx, andIdx })}
              />
              <Select
                disabled={andIdx !== 0}
                value={operator}
                defaultValue={OPERATORS_OPTION.MATCHES.value}
                options={[OPERATORS_OPTION.MATCHES, OPERATORS_OPTION.NOT_MATCHES]}
                onChange={value => onChangeCondition({ params: { operator: value }, orIdx, andIdx })}
              />

              <div>
                <SelectMultiple
                  value={value}
                  filters={filters}
                  valueType={valueType}
                  // Check if more than one an condition in or then pass selectedContentSourceId
                  contentSourceIds={
                    state.conditions.OR[orIdx || 0]?.AND?.length > 1 ? [selectedContentSourceId] : undefined
                  }
                  onChange={params => {
                    onChangeCondition({ params, orIdx, andIdx });
                  }}
                />

                {valueErrorMsg ? (
                  <Text color="#ff4d4f" className="ants-mt-2 ants-ml-2">
                    {valueErrorMsg}
                  </Text>
                ) : null}
              </div>
            </>
          )}

          <div
            className={`ants-flex ants-justify-between ants-pl-16 ants-space-x-10 ${
              !condition?.field && 'ants-col-start-5'
            }`}
          >
            <div>
              {isShowAddAndBtn && (
                <Button
                  onClick={() => onAddAndCondition({ orIdx })}
                  type="default"
                  className="ants-mt-1 ants-rounded-full ants-border-primary ants-w-16 ants-justify-self-center"
                  disabled={state.conditions.OR[orIdx || 0]?.AND.length >= 10}
                >
                  + {t(translations.and.title).toUpperCase()}
                </Button>
              )}
            </div>

            <div>
              {isShowCloseBtn && (
                <Button
                  onClick={() => onRemoveCondition({ orIdx, andIdx })}
                  type="text"
                  icon={<Icon type="icon-ants-remove-slim" size={15} />}
                />
              )}
            </div>
          </div>
        </RowCondition>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderRowCondition',
        args: { condition, orIdx, andIdx },
      });
    }
  };

  const onClickAddOrCondition = () => {
    try {
      setState(state => ({
        ...state,
        conditions: produce(state.conditions, draft => {
          draft.OR.push({ AND: [defaultAndCondition] });
        }),
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickAddOrCondition',
        args: {},
      });
    }
  };

  const onSubmitCondition = () => {
    try {
      const errors = validateFilters({ filters: state.conditions, formFields });

      if (errors.isError) {
        setState(state => ({ ...state, errors }));
        return;
      }

      setState(defaultState);
      onChange(state.conditions);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  const onCancelSubmitCondition = () => {
    try {
      toggleModal(false);

      setState(defaultState);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancel',
        args: {},
      });
    }
  };

  return (
    <>
      <SettingWrapper
        label={`${t(translations.conditions.title)} ${!!numberOfAndConditions ? `(${numberOfAndConditions})` : ''}`}
        labelClassName="ants-font-bold"
      >
        <Button icon={<Icon type="icon-ants-edit-2" />} type="text" onClick={() => toggleModal(true)} />
        <Modal
          destroyOnClose
          title={t(translations.conditions.title)}
          visible={state.visible}
          width={1200}
          bodyStyle={{
            maxHeight: '70vh',
            overflow: 'auto',
          }}
          okText={t(translations.apply.title)}
          onOk={() => onSubmitCondition()}
          onCancel={() => onCancelSubmitCondition()}
        >
          {state.conditions.OR.map((orCondition, orIdx) => {
            const isShowOrCondition = !!orCondition.AND?.length;

            return isShowOrCondition ? (
              <React.Fragment key={orIdx}>
                {!!orIdx && <Text>{t(translations.or.title).toUpperCase()}</Text>}
                <ConditionBlockWrapper>
                  {orCondition.AND.map((andCondition, andIdx) => (
                    <React.Fragment key={`${orIdx} + ${andIdx}`}>
                      {renderRowCondition({ condition: andCondition, orIdx, andIdx })}
                    </React.Fragment>
                  ))}
                </ConditionBlockWrapper>
              </React.Fragment>
            ) : null;
          })}

          <Button
            type="default"
            className="ants-mt-1 ants-rounded-full ants-border-primary ants-w-16 ants-justify-self-center"
            disabled={isDisableAddOrBtn}
            onClick={() => onClickAddOrCondition()}
          >
            + {t(translations.or.title).toUpperCase()}
          </Button>
        </Modal>
      </SettingWrapper>
    </>
  );
};

ConditionsSetting.defaultProps = {
  conditions: defaultState.conditions,
};

export default ConditionsSetting;
