// Locales
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const ANIMATION_TYPE = {
  NONE: {
    value: 'none',
    label: getTranslateMessage(translations.none.title),
  },
  BOUNCE: {
    value: 'bounce',
    label: getTranslateMessage(translations.bounce.title),
  },
  FLASH: {
    value: 'flash',
    label: getTranslateMessage(translations.flash.title),
  },
  PULSE: {
    value: 'pulse',
    label: getTranslateMessage(translations.pulse.title),
  },
  RUBBER_BAND: {
    value: 'rubberBand',
    label: getTranslateMessage(translations.rubberBand.title),
  },
  SHAKE_X: {
    value: 'shakeX',
    label: getTranslateMessage(translations.shakeX.title),
  },
  SHAKE_Y: {
    value: 'shakeY',
    label: getTranslateMessage(translations.shakeY.title),
  },
  HEAD_SHAKE: {
    value: 'headShake',
    label: getTranslateMessage(translations.headShake.title),
  },
  SWING: {
    value: 'swing',
    label: getTranslateMessage(translations.swing.title),
  },
  TADA: {
    value: 'tada',
    label: getTranslateMessage(translations.tada.title),
  },
  WOBBLE: {
    value: 'wobble',
    label: getTranslateMessage(translations.wobble.title),
  },
  JELLO: {
    value: 'jello',
    label: getTranslateMessage(translations.jello.title),
  },
  HEART_BEAT: {
    value: 'heartBeat',
    label: getTranslateMessage(translations.heartBeat.title),
  },
  JACK_IN_THE_BOX: { value: 'jackInTheBox', label: getTranslateMessage(translations.jackInTheBox.title) },
};

export const ANIMATION_ITERATION_STYLE = {
  INFINITE: {
    value: 'infinite',
    label: getTranslateMessage(translations.infinite.title),
  },
  FINITE: {
    value: 'finite',
    label: getTranslateMessage(translations.finite.title),
  },
};
