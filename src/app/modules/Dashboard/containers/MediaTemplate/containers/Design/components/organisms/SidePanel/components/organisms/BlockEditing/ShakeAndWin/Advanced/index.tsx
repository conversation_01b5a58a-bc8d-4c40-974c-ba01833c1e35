// Libraries
import React, { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';

// Translations
import { translations } from 'locales/translations';

// Selectors
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { handleError } from 'app/utils/handleError';

// Molecules
import { Collapse, Panel } from 'app/components/molecules/Collapse';

// Organisms
import { ContainerStyling } from '../../../ContainerStyling';
import { ShakeStyling } from './ShakeStyling';
import { BlockStyling } from '../../Common/BlockStyling';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Columns/Advanced/index.tsx';

interface AdvancedProps {}

export const Advanced: React.FC<AdvancedProps> = memo(props => {
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateBlockSetting } = mediaTemplateDesignActions;

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const blockSettings = blockSelected?.settings;

  // Handler Shake Styling
  const onChangeShakeStyling = useCallback(
    (blockStylesSettings = {}, blockStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.stylesSettings = {
                  ...draft.stylesSettings,
                  ...blockStylesSettings,
                };
                draft.styles = {
                  ...draft.styles,
                  ...blockStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  // Handler Shake Notification
  const onChangeNotificationStyling = useCallback(
    (newNotificationStylesSettings = {}, newNotificationStyles = {}) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.notificationStylesSettings = {
                  ...draft.notificationStylesSettings,
                  ...newNotificationStylesSettings,
                };
                draft.notificationStyles = {
                  ...draft.notificationStyles,
                  ...newNotificationStyles,
                };
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeNotificationStyling',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  if (blockSettings) {
    return (
      <Collapse defaultActiveKey={['1']} accordion>
        <Panel header={t(translations.shakeStyling.title)} key="1">
          <ShakeStyling
            blockStylesSettings={blockSettings.stylesSettings}
            notificationStylesSettings={blockSettings.notificationStylesSettings}
            notificationStyles={blockSettings.notificationStyles}
            blockStyles={blockSettings.styles}
            onChange={onChangeShakeStyling}
            onChangeNotificationStyling={onChangeNotificationStyling}
          />
        </Panel>
        <Panel header={t(translations.containerStyling.title)} key="2">
          <BlockStyling />
        </Panel>
      </Collapse>
    );
  }

  return null;
});
