import { useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TextEditor as TextEditorV2, TextEditorProvider, TextEditorProviderRefHandler } from '@antscorp/antsomi-ui';
import {
  selectBlockById,
  selectBlockSelectedId, // Added this import
  selectCSDataOfGroup,
  selectGlobalSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import {
  BlockProps,
  TDynamicTextSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { DEFAULT_FONT_CONFIGS } from './constants';
import { useGetDesignWrapperEl } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/hooks';
import { useDynamicText, useLinkText, useTextEditorManagement } from './hooks';
import { TextBlockContentWrapper } from './styled';
import { APP_CONFIG } from 'constants/appConfig';
import { isEmpty } from 'lodash';
import { useDeepCompareEffect } from 'app/hooks';
import tinycolor from 'tinycolor2';

interface TextBlockContentProps extends BlockProps {
  contentForPreview?: boolean;
}

export const TextBlockContentProvider = ({
  children,
  forPreview,
}: {
  children: React.ReactNode;
  forPreview: boolean;
}) => {
  const dispatch = useDispatch();

  const ref = useRef<TextEditorProviderRefHandler>(null);

  const globalSettings = useSelector(selectGlobalSettings);

  const { setGlobalSettings } = mediaTemplateDesignActions;

  const { customColors = [] } = globalSettings;

  const dataDynamicAttr = forPreview ? APP_CONFIG.DYNAMIC_ATTRIBUTE.PREVIEW : APP_CONFIG.DYNAMIC_ATTRIBUTE.DISPLAY;

  useDeepCompareEffect(() => {
    if (isEmpty(customColors)) return;

    ref.current?.updateColors?.(customColors);
  }, [customColors]);

  const onChangeColors = useCallback(
    (colors: string[]) => {
      dispatch(
        setGlobalSettings({
          customColors: colors.map(color => tinycolor(color).toHexString()),
        }),
      );
    },
    [dispatch, setGlobalSettings],
  );

  return (
    <TextEditorProvider ref={ref} onChangeColors={onChangeColors}>
      <TextBlockContentWrapper
        style={{
          display: forPreview ? 'none' : 'block',
        }}
        {...{ [dataDynamicAttr]: 1 }}
      >
        {children}
      </TextBlockContentWrapper>
    </TextEditorProvider>
  );
};

export const TextBlockContent = (props: TextBlockContentProps) => {
  const dispatch = useDispatch();

  const { contentForPreview = false, settings, isPreviewMode, id, type } = props;

  // Actions
  const { setSidePanel } = mediaTemplateDesignActions;

  // Selectors
  const contentSourcesData = useSelector(selectCSDataOfGroup);
  const textBlock = useSelector(selectBlockById(id, type));
  const { settings: textBlockSettings = {} } = textBlock || {};

  const bubbleMenuContainer = useGetDesignWrapperEl();

  const { editorId, editorRef, setProcessing, previewStyle, onChangeText } = useTextEditorManagement({
    id,
    type,
    settings,
    textBlockSettings: textBlockSettings as TDynamicTextSettings,
    contentForPreview,
  });

  const { highlightVariable, smartTagHandler } = useDynamicText({
    id,
    type,
    isPreviewMode,
    contentForPreview,
    textBlockSettings: textBlockSettings as TDynamicTextSettings,
    contentSourcesData,
    editorRef,
    setProcessing,
  });

  const { linkHandler } = useLinkText({
    id,
    type,
    isPreviewMode,
    contentForPreview,
    textBlockSettings: textBlockSettings as TDynamicTextSettings,
    contentSourcesData,
    editorRef,
    setProcessing,
  });

  // Original useEffect for focusing editor, can remain if not moved or can be part of useTextEditorManagement
  // For now, keeping it here as it uses blockSelectedId directly from this component's scope.
  // If useTextEditorManagement needs blockSelectedId, it should be passed as a prop.
  const blockSelectedId = useSelector(selectBlockSelectedId); // Reverted to use the selector

  useEffect(() => {
    if (!editorRef.current) return;

    if (blockSelectedId !== id) {
      editorRef.current?.blur();
    }
  }, [id, blockSelectedId, editorRef]);

  return (
    <TextBlockContentProvider forPreview={contentForPreview}>
      <TextEditorV2
        id={editorId}
        ref={editorRef}
        config={{
          SmartTag: {
            highlight: !isPreviewMode && highlightVariable,
          },
          Link: {
            highlightDynamic: !isPreviewMode && highlightVariable,
          },
          FontFamily: {
            fonts: DEFAULT_FONT_CONFIGS,
          },
          UnorderedList: {
            useCustomBullet: true,
          },
        }}
        initialContent={settings.rawHTML}
        editable={!isPreviewMode}
        onFocus={() => {
          dispatch(
            setSidePanel({
              blockSelectedId: id,
              type,
            }),
          );
        }}
        bubbleMenuProps={{
          container: bubbleMenuContainer,
        }}
        onUpdate={({ html, json }) => {
          onChangeText({ html, json }, 'user');
        }}
        smartTagHandler={smartTagHandler}
        linkHandler={linkHandler}
        {...(isPreviewMode && {
          style: previewStyle,
        })}
      />
    </TextBlockContentProvider>
  );
};
