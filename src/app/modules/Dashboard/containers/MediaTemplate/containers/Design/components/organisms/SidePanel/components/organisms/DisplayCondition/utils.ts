import { isNullish } from 'app/utils/common';
import { DYNAMIC_CONTENT_TYPE } from '../../../../../../constants';
import { regexCSType, serializeCSSelectedString } from '../AddDynamicContent/utils';
import { getInputType } from '../FilterSetting/utils';
import { TDisplayCondition } from './types';

export const getInputDefaultValue = ({ operator, dataType, property }) => {
  return getInputType({
    operator,
    dataType,
    property,
    resInputBoolean: 'true',
    resInputNumberBetween: '0 AND 0',
    resInputCalendarBetween: 'null AND null',
    resInputDateTimeAgo: 0,
    resInputCalendar: null,
    resInputOrSelect: '',
    resInputSelectMulti: [],
    resInputArray: [],
    resInputNumber: 0,
    resDefault: '',
    resInputDateTimeBetweenAgo: '0 AND 0',
  });
};

export const defaultDCValues: TDisplayCondition = {
  value: '',
  operator: '',
  event_metadata: {},
  value_type: '',
  time_unit: '',
  type: '',
  attribute: {},
  pool: null,
  index: null,
};

type dataPropsType = {
  'data-condition': string;
  'data-compare'?: string;
  'data-operator': string;
  'data-data-type': string | undefined;
};

export const buildDisplayConditionData = (settingDC: TDisplayCondition = {}) => {
  if (!settingDC) {
    return {};
  }

  if (!['show_when', 'hidden_when'].includes(settingDC.condition || '')) {
    return {};
  }

  // prettier-ignore
  const {
    operator,
    condition,
    index,
    event_metadata = {},
    value,
    value_type,
    time_unit,
    isHasNoIndex,
    attribute,
    type,
  } = settingDC;

  if (
    settingDC &&
    attribute &&
    // index &&
    condition &&
    operator &&
    (['exists', 'not_exists'].includes(operator) ||
      (value_type !== 'event' && value !== null) ||
      (value_type === 'event' &&
        event_metadata &&
        ((event_metadata.useBo && event_metadata.field_code_bo) ||
          (!event_metadata.useBo && event_metadata.item_property_name))))
  ) {
    let dataProps: dataPropsType | {} = {};

    if (isHasNoIndex) return dataProps;

    dataProps = {
      'data-condition': `${condition}`,
      'data-compare': buildDataCompare(settingDC),
      'data-operator': operator,
      'data-data-type': attribute.dataType,
    };

    if (value_type !== 'event') {
      dataProps['data-compare-to'] = time_unit ? `${value || ''}${time_unit || ''}` : `${value || ''}`;
    } else {
      const { useBo, field_code_bo, item_type_name, item_property_name } = event_metadata;
      if (useBo && field_code_bo) {
        const { groupId } = serializeCSSelectedString(type);

        dataProps['data-compare-to'] = `#{groups.${groupId}[${index}].${field_code_bo}}`;
      } else {
        dataProps['data-compare-to'] = item_type_name
          ? `#{event.${item_type_name}.${item_property_name}}`
          : `#{event.${item_property_name}}`;
      }
    }
    return dataProps;
  }
  return {};
};

const buildDataCompare = (settingDC: TDisplayCondition): string => {
  const { type, index, pool, attribute } = settingDC;

  if (attribute === undefined || type === undefined) return '';

  let dataCompare = '';

  switch (true) {
    case type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
      dataCompare = !isNullish(index)
        ? `#{event.${attribute.itemTypeName}[${index}].${attribute.propertyName}}`
        : `#{event.${attribute.value}}`;
      break;
    case type === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
      dataCompare = `#{visitor.${attribute.value}}`;
      break;
    case type === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
      dataCompare = `#{customer.${attribute.value}}`;
      break;
    case type === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
      dataCompare = pool ? `#{promotion_code.${pool}.${attribute.value}}` : '';
      break;
    case type === 'custom':
      dataCompare = `#{custom.${attribute.value}}`;
      break;
    case regexCSType.test(type): {
      const { groupId } = serializeCSSelectedString(type);
      dataCompare = index || index === 0 ? `#{groups.${groupId}[${index}].${attribute.value}}` : '';
      break;
    }
  }

  return dataCompare;
};
