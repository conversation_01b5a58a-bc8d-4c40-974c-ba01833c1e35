// Libraries
import React, { useEffect, useRef, useState } from 'react';
import { isEmpty } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';

//Action
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Icons
import { WarningIcon } from 'app/components/icons';

// Hook
import { useGetListPromotionPool } from 'app/queries/PromotionPool';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Atoms
import { Skeleton } from 'app/components/atoms';

// Styled
import { GiftContainer, GiftItem, ImageGiftPreview, ImageWrapper, SurpriseTreasureHuntBlockWrapper } from './styled';

// Selectors
import { selectCSDataOfGroup } from '../../../../../../slice/selectors';
import { selectSidePanel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Utils
import { random } from 'app/utils/common';
import { buildPromotionSectionsData, buildPromotionTagData } from '../../../utils';
import { SIDE_PANEL_COLLAPSE } from '../../../../../../constants';

interface SurpriseTreasureHuntBlockProps extends BlockProps {}

const STANDARD_WIDTH = 690;
const ERROR_POSITION = {
  TOP: 'top',
  CENTER: 'center',
  BOTTOM: 'bottom',
};

export const SurpriseTreasureHuntBlock: React.FC<SurpriseTreasureHuntBlockProps> = props => {
  const dispatch = useDispatch();

  // Actions
  const { setData } = mediaTemplateDesignActions;

  // Props
  const { namespace, settings, isPreviewMode, id } = props;
  const {
    imageGiftFirstRender,
    dimensions,
    metrics,
    cellImages,
    outerContainerStyles,
    cellContainerStyles,
    styles,
    cellStyles,
    backgroundImageSettings,
    sections,
    errorMessage,
    animationHover,
  } = settings;
  const { rows, columns } = metrics;
  const { boxes, cells } = dimensions;
  const { width } = boxes;
  const { gap } = cells;

  // Selectors
  const contentSourcesData = useSelector(selectCSDataOfGroup);
  const { data: promotionPools = { rows: [], total: 0 } } = useGetListPromotionPool();
  const sidePanel = useSelector(selectSidePanel);

  // Refs
  const giftRef = useRef<HTMLDivElement>(null);
  const giftWrapperRef = useRef<HTMLDivElement>(null);
  const giftErrorRef = useRef<HTMLDivElement>(null);
  const giftErrorPlaceholderRef = useRef<HTMLDivElement>(null);
  const isOverSize = useRef(false);
  const wrapperClientRect = giftWrapperRef.current ? giftWrapperRef.current.getBoundingClientRect() : null;
  const giftRect = giftRef.current ? giftRef.current.getBoundingClientRect() : null;

  // States
  const [wrapperSize, setWrapperSize] = useState({
    width: wrapperClientRect?.width || STANDARD_WIDTH,
    height: wrapperClientRect?.height || STANDARD_WIDTH,
  });

  isOverSize.current =
    wrapperClientRect && giftRect
      ? (isOverSize.current = Math.round(wrapperClientRect.width) <= Math.round(giftRect.width))
      : false;

  const [deltaScale, setDeltaScale] = useState(wrapperSize.height / STANDARD_WIDTH);

  useEffect(() => {
    const wrapperObserver = new ResizeObserver((entrys: ResizeObserverEntry[]) => {
      if (!giftWrapperRef?.current) {
        return;
      }
      for (let entry of entrys) {
        const cr = entry.contentRect;
        setWrapperSize(cr);
        setDeltaScale(Math.min(cr.width, cr.height) / STANDARD_WIDTH);
      }
    });

    giftWrapperRef?.current && wrapperObserver.observe(giftWrapperRef?.current);

    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      giftWrapperRef?.current && wrapperObserver.unobserve(giftWrapperRef?.current);
    };
  }, []);

  useEffect(() => {
    if (promotionPools?.total) {
      dispatch(
        setData({
          promotionPool: promotionPools,
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [promotionPools]);

  let realDetaScale = isOverSize.current ? deltaScale : deltaScale;

  if (giftRect && wrapperClientRect && STANDARD_WIDTH * realDetaScale > wrapperClientRect?.height) {
    realDetaScale = wrapperClientRect?.height / STANDARD_WIDTH;
  }

  useEffect(() => {
    if (
      giftErrorRef.current &&
      sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
      sidePanel.blockSelectedId === id
    ) {
      giftErrorRef.current.style.width = STANDARD_WIDTH * realDetaScale + 'px';
    }

    if (
      giftErrorPlaceholderRef.current &&
      sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
      sidePanel.blockSelectedId === id
    ) {
      giftErrorPlaceholderRef.current.style.width = STANDARD_WIDTH * realDetaScale + 'px';
    }
  }, [errorMessage, sidePanel.activePanel, sidePanel.blockSelectedId, id, realDetaScale]);

  const renderMessageWithPlaceholder = position => {
    return (
      <>
        {renderMessage(position)} {renderMessage(position, false)}
      </>
    );
  };

  const renderMessage = (position, isMainMessage = true) => {
    return position === errorMessage?.position ? (
      <div
        ref={isMainMessage ? giftErrorRef : giftErrorPlaceholderRef}
        className={`${
          isMainMessage ? namespace : 'ants-invisible placeholder'
        }-coupon-wheel-error ants-flex ants-justify-center ants-items-center ants-z-1
        ${
          position === ERROR_POSITION.CENTER
            ? 'ants-absolute ants-inset-0'
            : position === ERROR_POSITION.TOP
            ? isMainMessage
              ? 'ants-absolute ants-top-2'
              : 'ants-relative ants-top-2 ants-h-14'
            : isMainMessage
            ? 'ants-absolute ants-bottom-2'
            : 'ants-relative ants-bottom-2 ants-h-14'
        }`}
        style={{
          fontSize: 13,
          display:
            sidePanel.blockSelectedId === id &&
            sidePanel.activePanel === SIDE_PANEL_COLLAPSE.COUPON_SETTING &&
            !isPreviewMode
              ? 'flex'
              : 'none',
          ...(isOverSize.current
            ? {
                left: 'unset',
                right: 'unset',
              }
            : {
                left: '50%',
                right: 'unset',
                transform: `translateX(calc(-50%))`,
              }),
        }}
      >
        <div
          className="ants-px-15px ants-py-10px ants-bg-gray-4 
            ants-text-cus-primary ants-w-fit ants-h-fit
            ants-flex ants-items-center ants-gap-10px
            ants-rounded ants-relative ants-text-center"
        >
          <WarningIcon
            style={{
              width: 21,
              height: 21,
            }}
          />
          {errorMessage.message}
        </div>
      </div>
    ) : null;
  };

  const renderSkeleton = (): React.ReactNode => {
    const imagesSize = rows * columns;
    const dataSkeleton: Array<React.ReactNode> = [];
    let index = 0;

    while (index < imagesSize) {
      dataSkeleton.push(
        <GiftItem key={random(5)} align="center">
          <ImageWrapper>
            <Skeleton.Image className="!ants-w-full" style={{ height: 100, width: 90 }} />
          </ImageWrapper>
        </GiftItem>,
      );

      index++;
    }

    return dataSkeleton;
  };

  const renderLayoutGifts = (cellImageConfigs, imageUrl): React.ReactNode => {
    const { applyImageAllBefore = false, imageAllUrlBefore = '', imageDynamic = [] } = cellImageConfigs;

    if (isEmpty(imageDynamic) || !Array.isArray(imageDynamic)) return null;
    const flattenImages = imageDynamic.flat();

    const resultNodes: Array<React.ReactNode> = flattenImages.map(image => {
      let urlBinding = image.previewUrlBefore || imageGiftFirstRender;

      if (applyImageAllBefore) {
        urlBinding = imageAllUrlBefore || imageGiftFirstRender;
      }

      const onHoverGift = (id: string) => {
        if (id) {
          const giftElement: null | HTMLElement = document.getElementById(id);
          if (giftElement && animationHover === 'highlight') {
            giftElement.style.border = '1px solid #B8CFE6';
          }
        }
      };

      const onLeaveGift = (id: string) => {
        if (id) {
          const giftElement: null | HTMLElement = document.getElementById(id);
          if (giftElement && animationHover === 'highlight') {
            giftElement.style.border = 'none';
          }
        }
      };

      return (
        <GiftItem
          id={image.key}
          key={image.key}
          align={cellContainerStyles.justify}
          style={{ ...cellStyles, backgroundImage: `url(${imageUrl})` }}
          className="gift-item"
          onMouseEnter={() => onHoverGift(image.key)}
          onMouseLeave={() => onLeaveGift(image.key)}
        >
          <ImageWrapper>
            <ImageGiftPreview src={urlBinding} alt="gift for game" />
          </ImageWrapper>
        </GiftItem>
      );
    });

    return resultNodes;
  };

  return (
    <>
      {renderMessageWithPlaceholder(ERROR_POSITION.TOP)}
      {renderMessageWithPlaceholder(ERROR_POSITION.CENTER)}
      <SurpriseTreasureHuntBlockWrapper
        id={`${namespace}-${settings.component}--${id}`}
        ref={giftWrapperRef}
        className={`${namespace}-imge-content ${namespace}-${settings.component}--content`}
        align={outerContainerStyles.justify}
        data-wheel={buildPromotionSectionsData(sections, promotionPools)}
        data-tag={buildPromotionTagData(sections, promotionPools)}
      >
        <GiftContainer
          gap={gap}
          rows={rows}
          columns={columns}
          width={width}
          style={{
            ...styles,
            backgroundImage: backgroundImageSettings.giftBoxUrl
              ? `url(${backgroundImageSettings.giftBoxUrl})`
              : 'unset',
          }}
          className="animate__animated animate__fadeIn gift-container"
        >
          {contentSourcesData.isLoading
            ? renderSkeleton()
            : renderLayoutGifts(cellImages, backgroundImageSettings.cellUrl)}
        </GiftContainer>
      </SurpriseTreasureHuntBlockWrapper>
      {renderMessageWithPlaceholder(ERROR_POSITION.BOTTOM)}
    </>
  );
};
