// libraries
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';

// redux
import { useDispatch, useSelector } from 'react-redux';

// utils
import produce from 'immer';
import { translations } from 'locales/translations';
import { handleError } from 'app/utils';
import { get, set } from 'lodash';

// selectors
import {
  selectCodeModeSettings,
  selectCurrentViewPage,
  selectGlobalSettings,
  selectMediaTemplateTypes,
  selectWorkspace,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// components
import { Alert, Icon, Space, Text } from 'app/components/atoms';
import { InputNumber, RadioGroup, Select } from 'app/components/molecules';
import { SettingWrapper } from '../../../molecules';
import { EditorScript } from 'app/components/molecules/EditorScript';
import { PositionStyleSetting } from '../../PositionStyleSetting';

import MultiColorIcon from '@antscorp/icons/multicolor';
import '@antscorp/icons/multicolor.css';

// constants
import { SLIDER_UNITS } from 'app/components/molecules/SliderWithUnit/constants';
import { LAYOUT_TEMPLATE } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { MAP_POSITION_BY_DISPLAY_TYPE, TYPE_CHANGE } from './constants';
import { DATA_MIGRATE } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/config';

// actions
import { mediaTemplateDesignActions } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// types
import { TGlobalSettings } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { TUpdateViewSetting } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

// styled component
import { WrapperHtmlCodeMode } from './styled';

type THtmlCodeMode = {};

type TOnChangePayload = {
  globalSettings?: Partial<TGlobalSettings>;
  slideClosedContainerSettings?: Record<string, any>;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/HtmlCodeMode/index.tsx';

export const HtmlCodeMode: React.FC<THtmlCodeMode> = props => {
  const { SLIDE_IN, FLOATING_BAR, GAMIFIED, INLINE } = LAYOUT_TEMPLATE;

  const dispatch = useDispatch();
  // selectors
  const codeModeSettings: any = useSelector(selectCodeModeSettings);
  const storeMediaTemplateTypes = useSelector(selectMediaTemplateTypes);
  const workspace = useSelector(selectWorkspace);
  const currentViewPage = useSelector(selectCurrentViewPage);

  // Actions
  const { setTemplate, setGlobalSettings, updateViewSetting, updateCodeModeSetting } = mediaTemplateDesignActions;

  // rest state
  const { width, widthSuffix, height, heightSuffix, htmlAreaValue, displayArea = 'safe-area' } = codeModeSettings;

  const slideClosedContainerSettings: Record<string, any> = currentViewPage?.settings.slideClosedContainer || {};
  const globalSettings = workspace.settings;
  const templateType = workspace.template?.type;

  const { positionSettings: templatePositionSettings = DATA_MIGRATE[templateType]?.positionSettings } = globalSettings;

  const { position, positionSettings } = slideClosedContainerSettings;
  // i18n
  const { t } = useTranslation();

  const DISPLAY_AREA = {
    SAFE_AREA: {
      label: t(translations.safeArea.title),
      value: 'safe-area',
    },
    FULL_BLEED: {
      label: t(translations.fullBleed.title),
      value: 'full-bleed',
    },
  };

  const positionRef = useRef(positionSettings);
  positionRef.current = templateType === SLIDE_IN.name ? positionSettings : templatePositionSettings;

  useEffect(() => {
    if (templateType !== LAYOUT_TEMPLATE.SLIDE_IN.name) return;

    let dataUpdate = { ...positionRef.current };

    dataUpdate.top = 'auto';
    if (position === 'left') {
      dataUpdate.right = 'auto';
      dataUpdate.left = dataUpdate.left === 'auto' ? '0' : dataUpdate.left;
    } else {
      dataUpdate.left = 'auto';
      dataUpdate.right = dataUpdate.right === 'auto' ? '0' : dataUpdate.right;
    }
    onChangeTemplateSetting({
      slideClosedContainerSettings: {
        positionSettings: dataUpdate,
      },
    });
  }, [position, templateType]);

  useEffect(() => {
    if (templateType !== FLOATING_BAR.name) return;

    let dataUpdate = { ...positionRef.current };
    const { top, bottom } = dataUpdate;

    dataUpdate.right = 'auto';
    if (globalSettings.floatingBarPosition === 'top') {
      dataUpdate.top = top === 'auto' ? '0' : top;
      dataUpdate.bottom = 'auto';
    } else {
      dataUpdate.top = 'auto';
      dataUpdate.bottom = bottom === 'auto' ? '0' : bottom;
    }
    onChangeTemplateSetting({
      globalSettings: {
        positionSettings: dataUpdate,
      },
    });
  }, [globalSettings?.floatingBarPosition, templateType]);

  const getValueByDisplayType = () => {
    switch (templateType) {
      case FLOATING_BAR.name: {
        return get(workspace, 'settings.floatingBarPosition', '');
      }

      case SLIDE_IN.name: {
        return position;
      }

      default:
        return '';
    }
  };

  const renderContentPosition = () => {
    if (![FLOATING_BAR.name, SLIDE_IN.name].includes(templateType)) return null;

    return (
      <PositionStyleSetting
        settings={positionRef.current}
        onChange={settings => onChangeValue(TYPE_CHANGE.POSITION, settings)}
        isHasNegativeValue
      />
    );
  };

  const onChangeGlobalSettings = (payload: Partial<TGlobalSettings>) => {
    try {
      dispatch(setGlobalSettings(payload));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeGlobalSettings',
        args: { payload },
      });
    }
  };

  const onChangeViewSettings = (payload: TUpdateViewSetting) => {
    try {
      dispatch(updateViewSetting(payload));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeViewStyling',
        args: {},
      });
    }
  };

  const onChange = ({ globalSettings, slideClosedContainerSettings }) => {
    onChangeGlobalSettings({
      ...globalSettings,
    });
    onChangeViewSettings({
      slideClosedContainer: {
        ...slideClosedContainerSettings,
      },
    });
  };

  const onChangeTemplateSetting = payload => {
    try {
      onChange(payload);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeTemplateSetting',
        args: {},
      });
    }
  };

  const onChangeValue = (type: string, value: any) => {
    // change side - display type
    if (type === TYPE_CHANGE.SIDE) {
      switch (templateType) {
        case FLOATING_BAR.name: {
          onChangeTemplateSetting({
            globalSettings: {
              floatingBarPosition: value,
            },
          });
          break;
        }

        case SLIDE_IN.name: {
          onChangeTemplateSetting({
            slideClosedContainerSettings: {
              position: value,
            },
          });
          break;
        }

        default:
          break;
      }
    }

    // change type
    if (type === TYPE_CHANGE.POSITION) {
      switch (templateType) {
        case FLOATING_BAR.name: {
          onChangeTemplateSetting({
            globalSettings: {
              positionSettings: value,
            },
          });
          break;
        }

        case SLIDE_IN.name: {
          onChangeTemplateSetting({
            slideClosedContainerSettings: {
              positionSettings: value,
            },
          });
          break;
        }

        default:
          break;
      }
    }
  };

  const onChangeCodeModeSetting = (key, value) => {
    const blockSelected = workspace.viewPages[0].codeModeSettings || {};

    dispatch(
      updateCodeModeSetting(
        produce(blockSelected, draft => {
          set(draft, key, value);
        }),
      ),
    );
  };

  const onBlurHTMLEditorScript = (_, editor) => {
    try {
      const value = editor.getValue();
      onChangeCodeModeSetting('htmlAreaValue.value', value);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  return (
    <WrapperHtmlCodeMode>
      <Space size={20} direction="vertical">
        {[SLIDE_IN.name, INLINE.name].includes(templateType) && (
          <Alert
            message={t(translations.noSupportMobile.title)}
            type="warning"
            // showIcon
          />
        )}
        <div className="ants-flex ants-gap-10px">
          <SettingWrapper className="ants-gap-5px" label={t(translations.displayType.title)}>
            <Select
              className={'ants-w-24'}
              options={
                storeMediaTemplateTypes
                  ?.filter(({ id }) => ![GAMIFIED.id].includes(id))
                  .map(({ id, name, label }) => ({ id, value: name, label })) || []
              }
              value={templateType}
              onChange={(value, option: any) =>
                dispatch(
                  setTemplate({
                    id: option.id,
                    type: value,
                    name: option.label,
                  }),
                )
              }
            />
          </SettingWrapper>
          {[FLOATING_BAR.name, SLIDE_IN.name].includes(templateType) ? (
            <Select
              className="ants-w-28"
              options={MAP_POSITION_BY_DISPLAY_TYPE[templateType].map(option => ({
                ...option,
                label: (
                  <span className="ants-flex ants-gap-5px ants-items-center">
                    {option.icon && <MultiColorIcon type={option.icon} style={{ fontSize: 24 }} />}
                    {option.label}
                  </span>
                ),
              }))}
              value={getValueByDisplayType()}
              onChange={value => {
                onChangeValue(TYPE_CHANGE.SIDE, value);
              }}
            />
          ) : null}
        </div>
        {renderContentPosition()}
        <SettingWrapper label={t(translations.width.title)} childrenClassName="ants-flex ants-gap-10px">
          <InputNumber
            defaultValue={width}
            value={width}
            min={0}
            max={widthSuffix === '%' ? 100 : undefined}
            className="ants-shrink-0"
            precision={widthSuffix}
            onChange={value => onChangeCodeModeSetting('width', value)}
          />
          <div className="ants-flex ants-items-center ants-space-x-2">
            {Object.values(SLIDER_UNITS)
              .filter(({ value }) => value !== 'auto')
              .map(({ value, label }) => {
                return (
                  <Text
                    key={value}
                    className={classNames('ants-cursor-pointer', {
                      '!ants-text-primary ants-font-bold': widthSuffix === value,
                      '!ants-text-gray-4': widthSuffix !== value,
                    })}
                    onClick={() => onChangeCodeModeSetting('widthSuffix', value)}
                  >
                    {label}
                  </Text>
                );
              })}
          </div>
        </SettingWrapper>
        <SettingWrapper label={t(translations.height.title)} childrenClassName="ants-flex ants-gap-10px">
          <InputNumber
            defaultValue={height}
            value={height}
            min={0}
            max={heightSuffix === '%' ? 100 : undefined}
            className="ants-shrink-0"
            precision={heightSuffix}
            onChange={value => onChangeCodeModeSetting('height', value)}
          />
          <div className="ants-flex ants-items-center ants-space-x-2">
            {Object.values(SLIDER_UNITS)
              .filter(({ value }) => value !== 'auto')
              .map(({ value, label }) => {
                return (
                  <Text
                    key={value}
                    className={classNames('ants-cursor-pointer', {
                      '!ants-text-primary ants-font-bold': heightSuffix === value,
                      '!ants-text-gray-4': heightSuffix !== value,
                    })}
                    onClick={() => onChangeCodeModeSetting('heightSuffix', value)}
                  >
                    {label}
                  </Text>
                );
              })}
          </div>
        </SettingWrapper>
        <SettingWrapper label={t(translations.displayArea.title)} className="!ants-flex-nowrap">
          <RadioGroup
            options={Object.values(DISPLAY_AREA)}
            onChange={e => onChangeCodeModeSetting('displayArea', e.target.value)}
            value={displayArea}
          />
        </SettingWrapper>
        <Alert
          message={t(translations.warnDisplayArea.title)}
          type="warning"
          // showIcon
        />
        <EditorScript
          expandModalLabel={t(translations.htmlPanel.title)}
          value={htmlAreaValue.value}
          label={
            <>
              <Text className="!ants-text-gray-4">{t(translations.htmlPanel.description)}</Text>
            </>
          }
          onBlur={onBlurHTMLEditorScript}
          mode="html"
        />
      </Space>
    </WrapperHtmlCodeMode>
  );
};
