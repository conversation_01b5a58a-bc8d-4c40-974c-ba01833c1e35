// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

// Types
import { TResponsiveSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Slices

// Atoms
import { Space, Switch } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../molecules';

// Locales
import { translations } from 'locales/translations';

// Utils
import { handleError } from 'app/utils/handleError';

// Selectors
import { selectIsMobileMode } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

type ResponsiveSettingProps = {
  showNotStackOnMobile?: boolean;
  settings: TResponsiveSettings;
  onChange: (settings: TResponsiveSettings) => void;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/ResponsiveSetting/index.tsx';

export const ResponsiveSetting: React.FC<ResponsiveSettingProps> = props => {
  // I18n
  const { t } = useTranslation();

  // Selectors
  const isMobileMode = useSelector(selectIsMobileMode);

  // Props
  const { showNotStackOnMobile, settings, onChange } = props;

  const onChangeResponsiveSetting = (payload: Partial<TResponsiveSettings>) => {
    try {
      onChange({
        ...settings,
        ...payload,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeResponsiveSetting',
        args: {},
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <SettingWrapper label={t(translations.responsiveDesign[isMobileMode ? 'hideOnMobile' : 'hideOnDesktop'])}>
        <Switch
          checked={isMobileMode ? settings.hideOnMobile : settings.hideOnDesktop}
          onChange={checked => {
            onChangeResponsiveSetting({
              ...(isMobileMode ? { hideOnMobile: checked } : { hideOnDesktop: checked }),
            });
          }}
        />
      </SettingWrapper>

      {showNotStackOnMobile && isMobileMode && (
        <SettingWrapper label={t(translations.responsiveDesign.notStackOnMobile)}>
          <Switch
            checked={settings.notStackOnMobile}
            onChange={checked => {
              onChangeResponsiveSetting({
                notStackOnMobile: checked,
              });
            }}
          />
        </SettingWrapper>
      )}
    </Space>
  );
};

ResponsiveSetting.defaultProps = {
  showNotStackOnMobile: false,
  settings: {
    notStackOnMobile: false,
  },
};

export default ResponsiveSetting;
