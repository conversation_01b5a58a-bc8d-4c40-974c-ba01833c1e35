// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

export const DragBlockHereWrapper = styled.div`
  ${tw`ants-h-20 ants-p-2 ants-border ants-border-primary`}
`;

export const DragBlockHereContent = styled.div`
  ${tw`
    ants-relative ants-flex ants-flex-wrap ants-items-center ants-justify-center ants-w-full ants-h-full ants-space-x-2 ants-bg-blue-second-2
    ants-border ants-border-dashed ants-border-gray
    ants-transition-all ants-duration-200 ants-cursor-pointer
  `}
`;
