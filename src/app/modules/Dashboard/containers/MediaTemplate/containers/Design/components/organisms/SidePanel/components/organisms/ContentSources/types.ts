import { TFilters, TRanking } from '../../../../../../types';

export type TContentSourceGroup = {
  groupId: string;
  groupName: string;
  filters: TFilters;
  ranking: TRanking;
  itemTypeId: number | null;
  itemTypeName: string | null;
  itemTypeDisplay: string | null;
  fallback: string;
  level?: string;
  data?: Record<string, any>[];
};

export type TRelationship = {
  enabled: 1 | 0;
  parent_attr: string;
  recommendation_level: string;
};

interface Action {
  type: string;
  payload: any;
}

interface ActionUpdateSource extends Action {
  type: 'BO_TYPE';
  payload: {
    itemTypeId: TContentSourceGroup['itemTypeId'];
  };
}

interface ActionUpdateFilter extends Action {
  type: 'FILTER';
  payload: {
    conditions: TContentSourceGroup['filters'];
  };
}

interface ActionUpdateFallback extends Action {
  type: 'FALLBACK';
  payload: {
    fallback: TContentSourceGroup['fallback'];
  };
}

interface ActionUpdateName extends Action {
  type: 'NAME';
  payload: { name: string };
}

interface ActionUpdateAlgorithms extends Action {
  type: 'ALGORITHMS';
  payload: { ranking: TRanking };
}

interface ActionUpdateLevel extends Action {
  type: 'LEVEL';
  payload: { level?: string };
}

export type UpdateGroupAction =
  | ActionUpdateSource
  | ActionUpdateFilter
  | ActionUpdateFallback
  | ActionUpdateName
  | ActionUpdateAlgorithms
  | ActionUpdateLevel;
