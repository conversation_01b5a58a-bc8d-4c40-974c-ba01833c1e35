import { useCallback, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { TextEditorRef, useDeepCompareEffect, TextEditorJSONContent } from '@antscorp/antsomi-ui';
import { random } from 'app/utils/common';
import { selectCSDataOfGroup } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { TDynamicTextSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { DATA_MIGRATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';
import produce from 'immer';
import { difference, isEmpty } from 'lodash';
import { getDynamicLinkFromContentSource, extractLinksFromEditorContent, genDefaultStaticLink } from '../utils';
import {
  DYNAMIC_LINK_SETTING_KEY,
  DYNAMIC_LINK_TYPE,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { buildMergeTag } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';
import { serializeTrackingValue } from 'app/utils';
import { LinkAttrs } from '@antscorp/antsomi-ui/es/components/organism/TextEditor/extensions/Link';

interface UseLinkTextProps {
  id: string;
  type: string;
  isPreviewMode?: boolean;
  contentForPreview?: boolean;
  textBlockSettings: TDynamicTextSettings;
  contentSourcesData: ReturnType<typeof selectCSDataOfGroup>;
  editorRef: React.RefObject<TextEditorRef>;
  setProcessing: React.Dispatch<React.SetStateAction<boolean>>;
}

export const handleLinkDataUpdate = (
  currentLinkData: Record<string, any>,
  json: TextEditorJSONContent,
  currentLinkIds: string[],
) => {
  const newLinkData = produce(currentLinkData, draft => {
    const { linkIds: newLinkIds, byLinkInfo } = extractLinksFromEditorContent(json);

    // console.log({ newLinkIds, byLinkInfo });

    // Remove deleted links
    difference(currentLinkIds, newLinkIds).forEach(deletedLinkId => {
      delete draft[deletedLinkId];
    });

    // Update changed link texts
    Object.entries(byLinkInfo).forEach(([linkId, linkInfo]) => {
      const currentLink = draft[linkId];

      if (linkId.startsWith('temp_')) {
        const id = linkId.split('_').at(1);

        if (id) {
          draft[id] = genDefaultStaticLink({
            text: linkInfo.text,
            url: linkInfo.attrs.href,
          });
        }
      }

      if (currentLink && currentLink[DYNAMIC_LINK_SETTING_KEY.TEXT] !== linkInfo.text) {
        currentLink[DYNAMIC_LINK_SETTING_KEY.TEXT] = linkInfo.text;
      }
    });
  });

  return newLinkData;
};

export const useLinkText = ({
  id,
  type,
  isPreviewMode,
  contentForPreview,
  textBlockSettings,
  contentSourcesData,
  editorRef,
  setProcessing,
}: UseLinkTextProps) => {
  const dispatch = useDispatch();
  const { updateBlockText } = mediaTemplateDesignActions;

  const {
    link = DATA_MIGRATE[type].link as {
      data: Record<string, Record<string, any>>;
      selectedId: string;
    },
  } = textBlockSettings;

  const { data: dataLink = {}, selectedId: selectedLinkId } = link;
  const dataLinkIds = Object.keys({ ...dataLink });

  const showMergeTag = isPreviewMode && !contentForPreview;

  const creatingLinkId = useRef('');

  const handleClearCreatingLinkId = useCallback(() => {
    creatingLinkId.current = '';

    dispatch(
      updateBlockText({
        blockId: id,
        dataUpdate: [
          {
            fieldPath: 'settings.link.selectedId',
            data: '',
          },
        ],
        ignoreUndoAction: true,
      }),
    );
  }, [dispatch, id, updateBlockText]);

  const getDynamicLinkContent = useCallback(
    (row: Record<string, any>) => {
      const dynamicLinkContent = showMergeTag
        ? buildMergeTag(row)
        : getDynamicLinkFromContentSource({
            dynamicLinkData: row,
            csData: contentSourcesData.data,
          });

      // if (contentForPreview === false && dynamicLinkContent === '') {
      //   console.log({ contentForPreview, isPreviewMode, contentSourcesData, showMergeTag, row });
      // }

      return dynamicLinkContent;
    },
    [contentSourcesData.data, showMergeTag],
  );

  // Update text value of all key data link
  useEffect(() => {
    for (const linkId of Object.keys(dataLink)) {
      const row = dataLink[linkId];
      const href = getDynamicLinkContent(row);

      if (creatingLinkId.current === linkId) {
        continue;
      }

      const text = row[DYNAMIC_LINK_SETTING_KEY.TEXT];
      const title = row[DYNAMIC_LINK_SETTING_KEY.TITLE];
      const isDynamic = row[DYNAMIC_LINK_SETTING_KEY.LINK_TYPE] === DYNAMIC_LINK_TYPE.DYNAMIC;
      const openInNewTag = row[DYNAMIC_LINK_SETTING_KEY.OPEN_NEW_TAB];
      const elementName = serializeTrackingValue(row[DYNAMIC_LINK_SETTING_KEY.LABEL] || textBlockSettings?.name || '');

      if (showMergeTag && !isDynamic) {
        return;
      }

      editorRef.current?.updateLinkAttrs(
        currentAttrs => currentAttrs.data?.linkId === linkId,

        ({ currentAttrs }) => ({
          content: text,
          attrs: {
            href: href || './',
            title,
            target: openInNewTag ? '_blank' : '_self',
            data: {
              ...currentAttrs.data,
              dynamic: isDynamic ? 'true' : 'false',
              elementName: elementName || '',
            },
          },
        }),
      );
    }
  }, [editorRef, textBlockSettings?.name, dataLink, getDynamicLinkContent, showMergeTag]);

  // Update text value of creating link
  useEffect(() => {
    const newDataLink = creatingLinkId.current ? dataLink[creatingLinkId.current] : {};
    const existingNewLink = selectedLinkId && !isEmpty(newDataLink) && !selectedLinkId.split(':').at(1);

    if (!existingNewLink || contentSourcesData.isLoading) {
      return;
    }

    const href = getDynamicLinkContent(newDataLink);
    const text = newDataLink[DYNAMIC_LINK_SETTING_KEY.TEXT];
    const title = newDataLink[DYNAMIC_LINK_SETTING_KEY.TITLE];
    const isDynamic = newDataLink[DYNAMIC_LINK_SETTING_KEY.LINK_TYPE] === DYNAMIC_LINK_TYPE.DYNAMIC;
    const openInNewTag = newDataLink[DYNAMIC_LINK_SETTING_KEY.OPEN_NEW_TAB];
    const elementName = serializeTrackingValue(
      newDataLink[DYNAMIC_LINK_SETTING_KEY.LABEL] || textBlockSettings?.name || '',
    );

    editorRef.current?.setLink({
      content: text,
      attrs: {
        title,
        href: href || './',
        target: openInNewTag ? '_blank' : '_self',
        data: {
          linkId: creatingLinkId.current,
          dynamic: isDynamic ? 'true' : 'false',
          elementName: elementName || '',
        },
      },
    });

    handleClearCreatingLinkId();
  }, [
    dataLink,
    selectedLinkId,
    editorRef,
    contentSourcesData.isLoading,
    textBlockSettings?.name,
    getDynamicLinkContent,
    handleClearCreatingLinkId,
  ]);

  useDeepCompareEffect(() => {
    if (isPreviewMode || !selectedLinkId) {
      // Adjusted: allow in preview for consistency
      return;
    }

    const [dataLinkId, action] = selectedLinkId?.split(':') || [];

    switch (action) {
      case 'cancel': {
        dispatch(
          updateBlockText({
            blockId: id,
            dataUpdate: [
              {
                fieldPath: 'settings.link.selectedId',
                data: '',
              },
              {
                fieldPath: 'settings.link.data',
                data: produce(dataLink, draft => {
                  if (isEmpty(dataLink[dataLinkId])) {
                    delete draft[dataLinkId];
                  }
                }),
              },
            ],
            ignoreUndoAction: true,
          }),
        );
        setProcessing(false);
        break;
      }
      case 'delete': {
        dispatch(
          updateBlockText({
            blockId: id,
            dataUpdate: [
              {
                fieldPath: 'settings.link.selectedId',
                data: '',
              },
            ],
            ignoreUndoAction: true,
          }),
        );
        dispatch(
          updateBlockText({
            blockId: id,
            dataUpdate: [
              {
                fieldPath: 'settings.link.data',
                data: produce(dataLink, draft => {
                  delete draft[dataLinkId];
                }),
              },
            ],
          }),
        );
        editorRef.current?.deleteLink?.(attrs => attrs.data?.linkId === dataLinkId);
        setProcessing(false);
        break;
      }
      default:
        break;
    }
  }, [isPreviewMode, selectedLinkId, dataLink, id, updateBlockText, dispatch, editorRef, setProcessing]);

  const linkHandler = {
    edit: ({ attrs }: { attrs: LinkAttrs }) => {
      const dataLinkId = attrs.data?.linkId;

      if (!dataLinkId) return;

      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.link.selectedId',
              data: `${dataLinkId}:edit`,
            },
          ],
        }),
      );
    },
    setNew: ({ selectionText: linkText }: { selectionText: string }) => {
      const randomId = random(8);

      creatingLinkId.current = randomId;

      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.link.selectedId',
              data: `${randomId}:create`,
            },
            {
              fieldPath: 'settings.link.data',
              data: produce(dataLink, draft => {
                draft[randomId] = genDefaultStaticLink({ text: linkText });
              }),
            },
          ],
        }),
      );
    },
  };

  return {
    dataLink,
    dataLinkIds,
    selectedLinkId,
    linkHandler,
    creatingLinkIdRef: creatingLinkId,
    getDynamicLinkContent,
  };
};
