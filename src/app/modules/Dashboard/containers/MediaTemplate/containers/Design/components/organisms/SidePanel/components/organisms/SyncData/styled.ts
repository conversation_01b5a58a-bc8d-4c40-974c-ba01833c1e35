// Libraries
import styled from 'styled-components';
import { Table } from 'app/components/molecules';

// Atoms
import { Text } from 'app/components/atoms';

// Antd
import Search from 'antd/lib/input/Search';
import { TreeSelect, Select } from 'antd';
import tw from 'twin.macro';

export const { Option: OptionLibary } = Select;

export const TableSyncData = styled(Table)`
  .ants-text-primary {
    display: none;
  }
  && tbody > tr:hover > td {
    .ants-text-primary {
      display: inline;
    }
  }
  .disabled-select {
    .ant-select-selection-item {
      color: var(--text-base-color) !important;
    }
  }
`;

export const TextFieldName = styled(Text)`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const StyledSearch = styled(Search)`
  .ant-input-group-addon {
    button {
      height: 32px;
    }
  }
`;

export const TreeSelectBO = styled(TreeSelect)`
  .ant-select-selection-item {
    div {
      text-overflow: ellipsis;
      overflow: hidden;
      width: 130px;
      white-space: nowrap;
    }
  }
`;

export const SelectLibrary = styled(Select)``;

export const FieldNameDropdownBtn = styled.div`
  ${tw`ants-flex ants-items-center ants-justify-between ants-gap-x-1 ants-cursor-pointer`}
`;
// export const OptionLibary = Option;
