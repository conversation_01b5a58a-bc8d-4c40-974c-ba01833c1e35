// Libraries
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';

// Components
import VerifiedSubmit from './components/VerifiedSubmit';

// Atoms
import { Button, Radio, Space, Text, Input, Icon } from 'app/components/atoms';

// Locales
import { translations } from 'locales/translations';

// Molecules
import { RadioGroup, Select, InputNumber } from 'app/components/molecules';

// Molecules
import { SettingWrapper } from '../../molecules/SettingWrapper';

// Constants
import { DEFAULT_CONDITION, LIMIT_ACTION, checkDisableOption } from './constants';

// Styled
import { LimitedSubmitWrapper, TextLimit, WrapperFlex } from './styled';

// Hooks
import { useDeepCompareEffect } from 'app/hooks';
import isEqual from 'react-fast-compare';

interface LimitedSubmitSettingProps {
  data: Record<string, any>;
  fields: any;
  onChange: (payload: any, ignoreUndoAction?: boolean) => void;
  isTempMode?: boolean;
}

interface LimitedSubmitConditionRowProps {
  fields: any[];
  condition: Record<string, any>;
  conditions: Record<string, any>[];
  onChange: (condition: Record<string, any>) => void;
  onChangeRemoveCondition: () => void;
  isClosedButton: boolean;
  timeUnits: any[];
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/LimitedSubmitButton/index.tsx';

const LimitedSubmitConditionRow: React.FC<LimitedSubmitConditionRowProps> = props => {
  // Props
  const { condition, conditions, fields, onChange, onChangeRemoveCondition, isClosedButton, timeUnits } = props;
  const { errorMessage, field, timeUnit, value } = condition;
  const { t } = useTranslation();

  const onChangeCondition = payload => {
    onChange &&
      onChange({
        ...condition,
        ...payload,
      });
  };

  return (
    <div>
      <Space>
        <WrapperFlex>
          {' '}
          <InputNumber
            min={1}
            max={999}
            value={value}
            required
            onChange={value => {
              onChangeCondition({
                value,
              });
            }}
          />
          <TextLimit className="!ants-text-gray-4 "> {t(translations.time.title)}</TextLimit>
          <Select
            options={timeUnits.map(timeUnit => ({
              ...timeUnit,
              disabled: checkDisableOption({
                currentOption: condition,
                options: conditions,
                primaryKey: 'timeUnit',
                compareKey: 'field',
                value: timeUnit.value,
              }),
            }))}
            className="ants-w-[70px]"
            onChange={timeUnit => {
              onChangeCondition({
                timeUnit,
              });
            }}
            value={timeUnit}
          />
          <TextLimit className="!ants-text-gray-4 ">{t(translations.per.title)}</TextLimit>
          <Select
            options={fields.map(field => ({
              ...field,
              disabled: checkDisableOption({
                currentOption: condition,
                options: conditions,
                primaryKey: 'field',
                compareKey: 'timeUnit',
                value: field.value,
              }),
            }))}
            className="ants-w-[70px]"
            value={field}
            onChange={field =>
              onChangeCondition({
                field,
              })
            }
          />
          <Button
            onClick={onChangeRemoveCondition}
            type="text"
            icon={<Icon type="icon-ants-remove-slim" size={10} />}
            className="ants-justify-items-center ants-w-2 ants-h-2 ants-pr-0 ants-mt-2 ants-bg-none"
            hidden={isClosedButton === true}
          />
        </WrapperFlex>
      </Space>
      <div>
        <Text className="!ants-text-gray-4 ants-mb-5px ants-mt-10px">{t(translations.errorMessage.title)}</Text>
        <Input
          placeholder={t(translations.allAvailableCodeHasBeenAllocated.title)}
          value={errorMessage}
          onAfterChange={errorMessage =>
            onChangeCondition({
              errorMessage,
            })
          }
        />
      </div>
    </div>
  );
};

export const LimitedSubmitSetting: React.FC<LimitedSubmitSettingProps> = props => {
  // Props
  const { data, fields, onChange = () => {}, isTempMode } = props;
  const { typeFrequency, conditions, messagePosition, cappingLevel } = data || {};

  const { t } = useTranslation();

  // Memo
  const memoizedFields = useMemo(() => {
    if (typeof fields === 'object') {
      const draftFields: any[] = Object.values({ ...fields })
        .filter(
          (field: any) =>
            !['firstNameInput', 'lastNameInput', 'privacyText', 'submitButton'].includes(field.id) &&
            field.order !== -1,
        )
        .map((field: any) => ({
          label: field.nameField || field.inputName,
          value: field.isCustom ? field.id : field.inputName,
        }));
      return draftFields;
    }

    return [];
  }, [fields]);

  const memoizedTimeUnits = useMemo(() => {
    const draftTimeUnits: any[] = Object.values(LIMIT_ACTION.TIME_UNIT).map((timeUnit: any) => ({
      label: timeUnit.label,
      value: timeUnit.value,
    }));

    return draftTimeUnits;
  }, []);

  // UseEffect
  useDeepCompareEffect(() => {
    const draftConditions = cloneDeep(conditions);

    draftConditions.forEach((condition, index) => {
      if (!memoizedFields.some(field => field.value === condition.field)) {
        draftConditions.splice(index, 1);
      }
    });

    if (!isEqual(draftConditions, conditions)) {
      onChangeLimitedSubmit(
        {
          conditions: draftConditions,
          ...(!draftConditions.length && { typeFrequency: 'unlimited' }),
        },
        true,
      );
    }
  }, [memoizedFields, conditions]);

  const onChangeLimitedSubmit = (payload: Record<string, any>, ignoreUndoAction?: boolean) => {
    onChange(
      {
        ...data,
        ...payload,
      },
      ignoreUndoAction,
    );
  };

  const onChangeCondition = ({ index, condition }) => {
    const draftConditions = [...conditions];

    draftConditions[index] = condition;

    onChangeLimitedSubmit({
      conditions: draftConditions,
    });
  };

  const onClickRemoveCondition = index => {
    const draftConditions = [...conditions];
    draftConditions.splice(index, 1);
    onChangeLimitedSubmit({
      conditions: draftConditions,
    });
  };

  const onClickAddCondition = () => {
    const draftConditions = [...conditions];

    const availableTimeUnit = (memoizedTimeUnits.filter(
      ({ value }) =>
        (conditions.filter(condition => condition.timeUnit === value)?.length || 0) < memoizedFields.length,
    ) || [])[0];

    const availableField = (memoizedFields.filter(
      ({ value }) =>
        !conditions
          .filter(condition => condition.field === value)
          ?.some(({ timeUnit }) => timeUnit === availableTimeUnit?.value),
    ) || [])[0];

    draftConditions.push({
      ...DEFAULT_CONDITION,
      timeUnit: availableTimeUnit?.value,
      field: availableField?.value,
    });

    onChangeLimitedSubmit({
      conditions: draftConditions,
    });
  };

  const onChangeTypeFrequency = ({ typeFrequency }) => {
    onChangeLimitedSubmit({
      typeFrequency,
      conditions: [
        {
          ...DEFAULT_CONDITION,
          field: memoizedFields[0]?.value,
          timeUnit: memoizedTimeUnits[0]?.value,
        },
      ],
    });
  };

  return isTempMode ? null : (
    <LimitedSubmitWrapper>
      <Space size={20} direction="vertical">
        <Text size={'medium'} className="ants-font-medium !ants-text-cus-dark">
          {t(translations.frequency.title)}
        </Text>

        <RadioGroup
          className="ants-flex ants-flex-col ants-space-y-2"
          onChange={e => onChangeTypeFrequency({ typeFrequency: e.target.value })}
          value={typeFrequency || LIMIT_ACTION.UNLIMITED_FREQUENCY.value}
        >
          <Radio value={LIMIT_ACTION.UNLIMITED_FREQUENCY.value} style={{ marginRight: 0 }}>
            <Space direction="vertical" size={5}>
              <Text>{LIMIT_ACTION.UNLIMITED_FREQUENCY.label}</Text>
            </Space>
          </Radio>
          <Radio value={LIMIT_ACTION.LIMITED_FREQUENCY.value} style={{ marginRight: 0 }}>
            <Space direction="vertical" size={5}>
              <Text>{LIMIT_ACTION.LIMITED_FREQUENCY.label}</Text>
            </Space>
          </Radio>
        </RadioGroup>

        {typeFrequency === LIMIT_ACTION.LIMITED_FREQUENCY.value && (
          <>
            <SettingWrapper label={t(translations.cappingLevel.title)}>
              <Select
                className="ants-w-28"
                options={Object.values(LIMIT_ACTION.CAPPING_LEVEL)}
                value={cappingLevel}
                onChange={cappingLevel =>
                  onChangeLimitedSubmit({
                    cappingLevel,
                  })
                }
              />
            </SettingWrapper>
            {Array.isArray(conditions) &&
              (conditions.length > 0 ? (
                conditions.map((item, index) => (
                  <div key={index} className="ants-relative ants-flex-nowrap ants-gap-5">
                    <LimitedSubmitConditionRow
                      conditions={conditions}
                      condition={item}
                      fields={memoizedFields}
                      onChange={condition => onChangeCondition({ condition, index })}
                      onChangeRemoveCondition={() => onClickRemoveCondition(index)}
                      isClosedButton={conditions.length === 1}
                      timeUnits={memoizedTimeUnits}
                    />
                  </div>
                ))
              ) : (
                <></>
              ))}

            <div>
              <Button
                type="text"
                disabled={conditions.length >= memoizedFields.length * memoizedTimeUnits.length}
                onClick={onClickAddCondition}
              >
                + Add more
              </Button>
            </div>
            <div>
              <Text className="!ants-text-gray-4 ants-mb-5px">{LIMIT_ACTION.LIMIT_MESS_POSITION.label}</Text>
              <Select
                options={Object.values(LIMIT_ACTION.POSITION)}
                value={messagePosition}
                onChange={messagePosition =>
                  onChangeLimitedSubmit({
                    messagePosition,
                  })
                }
              />{' '}
            </div>
          </>
        )}
      </Space>

      <VerifiedSubmit />
    </LimitedSubmitWrapper>
  );
};
