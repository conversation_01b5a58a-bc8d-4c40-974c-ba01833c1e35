import React, { useEffect, useState } from 'react';
import { TListBlockSquare } from './type';
import { WrapperBlockSquare } from './styled';
import { BlockSquareItem } from './BlockSquareItem';

export const ListBlockSquare: React.FC<TListBlockSquare> = props => {
  const { data, createLabel, activeId, callback = () => {} } = props;

  // state
  const [listSquare, setListSquare] = useState<any[]>([]);
  const [isOpenCreate, setIsOpenCreate] = useState<boolean>(false);
  useEffect(() => {
    if (Array.isArray(data)) {
      const createBlockSquare = { label: createLabel, icon: 'icon-ants-plus-slim', value: 'CREATE' };
      const listBlockSquare = [createBlockSquare, ...data];
      setListSquare(listBlockSquare);
    }
  }, [data]);

  const handleClickItem = (type?: string, value?: any) => {
    switch (type) {
      case 'EDIT':
      case 'REMOVE':
        const data = listSquare.find(square => square.value === value);
        callback(type, data);
        break;
      case 'CREATE':
        callback(type);
        break;

      default: {
        break;
      }
    }
  };

  return (
    <>
      <WrapperBlockSquare>
        {listSquare.map(square => {
          return (
            <BlockSquareItem
              activeId={activeId}
              value={square.value}
              label={square.label}
              icon={square.icon}
              imageUrl={square.imageUrl}
              callback={(type?: string, value?: any) => handleClickItem(type, value)}
            />
          );
        })}
      </WrapperBlockSquare>
    </>
  );
};

ListBlockSquare.defaultProps = {
  createLabel: 'Create New',
  data: [],
  createEl: () => <></>,
};
