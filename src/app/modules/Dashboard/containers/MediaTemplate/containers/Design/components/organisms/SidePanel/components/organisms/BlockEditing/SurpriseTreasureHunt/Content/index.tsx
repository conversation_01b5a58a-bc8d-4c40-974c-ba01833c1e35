// Libraries
import React, { useState, useEffect } from 'react';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { cloneDeep, isEmpty } from 'lodash';

// Atoms
import { Space, Button, Text, Input } from 'app/components/atoms';

// Molecules
import { UploadImage } from 'app/components/molecules/UploadImage';
import { AlignSetting } from '../../../../molecules/AlignSetting';
import { CollapsePanel, InputNumber, Select, SliderWithInputNumber } from 'app/components/molecules';
import { SettingWrapper, SwitchLabel } from '../../../../molecules';
import { Collapse } from 'app/components/molecules/Collapse';
import { Tooltip } from '@antscorp/antsomi-ui';
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Locales
import { translations } from 'locales/translations';

// Selectors
import {
  selectBlockSelected,
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectSidePanel,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Styled
import { WrapperBlockTime, WrapperUnitTime } from './styled';

// Molecules
import CustomizeReward from '../../../CustomizeReward';

// Utils
import { random } from 'app/utils/common';
import { handleError } from 'app/utils/handleError';
import { getPreventKeyboardAction } from 'app/utils/web';
import { getTranslateMessage } from 'utils/messages';

// Types
import { TElement } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

// Constants
import {
  ACTION_TYPES_CHANGE,
  ANIMATION_CLICKED,
  ANIMATED_VALUE,
  MIN_CELL_GAP,
  MIN_OF_METRICS,
  RANGE_DIMENSIONS,
  ANIMATION_HOVER,
} from './constants';
import {
  SIDE_PANEL_COLLAPSE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import CellImages from '../../../CellImages';

const ERROR_POSITION = {
  TOP: {
    value: 'top',
    label: getTranslateMessage(translations.top.title),
  },
  CENTER: {
    value: 'center',
    label: getTranslateMessage(translations.center.title),
  },
  BOTTOM: {
    value: 'bottom',
    label: getTranslateMessage(translations.bottom.title),
  },
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/SurpriseTreasureHunt/Content/index.tsx';

interface ContentProps {}

export const Content: React.FC<ContentProps> = props => {
  const dispatch = useDispatch();

  // Actions
  const { updateBlock, setSidePanel, updateBlockSetting } = mediaTemplateDesignActions;

  // I18n
  const { t } = useTranslation();

  // Selector
  const blockSelected = useSelector(selectBlockSelected) as TElement;
  const sidePanel = useSelector(selectSidePanel);
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);

  const errorMessage = blockSelected.settings.errorMessage || {};

  //state
  const [openCustomizeWheel, setOpenCustomizeWheel] = useState<boolean>(false);
  const [isOpenCellImages, setIsOpenCellImages] = useState(false);
  const [isShowSectionValidate, setIsShowSectionValidate] = useState<boolean>(false);
  const { sections = [], isChangedCellImageFirstTime = false } = blockSelected?.settings;

  useEffect(() => {
    const preventHandleKeyDown = getPreventKeyboardAction(['undo']).onKeyDown;
    const preventHandleKeyUp = getPreventKeyboardAction(['undo']).onKeyUp;
    if (openCustomizeWheel) {
      window.addEventListener('keydown', preventHandleKeyDown);
      window.addEventListener('keyup', preventHandleKeyUp);
    }

    return () => {
      window.removeEventListener('keydown', preventHandleKeyDown);
      window.removeEventListener('keyup', preventHandleKeyUp);
    };
  }, [openCustomizeWheel]);

  const onChangeSurpriseTreasureHunt = (type: string, value: any) => {
    try {
      dispatch(
        updateBlock(
          produce(blockSelected, draft => {
            switch (type) {
              case ACTION_TYPES_CHANGE.UPDATE_IMAGE_ALL: {
                draft.settings.cellImages.imageDynamic = value;
                draft.settings.isChangedCellImageFirstTime = true;
                break;
              }
              case ACTION_TYPES_CHANGE.METRICS.ROWS: {
                const imageDynamic = cloneDeep(blockSelected.settings.cellImages.imageDynamic);

                if (!Array.isArray(imageDynamic)) return;
                const isIncrease = blockSelected.settings.metrics.rows < value;

                const numberLoop = Math.abs(blockSelected.settings.metrics.rows - value);

                if (isIncrease) {
                  const columnSizes = blockSelected.settings.metrics.columns;

                  let indexLoop = 0;
                  while (indexLoop < numberLoop) {
                    const newDataAppend: Array<{ key?: string; previewUrlBefore?: string; previewUrlAfter?: string }> =
                      [];
                    let index = 0;
                    while (index < columnSizes) {
                      const randomKey = random(10);
                      newDataAppend.push({
                        key: randomKey,
                        previewUrlBefore: '',
                        previewUrlAfter: '',
                      });

                      index++;
                    }

                    imageDynamic.push(newDataAppend);
                    indexLoop++;
                  }

                  draft.settings.cellImages.imageDynamic = imageDynamic;
                } else {
                  let indexLoop = 0;
                  while (indexLoop < numberLoop) {
                    imageDynamic.pop();
                    indexLoop++;
                  }
                  draft.settings.cellImages.imageDynamic = imageDynamic;
                }

                draft.settings.metrics.rows = value;
                break;
              }
              case ACTION_TYPES_CHANGE.METRICS.COLUMNS: {
                let imageDynamicTemp = cloneDeep(blockSelected.settings.cellImages.imageDynamic);
                if (!Array.isArray(imageDynamicTemp)) return;
                const { rows, columns } = blockSelected.settings.metrics;
                const isIncrease = columns < value;
                const numberLoop = Math.abs(columns - value);
                let maxCurrentLength = value;
                let indexLoop = 0;
                while (indexLoop < numberLoop) {
                  for (let i = 0; i < rows; i++) {
                    if (isIncrease) {
                      if (imageDynamicTemp[i].length < maxCurrentLength) {
                        const randomKey = random(10);
                        imageDynamicTemp[i].push({
                          key: randomKey,
                          previewUrlBefore: '',
                          previewUrlAfter: '',
                        });
                      }
                    } else {
                      if (imageDynamicTemp[i].length > maxCurrentLength) {
                        imageDynamicTemp[i].pop();
                      }
                    }
                  }
                  indexLoop++;
                }

                draft.settings.cellImages.imageDynamic = imageDynamicTemp;
                draft.settings.metrics.columns = value;
                break;
              }
              case ACTION_TYPES_CHANGE.CHANGE_IMAGE_DYNAMIC: {
                const { imageKey = '', imageUrl = '', isApplyAll = false, isBefore = false } = value;
                let imageDynamicTemp = cloneDeep(blockSelected.settings.cellImages.imageDynamic);

                if (!Array.isArray(imageDynamicTemp)) return;

                const { rows, columns } = blockSelected.settings.metrics;

                if (isApplyAll) {
                  const keyUpdate = isBefore ? 'imageAllUrlBefore' : 'imageAllUrlAfter';
                  draft.settings.cellImages[keyUpdate] = imageUrl;
                  break;
                }

                const previewUrlKey = isBefore ? 'previewUrlBefore' : 'previewUrlAfter';
                for (let i = 0; i < rows; i++) {
                  for (let j = 0; j < columns; j++) {
                    const item = imageDynamicTemp[i][j];
                    if (item.key === imageKey) {
                      item[previewUrlKey] = imageUrl;
                    }
                  }
                }

                draft.settings.cellImages.imageDynamic = imageDynamicTemp;
                break;
              }
              case ACTION_TYPES_CHANGE.CONTAINER_WIDTH: {
                draft.settings.dimensions.boxes.width = value;
                break;
              }
              case ACTION_TYPES_CHANGE.CELL_GAP: {
                draft.settings.dimensions.cells.gap = value;
                break;
              }
              case ACTION_TYPES_CHANGE.ALIGN.GIFT_BOXES: {
                draft.settings.outerContainerStyles.justify = value;
                break;
              }
              case ACTION_TYPES_CHANGE.ALIGN.CELLS: {
                draft.settings.cellContainerStyles.justify = value;
                break;
              }
              case ACTION_TYPES_CHANGE.TIME_DELAY: {
                draft.settings.translationTime = value;
                break;
              }
              case ACTION_TYPES_CHANGE.ANIMATION_CLICKED.CHANGE_TYPE: {
                draft.settings.animationClicked = {
                  imagePreviewUrl: '',
                  type: value,
                };
                break;
              }
              case ACTION_TYPES_CHANGE.ANIMATION_CLICKED.CHANGE_IMAGE: {
                draft.settings.animationClicked.imagePreviewUrl = value.url;
                break;
              }
              case ACTION_TYPES_CHANGE.IS_APPLY_IMAGE_ALL: {
                if (Object.keys(value).length) {
                  Object.keys(value).forEach(key => {
                    draft.settings.cellImages[key] = value[key];
                  });
                }
                break;
              }
              case ACTION_TYPES_CHANGE.ERROR_MESSAGE: {
                draft.settings.errorMessage = {
                  ...(blockSelected.settings.errorMessage || {
                    position: 'bottom',
                    message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
                  }),
                  ...value,
                };
                break;
              }
              case ACTION_TYPES_CHANGE.APPLY_CELL_IMAGES: {
                draft.settings.cellImages = value;
                break;
              }
              default: {
                break;
              }
            }
          }),
        ),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeSurpriseTreasureHunt',
        args: {},
      });
    }
  };

  const onChangeAnimationHover = (dataIn: any) => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockSetting(
            produce(blockSelected.settings, draft => {
              draft.animationHover = dataIn;
            }),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeAnimationHover',
        args: {},
      });
    }
  };

  useEffect(() => {
    if (blockSelected && !isChangedCellImageFirstTime) {
      const { rows, columns } = blockSelected.settings.metrics;
      const data: Array<{ key?: string; previewUrlBefore?: string; previewUrlAfter?: string }[]> = [];

      for (let i = 0; i < rows; i++) {
        data[i] = [];
        for (let j = 0; j < columns; j++) {
          // Gán giá trị tự động vào mỗi phần tử
          const randomKey = random(10);
          // const index = i * columns + j + 1;
          data[i][j] = {
            key: randomKey,
            previewUrlBefore: '',
            previewUrlAfter: '',
          };
        }
      }

      onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.UPDATE_IMAGE_ALL, data);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // Init default active panel
    if (
      ![
        SIDE_PANEL_COLLAPSE.SURPRISE_TREASURE_HUNT,
        SIDE_PANEL_COLLAPSE.SURPRISE_TREASURE_HUNT_CELLS,
        SIDE_PANEL_COLLAPSE.COUPON_SETTING,
      ].includes(sidePanel.activePanel)
    ) {
      dispatch(
        setSidePanel({
          activePanel: SIDE_PANEL_COLLAPSE.SURPRISE_TREASURE_HUNT,
        }),
      );
    }

    if (!errorMessage || !errorMessage.position) {
      onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ERROR_MESSAGE, {});
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (blockSelected) {
    const { settings } = blockSelected;
    const { metrics, dimensions } = settings;

    const closeCustomizeWheel = () => {
      if (sections.every(s => s.validateInfo?.filter(v => v.status === 'error').length === 0)) {
        setOpenCustomizeWheel(false);
        return;
      }
      setIsShowSectionValidate(true);
    };

    const renderContentGiftBoxes = (): React.ReactNode => (
      <Space size={20} direction="vertical">
        <Button className="ants-w-full" type="primary" onClick={() => setOpenCustomizeWheel(true)}>
          {t(translations.customizeRewards.title)}

          {errors[blockSelected.id] ? (
            <Tooltip title={'This pool is removed'}>
              <ErrorIcon />
            </Tooltip>
          ) : warnings[blockSelected.id] ? (
            <Tooltip title={'You do not have permission on this pool anymore'}>
              <WarningIcon />
            </Tooltip>
          ) : null}
        </Button>
        <SettingWrapper label={t(translations.numberOfRows.title)}>
          <InputNumber
            value={metrics.rows}
            min={MIN_OF_METRICS.ROWS}
            required
            onChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.METRICS.ROWS, value)}
          />
        </SettingWrapper>
        <SettingWrapper label={t(translations.numberOfColumns.title)}>
          <InputNumber
            value={metrics.columns}
            min={MIN_OF_METRICS.COLUMNS}
            required
            onChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.METRICS.COLUMNS, value)}
          />
        </SettingWrapper>
        <SliderWithInputNumber
          label={`${t(translations.widthPercent.title)}`}
          labelClassName="!ants-text-gray-4"
          min={RANGE_DIMENSIONS.BOXES.MIN}
          max={RANGE_DIMENSIONS.BOXES.MAX}
          value={dimensions.boxes.width}
          onAfterChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.CONTAINER_WIDTH, value)}
        />
        <AlignSetting
          label={t(translations.align.title)}
          align={settings.outerContainerStyles.justify}
          onChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ALIGN.GIFT_BOXES, value)}
        />
      </Space>
    );

    const renderContentCell = (): React.ReactNode => (
      <Space size={20} direction="vertical">
        <Button className="ants-w-full" type="primary" onClick={() => setIsOpenCellImages(true)}>
          {t(translations.cellImage.title)}
        </Button>
        <SliderWithInputNumber
          label={`${t(translations.cellGapPx.title)}`}
          labelClassName="!ants-text-gray-4"
          min={MIN_CELL_GAP}
          value={dimensions.cells.gap}
          onAfterChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.CELL_GAP, value)}
        />
        <AlignSetting
          label={t(translations.align.title)}
          align={settings.cellContainerStyles.justify}
          onChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ALIGN.CELLS, value)}
        />
        <Select
          label={t(translations.animationWhenHover.title)}
          value={settings.animationHover}
          options={Object.values(ANIMATION_HOVER)}
          onChange={value => onChangeAnimationHover(value)}
        />
        <Select
          label={t(translations.animationWhenClicked.title)}
          value={settings.animationClicked.type || ANIMATED_VALUE.BLOCK}
          options={Object.values(ANIMATION_CLICKED)}
          onChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ANIMATION_CLICKED.CHANGE_TYPE, value)}
        />
        {[ANIMATED_VALUE.BLOCK].includes(settings.animationClicked.type) && (
          <>
            <SettingWrapper label={t(translations.replaceImage.title)} />
            <UploadImage
              required
              selectedImage={{ url: settings.animationClicked.imagePreviewUrl }}
              // showImageURL={false}
              onRemoveImage={value =>
                onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ANIMATION_CLICKED.CHANGE_IMAGE, '')
              }
              onChangeImage={value =>
                onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ANIMATION_CLICKED.CHANGE_IMAGE, value)
              }
            />
          </>
        )}
        <WrapperBlockTime>
          <Text>{t(translations.translationTime.title)}</Text>
          <WrapperUnitTime>
            <InputNumber
              value={Number(settings?.translationTime)}
              required
              min={0}
              onChange={value => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.TIME_DELAY, value)}
            />
            <Text key="">{t(translations.seconds.unit)}</Text>
          </WrapperUnitTime>
        </WrapperBlockTime>
      </Space>
    );

    return (
      <>
        <Collapse
          defaultActiveKey={SIDE_PANEL_COLLAPSE.SURPRISE_TREASURE_HUNT}
          accordion
          activeKey={sidePanel.activePanel}
          onChange={activePanel => {
            dispatch(
              setSidePanel({
                activePanel: activePanel as string,
              }),
            );
          }}
        >
          <CollapsePanel header={t(translations.giftBox.title)} key={SIDE_PANEL_COLLAPSE.SURPRISE_TREASURE_HUNT}>
            {renderContentGiftBoxes()}
          </CollapsePanel>
          <CollapsePanel
            header={t(translations.giftCells.title)}
            key={SIDE_PANEL_COLLAPSE.SURPRISE_TREASURE_HUNT_CELLS}
          >
            {renderContentCell()}
          </CollapsePanel>
          <CollapsePanel header={t(translations.couponSetting.title)} key={SIDE_PANEL_COLLAPSE.COUPON_SETTING}>
            <div className="ants-flex ants-flex-col ants-space-y-5">
              <div>
                <Text className="!ants-text-gray-4 ants-mb-5px">{t(translations.errorMessage.title)}</Text>
                <Input
                  placeholder={t(translations.errorMessage.title)}
                  value={errorMessage?.message}
                  onChange={e =>
                    onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ERROR_MESSAGE, { message: e.target.value })
                  }
                />
              </div>

              <Select
                label={t(translations.position.title)}
                options={Object.values(ERROR_POSITION)}
                // disabled={!item.canWin}
                onChange={position => onChangeSurpriseTreasureHunt(ACTION_TYPES_CHANGE.ERROR_MESSAGE, { position })}
                value={errorMessage?.position}
                className={`ants-w-full ants-h-[30px]`}
              />
            </div>
          </CollapsePanel>
        </Collapse>
        <CustomizeReward
          openCustomizeWheel={openCustomizeWheel}
          blockSelected={blockSelected}
          sections={sections}
          closeCustomizeWheel={closeCustomizeWheel}
          isShowSectionValidate={isShowSectionValidate}
          setIsShowSectionValidate={setIsShowSectionValidate}
          actionKey={STANDARDS_BLOCKS.SURPRISE_TREASURE_HUNT.actionKey}
        />
        <CellImages
          visible={isOpenCellImages}
          toggle={data => setIsOpenCellImages(data)}
          settings={blockSelected?.settings}
          onChangeSurpriseTreasureHunt={onChangeSurpriseTreasureHunt}
        />
      </>
    );
  }

  return null;
};
