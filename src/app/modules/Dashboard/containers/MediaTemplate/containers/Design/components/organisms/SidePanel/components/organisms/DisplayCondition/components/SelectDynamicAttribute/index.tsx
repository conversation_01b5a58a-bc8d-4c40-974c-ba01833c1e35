// Libraries
import { useState } from 'react';
import { isEmpty, omit } from 'lodash';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { useSelector } from 'react-redux';
import { Tooltip } from '@antscorp/antsomi-ui';

// Atoms
import { Text, Space, Icon, Button } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../../../molecules';

// Components
import { AddDynamicContent } from '../../../AddDynamicContent';
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Selectors
import { selectContentSources } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { selectJourneySettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import {
  selectBlockSelected,
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectWorkspaceErrors,
  selectWorkspaceWarnings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

type TProps = {
  values: Record<string, any>;
  onOk?: (values: Record<string, any>) => void;
  className?: string;
  showIndex?: boolean;
};

const SelectDynamicAttribute = (props: TProps) => {
  const { t } = useTranslation();

  const { values, showIndex = true } = props;
  const [isOpenDynamicContent, setOpenDynamicContent] = useState<boolean>(false);

  // Selectors
  const contentSources = useSelector(selectContentSources);
  const journeySettings = useSelector(selectJourneySettings);
  const blockSelected = useSelector(selectBlockSelected) || {};
  const workspaceErrors = useSelector(selectWorkspaceErrors);
  const workspaceWarnings = useSelector(selectWorkspaceWarnings);
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);

  const handleOnOk = (values: Record<string, any>) => {
    let draftValue = { ...values, index: values.index };

    const notUsedField = ['mappingFields'];

    if (props.onOk) props.onOk(omit(draftValue, notUsedField));

    setOpenDynamicContent(false);
  };

  return (
    <div className={props.className}>
      {values?.attribute?.value ? (
        <SettingWrapper
          label={values.attribute.label || values.attribute.value}
          labelClassName="!ants-text-black !ants-font-medium"
        >
          <Space>
            {/* Show Icon Error or Warning */}
            {errors[blockSelected.id] && workspaceErrors?.promotionPool?.[values?.pool] ? (
              <Tooltip title={workspaceErrors?.promotionPool?.[values?.pool]}>
                <ErrorIcon />
              </Tooltip>
            ) : warnings[blockSelected.id] && workspaceWarnings?.promotionPool?.[values?.pool] ? (
              <Tooltip title={workspaceWarnings?.promotionPool?.[values?.pool]}>
                <WarningIcon />
              </Tooltip>
            ) : null}

            {values.index && (
              <Text>
                {t(translations.index.title, 'Index')}: {values.index}
              </Text>
            )}

            <Button type="text" icon={<Icon type="icon-ants-edit-2" />} onClick={() => setOpenDynamicContent(true)} />
          </Space>
        </SettingWrapper>
      ) : (
        <Button className="ants-w-100" onClick={() => setOpenDynamicContent(true)}>
          + {t(translations.displayCondition.addField.title, 'Add field')}
        </Button>
      )}

      <AddDynamicContent
        contentSources={contentSources}
        journeySettings={journeySettings}
        defaultData={!isEmpty(values?.attribute) ? values : {}}
        defaultDynamicIndex={1}
        showDisplayFormat={false}
        visible={isOpenDynamicContent}
        onCancel={() => setOpenDynamicContent(false)}
        onOk={handleOnOk}
        additionalAttrProperties={['displayFormat', 'autoSuggestion']}
        showIndex={showIndex}
        modalTitle={t(translations.displayCondition.addField.title, 'Add field')}
      />
    </div>
  );
};

export default SelectDynamicAttribute;
