// Libraries
import { get } from 'lodash';

// Types
import { TVerifiedSubmitConditions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Locales
import { translations } from 'locales/translations';

export const validateFilters = ({
  filters,
  formFields,
}: {
  filters: TVerifiedSubmitConditions;
  formFields: Array<Record<string, any>>;
}) => {
  const errors: any = {
    OR: [],
    isError: false,
  };

  if (
    get(filters, 'OR.length') === 1 &&
    get(filters, `OR[0].AND.length`) === 1 &&
    !get(filters, `OR[0].AND[0].field`)
  ) {
    return errors;
  }

  filters.OR?.forEach(or => {
    const orErrs: any = {
      AND: [],
    };
    or.AND.forEach(and => {
      const andErrs = {
        field: '',
        operator: '',
        value: '',
      };
      const { field, operator, value, valueType } = and;

      const falsyValue =
        (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) &&
        operator !== 'exists' &&
        operator !== 'not_exists';

      const isRequiredField = !field;
      const isFieldNotExist = field && !formFields.find(formField => formField.value === field);
      const isRequiredValue =
        (valueType === 'attribute' && !value?.attribute) || (valueType === 'normal' && falsyValue);

      if (isRequiredField) {
        andErrs.field = getTranslateMessage(translations.thisFieldIsRequired.title);
      }

      if (isFieldNotExist) {
        andErrs.field = getTranslateMessage(translations.fieldNotExist.title);
      }

      if (isRequiredValue) {
        andErrs.value = getTranslateMessage(translations.thisFieldIsRequired.title);
      }

      if (isRequiredField || isFieldNotExist || isRequiredValue) {
        errors.isError = true;
      }

      orErrs.AND.push(andErrs);
    });

    errors.OR.push(orErrs);
  });

  return errors;
};
