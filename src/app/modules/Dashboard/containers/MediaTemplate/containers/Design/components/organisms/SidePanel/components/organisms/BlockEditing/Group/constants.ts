import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const DISPLAY_OPTIONS = {
  FLEX: {
    label: 'Flex',
    value: 'flex',
  },
  BLOCK: {
    label: 'Block',
    value: 'block',
  },
  INLINE: {
    label: 'Inline',
    value: 'inline',
  },
  INLINE_BLOCK: {
    label: 'Inline Block',
    value: 'inline-block',
  },
};

export const DIRECTION_OPTIONS = {
  HORIZONTAL: {
    label: getTranslateMessage(translations.horizontal.title),
    value: 'row',
  },
  VERTICAL: {
    label: getTranslateMessage(translations.vertical.title),
    value: 'column',
  },
};

export const ALIGN_OPTIONS = {
  START: {
    label: getTranslateMessage(translations.start.title),
    value: 'flex-start',
  },
  CENTER: {
    label: getTranslateMessage(translations.center.title),
    value: 'center',
  },
  END: {
    label: getTranslateMessage(translations.end.title),
    value: 'flex-end',
  },
  STRETCH: {
    label: getTranslateMessage(translations.stretch.title),
    value: 'stretch',
  },
  BASELINE: {
    label: getTranslateMessage(translations.baseline.title),
    value: 'baseline',
  },
};

export const JUSTIFY_OPTIONS = {
  START: {
    label: getTranslateMessage(translations.start.title),
    value: 'flex-start',
  },
  CENTER: {
    label: getTranslateMessage(translations.center.title),
    value: 'center',
  },
  END: {
    label: getTranslateMessage(translations.end.title),
    value: 'flex-end',
  },
  SPACE_BETWEEN: {
    label: getTranslateMessage(translations.spaceBetween.title),
    value: 'space-between',
  },
  SPACE_AROUND: {
    label: getTranslateMessage(translations.spaceAround.title),
    value: 'space-around',
  },
};

export const ALIGN_CONTENT_OPTIONS = {
  ...JUSTIFY_OPTIONS,
  STRETCH: {
    label: getTranslateMessage(translations.stretch.title),
    value: 'stretch',
  },
};

export const WRAP_OPTIONS = {
  NO_WRAP: {
    label: getTranslateMessage(translations.noWrap.title),
    value: 'nowrap',
  },
  WRAP: {
    label: getTranslateMessage(translations.wrap.title),
    value: 'wrap',
  },
};
