// Libraries
import { translations } from 'locales/translations';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Input, RequiredLabel, Space, Text } from 'app/components/atoms';

// Components
import ConditionsSetting from '../ConditionsSetting';

// Slice
import {
  selectBlockSelected,
  selectIsShowErrorAlert,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { handleError } from 'app/utils/handleError';

interface VerifiedSubmitProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/LimitedSubmitButton/components/VerifiedSubmit/index.tsx';

const VerifiedSubmit: React.FC<VerifiedSubmitProps> = () => {
  // Hooks
  const dispatch = useDispatch();

  // i18n
  const { t } = useTranslation();

  // Selectors
  const isShowErrorAlert = useSelector(selectIsShowErrorAlert);

  const blockSelected = useSelector(selectBlockSelected);
  const { settings } = blockSelected || {};
  const { fields, verifiedSubmit } = settings || {};
  const { conditions, errorMessage } = verifiedSubmit || {};

  // Actions
  const { updateBlockFieldsSelected } = mediaTemplateDesignActions;

  // Memo
  const memoizedFields = useMemo(() => {
    if (typeof fields === 'object') {
      const draftFields: any[] = Object.values({ ...fields })
        .filter((field: any) => !['privacyText', 'submitButton'].includes(field.id) && field.order !== -1)
        .map((field: any) => ({
          label: field.nameField,
          value: field.isCustom ? field.id : field.inputName,
        }));
      return draftFields;
    }

    return [];
  }, [fields]);

  // Handlers
  const onChangeSettings = (
    dataUpdate: Array<{
      fieldPath: string;
      data: any;
    }>,
  ) => {
    try {
      dispatch(
        updateBlockFieldsSelected({
          dataUpdate,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeConditionSetting',
        args: {},
      });
    }
  };

  return (
    <div className="ants-mt-5">
      <Space direction="vertical" size={20}>
        <Text size="medium" className="ants-font-medium !ants-text-cus-dark">
          {t(translations.verifiedSubmit.title)}
        </Text>

        <ConditionsSetting
          conditions={conditions}
          formFields={memoizedFields}
          onChange={conditions =>
            onChangeSettings([{ fieldPath: 'settings.verifiedSubmit.conditions', data: conditions }])
          }
        />

        <Space direction="vertical" size={5}>
          <RequiredLabel>{t(translations.errorMessage.title)}</RequiredLabel>
          <Input
            required
            focused={isShowErrorAlert}
            value={errorMessage}
            onAfterChange={value =>
              onChangeSettings([{ fieldPath: 'settings.verifiedSubmit.errorMessage', data: value }])
            }
          />
        </Space>
      </Space>
    </div>
  );
};

export default VerifiedSubmit;
