// libraries
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

// redux
import { useDispatch, useSelector } from 'react-redux';

// constants
import { DESIGN_TEMPLATE_MODE } from 'modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// components
import { ModalV2 } from '@antscorp/antsomi-ui';
import { Icon } from 'app/components/atoms';
import { RadioGroup } from 'app/components/molecules';

// utils
import { translations } from 'locales/translations';
import { checkIsChangeSettingsCodeMode, checkIsChangeSettingsDesignMode } from './utils';

// selectors
import {
  selectDesignTemplateMode,
  selectMediaTemplateDesign,
  selectWorkspace,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// actions
import {
  INIT_PAGE_CODE_MODE,
  mediaTemplateDesignActions,
} from 'modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

type TSelectModeDesign = {};

const options = [
  {
    label: <Icon title="general" className="ants-flex ants-items-center" type="icon-ants-desgin-mode" size={18} />,
    value: DESIGN_TEMPLATE_MODE.GENERAL,
  },
  {
    label: <Icon title="code" className="ants-flex ants-items-center" type="icon-ants-code-mode" size={18} />,
    value: DESIGN_TEMPLATE_MODE.CODE,
  },
];

export const SelectModeDesign: React.FC<TSelectModeDesign> = props => {
  // selectors
  const designTemplateMode = useSelector(selectDesignTemplateMode);
  const workspace = useSelector(selectWorkspace);
  const { present } = useSelector(selectMediaTemplateDesign) || {};

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { switchModeDesign } = mediaTemplateDesignActions;

  // state
  const [stateModeInfo, setStateModeInfo] = useState({
    selectedMode: '',
    isOpenPopupSwitchMode: false,
  });

  const { selectedMode, isOpenPopupSwitchMode } = stateModeInfo;

  const handlePopupSwitchDesignMode = (mode: string) => {
    const isCodeModeDesign = designTemplateMode === DESIGN_TEMPLATE_MODE.CODE;
    const { viewPages = [], template } = workspace;

    const isHasChangeWorkspace = isCodeModeDesign
      ? checkIsChangeSettingsCodeMode(workspace)
      : checkIsChangeSettingsDesignMode(present);

    if (isHasChangeWorkspace) {
      setStateModeInfo(prev => ({
        ...prev,
        selectedMode: mode,
        isOpenPopupSwitchMode: true,
      }));
    } else {
      dispatch(switchModeDesign(mode));
    }
  };

  const handleSwitchMode = () => {
    dispatch(switchModeDesign(selectedMode));
    setStateModeInfo(prev => ({
      ...prev,
      selectedMode: '',
      isOpenPopupSwitchMode: false,
    }));
  };

  return (
    <>
      <RadioGroup
        label={`${t(translations.mode.title)}:`}
        value={designTemplateMode}
        options={options}
        onChange={event => handlePopupSwitchDesignMode(event.target.value)}
        optionType="button"
        customStyles={{
          item: {
            padding: '0 8px',
            color: '#005eb8',
          },
          activeItemStyle: {
            backgroundColor: '#005eb8',
            color: '#fff',
          },
        }}
      />

      {/* modal switch Mode */}
      <ModalV2
        width={380}
        okText={t(translations.confirm.title)}
        cancelText={t(translations.cancel.title)}
        title={t(translations.switchMode.title)}
        open={isOpenPopupSwitchMode}
        centered
        onCancel={() => setStateModeInfo(prev => ({ ...prev, isOpenPopupSwitchMode: false, selectedMode: '' }))}
        onOk={handleSwitchMode}
      >
        {t(translations.switchMode.content)}
      </ModalV2>
    </>
  );
};
