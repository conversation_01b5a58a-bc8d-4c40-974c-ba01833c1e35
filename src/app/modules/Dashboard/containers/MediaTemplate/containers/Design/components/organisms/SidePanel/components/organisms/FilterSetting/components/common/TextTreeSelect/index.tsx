// Libraries
import { ReactNode } from 'react';

// Atoms
import { Text } from 'app/components/atoms';

interface TextTreeSelectProps {
  label?: ReactNode | null;
  attribute: string | null;
  searchValue?: string;
  open?: boolean;
  onDropdownVisibleChange?: any;
}

export const TextTreeSelect: React.FC<TextTreeSelectProps> = props => {
  const { label, attribute, searchValue, open, onDropdownVisibleChange } = props;

  const onClick = (event: React.MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    onDropdownVisibleChange(open);
  };

  return (
    <Text className="ants-ml-2" onClick={onClick}>
      {searchValue ? '' : attribute ? label : ''}
    </Text>
  );
};
