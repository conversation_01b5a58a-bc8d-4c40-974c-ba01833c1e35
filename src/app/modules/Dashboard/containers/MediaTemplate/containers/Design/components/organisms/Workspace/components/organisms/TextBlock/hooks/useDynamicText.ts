import { useCallback, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { TextEditorRef, useDeepCompareEffect } from '@antscorp/antsomi-ui';
import { random } from 'app/utils/common';
import { selectCSDataOfGroup } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { TDynamicTextSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { DATA_MIGRATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';
import produce from 'immer';
import { isEmpty, omit, difference } from 'lodash';
import { getUpdatedDynamicTextValue } from '../utils';
import { buildMergeTag } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';

interface UseDynamicTextProps {
  id: string;
  type: string;
  isPreviewMode?: boolean;
  contentForPreview?: boolean;
  textBlockSettings: TDynamicTextSettings;
  contentSourcesData: ReturnType<typeof selectCSDataOfGroup>;
  editorRef: React.RefObject<TextEditorRef>;
  setProcessing: React.Dispatch<React.SetStateAction<boolean>>;
}

export const handleDynamicIdsUpdate = (currentIds: string[], newIds: string[], currentData: Record<string, any>) => {
  const deletedIds = difference(currentIds, newIds);

  if (deletedIds.length === 0) return null;

  return {
    fieldPath: 'settings.dynamic.data',
    data: omit(currentData, deletedIds),
  };
};

export const useDynamicText = ({
  id,
  type,
  isPreviewMode,
  contentForPreview,
  textBlockSettings,
  contentSourcesData,
  editorRef,
  setProcessing,
}: UseDynamicTextProps) => {
  const dispatch = useDispatch();
  const { updateBlockText } = mediaTemplateDesignActions;

  const {
    dynamic = DATA_MIGRATE[type].dynamic as {
      data: Record<string, any>;
      highlight: boolean;
      selectedId: string;
    },
  } = textBlockSettings;

  const { data: dataDynamic = {}, highlight: highlightVariable = true, selectedId: selectedDynamicId } = dynamic;
  const dataDynamicIds = Object.keys({ ...dataDynamic });

  const showMergeTag = isPreviewMode && !contentForPreview;

  const creatingDynamicId = useRef('');

  const handleClearCreatingDynamicId = useCallback(() => {
    creatingDynamicId.current = '';

    dispatch(
      updateBlockText({
        blockId: id,
        dataUpdate: [
          {
            fieldPath: 'settings.dynamic.selectedId',
            data: '',
          },
        ],
        ignoreUndoAction: true,
      }),
    );
  }, [dispatch, id, updateBlockText]);

  const getDynamicContent = useCallback(
    (row: Record<string, any>) => {
      const dynamicContent = showMergeTag
        ? buildMergeTag(row)
        : getUpdatedDynamicTextValue({
            dataDynamicRow: row,
            csData: contentSourcesData.data,
          });

      return dynamicContent;
    },
    [contentSourcesData.data, showMergeTag],
  );

  // Update text value of all key data dynamic
  useEffect(() => {
    if (contentSourcesData.isLoading) {
      return;
    }

    for (const dynamicId of Object.keys(dataDynamic)) {
      if (creatingDynamicId.current === dynamicId) continue;

      const row = dataDynamic[dynamicId];
      const textValue = getDynamicContent(row);

      editorRef.current?.updateSmartTagAttrs(dynamicId, {
        content: textValue,
      });
    }
  }, [contentSourcesData.isLoading, dataDynamic, getDynamicContent, editorRef]);

  // Update text value of creating dynamic
  useEffect(() => {
    const newDataDynamic = creatingDynamicId.current ? dataDynamic[creatingDynamicId.current] : {};
    const existingNewDynamic = selectedDynamicId && !isEmpty(newDataDynamic) && !selectedDynamicId.split(':').at(1);

    if (!existingNewDynamic || isPreviewMode || contentSourcesData.isLoading) {
      return;
    }

    const textValue = getDynamicContent(newDataDynamic);

    editorRef.current?.setSmartTag({
      id: creatingDynamicId.current,
      content: textValue,
    });
    handleClearCreatingDynamicId();
  }, [
    contentSourcesData.isLoading,
    dataDynamic,
    editorRef,
    getDynamicContent,
    handleClearCreatingDynamicId,
    isPreviewMode,
    selectedDynamicId,
  ]);

  useDeepCompareEffect(() => {
    if (isPreviewMode || !selectedDynamicId) {
      // Adjusted: allow in preview for consistency if needed, or keep !isPreviewMode
      return;
    }

    const [dataDynamicId, action] = selectedDynamicId?.split(':') || [];

    switch (action) {
      case 'cancel': {
        dispatch(
          updateBlockText({
            blockId: id,
            dataUpdate: [
              {
                fieldPath: 'settings.dynamic.selectedId',
                data: '',
              },
              {
                fieldPath: 'settings.dynamic.data',
                data: produce(dataDynamic, draft => {
                  if (isEmpty(dataDynamic[dataDynamicId])) {
                    delete draft[dataDynamicId];
                  }
                }),
              },
            ],
            ignoreUndoAction: true,
          }),
        );
        setProcessing(false);
        break;
      }
      case 'delete': {
        dispatch(
          updateBlockText({
            blockId: id,
            dataUpdate: [
              {
                fieldPath: 'settings.dynamic.selectedId',
                data: '',
              },
            ],
            ignoreUndoAction: true,
          }),
        );
        dispatch(
          updateBlockText({
            blockId: id,
            dataUpdate: [
              {
                fieldPath: 'settings.dynamic.data',
                data: produce(dataDynamic, draft => {
                  delete draft[dataDynamicId];
                }),
              },
            ],
          }),
        );
        editorRef.current?.deleteSmartTag(dataDynamicId);
        setProcessing(false);
        break;
      }
      default:
        break;
    }
  }, [isPreviewMode, selectedDynamicId, dataDynamic, id, updateBlockText, dispatch, editorRef, setProcessing]);

  const smartTagHandler = {
    edit: (dataDynamicId: string) => {
      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.dynamic.selectedId',
              data: `${dataDynamicId}:edit`,
            },
          ],
        }),
      );
    },
    setNew: () => {
      const randomId = random(8);
      creatingDynamicId.current = randomId;
      dispatch(
        updateBlockText({
          blockId: id,
          dataUpdate: [
            {
              fieldPath: 'settings.dynamic.selectedId',
              data: `${randomId}:create`,
            },
            {
              fieldPath: 'settings.dynamic.data',
              data: produce(dataDynamic, draft => {
                draft[randomId] = {};
              }),
            },
          ],
        }),
      );
    },
  };

  return {
    dataDynamic,
    dataDynamicIds,
    highlightVariable,
    selectedDynamicId,
    getDynamicContent, // May not be needed outside if editor updates handle it
    smartTagHandler,
    creatingDynamicIdRef: creatingDynamicId, // Expose ref if TextContent needs to interact
  };
};
