import { Moment } from 'moment';
import {
  TDataType,
  TFilters,
  TOperatorValue,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { DATA_TYPES, OPERATORS_OPTION } from './constants';

type Property = any;

export const getValidMomentDate = (date: Moment) => (date && date.isValid() ? date : null);

export function isInputBoolean(operatorValue: TOperatorValue, dataType: TDataType) {
  if (
    (operatorValue === OPERATORS_OPTION.EQUALS.value || operatorValue === OPERATORS_OPTION.NOT_EQUALS.value) &&
    // operatorValue === 'change_value')
    dataType === 'boolean'
  ) {
    return true;
  }

  return false;
}

export function isInputNumberBetween(operatorValue: TOperatorValue, dataType: TDataType) {
  if (operatorValue === OPERATORS_OPTION.BETWEEN.value && dataType === 'number') {
    return true;
  }
  return false;
}

export function isInputCalendarBetween(operatorValue: TOperatorValue, dataType: TDataType) {
  if (operatorValue === OPERATORS_OPTION.BETWEEN.value && dataType === 'datetime') {
    return true;
  }
  return false;
}

export function isInputDateTimeAgo(operatorValue: TOperatorValue, dataType: TDataType) {
  if (
    (operatorValue === OPERATORS_OPTION.EQUAL_TIME_AGO.value ||
      operatorValue === OPERATORS_OPTION.NOT_EQUAL_TIME_AGO.value ||
      operatorValue === OPERATORS_OPTION.BEFORE_TIME_AGO.value ||
      operatorValue === OPERATORS_OPTION.AFTER_TIME_AGO.value) &&
    dataType === 'datetime'
  ) {
    return true;
  }

  return false;
}

export function isInputDateTimeBetweenAgo(operatorValue: TOperatorValue, dataType: TDataType) {
  if (operatorValue === OPERATORS_OPTION.BETWEEN_TIME_AGO.value && dataType === 'datetime') {
    return true;
  }

  return false;
}

export function isInputCalendar(operatorValue: TOperatorValue, dataType: TDataType) {
  if (
    (operatorValue === OPERATORS_OPTION.EQUALS.value ||
      operatorValue === OPERATORS_OPTION.NOT_EQUALS.value ||
      operatorValue === OPERATORS_OPTION.BEFORE_DATE.value ||
      operatorValue === OPERATORS_OPTION.AFTER_DATE.value) &&
    dataType === 'datetime'
  ) {
    return true;
  }

  return false;
}

export function isInputOrSelect(
  operatorValue: TOperatorValue,
  dataType: TDataType = DATA_TYPES.STRING as TDataType,
  property: Property = {},
) {
  if (property === null || property === undefined) {
    return false;
  }

  // const operators = OPERATORS[dataType];
  // check with operatorValue for use case edit with condition matches and not not matches
  // if (isOperatorMulti(operatorValue) && !operators.includes(operatorValue)) {
  //   return true;
  // }
  const validOperator = isOperatorSuggestion(operatorValue);

  return validOperator || dataType === DATA_TYPES.NUMBER;
}

export function isInputSelectMulti(operatorValue: TOperatorValue, dataType: TDataType, property: Property) {
  const isMulti = isOperatorMulti(operatorValue);
  const validOperator = isOperatorSuggestion(operatorValue);
  const { autoSuggestion } = property || {};

  return isMulti && ((validOperator && autoSuggestion) === 1 || dataType === DATA_TYPES.NUMBER);
}

export function isOperatorMulti(operatorValue: TOperatorValue) {
  if (
    operatorValue === OPERATORS_OPTION.MATCHES.value ||
    operatorValue === OPERATORS_OPTION.NOT_MATCHES.value ||
    operatorValue === OPERATORS_OPTION.DOESNT_INCLUDE.value ||
    operatorValue === OPERATORS_OPTION.INCLUDES.value
  ) {
    return true;
  }

  return false;
}

export function isOperatorSuggestion(operatorValue: TOperatorValue) {
  if (
    operatorValue === OPERATORS_OPTION.MATCHES.value ||
    operatorValue === OPERATORS_OPTION.NOT_MATCHES.value ||
    operatorValue === OPERATORS_OPTION.EQUALS.value ||
    operatorValue === OPERATORS_OPTION.NOT_EQUALS.value ||
    operatorValue === OPERATORS_OPTION.CONTAINS.value ||
    operatorValue === OPERATORS_OPTION.DOESNT_CONTAIN.value
  ) {
    return true;
  }

  return false;
}

export function isCheckConditionMultiCheckbox(dataType: TDataType) {
  return dataType?.startsWith('array');
}

export function isInputNumber(_operatorValue: TOperatorValue, property) {
  if (property !== null && property !== undefined && property.displayFormat !== null) {
    const displayFormat = property.displayFormat || {};
    const { type } = displayFormat;

    if (type === 'NUMBER' || type === 'PERCENTAGE' || type === 'CURRENCY') {
      return true;
    }
  }
  return false;
}

export function isInputNull(operatorValue: TOperatorValue, dataType: TDataType) {
  if (operatorValue === OPERATORS_OPTION.NOT_EXISTS.value || operatorValue === OPERATORS_OPTION.EXISTS.value) {
    return true;
  }
  if (!dataType) {
    return true;
  }
  return false;
}

export const getInputType = ({
  operator,
  dataType,
  property = {},
  resInputBoolean,
  resInputNumberBetween,
  resInputCalendarBetween,
  resInputDateTimeAgo,
  resInputDateTimeBetweenAgo,
  resInputCalendar,
  resInputOrSelect,
  resInputSelectMulti,
  resInputArray,
  resInputNumber,
  resDefault,
}) => {
  let result: any = null;
  switch (true) {
    case isInputNull(operator, dataType): {
      result = null;
      break;
    }
    case isInputBoolean(operator, dataType): {
      result = resInputBoolean;
      break;
    }
    case isInputNumberBetween(operator, dataType): {
      result = resInputNumberBetween;
      break;
    }
    case isInputCalendarBetween(operator, dataType): {
      result = resInputCalendarBetween;
      break;
    }
    case isInputDateTimeAgo(operator, dataType): {
      result = resInputDateTimeAgo;
      break;
    }
    case isInputDateTimeBetweenAgo(operator, dataType): {
      result = resInputDateTimeBetweenAgo;
      break;
    }
    case isInputCalendar(operator, dataType): {
      result = resInputCalendar;
      break;
    }
    // case dataType === 'object_id' &&
    //   isCheckPropertyHasAutoSuggestion(property) &&
    //   isOperatorObjectIdHasSuggestion(operator, item): {
    //   element = <Input placeholder="object_id" value="" />;
    //   // return (
    //   //   <ConditionValueObjectId
    //   //     componentKey={componentKey}
    //   //     encryptConfig={props.encryptConfig}
    //   //     operatorValue={operatorValue}
    //   //     use={props.use}
    //   //     item={item}
    //   //     onChange={changeValue}
    //   //     callback={props.callback}
    //   //   />
    //   // );
    //   break;
    // }
    case isInputSelectMulti(operator, dataType, property): {
      result = resInputSelectMulti;
      break;
    }
    case isInputOrSelect(operator, dataType, property): {
      result = resInputOrSelect;
      break;
    }
    case isCheckConditionMultiCheckbox(dataType): {
      result = resInputArray;
      break;
    }
    case isInputNumber(operator, property): {
      result = resInputNumber;
      break;
    }
    default: {
      result = resDefault;
    }
  }
  return result;
};

export const getInputDefaultValue = ({ operator, dataType, property }) => {
  return getInputType({
    operator,
    dataType,
    property,
    resInputBoolean: 'true',
    resInputNumberBetween: '0 AND 0',
    resInputCalendarBetween: 'null AND null',
    resInputDateTimeAgo: {
      value: '0',
      time_unit: 'DAY',
    },
    resInputCalendar: null,
    resInputOrSelect: '',
    resInputSelectMulti: [],
    resInputArray: [],
    resInputNumber: 0,
    resDefault: '',
    resInputDateTimeBetweenAgo: {
      value: '0 AND 0',
      time_unit: 'DAY',
    },
  });
};

// export const validateFilters = (filters: TFilters, validateFirstCondition = false) => {
//   const errors: any = {
//     OR: [],
//     isError: false,
//   };
//   if (
//     filters.OR.length === 1 &&
//     filters.OR[0].AND.length === 1 &&
//     !filters.OR[0].AND[0].column &&
//     !validateFirstCondition
//   ) {
//     // Nếu có 1 hàng filter nhưng không chọn gì cho column thì vẫn sẽ pass qua, thay vì sẽ báo lỗi requied như trước đây
//     return errors;
//   } else {
//     filters.OR.forEach(or => {
//       const orErrs: any = {
//         AND: [],
//       };
//       or.AND.forEach(and => {
//         const andErrs = {
//           column: true,
//           operator: true,
//           value: true,
//         };
//         const { column, operator, value, event_metadata, customer_metadata, visitor_metadata, value_type } = and;

//         const falsyValue =
//           (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) &&
//           operator !== 'exists' &&
//           operator !== 'not_exists';

//         if (
//           !column ||
//           !operator ||
//           (value_type === 'normal' && falsyValue) ||
//           (value_type === 'event' && !event_metadata?.item_property_name) ||
//           (value_type === 'customer' && !customer_metadata?.item_property_name) ||
//           (value_type === 'visitor' && !visitor_metadata?.item_property_name)
//         ) {
//           if (!column) {
//             andErrs.column = false;
//           }
//           if (!operator) {
//             andErrs.operator = false;
//           }
//           if (
//             (value_type === 'normal' && falsyValue) ||
//             (value_type === 'event' && !event_metadata?.item_property_name) ||
//             (value_type === 'customer' && !customer_metadata?.item_property_name) ||
//             (value_type === 'visitor' && !visitor_metadata?.item_property_name)
//           ) {
//             andErrs.value = false;
//           }
//           errors.isError = true;
//         } else {
//           andErrs.column = true;
//           andErrs.operator = true;
//           andErrs.value = true;
//         }

//         orErrs.AND.push(andErrs);
//       });
//       errors.OR.push(orErrs);
//     });
//   }
//   return errors;
// };
export const validateFilters = (filters: TFilters, validateFirstCondition = false) => {
  const errors: any = {
    OR: [],
    isError: false,
  };
  if (
    filters.OR.length === 1 &&
    filters.OR[0].AND.length === 1 &&
    !filters.OR[0].AND[0].column &&
    !validateFirstCondition
  ) {
    // Nếu có 1 hàng filter nhưng không chọn gì cho column thì vẫn sẽ pass qua, thay vì sẽ báo lỗi requied như trước đây
    return errors;
  } else {
    filters.OR.forEach(or => {
      const orErrs: any = {
        AND: [],
      };
      or.AND.forEach(and => {
        const andErrs = {
          column: true,
          operator: true,
          value: true,
          extend: true,
        };
        const { column, operator, value, event_metadata, customer_metadata, visitor_metadata, value_type, extend } =
          and;
        const falsyValue =
          (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) &&
          operator !== 'exists' &&
          operator !== 'not_exists';

        if (
          !column ||
          !operator ||
          (value_type === 'normal' && falsyValue && (!extend || extend.length <= 0)) ||
          (value_type === 'event' && !event_metadata?.item_property_name) ||
          (value_type === 'customer' && !customer_metadata?.item_property_name) ||
          (value_type === 'visitor' && !visitor_metadata?.item_property_name)
        ) {
          if (!column) {
            andErrs.column = false;
          }
          if (!operator) {
            andErrs.operator = false;
          }
          if (
            (value_type === 'normal' && falsyValue && (!extend || extend.length <= 0)) ||
            (value_type === 'event' && !event_metadata?.item_property_name) ||
            (value_type === 'customer' && !customer_metadata?.item_property_name) ||
            (value_type === 'visitor' && !visitor_metadata?.item_property_name)
          ) {
            andErrs.value = false;
            andErrs.extend = false;
          }
          errors.isError = true;
        } else {
          andErrs.column = true;
          andErrs.operator = true;
          andErrs.value = true;
          andErrs.extend = true;
        }
        orErrs.AND.push(andErrs);
      });
      errors.OR.push(orErrs);
    });
  }
  return errors;
};
export const getScopSuggest = ({ itemTypeId }) => {
  switch (itemTypeId) {
    case -1003: // Customers
      return 3;

    case -1007: // Visitors
      return 2;

    default:
      // Another BO
      return 1;
  }
};
export const setValueSelectAll = (treeData: any) => {
  const dataOut: any = [];
  treeData.map(each => {
    dataOut.push(each.value);
  });
  return dataOut;
};
export const checkDuplicateArray = data => data.filter((v, i, a) => a.findIndex(v2 => v2 === v) === i);
export const checkValueExtend = (value: any, extendValue: any = []) => {
  const dataOut: Array<[]> = [];
  if (Array.isArray(value) && value.length > 0) {
    value.forEach(each => {
      if (!extendValue?.includes(each)) {
        dataOut.push(each);
      }
    });
    return dataOut;
  }
  return value;
};
