// Libraries
import { useTranslation } from 'react-i18next';
import { upperCase } from 'lodash';

// Molecules
import { Select, InputNumber } from 'app/components/molecules';

// Atoms
import { Text } from 'app/components/atoms';

// Locales
import { translations } from 'locales/translations';

const LIMIT_SPINNING_TYPE = {
  outOfCode: 'out_of_code',
  unlimited: 'unlimited',
  setValue: 'set_value',
} as const;

export const isLimitSpinningType = (value: unknown): value is TLimitSpinning['type'] => {
  return typeof value === 'string' && ['out_of_code', 'unlimited', 'set_value'].includes(value);
};

export type TLimitSpinning =
  | {
      type: typeof LIMIT_SPINNING_TYPE.outOfCode | typeof LIMIT_SPINNING_TYPE.unlimited;
    }
  | {
      type: typeof LIMIT_SPINNING_TYPE.setValue;
      value: number;
    };

type TProps = {
  values: TLimitSpinning;
  onChange?: (values: TLimitSpinning) => void;
  showOptions?: TLimitSpinning['type'][];
  disabled?: boolean;
  usePool: boolean;
};

const LimitSpinning = (props: TProps) => {
  const { showOptions = [] } = props;

  const { t } = useTranslation();

  const LIMIT_SPINNING_OPTIONS = [
    {
      label: t(translations.outOfCode.title),
      value: LIMIT_SPINNING_TYPE.outOfCode,
    },
    {
      label: t(translations.setValue.title),
      value: LIMIT_SPINNING_TYPE.setValue,
    },
    {
      label: t(translations.unlimited.title),
      value: LIMIT_SPINNING_TYPE.unlimited,
    },
  ].filter(option => showOptions.includes(option.value));

  const hanldleChangeType = (value: string) => {
    if (props.onChange && isLimitSpinningType(value)) {
      if (value === 'set_value') {
        props.onChange({ type: 'set_value', value: 1 });
        return;
      }

      props.onChange({ type: value });
    }
  };

  const handleChangeSetValue = (value: number) => {
    if (props.onChange) {
      props.onChange({ type: 'set_value', value: value });
    }
  };

  return (
    <>
      <div className="ants-flex ants-gap-3 ants-items-center ants-w-100">
        <Select
          className="ants-w-[110px]"
          options={LIMIT_SPINNING_OPTIONS}
          onChange={hanldleChangeType}
          value={props.values.type}
          disabled={props.disabled}
        />
        {props.values.type === 'set_value' ? (
          <InputNumber
            width="auto"
            min={1}
            value={props.values.value}
            onChange={value => handleChangeSetValue(Number(value))}
            disabled={props.disabled}
            disableUndo
          />
        ) : null}
      </div>
      {props.usePool && props.values.type === 'set_value' && (
        <Text className="ants-w-100 ants-mt-1 !ants-text-[11px] !ants-text-[#7f7f7f]">
          <span className="ants-font-normal">{upperCase(t(translations.or.title))}</span>{' '}
          <span className="ants-font-bold">{t(translations.outOfCode.title)}</span>
        </Text>
      )}
    </>
  );
};

export default LimitSpinning;
