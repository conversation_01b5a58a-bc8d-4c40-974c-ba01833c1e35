/* eslint-disable no-loop-func */
import { capitalizeFirstLetter } from 'app/utils/common';
import { translations } from 'locales/translations';
import { isEmpty } from 'lodash';
import { getTranslateMessage } from 'utils/messages';
import { checkStatusPromotionPool } from '../../../utils';
import { TSectionValidate, TWheelItem } from './types';

export const regexInternalCode = new RegExp(/^[a-zA-Z0-9_]*$/);

export const roundNumber = (number: number, decimal = 2) => {
  return Math.floor(Math.pow(10, decimal) * number) / Math.pow(10, decimal);
};

export const calculateWinChance = (arrSections: any) => {
  const canWinItems = arrSections.filter((item: any) => item.canWin);
  const newWinChance = roundNumber(100 / canWinItems.length, 1);

  return [...arrSections].map(section => ({
    ...section,
    winChance: section.canWin ? newWinChance : section.winChance,
  }));
};

export const getCappingLevelOptions = () => [
  {
    label: getTranslateMessage(translations.cappingLevel.level.journey.title),
    value: 'journey',
  },
  {
    label: getTranslateMessage(translations.cappingLevel.level.campaign.title),
    value: 'campaign',
  },
  {
    label: getTranslateMessage(translations.cappingLevel.level.variant.title),
    value: 'variant',
  },
];

export const getViewsOptions = () => [
  {
    label: getTranslateMessage(translations.successView.title),
    value: 'success',
  },
  {
    label: getTranslateMessage(translations.optinView.title),
    value: 'view',
  },
  {
    label: getTranslateMessage(translations.yesnoView.title),
    value: 'yes no',
  },
];

export const getFrequencyOptions = () =>
  [
    {
      label: getTranslateMessage(translations.hour.title),
      value: 'this_hour',
    },
    {
      label: getTranslateMessage(translations.day.title),
      value: 'this_day',
    },
    {
      label: getTranslateMessage(translations.week.title),
      value: 'this_week',
    },
    {
      label: getTranslateMessage(translations.month.title),
      value: 'this_month',
    },
    {
      label: getTranslateMessage(translations.lifeTime.title),
      value: 'lifetime',
    },
  ].map(option => ({ ...option, label: capitalizeFirstLetter(option.label) }));

export const isValidInternalCode = (internalCode: string): boolean => {
  return internalCode !== '' && regexInternalCode.test(internalCode);
};

export const validateSections = (
  sections: TWheelItem[],
  moreInfo: {
    promotionPools: any[];
    promotionErrors?: Record<string, string>;
    promotionWarnings?: Record<string, string>;
  },
) => {
  const errors: TSectionValidate[] = [];

  sections.forEach(section => {
    const { internalCode } = section;

    if (!isValidInternalCode(internalCode)) {
      errors.push({
        id: section.sectionId,
        key: 'internalCode',
        msg: getTranslateMessage(translations.couponWheelSectionError.internalCode.invalid),
        status: 'error',
      });
    }

    if (sections.filter(s => s.internalCode === internalCode).length > 1) {
      errors.push({
        id: section.sectionId,
        key: 'internalCode',
        msg: getTranslateMessage(translations.couponWheelSectionError.internalCode.same),
        status: 'error',
      });
    }

    if (section.canWin && section.winChance > 0) {
      if (section.limitSpinning.type === 'set_value' && section.limitSpinning.value <= 0) {
        errors.push({
          id: section.sectionId,
          key: 'limitSpinning',
          msg: getTranslateMessage(translations.couponWheelSectionError.limitSpinning.invalid),
          status: 'error',
        });
      }

      if (!section.pool && isEmpty(section.couponCode)) {
        errors.push({
          id: section.sectionId,
          key: 'couponCode',
          msg: getTranslateMessage(translations.couponWheelSectionError.couponCode.required),
          status: 'error',
        });
      }

      if (section.pool) {
        if (section.couponCode) {
          const statusPromotionPool = checkStatusPromotionPool(moreInfo, section.couponCode);

          if (statusPromotionPool) {
            errors.push({
              id: section.sectionId,
              key: 'couponCode',
              msg: statusPromotionPool.message,
              status: statusPromotionPool.status,
            });
          }
        }

        if (isEmpty(section.couponCodeAttr)) {
          errors.push({
            id: section.sectionId,
            key: 'couponCodeAttr',
            msg: getTranslateMessage(translations.couponWheelSectionError.couponCodeAttr.required),
            status: 'error',
          });
        }
      }
    }
  });

  return errors;
};

export const sortValidateSectionsResults = (listValidateSections: TSectionValidate[]) => {
  const mapOrderByStatus: Record<TSectionValidate['status'], number> = {
    error: 1,
    warning: 2,
  };

  return listValidateSections.sort((a, b) => mapOrderByStatus[a.status] - mapOrderByStatus[b.status]);
};

export const generateInternalCode = (
  currentLabel: string,
  listInternalCodes: string[],
  options: {
    autoGenWhenLabelEmpty: boolean;
  } = {
    autoGenWhenLabelEmpty: false,
  },
) => {
  const regex = new RegExp(/[\W]+/g);

  let sectionInternalCode = '';

  if (!isEmpty(currentLabel)) {
    sectionInternalCode = currentLabel.toLowerCase().replace(/ /g, '_').replace(regex, '');
  } else if (options.autoGenWhenLabelEmpty) {
    sectionInternalCode = 'section';
  }

  if (!isEmpty(sectionInternalCode) && listInternalCodes.some(code => code === sectionInternalCode)) {
    let i = 1;
    while (listInternalCodes.some(code => code === `${sectionInternalCode}_${i}`)) ++i;
    sectionInternalCode = `${sectionInternalCode}_${i}`;
  }

  return sectionInternalCode;
};

export const OBJECT_VIEWS_PAGES = {
  'yes/no': 'optin',
  optin: 'success',
  success: 'yes/no',
};
