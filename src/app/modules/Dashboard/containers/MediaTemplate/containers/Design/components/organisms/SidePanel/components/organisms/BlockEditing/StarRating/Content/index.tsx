// Libraries
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import produce from 'immer';

// Translations
import { translations } from 'locales/translations';

// Utils
import { handleError } from 'app/utils/handleError';

// Atoms
import { Space, Text as AtomText } from 'app/components/atoms';

// Molecules
import { InputNumber, Select, SliderWithInputNumber } from 'app/components/molecules';
import { DynamicSetting } from '../../../../molecules/DynamicSetting';
import {
  AlignSetting,
  SettingWrapper,
  ColorSetting,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/';

// Styled
import { ContentWrapper, ColorSettingWrapper } from './styled';

// Constants
import {
  RATING_TYPE,
  RATING_VALUE_NUMBER_ITEMS_MIN,
  RATING_NUMBER_ITEMS_MAX,
  RATING_NUMBER_ITEMS_MIN,
  RATING_NUMBER_SIZE_MIN,
  RATING_NUMBER_SIZE_MAX,
} from './constants';

// Selectors
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Types
import { TTab } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/types';
import { TStylesSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { TRow } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

// Config
import { DATA_MIGRATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

const PATH =
  'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/StarRating/Content/index.tsx';

interface ContentProps extends TTab {}

export const Content: React.FC<ContentProps> = () => {
  // Dispatch
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const blockSettings = blockSelected?.settings;

  // Actions
  const { updateBlock } = mediaTemplateDesignActions;

  // Handlers
  const onChangeRatingContent = (type: string, value: any) => {
    try {
      dispatch(
        updateBlock(
          produce(blockSelected, draft => {
            switch (type) {
              case 'ratingType':
                draft.settings.ratingTypes = value;
                break;
              case 'totalItems':
                draft.settings.totalItems = value;
                break;
              case 'totalFillItems':
                draft.settings.totalFillItems = value;
                break;
              case 'align':
                draft.settings.outerContainerStyles.justify = value;
                break;
              case 'size':
                draft.settings.size = value;
                break;
              case 'borderColor':
                draft.settings.iconStyles.borderColor = value;
                break;
              case 'BGBeforeRating':
                draft.settings.BGBeforeRating = value;
                break;
              case 'BGAfterRating':
                draft.settings.BGAfterRating = value;
                break;
              case 'dynamic':
                draft.settings.dynamic = {
                  ...draft.settings.dynamic,
                  value: { ...value },
                };
                break;
            }
          }),
        ),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeDividerContent',
        args: {},
      });
    }
  };

  return (
    <ContentWrapper>
      <AtomText size="medium" className="ants-font-medium !ants-text-cus-dark ants-mb-5">
        {t(translations.rating.title)}
      </AtomText>
      <Space size={20} direction="vertical">
        <SettingWrapper label={t(translations.rating.ratingType.title)}>
          <Select
            className="ants-w-24"
            options={Object.values(RATING_TYPE)}
            value={blockSettings.ratingTypes}
            onChange={value => onChangeRatingContent('ratingType', value)}
          />
        </SettingWrapper>
        <SettingWrapper label={t(translations.numberOfItems.title)}>
          <InputNumber
            value={blockSettings.totalItems}
            min={RATING_NUMBER_ITEMS_MIN}
            max={RATING_NUMBER_ITEMS_MAX}
            required
            onChange={value => onChangeRatingContent('totalItems', value)}
          />
        </SettingWrapper>
        <DynamicSetting
          label={t(translations.rating.valueType)}
          settings={blockSettings.dynamic.value}
          onChange={(setting, keyChange) => {
            onChangeRatingContent('dynamic', setting);
          }}
          renderStatic={() => (
            <Space size={20} direction="vertical">
              <SettingWrapper label={t(translations.rating.value)}>
                <InputNumber
                  value={
                    blockSettings.totalItems < blockSettings.totalFillItems
                      ? blockSettings.totalItems
                      : blockSettings.totalFillItems
                  }
                  min={RATING_VALUE_NUMBER_ITEMS_MIN}
                  max={Math.min(RATING_NUMBER_SIZE_MAX, blockSettings.totalItems)}
                  required
                  step={0.5}
                  onChange={totalFillItems => onChangeRatingContent('totalFillItems', totalFillItems)}
                />
              </SettingWrapper>
            </Space>
          )}
        />
        <AlignSetting
          label={t(translations.align.title)}
          align={blockSettings.outerContainerStyles.justify}
          onChange={value => onChangeRatingContent('align', value)}
        />
        <SliderWithInputNumber
          label={`${t(translations.size.title)} (px)`}
          labelClassName="!ants-text-gray-4"
          min={RATING_NUMBER_SIZE_MIN}
          max={RATING_NUMBER_SIZE_MAX}
          value={blockSettings.size}
          onAfterChange={value => onChangeRatingContent('size', value)}
        />
        <ColorSetting
          labelClassName="!ants-text-gray-4"
          label={t(translations.borderColor.title)}
          color={blockSettings.iconStyles.borderColor}
          onChange={borderColor => onChangeRatingContent('borderColor', borderColor)}
        />
        <ColorSettingWrapper>
          <ColorSetting
            labelClassName="!ants-text-gray-4"
            label={t(translations.rating.colors.before)}
            color={blockSettings.BGBeforeRating}
            onChange={BGBeforeRating => onChangeRatingContent('BGBeforeRating', BGBeforeRating)}
          />
          <ColorSetting
            labelClassName="!ants-text-gray-4"
            className="!ants-justify-end"
            label={t(translations.rating.colors.after)}
            color={blockSettings.BGAfterRating}
            onChange={BGAfterRating => onChangeRatingContent('BGAfterRating', BGAfterRating)}
          />
        </ColorSettingWrapper>
      </Space>
    </ContentWrapper>
  );
};
