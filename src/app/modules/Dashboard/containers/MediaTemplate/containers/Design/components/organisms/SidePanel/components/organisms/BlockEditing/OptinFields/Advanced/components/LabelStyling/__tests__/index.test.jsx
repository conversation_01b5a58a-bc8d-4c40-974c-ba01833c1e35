import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import LabelStyling from '../index';

describe('LabelStyling component', () => {
  const mockOnChange = jest.fn();

  const defaultProps = {
    blockSettings: {
      labelStyles: {},
      labelStylesSettings: {},
    },
    labelStylesSettings: {},
    labelStyles: {},
    onChange: mockOnChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the component', () => {
    const { getByLabelText } = render(<LabelStyling {...defaultProps} />);
    expect(getByLabelText('Alignment')).toBeInTheDocument();
    expect(getByLabelText('Font Size')).toBeInTheDocument();
    expect(getByLabelText('Gap')).toBeInTheDocument();
    expect(getByLabelText('Indentation')).toBeInTheDocument();
  });

  it('should call onChange when alignment is changed', () => {
    const { getByLabelText } = render(<LabelStyling {...defaultProps} />);
    const alignmentSelect = getByLabelText('Alignment');
    fireEvent.change(alignmentSelect, { target: { value: 'center' } });
    expect(mockOnChange).toHaveBeenCalledWith({ textAlign: 'center' }, { textAlign: 'center' });
  });

  it('should call onChange when font size is changed', () => {
    const { getByLabelText } = render(<LabelStyling {...defaultProps} />);
    const fontSizeInput = getByLabelText('Font Size');
    fireEvent.change(fontSizeInput, { target: { value: '20px' } });
    expect(mockOnChange).toHaveBeenCalledWith({ inputSize: 'custom' }, { fontSize: '20px' });
  });

  it('should call onChange when gap settings are changed', () => {
    const { getByLabelText } = render(<LabelStyling {...defaultProps} />);
    const gapXInput = getByLabelText('Gap (Column)');
    const gapYInput = getByLabelText('Gap (Row)');

    fireEvent.change(gapXInput, { target: { value: '10' } });
    fireEvent.change(gapYInput, { target: { value: '20' } });

    expect(mockOnChange).toHaveBeenCalledWith(
      {
        gapX: '10px',
        gapY: '20px',
        gapSuffix: 'px',
        linkedGapInput: true,
      },
      {
        gapX: '10px',
        gapY: '20px',
        gapSuffix: 'px',
        linkedGapInput: true,
      },
    );
  });

  it('should call onChange when indentation is changed', () => {
    const { getByLabelText } = render(<LabelStyling {...defaultProps} />);
    const indentationSlider = getByLabelText('Indentation');

    fireEvent.change(indentationSlider, { target: { value: '50' } });

    expect(mockOnChange).toHaveBeenCalledWith(
      {
        indentation: 50,
      },
      {
        paddingLeft: '50px',
      },
    );
  });
});
