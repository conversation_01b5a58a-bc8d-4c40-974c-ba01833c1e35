// Libraries
import React, { useState, useEffect } from 'react';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next'; // Atoms
import { Space, Button, Input, Text } from 'app/components/atoms';

// Molecules
import { UploadImage } from 'app/components/molecules/UploadImage';
import { AlignSetting } from '../../../../molecules/AlignSetting';
import { CollapsePanel, InputNumber, RadioGroup, Select, SliderWithUnit } from 'app/components/molecules';
import { SettingWrapper } from '../../../../molecules';
import { Collapse } from 'app/components/molecules/Collapse';
import { Tooltip } from '@antscorp/antsomi-ui';
import { ErrorIcon, WarningIcon } from 'app/components/icons';

// Organisms
import { TriggerSetting } from '../components/TriggerSetting';

// Locales
import { translations } from 'locales/translations';

// Selectors
import {
  selectBlockSelected,
  selectCurrentBlockErrors,
  selectCurrentBlockWarnings,
  selectSidePanel,
  // selectParentBlockSelected,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { handleError } from 'app/utils/handleError';
import { getNumberFromString } from 'app/utils/common';
import { getPreventKeyboardAction } from 'app/utils/web';
import { getTranslateMessage } from 'utils/messages';

// Types
import { TElement } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/types';

// Constants
import {
  POSITION,
  SIDE_PANEL_COLLAPSE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { IMAGE_SIZE, IMAGE_TO_SHAKE } from './constants';

import CustomizeReward from '../../../CustomizeReward';
import { WrapperBlockTime, WrapperUnitTime } from '../components/TriggerSetting/styled';
import { SHAKE_TYPE_OPTIONS } from '../components/TriggerSetting/constants';

const ERROR_POSITION = {
  TOP: {
    value: 'top',
    label: getTranslateMessage(translations.top.title),
  },
  CENTER: {
    value: 'center',
    label: getTranslateMessage(translations.center.title),
  },
  BOTTOM: {
    value: 'bottom',
    label: getTranslateMessage(translations.bottom.title),
  },
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Image/Content/index.tsx';

interface ContentProps {}

export const Content: React.FC<ContentProps> = props => {
  const dispatch = useDispatch();

  // Actions
  const { updateBlock, setSidePanel } = mediaTemplateDesignActions;

  // I18n
  const { t } = useTranslation();

  // Selector
  const blockSelected = useSelector(selectBlockSelected) as TElement;
  const { settings } = blockSelected;
  const { triggerSettings } = settings;
  const sidePanel = useSelector(selectSidePanel);
  const errors = useSelector(selectCurrentBlockErrors);
  const warnings = useSelector(selectCurrentBlockWarnings);

  const errorMessage = blockSelected.settings.errorMessage || {};

  //state
  const [openCustomizeWheel, setOpenCustomizeWheel] = useState<boolean>(false);
  const [isShowSectionValidate, setIsShowSectionValidate] = useState<boolean>(false);
  const { sections = [] } = blockSelected?.settings;

  useEffect(() => {
    const preventHandleKeyDown = getPreventKeyboardAction(['undo']).onKeyDown;
    const preventHandleKeyUp = getPreventKeyboardAction(['undo']).onKeyUp;
    if (openCustomizeWheel) {
      window.addEventListener('keydown', preventHandleKeyDown);
      window.addEventListener('keyup', preventHandleKeyUp);
    }

    return () => {
      window.removeEventListener('keydown', preventHandleKeyDown);
      window.removeEventListener('keyup', preventHandleKeyUp);
    };
  }, [openCustomizeWheel]);

  // Handlers
  const onChangeShakeContent = (type: string, value: any) => {
    try {
      dispatch(
        updateBlock(
          produce(blockSelected, draft => {
            const { widthSuffix = '%', heightSuffix = '%' } = draft.settings.stylesSettings || {};

            switch (type) {
              case 'imageBefore':
                draft.settings.uploadedImage.previewUrlBefore = value.url || false;
                break;
              case 'imageAfter':
                draft.settings.uploadedImage.previewUrlAfter = value.url || false;
                break;
              case 'align':
                draft.settings.outerContainerStyles.textAlign = value;
                break;
              case 'width':
                draft.settings.styles.width = value + widthSuffix;
                break;
              case 'height':
                draft.settings.styles.height = value + heightSuffix;
                break;
              case 'widthSuffix':
                const width = getNumberFromString(settings.styles.width) || 100;

                draft.settings.stylesSettings.widthSuffix = value;

                draft.settings.styles.width = width + value;

                if (value === '%' && width > 100) {
                  draft.settings.styles.width = '100%';
                  return;
                }

                if (value === 'auto') {
                  draft.settings.styles.width = 'auto';
                  return;
                }

                break;
              case 'heightSuffix':
                const height = getNumberFromString(settings.styles.height) || 100;

                draft.settings.stylesSettings.heightSuffix = value;

                draft.settings.styles.height = height + value;

                if (value === '%' && height > 100) {
                  draft.settings.styles.height = '100%';
                  return;
                }

                if (value === 'auto') {
                  draft.settings.styles.height = 'auto';
                  return;
                }

                break;
              case 'dynamic':
                draft.settings.dynamic = {
                  ...draft.settings.dynamic,
                  ...value,
                };
                break;
              case 'objectPosition':
                draft.settings.styles.objectPosition = value;
                break;
              case 'objectFit':
                draft.settings.styles.objectFit = value;
                break;
              case 'shakeTrigger':
              case 'by':
              case 'reminderNotification':
              case 'referral':
              case 'timeToDelay':
                draft.settings.triggerSettings = {
                  ...draft.settings.triggerSettings,
                  ...value,
                };
                break;
              case 'errorMessage': {
                draft.settings.errorMessage = {
                  ...(blockSelected.settings.errorMessage || {
                    position: 'bottom',
                    message: getTranslateMessage(translations.allAvailableCodeHasBeenAllocated.title),
                  }),
                  ...value,
                };
                break;
              }
              case 'imageToShake':
                draft.settings.imageToShake.type = value;
                break;
              case 'imageOtherUrl':
                draft.settings.imageToShake.otherUrl = value.url || false;
                break;
              case 'timeToShake':
              case 'translationTime':
              case 'shakeType':
                draft.settings[type] = value;
                break;
            }
          }),
        ),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeImageBlock',
        args: {},
      });
    }
  };

  useEffect(() => {
    // Init default active panel
    if (
      ![
        SIDE_PANEL_COLLAPSE.SHAKE_AND_WIN,
        SIDE_PANEL_COLLAPSE.SHAKE_TRIGGER_SETTING,
        SIDE_PANEL_COLLAPSE.COUPON_SETTING,
      ].includes(sidePanel.activePanel)
    ) {
      dispatch(
        setSidePanel({
          activePanel: SIDE_PANEL_COLLAPSE.SHAKE_AND_WIN,
        }),
      );
    }

    if (!errorMessage || !errorMessage.position) {
      onChangeShakeContent('errorMessage', {});
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (blockSelected) {
    const closeCustomizeWheel = () => {
      if (sections.every(s => s.validateInfo.filter(v => v.status === 'error').length === 0)) {
        setOpenCustomizeWheel(false);
        return;
      }
      setIsShowSectionValidate(true);
    };

    // const onClickDeleteImageElement = () => {
    //   try {
    //     Modal.confirm({
    //       title: t(translations.confirmDeletionBlock.title),
    //       icon: null,
    //       centered: true,
    //       content: t(translations.confirmDeletionBlock.description),
    //       onOk() {
    //         if (sidePanel.blockSelectedId === id) {
    //           dispatch(
    //             setSidePanel({
    //               blockSelectedId: '',
    //               type: SIDE_PANEL_TYPE.BLOCKS.name,
    //             }),
    //           );
    //         }

    //         dispatch(
    //           removeBlock({
    //             parentBlockId: parentBlockSelected.id,
    //             blockId: id.toString(),
    //             blockType: blockSelected.type,
    //           }),
    //         );
    //       },
    //     });
    //   } catch (error) {
    //     handleError(error, {
    //       path: PATH,
    //       name: 'onClickDeleteImageElement',
    //       args: {},
    //     });
    //   }
    // };
    return (
      <>
        <Collapse
          defaultActiveKey={SIDE_PANEL_COLLAPSE.SHAKE_AND_WIN}
          accordion
          activeKey={sidePanel.activePanel}
          onChange={activePanel => {
            dispatch(
              setSidePanel({
                activePanel: activePanel as string,
              }),
            );
          }}
        >
          <CollapsePanel header={t(translations.shake.title)} key={SIDE_PANEL_COLLAPSE.SHAKE_AND_WIN}>
            <Space size={20} direction="vertical">
              {/* <DynamicSetting
                label={t(translations.image.title)}
                settings={dynamic.previewUrl}
                onChange={(setting, keyChange) => {
                  onChangeImageContent('dynamic', {
                    previewUrl: setting,
                    ...(keyChange === 'isDynamic' && {
                      altText: {
                        ...dynamic.altText,
                        isDynamic: setting.isDynamic,
                      },
                      linkedUrl: {
                        ...dynamic.linkedUrl,
                        isDynamic: setting.isDynamic,
                      },
                    }),
                    ...(keyChange === 'index' && {
                      altText: {
                        ...dynamic.altText,
                        index: setting.index,
                      },
                      linkedUrl: {
                        ...dynamic.linkedUrl,
                        index: setting.index,
                      },
                    }),
                  });
                }}
                renderStatic={() => (
                  <UploadImage
                    isOpen={settings.type === 'image' && settings.uploadedImage.previewUrlBefore === false}
                    selectedImage={{ url: settings.uploadedImage.previewUrlBefore } || ''}
                    onRemoveImage={value => onChangeImageContent('image', '')}
                    onChangeImage={value => onChangeImageContent('image', value)}
                  />
              /> */}
              <Button className="ants-w-full" type="primary" onClick={() => setOpenCustomizeWheel(true)}>
                {t(translations.customizeRewards.title)}

                {errors[blockSelected.id] ? (
                  <Tooltip title={'This pool is removed'}>
                    <ErrorIcon />
                  </Tooltip>
                ) : warnings[blockSelected.id] ? (
                  <Tooltip title={'You do not have permission on this pool anymore'}>
                    <WarningIcon />
                  </Tooltip>
                ) : null}
              </Button>
              <SettingWrapper label={t(translations.imageBeforeShake.title)}></SettingWrapper>
              <UploadImage
                isOpen={settings.type === 'shake-and-win' && settings.uploadedImage.previewUrlBefore === false}
                selectedImage={{ url: settings.uploadedImage.previewUrlBefore } || ''}
                onRemoveImage={value => onChangeShakeContent('imageBefore', '')}
                onChangeImage={value => onChangeShakeContent('imageBefore', value)}
                titleImageUrl={t(translations.imageBeforeShake.titleUrl)}
              />
              <SettingWrapper label={t(translations.imageToShake.title)}>
                <RadioGroup
                  options={Object.values(IMAGE_TO_SHAKE)}
                  value={settings.imageToShake.type}
                  onChange={e => onChangeShakeContent('imageToShake', e.target.value)}
                />
              </SettingWrapper>
              {settings.imageToShake.type === 'other-image' && (
                <UploadImage
                  selectedImage={{ url: settings.imageToShake.otherUrl } || ''}
                  onRemoveImage={value => onChangeShakeContent('imageOtherUrl', '')}
                  onChangeImage={value => onChangeShakeContent('imageOtherUrl', value)}
                  titleImageUrl={t(translations.imageToShake.titleUrl)}
                />
              )}
              <WrapperBlockTime>
                <Text>{t(translations.timeToShake.title)}</Text>
                <WrapperUnitTime>
                  <InputNumber
                    value={Number(settings?.timeToShake)}
                    required
                    min={0}
                    onChange={value => onChangeShakeContent('timeToShake', value)}
                  />
                  <Text key="">{t(translations.seconds.unit)}</Text>
                </WrapperUnitTime>
              </WrapperBlockTime>
              <Select
                label={t(translations.shakeType.title)}
                value={settings.shakeType}
                options={Object.values(SHAKE_TYPE_OPTIONS)}
                onChange={value => onChangeShakeContent('shakeType', value)}
              />
              <SettingWrapper label={t(translations.imageAfterShake.title)}></SettingWrapper>
              <UploadImage
                selectedImage={{ url: settings.uploadedImage.previewUrlAfter } || ''}
                onRemoveImage={value => onChangeShakeContent('imageAfter', '')}
                onChangeImage={value => onChangeShakeContent('imageAfter', value)}
                titleImageUrl={t(translations.imageAfterShake.titleUrl)}
              />
              <WrapperBlockTime>
                <Text>{t(translations.translationTime.title)}</Text>
                <WrapperUnitTime>
                  <InputNumber
                    value={Number(settings?.translationTime)}
                    required
                    min={0}
                    onChange={value => onChangeShakeContent('translationTime', value)}
                  />
                  <Text key="">{t(translations.seconds.unit)}</Text>
                </WrapperUnitTime>
              </WrapperBlockTime>
              {/* {!dynamic.previewUrl.isDynamic && !settings.uploadedImage.previewUrlBefore && (
            <Alert
              type="warning"
              message={
                <div>
                  <Text className="ants-inline">{t(translations.browseImage.warning)}</Text>{' '}
                  <Text className="ants-inline ants-underline ants-cursor-pointer" onClick={onClickDeleteImageElement}>
                    {t(translations.deleteImageElement.title)}
                  </Text>{' '}
                  <Text className="ants-inline">{t(translations.browseImage.asWell)}</Text>
                </div>
                // <Trans i18nKey={translations.browseImage.warning} values={{ action: <div>This is a hello</div> }} />
              }
            />
          )} */}
              <Select
                label={t(translations.imagePosition.title)}
                value={settings.styles?.objectPosition || 'center center'}
                options={Object.values(POSITION)}
                onChange={value => onChangeShakeContent('objectPosition', value)}
              />
              <Select
                label={t(translations.imageSize.title)}
                value={settings.styles?.objectFit || 'unset'}
                options={Object.values(IMAGE_SIZE)}
                onChange={value => onChangeShakeContent('objectFit', value)}
              />
              <SliderWithUnit
                label={t(translations.width.title)}
                value={parseInt(settings.styles.width)}
                unit={settings.stylesSettings?.widthSuffix || '%'}
                min={0}
                max={(settings.stylesSettings?.widthSuffix || '%') === '%' ? 100 : 1000}
                onAfterChange={value => onChangeShakeContent('width', value)}
                onChangeUnit={value => onChangeShakeContent('widthSuffix', value)}
              />
              <SliderWithUnit
                label={t(translations.height.title)}
                value={parseInt(settings.styles.height)}
                unit={settings.stylesSettings?.heightSuffix || 'auto'}
                min={0}
                max={1000}
                hideUnits={['%']}
                onAfterChange={value => onChangeShakeContent('height', value)}
                onChangeUnit={value => onChangeShakeContent('heightSuffix', value)}
              />
              <AlignSetting
                label={t(translations.align.title)}
                align={settings.outerContainerStyles.textAlign}
                onChange={value => onChangeShakeContent('align', value)}
              />
            </Space>
          </CollapsePanel>
          <CollapsePanel
            header={t(translations.shakeTriggerSetting.title)}
            key={SIDE_PANEL_COLLAPSE.SHAKE_TRIGGER_SETTING}
          >
            <TriggerSetting
              label={t(translations.shakeTrigger.title)}
              onChange={(setting, keyChange = '') => {
                onChangeShakeContent(keyChange, setting);
              }}
              settings={triggerSettings}
            />
          </CollapsePanel>
          <CollapsePanel header={t(translations.couponSetting.title)} key={SIDE_PANEL_COLLAPSE.COUPON_SETTING}>
            <div className="ants-flex ants-flex-col ants-space-y-5">
              <div>
                <Text className="!ants-text-gray-4 ants-mb-5px">{t(translations.errorMessage.title)}</Text>
                <Input
                  placeholder={t(translations.errorMessage.title)}
                  value={errorMessage?.message}
                  onChange={e => onChangeShakeContent('errorMessage', { message: e.target.value })}
                />
              </div>

              <Select
                label={t(translations.position.title)}
                options={Object.values(ERROR_POSITION)}
                // disabled={!item.canWin}
                onChange={position => onChangeShakeContent('errorMessage', { position })}
                value={errorMessage?.position}
                className={`ants-w-full ants-h-[30px]`}
              />
            </div>
          </CollapsePanel>
        </Collapse>
        <CustomizeReward
          openCustomizeWheel={openCustomizeWheel}
          blockSelected={blockSelected}
          sections={sections}
          closeCustomizeWheel={closeCustomizeWheel}
          isShowSectionValidate={isShowSectionValidate}
          setIsShowSectionValidate={setIsShowSectionValidate}
          actionKey={STANDARDS_BLOCKS.SHAKE_AND_WIN.actionKey}
        />
      </>
    );
  }

  return null;
};
