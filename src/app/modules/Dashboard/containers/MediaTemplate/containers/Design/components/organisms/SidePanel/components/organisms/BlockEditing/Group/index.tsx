// Libraries
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import produce from 'immer';

// Atoms
import { Space } from 'app/components/atoms';

// Molecules
import { Collapse, Select } from 'app/components/molecules';
import { Panel } from 'app/components/molecules/Collapse';
import { GapSetting, SettingWrapper } from '../../../molecules';
import MobileWarning from '../../../molecules/MobileWarning';

// Organisms
import { DisplayCondition } from '../../DisplayCondition';

// Constants
import { SIDE_PANEL_COLLAPSE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import {
  ALIGN_CONTENT_OPTIONS,
  ALIGN_OPTIONS,
  DIRECTION_OPTIONS,
  DISPLAY_OPTIONS,
  JUSTIFY_OPTIONS,
  WRAP_OPTIONS,
} from './constants';

// Utils
import { handleError } from 'app/utils/handleError';
import { getGapSettings } from '../../../../utils';

// Locales
import { translations } from 'locales/translations';

// Slices
import { selectBlockSelected } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { BlockStyling } from '../Common/BlockStyling';

interface GroupProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Group/index.tsx';

export const Group: React.FC<GroupProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Selector
  const blockSelected = useSelector(selectBlockSelected);
  const blockSettings = blockSelected?.settings;

  // Actions
  const { updateBlockSetting, setSidePanel, updateBlockFieldsSelected } = mediaTemplateDesignActions;

  if (!blockSettings) {
    return null;
  }

  const { groupStyles, groupSettings, blockStylesSettings } = blockSettings;

  const {
    displayCondition = {
      condition: '',
      field: '',
      index: 1,
      operator: '',
      dataType: '',
      value: '',
    },
  } = blockStylesSettings;

  const onChangeGroupStyling = ({ groupSettings = {}, groupStyles = {} }) => {
    try {
      if (blockSelected) {
        dispatch(
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                draft.groupSettings = {
                  ...draft.groupSettings,
                  ...groupSettings,
                };
                draft.groupStyles = {
                  ...draft.groupStyles,
                  ...groupStyles,
                };
              }),
            ),
          ),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeGroupStyling',
        args: { groupSettings, groupStyles },
      });
    }
  };

  const handleOnClickPanel = (activePanel: any) => {
    try {
      dispatch(
        setSidePanel({
          activePanel,
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleOnClickPanel',
        args: {},
      });
    }
  };

  const renderAdvancedDisplay = (displayType: string) => {
    try {
      switch (displayType) {
        case DISPLAY_OPTIONS.FLEX.value:
          return (
            <Space direction="vertical" size={20}>
              <SettingWrapper label={t(translations.direction.title)}>
                <Select
                  value={groupSettings.flex?.direction}
                  options={Object.values(DIRECTION_OPTIONS)}
                  style={{ width: 100 }}
                  onChange={value =>
                    onChangeGroupStyling({
                      groupSettings: {
                        flex: {
                          ...groupSettings.flex,
                          direction: value,
                        },
                      },
                    })
                  }
                />
              </SettingWrapper>
              <SettingWrapper label={t(translations.align.title)}>
                <Select
                  value={groupSettings.flex?.align}
                  options={Object.values(ALIGN_OPTIONS)}
                  style={{ width: 100 }}
                  onChange={value =>
                    onChangeGroupStyling({
                      groupSettings: {
                        flex: {
                          ...groupSettings.flex,
                          align: value,
                        },
                      },
                    })
                  }
                />
              </SettingWrapper>
              <SettingWrapper label={t(translations.justify.title)}>
                <Select
                  value={groupSettings.flex?.justify}
                  options={Object.values(JUSTIFY_OPTIONS)}
                  style={{ width: 100 }}
                  onChange={value =>
                    onChangeGroupStyling({
                      groupSettings: {
                        flex: {
                          ...groupSettings.flex,
                          justify: value,
                        },
                      },
                    })
                  }
                />
              </SettingWrapper>
              <GapSetting
                settings={getGapSettings(groupSettings.flex || {})}
                onChange={gapSettings =>
                  onChangeGroupStyling({
                    groupSettings: {
                      flex: {
                        ...groupSettings.flex,
                        ...gapSettings,
                      },
                    },
                  })
                }
              />
              <SettingWrapper label={t(translations.wrap.title)}>
                <Select
                  value={groupSettings.flex?.wrap}
                  options={Object.values(WRAP_OPTIONS)}
                  style={{ width: 100 }}
                  onChange={value =>
                    onChangeGroupStyling({
                      groupSettings: {
                        flex: {
                          ...groupSettings.flex,
                          wrap: value,
                        },
                      },
                    })
                  }
                />
              </SettingWrapper>
              {groupSettings.flex?.wrap === WRAP_OPTIONS.WRAP.value && (
                <SettingWrapper label={t(translations.align.title)}>
                  <Select
                    value={groupSettings.flex?.alignContent}
                    options={Object.values(ALIGN_CONTENT_OPTIONS)}
                    style={{ width: 100 }}
                    onChange={value =>
                      onChangeGroupStyling({
                        groupSettings: {
                          flex: {
                            ...groupSettings.flex,
                            alignContent: value,
                          },
                        },
                      })
                    }
                  />
                </SettingWrapper>
              )}
            </Space>
          );
        default:
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'renderAdvancedDisplay',
        args: {},
      });
    }
  };

  const onChangeDisplayCondition = (settings = {}) => {
    try {
      if (blockSelected) {
        dispatch(
          updateBlockFieldsSelected({
            dataUpdate: [
              {
                fieldPath: 'settings.blockStylesSettings.displayCondition',
                data: settings,
              },
            ],
          }),
        );
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeDisplayCondition',
        args: { settings },
      });
    }
  };

  return (
    <Collapse
      accordion
      // activeKey={activePanel}
      defaultActiveKey={SIDE_PANEL_COLLAPSE.GROUP_STYLING}
      onChange={handleOnClickPanel}
      destroyInactivePanel
    >
      <MobileWarning />
      <Panel key={SIDE_PANEL_COLLAPSE.GROUP_STYLING} header={t(translations.groupStyle.title)}>
        <div className="ants-flex ants-flex-col ants-space-y-5">
          <SettingWrapper label={t(translations.display.title)}>
            <Select
              value={groupStyles.display}
              options={Object.values(DISPLAY_OPTIONS)}
              style={{ width: 100 }}
              onChange={value => onChangeGroupStyling({ groupStyles: { display: value } })}
            ></Select>
          </SettingWrapper>
          {renderAdvancedDisplay(groupStyles?.display)}
        </div>
      </Panel>
      <Panel key={SIDE_PANEL_COLLAPSE.CONTAINER_STYLING} header={t(translations.containerStyle.title)}>
        <div className="ants-flex ants-flex-col ants-space-y-5">
          <BlockStyling />
        </div>
      </Panel>
      <Panel header={t(translations.displayCondition.title)} key={SIDE_PANEL_COLLAPSE.DISPLAY_CONDITION}>
        <DisplayCondition
          displayCondition={displayCondition}
          valueCondition={displayCondition.condition}
          onChange={onChangeDisplayCondition}
        />
      </Panel>
    </Collapse>
  );
});
