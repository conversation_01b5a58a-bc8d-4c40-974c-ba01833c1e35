import React, { useState } from 'react';
import { DropdownContainer, StyledSelect, WrapperDatePicker } from './styled';
import { Icon } from 'app/components/atoms';
import { handleError } from 'app/utils/handleError';
import { buildCusFieldDataset } from '../../utils';

// Molecules
import { SelectCustom } from 'app/components/molecules';

// Utils
import { getObjSafely } from 'app/utils/common';

interface RawInfoFieldProps {
  [key: string]: any;
}

export type TDateTimeWithType = {
  id?: string;
  namespace?: string;
  inputName?: string;
  name?: string;
  type?: string;
  order?: number;
  placeholder?: string;
  placeholderColor?: string;
  inputWidth?: string;
  required?: boolean;
  isCustom?: boolean;
  datetimeType?: string;
  dateTimeFormat?: string;
  dropdownDate?: any[];
  dropdownTime?: any[];
  openType?: string;
  field?: RawInfoFieldProps;
  callback?: Function;
  isPreviewMode?: boolean;
  style?: React.CSSProperties;
  onClickElement: Function;
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/organisms/OptinFieldsBlock/CustomOptinField/DateTimeWithType/index.tsx';

const hours = Array.from({ length: 24 }, (_, index) => ({
  label: `${index + 1 < 10 ? `0${index + 1}` : index + 1}`,
  value: index + 1,
}));
const time = Array.from({ length: 60 }, (_, index) => ({
  label: `${index + 1 < 10 ? `0${index + 1}` : index + 1}`,
  value: index + 1,
}));
const days = Array.from({ length: 31 }, (_, index) => ({
  value: `${index + 1 < 10 ? `0${index + 1}` : index}`,
  label: index + 1,
}));
const months = Array.from({ length: 12 }, (_, index) => ({
  value: `${index + 1 < 10 ? `0${index + 1}` : index + 1}`,
  label: index + 1,
}));

const currentYear = new Date().getFullYear();
const years = Array.from({ length: currentYear - 1900 + 1 }, (_, index) => ({
  value: `${currentYear - index}`,
  label: `${currentYear - index}`,
}));

const OptionDateTime: any = props => {
  const {
    displayAlias,
    type,
    isActive,
    label,
    placeholderColor = '',
    openType = '',
    callback = () => {},
    style,
  } = props;

  const [value, setValue] = useState('');

  let listOption: any = [];

  const handleSetValue = (e, value: any) => {
    try {
      e.stopPropagation();

      setValue(value);
      if (typeof callback === 'function') {
        callback(type);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleSetValue',
        args: {},
      });
    }
  };

  switch (type) {
    case 'day':
      listOption = days;
      break;

    case 'month':
      listOption = months;
      break;

    case 'year':
      listOption = years;
      break;

    case 'hour':
      listOption = hours;
      break;

    case 'minute':
    case 'second':
      listOption = time;
      break;

    default:
      break;
  }

  const handleClickDateTimeHeading = e => {
    try {
      e.stopPropagation();

      if (typeof callback === 'function') {
        callback(type);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleClickDateTimeHeading',
        args: {},
      });
    }
  };

  return (
    <StyledSelect style={style}>
      <div
        // onClick={e => (hideDropdown ? e.stopPropagation() : handleClickDateTimeHeading(e))}
        className="selected-option"
      >
        {value ? (
          <span className="value-selected" style={{ color: placeholderColor || 'inherit' }}>{`${
            parseInt(value) < 10 ? `0${parseInt(value)}` : value
          }`}</span>
        ) : (
          <span className="placeholder-selected" style={{ color: placeholderColor || 'inherit' }}>
            {isActive && displayAlias ? displayAlias : label}
          </span>
        )}
        <Icon
          type="icon-ants-expand-more"
          className="icon-arrow-down"
          style={{ color: placeholderColor || 'inherit' }}
        />
      </div>
      <ul className={`list-option ${type === openType ? 'open' : ''}`}>
        {listOption.map(option => (
          <li
            key={option.value}
            className={`option ${value && value === option.value ? 'active' : ''}`}
            value={option.value}
            onClick={e => handleSetValue(e, option.value)}
          >
            {option.label}
          </li>
        ))}
      </ul>
    </StyledSelect>
  );
};

const DateTimeWithType: React.FC<TDateTimeWithType> = props => {
  const {
    id,
    datetimeType,
    dropdownDate,
    dropdownTime,
    field = {},
    openType = '',
    callback = () => {},
    isPreviewMode,
    style,
    namespace,
    placeholderColor = '',
    onClickElement,
  } = props;

  const { dateTimeFormat = '', dateTimeFormatLabel, type, value, className, dataset, inputWidth, timeFormat } = field;

  const { display, marginBottom, marginLeft, marginRight, marginTop, maxWidth, width, ...rest } = style || {};
  const wrapperStyle = {
    // display,
    marginBottom,
    marginLeft,
    marginRight,
    marginTop,
    maxWidth,
    width,
  };

  if (isPreviewMode) {
    const renderSelect = (optionKey: string, optionLabel: string) => {
      return (
        <SelectCustom
          isPreviewMode={true}
          key={`${field.id}-${optionKey}-select`}
          value=""
          placeholder={optionLabel}
          action={optionKey}
          style={rest}
          selectStyle={{
            height: '100%',
          }}
          dataset={{
            'data-placeholder-color': placeholderColor,
          }}
        />
      );
    };

    const joinDateValue = (arrValue, delimiter = '/') => {
      return [...arrValue]
        ?.filter(item => item.isActive)
        .sort((a, b) => a.order - b.order)
        .map(d => d.id)
        .join(delimiter);
    };

    const dateFormat =
      datetimeType === 'dropdown' ? [joinDateValue(dropdownDate), joinDateValue(dropdownTime, ':')].join(' ') : '';

    if (datetimeType === 'dropdown') {
      const activeDropdownDate = getObjSafely(() => dropdownDate?.filter(each => each?.isActive), [] as any);
      const lastItemDate = activeDropdownDate[activeDropdownDate.length - 1] || {};
      const activeDropdownTime = getObjSafely(() => dropdownTime?.filter(each => each?.isActive), [] as any);
      const lastItemTime = activeDropdownTime[activeDropdownTime.length - 1] || {};

      return (
        <DropdownContainer
          id={id}
          style={wrapperStyle}
          {...buildCusFieldDataset(field, namespace || '', placeholderColor)}
          data-format={dateFormat}
          data-datetime-type={datetimeType}
        >
          <div className="time-content preview">
            {dropdownDate?.map((date, index) => {
              return (
                date.isActive && (
                  <>
                    {renderSelect(date.id, date.displayAlias || date.label)}
                    {lastItemDate?.id === date?.id ? null : (
                      <span className="divider-time" style={{ color: placeholderColor || 'inherit' }}>
                        /
                      </span>
                    )}
                  </>
                )
              );
            })}
          </div>
          <div className="time-content preview">
            {dropdownTime?.map((date, index) => {
              return (
                date.isActive && (
                  <>
                    {renderSelect(date.id, date.displayAlias || date.label)}
                    {lastItemTime?.id === date?.id ? null : (
                      <span className="divider-time" style={{ color: placeholderColor || 'inherit' }}>
                        :
                      </span>
                    )}
                  </>
                )
              );
            })}
          </div>
        </DropdownContainer>
      );
    } else {
      return (
        <DropdownContainer id={id} style={wrapperStyle}>
          <div
            data-custom-field={type}
            data-value={value}
            data-style={JSON.stringify({ ...rest, inputWidth })}
            data-class={className}
            data-datetime-type={datetimeType}
            data-dateTime-format={dateTimeFormat}
            data-dateTime-format-label={dateTimeFormatLabel}
            data-time-format={timeFormat}
            data-full-format={
              timeFormat === '24h' || !dateTimeFormat.includes('H')
                ? dateTimeFormat
                : dateTimeFormat.replaceAll('HH', 'hh') + ' aa'
            }
            {...buildCusFieldDataset(field, namespace || '', placeholderColor)}
            {...dataset}
          ></div>
        </DropdownContainer>
      );
    }
  }

  if (datetimeType === 'dropdown') {
    const {
      // borderTopWidth,
      // borderRightWidth,
      // borderBottomWidth,
      // borderLeftWidth,
      paddingTop,
      paddingRight,
      paddingBottom,
      paddingLeft,
      ...rest
    } = style || {};

    const activeDropdownDate = getObjSafely(() => dropdownDate?.filter(each => each?.isActive), [] as any);
    const lastItemDate = activeDropdownDate[activeDropdownDate.length - 1] || {};
    const activeDropdownTime = getObjSafely(() => dropdownTime?.filter(each => each?.isActive), [] as any);
    const lastItemTime = activeDropdownTime[activeDropdownTime.length - 1] || {};

    return (
      <DropdownContainer id={id} style={wrapperStyle} onClick={e => onClickElement(field.id)}>
        <div className="time-content">
          {dropdownDate?.map((date, index) => {
            return (
              date.isActive && (
                <>
                  <OptionDateTime
                    type={date.id}
                    label={date.label}
                    placeholderColor={placeholderColor}
                    isActive={date.isActive}
                    displayAlias={date.displayAlias}
                    openType={openType}
                    callback={callback}
                    style={rest}
                  />
                  {lastItemDate?.id === date?.id ? null : (
                    <span className="divider-time" style={{ color: placeholderColor || 'inherit' }}>
                      /
                    </span>
                  )}
                </>
              )
            );
          })}
        </div>
        <div className="time-content">
          {dropdownTime?.map((date, index) => {
            return (
              date.isActive && (
                <>
                  <OptionDateTime
                    type={date.id}
                    isActive={date.isActive}
                    placeholderColor={placeholderColor}
                    label={date.label}
                    displayAlias={date.displayAlias}
                    openType={openType}
                    callback={callback}
                    style={rest}
                  />
                  {lastItemTime?.id === date?.id ? null : (
                    <span className="divider-time" style={{ color: placeholderColor || 'inherit' }}>
                      :
                    </span>
                  )}
                </>
              )
            );
          })}
        </div>
      </DropdownContainer>
    );
  }

  return (
    <WrapperDatePicker id={id} style={style} onClick={e => onClickElement(field.id)}>
      <span style={{ color: placeholderColor || '#d5dae3' }}>
        {dateTimeFormatLabel || dateTimeFormat || 'This is a Datetime picker custom field'}
      </span>
    </WrapperDatePicker>
  );
};

export default DateTimeWithType;

DateTimeWithType.defaultProps = {};
