// Locales
import { translations } from 'locales/translations';
// Utils
import { getTranslateMessage } from 'utils/messages';

export const TRIGGER_OPTIONS = {
  SYSTEM: {
    value: 'system',
    label: getTranslateMessage(translations.system.title),
  },
  USER_ACTION: {
    value: 'user_action',
    label: getTranslateMessage(translations.userAction.title),
  },
};

export const BY_OPTIONS = {
  CLICK_ON_THIS_BLOCK: {
    value: 'click_on_this_block',
    label: getTranslateMessage(translations.clickOnThisBlock.title),
  },
  OPTIN_FIELD: {
    value: 'optin_field',
    label: getTranslateMessage(translations.optinField.title),
  },
  BUTTON: {
    value: 'button',
    label: getTranslateMessage(translations.button.title),
  },
  PAGE: {
    value: 'page',
    label: getTranslateMessage(translations.page.title),
  },
};

export const BY_OPTIONS_PAGES = {
  PAGE: {
    value: 'page',
    label: getTranslateMessage(translations.page.title),
  },
};

export const SHAKE_TYPE_OPTIONS = {
  VERTICAL_SHAKE: {
    value: 'vertical_shake',
    label: getTranslateMessage(translations.verticalShake.title),
  },
  HORIZONTAL_SHAKE: {
    value: 'horizontal_shake',
    label: getTranslateMessage(translations.horizontalShake.title),
  },
  JUMP_SHAKE: {
    value: 'jump_and_shake',
    label: getTranslateMessage(translations.jumpShake.title),
  },
  HORIZONTAL_SKEWED_SHAKING: {
    value: 'horizontal_skewed_shaking',
    label: getTranslateMessage(translations.horizontalSkewedShaking.title),
  },
  VERTIAL_SKEWED_SHAKING: {
    value: 'vertical_skewed_shaking',
    label: getTranslateMessage(translations.verticalSkewedShaking.title),
  },
  CONSTANT_TILT: {
    value: 'constant_tilt',
    label: getTranslateMessage(translations.constantTilt.title),
  },
};
