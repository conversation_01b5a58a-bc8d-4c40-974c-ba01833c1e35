import { SelectProps, Select } from 'app/components/molecules/Select';

interface InputBooleanProps extends SelectProps {
  value: string;
  onChange: (v: InputBooleanProps['value']) => void;
}
export const InputBoolean: React.FC<InputBooleanProps> = props => {
  const { value, options, onChange, ...restOf } = props;
  return (
    <Select
      value={value}
      options={[
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
      ]}
      onChange={val => onChange(val)}
      {...restOf}
    />
  );
};
