// Libraries
import React, { useCallback } from 'react';
import produce from 'immer';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { parseInt } from 'lodash';

// Types
import {
  TColumnTableBlock,
  TTableSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Config, Constants
import { CHANGE_CONTENT_TYPE } from '../constants';
import { BLOCK_SETTING_DEFAULT } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';

// Locales
import { translations } from 'locales/translations';

// Action
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// components
import AddColFromBO from '../components/AddColFromBo';

// Organisms
import { FilterSetting } from '../../../FilterSetting';

// Atom
import { Text, Space } from 'app/components/atoms';

// Molecules
import { Collapse, CollapsePanel, InputNumber, SliderWithInputNumber, Select } from 'app/components/molecules';

// Utils
import { random } from 'app/utils/common';
import { handleError } from 'app/utils/handleError';
import TableSortSettings from '../components/TableSortSettings';
import {
  TABLE_DE_MAX_COL,
  TABLE_ME_MAX_COL,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { buildOptionArchive, checkStatusBO, optionNotExitsValue } from '../../../../../utils';

type TableContentProps = {
  tableBlockSettings: TTableSettings;
  dimensions: any[];
  metrics: any[];
  selectedMetricCols: TColumnTableBlock[];
  selectedDimensionCols: TColumnTableBlock[];
  isLoadingDataTableBo: boolean;
  isLoadingGetListBO: boolean;
  listBO: any[];
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/Table/Content/index.tsx';

const Content = (props: TableContentProps) => {
  const {
    tableBlockSettings,
    isLoadingDataTableBo,
    metrics,
    dimensions,
    selectedDimensionCols,
    selectedMetricCols,
    isLoadingGetListBO,
    listBO,
  } = props;

  const { t } = useTranslation();

  const dispatch = useDispatch();

  const { updateBlockFieldsSelected } = mediaTemplateDesignActions;

  const { table } = tableBlockSettings;

  const boErrorMessage = checkStatusBO({
    listBO,
    itemTypeId: tableBlockSettings.boId,
  });

  const changeTableContent = useCallback(
    (type: string | undefined, value?: any, options?: any) => {
      const tableBlock = document.querySelector<HTMLTableElement>('.table-block__wrapper table');

      if (!tableBlock) return;

      const tableBlockWidth = tableBlock.offsetWidth;

      try {
        let dataUpdate = [] as { fieldPath: string; data: any }[];

        switch (type) {
          case CHANGE_CONTENT_TYPE.TABLE_WIDTH: {
            dataUpdate.push({
              fieldPath: `settings.table.styles.width`,
              data: value.width + '%',
            });

            break;
          }
          case CHANGE_CONTENT_TYPE.BO_ID: {
            dataUpdate = [
              {
                fieldPath: 'settings.boId',
                data: value,
              },
              {
                fieldPath: 'settings.columns',
                data: [],
              },
              {
                fieldPath: 'settings.filters',
                data: BLOCK_SETTING_DEFAULT.TABLE.filters,
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.FILTERS: {
            dataUpdate = [
              {
                fieldPath: 'settings.filters',
                data: value,
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.TABLE_STYLE: {
            dataUpdate = [
              {
                fieldPath: 'settings.table.styles',
                data: {
                  ...tableBlockSettings.table.styles,
                  ...value,
                },
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.TABLE_SETTINGS: {
            dataUpdate = [
              {
                fieldPath: 'settings.table.settings',
                data: {
                  ...tableBlockSettings.table.settings,
                  ...value,
                },
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.ADD_COL: {
            const { colType } = value;

            const columnsUpdated = produce(tableBlockSettings.columns, draft => {
              const sourceDataCol = colType === 'dimension' ? dimensions : metrics;

              draft.push({
                id: random(6),
                value: sourceDataCol[0].itemPropertyName,
                label: sourceDataCol[0].itemPropertyDisplay,
                dataType: sourceDataCol[0].dataType,
                placement: colType === 'dimension' ? 'left' : 'right',
                width: '',
                colType,
                ...(colType === 'metric' && {
                  displayFormat: {
                    type: 'number',
                  },
                }),
              });

              // columns ordered [...dismension, ...metris];
              draft.sort((a, b) => {
                if (a.colType === 'index' || b.colType === 'index') return 0;

                if (a.colType === b.colType) return 0;
                if (a.colType === 'dimension') return -1;

                return 1;
              });
            });

            dataUpdate = [
              {
                fieldPath: 'settings.columns',
                data: columnsUpdated.map(col => ({
                  ...col,
                  width: tableBlockWidth / columnsUpdated.length + 'px',
                })),
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.UPDATE_COL: {
            const { colId: updatedColId, updatedData } = value;

            const colIdx = tableBlockSettings.columns.findIndex(col => col.id === updatedColId);

            dataUpdate = [
              {
                fieldPath: `settings.columns[${colIdx}]`,
                data: updatedData,
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.DELETE_COL: {
            const colIdx = tableBlockSettings.columns.findIndex(col => col.id === value);
            const data = produce(tableBlockSettings.columns, draft => {
              draft.splice(colIdx, 1);
            });

            dataUpdate = [
              {
                fieldPath: 'settings.columns',
                data: data.map((col, _, cols) => ({
                  ...col,
                  width: tableBlockWidth / cols.length + 'px',
                })),
              },
            ];
            break;
          }
          case CHANGE_CONTENT_TYPE.SORT_BY: {
            dataUpdate = [{ fieldPath: 'settings.sortBy', data: value }];
            break;
          }
          default:
            return;
        }

        dispatch(
          updateBlockFieldsSelected({
            dataUpdate,
            ...options,
          }),
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeImageBlock',
          args: {},
        });
      }
    },
    [dimensions, metrics, dispatch, tableBlockSettings, updateBlockFieldsSelected],
  );

  return (
    <Collapse defaultActiveKey={'table'} accordion>
      <CollapsePanel header={t(translations.tableBlock.title)} key="table">
        <Space size={20} direction="vertical">
          <SliderWithInputNumber
            label={t(translations.widthPercent.title)}
            min={0}
            max={100}
            value={parseInt('' + table.styles.width, 10)}
            onAfterChange={value =>
              changeTableContent(CHANGE_CONTENT_TYPE.TABLE_WIDTH, {
                width: value,
              })
            }
          />

          <Select
            showSearch
            loading={isLoadingGetListBO}
            label={t(translations.businessObject.title)}
            value={optionNotExitsValue(listBO, tableBlockSettings.boId)}
            options={buildOptionArchive(
              listBO?.map(bo => ({
                value: bo.value,
                label: bo.label,
                status: bo.status,
              })),
              tableBlockSettings.boId,
            )}
            placeholder={t(translations.selectAnItem.title)}
            status={boErrorMessage ? 'error' : ''}
            errorMsg={boErrorMessage}
            onChange={value => changeTableContent(CHANGE_CONTENT_TYPE.BO_ID, value)}
          />

          {tableBlockSettings.boId && (
            <React.Fragment>
              <Space direction="vertical" size={10}>
                <Text bold size={14}>
                  {t(translations.dimensions.title)}
                </Text>

                <AddColFromBO
                  columnOptions={dimensions}
                  columnsSelected={selectedDimensionCols}
                  onChangeColumn={(colId, updatedData) =>
                    changeTableContent(CHANGE_CONTENT_TYPE.UPDATE_COL, {
                      colId,
                      updatedData,
                    })
                  }
                  onDeleteColumn={colId => changeTableContent(CHANGE_CONTENT_TYPE.DELETE_COL, colId)}
                  showDeleteColumnIcon={tableBlockSettings.columns.filter(col => col.colType !== 'index').length > 1}
                  isLoadingColumnOptions={isLoadingDataTableBo}
                  onAddColumn={() => changeTableContent(CHANGE_CONTENT_TYPE.ADD_COL, { colType: 'dimension' })}
                  showAddColumnBtn={selectedDimensionCols.length < TABLE_DE_MAX_COL}
                  addColBtnText={`+  ${t(translations.addDimension.title)}`}
                  titleSelectInput={t(translations.dimensions.title)}
                />
              </Space>

              <Space direction="vertical" size={10}>
                <Text bold size={14}>
                  {t(translations.metric.title)}
                </Text>

                <AddColFromBO
                  columnOptions={metrics}
                  columnsSelected={selectedMetricCols}
                  onChangeColumn={(colId, updatedData) =>
                    changeTableContent(CHANGE_CONTENT_TYPE.UPDATE_COL, {
                      colId,
                      updatedData,
                    })
                  }
                  onDeleteColumn={colId => changeTableContent(CHANGE_CONTENT_TYPE.DELETE_COL, colId)}
                  showDeleteColumnIcon={tableBlockSettings.columns.filter(col => col.colType !== 'index').length > 1}
                  isLoadingColumnOptions={isLoadingDataTableBo}
                  onAddColumn={() => changeTableContent(CHANGE_CONTENT_TYPE.ADD_COL, { colType: 'metric' })}
                  showAddColumnBtn={selectedMetricCols.length < TABLE_ME_MAX_COL}
                  addColBtnText={`+  ${t(translations.addMetric.title)}`}
                  titleSelectInput={t(translations.metric.title)}
                />
              </Space>

              <div className="ants-flex ants-items-center ants-w-100 ants-justify-between">
                <Text>{t(translations.showTop.title)}#</Text>
                <InputNumber
                  value={table.settings.showTop}
                  onChange={value =>
                    changeTableContent(CHANGE_CONTENT_TYPE.TABLE_SETTINGS, {
                      showTop: value,
                    })
                  }
                  min={1}
                  max={10000}
                />
              </div>

              <TableSortSettings
                isLoadingSelectCol={isLoadingGetListBO || isLoadingDataTableBo}
                disableSelectOrder={isLoadingGetListBO || isLoadingDataTableBo}
                columns={tableBlockSettings.columns}
                sortSettings={tableBlockSettings.sortBy}
                onChangeValues={values => {
                  changeTableContent(CHANGE_CONTENT_TYPE.SORT_BY, values);
                }}
              />

              <FilterSetting
                isFormatDateTimeMilliseconds={true}
                boId={tableBlockSettings.boId}
                filters={tableBlockSettings.filters}
                onOKChangeFilter={conditions => changeTableContent(CHANGE_CONTENT_TYPE.FILTERS, conditions)}
              />
            </React.Fragment>
          )}
        </Space>
      </CollapsePanel>
    </Collapse>
  );
};

export default Content;
