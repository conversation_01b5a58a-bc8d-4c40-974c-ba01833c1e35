// Libraries
import React, { memo, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { get, isEmpty, keyBy } from 'lodash';
import produce from 'immer';

// Components
import {
  InputArray,
  InputBoolean,
  InputCalendar,
  InputCalendarBetween,
  InputDateTimeAgo,
  InputDateTimeBetweenAgo,
  InputNumberBetween,
  InputOrSelect,
  InputSelectMulti,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FilterSetting/components/common';

// Atoms
import { Button, Divider, Icon, Input, Text } from 'app/components/atoms';

// Molecules
import { Empty, Form, InputNumber, Select } from 'app/components/molecules';
import { Modal, ModalProps } from 'app/components/molecules/Modal';

// Translations
import { translations } from 'locales/translations';
import { handleError } from 'app/utils/handleError';

// Constants
import { FORM_VALIDATE } from 'constants/formValidate';
import {
  OPERATORS,
  SEGMENT_IDS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FilterSetting/constants';

// Queries
import { useGetListBO, useGetListAttributeBO, useGetListCollectionBO } from 'app/queries/BusinessObject';

// Utils
import { getTranslateMessage } from 'utils/messages';
import {
  buildOptionAttrArchive,
  checkStatusCollection,
  checkStatusAttr,
  buildOptionArchive,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/utils';
import {
  getInputDefaultValue,
  getInputType,
  isInputDateTimeAgo,
  isInputDateTimeBetweenAgo,
  validateFilters,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FilterSetting/utils';

// Types
import {
  TFilters,
  TFilter,
  EventMetadata,
  CustomerMetadata,
  VisitorMetadata,
  TOperatorValue,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { useDeepCompareEffect } from 'app/hooks';

type TValue = Record<string, any> | string | null;

interface TSelectMultiple {
  value: TValue;
  filters: TFilters;
  valueType: string | undefined;
  contentSourceIds?: Array<string | number>;
  onChange: ({ value, filters, valueType }: { value: TValue; filters: TFilters; valueType: string }) => void;
}

interface TSelectFieldModal extends ModalProps {
  value: TValue;
  filters: TFilters;
  contentSourceIds?: Array<string | number>;
  onSubmit: ({ value, filters }: { value: TValue; filters: TFilters }) => void;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/LimitedSubmitButton/components/ConditionsSetting/components/SelectMultiple/index.tsx';

const FORM_FIELDS = {
  ITEM_TYPE_ID: {
    label: getTranslateMessage(translations.contentSources.title),
    name: 'item_type_id',
  },
  ATTRIBUTE: {
    label: getTranslateMessage(translations.selectAttribute.title),
    name: 'attribute',
  },
  FILTERS: {
    label: getTranslateMessage(translations.filters.title),
    name: 'filters',
  },
};

const defaultAndCondition: TFilter = {
  value: null,
  operator: null,
  data_type: null,
  column: null,
  // itemTypeId: null,
  condition_type: 'comp_attr',
  value_type: 'normal',
  extend: null,
};

const defaultFilters: TFilters = { OR: [{ AND: [] }] };

const defaultErrors = {
  OR: [],
  isError: false,
};

export const SelectMultiple: React.FC<TSelectMultiple> = memo(props => {
  // I18n
  const { t } = useTranslation();

  // Props
  const { value, valueType, filters, onChange } = props;
  // State
  const [selectFieldModal, setSelectFieldModal] = useState({
    visible: false,
  });

  // Handlers
  const toggleSelectFieldModal = (visible: boolean) => {
    try {
      setSelectFieldModal(modalState => ({
        ...modalState,
        visible: visible != null ? visible : modalState.visible,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'toggleModal',
        args: {},
      });
    }
  };

  const onSubmitSelectFieldModal = ({ value, filters }) => {
    try {
      onChange({ value, filters, valueType: 'attribute' });

      toggleSelectFieldModal(false);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onSubmitSelectFieldModal',
        args: { value },
      });
    }
  };

  const onChangeMultipleSelect = data => {
    try {
      switch (valueType) {
        case 'attribute':
          if (Array.isArray(data) && data.length === 0) {
            onChange({
              value: null,
              filters: defaultFilters,
              valueType: 'normal',
            });
          }
          break;
        default:
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeMultipleSelect',
        args: {},
      });
    }
  };

  const onClickSelectMultiple = () => {
    try {
      switch (valueType) {
        case 'attribute':
          toggleSelectFieldModal(true);
          break;

        default:
          break;
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickSelectMultiple',
        args: {},
      });
    }
  };

  return (
    <div>
      <Select
        mode="multiple"
        value={['attribute'].includes(valueType || '') ? [get(value, 'attribute.label', null)] : []}
        dropdownRender={menu => {
          return !['attribute'].includes(valueType || '') ? (
            <>
              <div className="ants-flex ants-items-center ants-justify-between ants-px-1">
                <Button size="small" type="text">
                  {t(translations.showChecked.title)}
                </Button>
                <Button size="small" type="text">
                  {t(translations.uncheckAll.title)}
                </Button>
              </div>
              <Divider style={{ margin: '5px 0px' }} />
              <Empty />
              <Divider style={{ margin: '5px 0px' }} />

              <div className="ants-flex ants-items-center ants-px-2.5 ants-space-x-1">
                <Text>{t(translations.or.title)}</Text>
                <Button type="text" onClick={() => toggleSelectFieldModal(true)}>
                  {t(translations.selectAField.title)}
                </Button>
              </div>
            </>
          ) : (
            <div />
          );
        }}
        dropdownClassName={['attribute'].includes(valueType || '') ? '!ants-p-0' : ''}
        placeholder={t(translations.selectAnItem.title)}
        onChange={onChangeMultipleSelect}
        onClick={onClickSelectMultiple}
      />

      <SelectFieldModal
        value={value}
        filters={filters}
        visible={selectFieldModal.visible}
        contentSourceIds={props.contentSourceIds}
        onSubmit={data => onSubmitSelectFieldModal(data)}
        onCancel={() => toggleSelectFieldModal(false)}
      />
    </div>
  );
});

export const SelectFieldModal: React.FC<TSelectFieldModal> = memo(props => {
  // Props
  const { value, contentSourceIds, onCancel, onSubmit, ...restOfProps } = props;

  // I18n
  const { t } = useTranslation();

  // Form
  const [form] = Form.useForm();
  const itemTypeId = Form.useWatch(FORM_FIELDS.ITEM_TYPE_ID.name, form);

  // State
  const [filters, setFilters] = useState<TFilters>(defaultFilters);
  const [errors, setErrors] = useState<any>({
    OR: [],
    isError: false,
  });
  // Queries
  const { data: listBO, isFetching: isLoadingListBO } = useGetListBO();
  const { data: listAttribute, isFetching: isLoadingListAttribute } = useGetListAttributeBO({
    itemTypeIds: [itemTypeId],
    options: {
      select(data) {
        const { rows = [] } = data || {};

        return (
          rows[0]?.properties.map(item => ({
            ...item,
            value: item.itemPropertyName,
            label: item.translateLabel,
            disabled: parseInt(item.status) === 4,
          })) || []
        );
      },
    },
  });
  const { data: listCollection } = useGetListCollectionBO({
    itemTypeId,
    options: {
      select(data) {
        const { rows } = data || {};

        return rows?.map(item => ({
          value: item.value,
          id: item.value,
          label: item.label,
          disabled: parseInt(item.model.status) === 4,
          status: parseInt(item.model.status),
        }));
      },
    },
  });

  // Memo
  const mapListAttr = useMemo(() => {
    return keyBy(listAttribute, 'itemPropertyName');
  }, [listAttribute]);

  const boOptions = useMemo(() => {
    let contentSourcesOptions: any[] = [];

    contentSourcesOptions = listBO?.filter(({ value }) => contentSourceIds?.includes(value));

    return buildOptionArchive(
      !isEmpty(contentSourcesOptions) ? contentSourcesOptions : (listBO as any),
      itemTypeId,
    )?.map(bo => ({
      value: bo.value,
      label: bo.label,
    }));
  }, [listBO, itemTypeId, contentSourceIds]);

  const itemTypeName = listBO?.find(({ id }) => id === itemTypeId)?.name;

  useDeepCompareEffect(() => {
    if (props.visible) {
      if (!isEmpty(value)) {
        form.setFieldsValue({
          [FORM_FIELDS.ATTRIBUTE.name]: get(value, 'attribute.value', null),
          [FORM_FIELDS.ITEM_TYPE_ID.name]: get(value, 'itemTypeId', null),
        });
      }

      if (!isEmpty(props.filters)) {
        setFilters(props.filters);
      }
    }
  }, [value, props.filters, props.visible]);

  // Handlers
  const onFinishForm = values => {
    try {
      const draftData = {
        value: {
          itemTypeId: values[FORM_FIELDS.ITEM_TYPE_ID.name],
          itemTypeName: itemTypeName,
          attribute: {},
        },
        filters,
      };

      const selectedAttribute = listAttribute?.find(({ value }) => value === values[FORM_FIELDS.ATTRIBUTE.name]);

      if (selectedAttribute) {
        const { value, label, status, disabled, dataType } = selectedAttribute;
        draftData.value.attribute = {
          value,
          label,
          status,
          disabled,
          dataType,
        };
      }

      const errors = validateFilters(filters, true);

      if (errors.isError) {
        setErrors(errors);
        return;
      }

      form.resetFields();

      setFilters(defaultFilters);
      setErrors(defaultErrors);

      onSubmit({
        ...draftData,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onFinishForm',
        args: {},
      });
    }
  };

  const onCancelModal = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    try {
      form.resetFields();

      setFilters(defaultFilters);
      setErrors(defaultErrors);

      typeof onCancel === 'function' && onCancel(e);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onCancel',
        args: {},
      });
    }
  };

  const onClickAddRowCondition = () => {
    try {
      // const draf;
      setFilters(filters =>
        produce(filters, draftFilters => {
          draftFilters.OR[0].AND.push(defaultAndCondition);
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onClickAddRowCondition',
        args: {},
      });
    }
  };

  const onResetError = (orIndex: number, andIndex: number) => {
    const newErrors = { ...errors };

    if (newErrors.OR[orIndex] && newErrors.OR[orIndex].AND[andIndex]) {
      const error = {
        column: true,
        operator: true,
        value: true,
        extend: true,
      };
      newErrors.OR[orIndex].AND[andIndex] = error;
      setErrors(newErrors);
    }
  };

  const onChangeAttr = (item: TFilter, orIndex: number, andIndex: number) => {
    const property = listAttribute.find(attr => attr.itemPropertyName === item.column);

    if (property) {
      const { itemPropertyName, dataType } = property;
      const defaultOperator = OPERATORS[dataType][0].value as TOperatorValue;
      const defaultValue = getInputDefaultValue({
        operator: defaultOperator,
        dataType,
        property,
      });

      setFilters(filters =>
        produce(filters, draft => {
          draft.OR[orIndex].AND[andIndex] = {
            ...filters.OR[orIndex].AND[andIndex],
            column: itemPropertyName,
            data_type: dataType,
            operator: defaultOperator,
            value: defaultValue,
            extend: [],
          };
        }),
      );

      onResetError(orIndex, andIndex);
    }
  };

  const onChangeOperator = (item: TFilter, orIndex: number, andIndex: number) => {
    const property = listAttribute.find(attr => attr.itemPropertyName === item.column) || {};
    if (property) {
      const defaultValue = getInputDefaultValue({
        operator: item.operator,
        dataType: item.data_type,
        property,
      });

      setFilters(filters =>
        produce(filters, draft => {
          if (
            isInputDateTimeAgo(item.operator, item.data_type) ||
            isInputDateTimeBetweenAgo(item.operator, item.data_type)
          ) {
            draft.OR[orIndex].AND[andIndex].operator = item.operator;
            draft.OR[orIndex].AND[andIndex].value = defaultValue.value;
            draft.OR[orIndex].AND[andIndex].time_unit = defaultValue.time_unit;
          } else {
            draft.OR[orIndex].AND[andIndex].operator = item.operator;
            draft.OR[orIndex].AND[andIndex].value = defaultValue;
          }
        }),
      );

      onResetError(orIndex, andIndex);
    }
  };

  const onChangeValue = (value: any, orIndex: number, andIndex: number, key: string = 'value', key2?: string) => {
    setFilters(filters =>
      produce(filters, draft => {
        draft.OR[orIndex].AND[andIndex] = { ...filters.OR[orIndex].AND[andIndex], [key]: value };
        switch (key) {
          case 'extend':
            const valueTmp = draft.OR[orIndex].AND[andIndex].value.concat(value);
            draft.OR[orIndex].AND[andIndex] = { ...draft.OR[orIndex].AND[andIndex], value: valueTmp };
            break;
          case 'value':
            if (draft.OR[orIndex].AND[andIndex].extend.length > 0) {
              const valueTmp = draft.OR[orIndex].AND[andIndex].value.concat(draft.OR[orIndex].AND[andIndex].extend);
              draft.OR[orIndex].AND[andIndex] = { ...draft.OR[orIndex].AND[andIndex], value: valueTmp };
            }
            break;
          default:
            break;
        }
      }),
    );

    onResetError(orIndex, andIndex);
  };

  const onChangeMultipleValue = (value: any, orIndex: number, andIndex: number) => {
    setFilters(filters =>
      produce(filters, draft => {
        draft.OR[orIndex].AND[andIndex] = { ...filters.OR[orIndex].AND[andIndex], ...value };
      }),
    );
    onResetError(orIndex, andIndex);
  };

  const onRemoveCondition = ({ orIndex, andIndex }) => {
    try {
      setFilters(filters =>
        produce(filters, draft => {
          draft.OR[orIndex]?.AND?.splice(andIndex, 1);
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onRemoveCondition',
        args: {},
      });
    }
  };

  const renderInputValue = (item: TFilter, orIndex: number, andIndex: number) => {
    const {
      column,
      value,
      data_type,
      operator,
      value_type,
      event_metadata = {} as EventMetadata,
      customer_metadata = {} as CustomerMetadata,
      visitor_metadata = {} as VisitorMetadata,
      time_unit,
      extend,
    } = item;

    const onChangeValueItem = (val: any, key: string = 'value', key2?: string) => {
      onChangeValue(val, orIndex, andIndex, key, key2);
    };
    const onChangeMultipleValueItem = (val: any) => {
      onChangeMultipleValue(val, orIndex, andIndex);
    };

    const property = listAttribute?.find(item => item.itemPropertyName === column);

    const { errorMessage } = checkStatusCollection({
      listBO: listBO as any,
      itemTypeId: itemTypeId || 0,
      listCollection,
      field: value,
    });

    const element = getInputType({
      operator,
      dataType: data_type,
      property,
      resInputBoolean: <InputBoolean value={value} onChange={onChangeValueItem as any} />,
      resInputNumberBetween: <InputNumberBetween value={value} onChange={onChangeValueItem} />,
      resInputCalendarBetween: <InputCalendarBetween value={value} onChange={onChangeValueItem} />,
      resInputDateTimeBetweenAgo: (
        <InputDateTimeBetweenAgo
          value={value}
          timeUnit={time_unit}
          onChange={value => onChangeMultipleValueItem(value)}
        />
      ),
      resInputDateTimeAgo: (
        <InputDateTimeAgo
          value={value}
          timeUnit={time_unit}
          onChangeMultipleValue={value =>
            onChangeMultipleValueItem({
              ...value,
              ...(value.value !== undefined && {
                value: String(value.value),
              }),
            })
          }
        />
      ),
      resInputCalendar: <InputCalendar value={value} operator={operator} onChange={onChangeValueItem} />,
      resInputSelectMulti: (
        <InputSelectMulti
          value={value}
          extend={extend}
          eventMetadata={event_metadata}
          customerMetadata={customer_metadata}
          visitorMetadata={visitor_metadata}
          valueType={value_type}
          onChange={onChangeValueItem}
          onChangeMultipleValue={onChangeMultipleValueItem}
          itemTypeId={itemTypeId}
          itemTypeName={itemTypeName}
          errorMessage={column === SEGMENT_IDS ? errorMessage : ''}
          // isDisable={isDisableCollection}
          column={column}
          hideSelectField={true}
        />
      ),
      resInputOrSelect: (
        <InputOrSelect
          value={value}
          onChangeMultipleValue={onChangeMultipleValueItem}
          eventMetadata={event_metadata}
          customerMetadata={customer_metadata}
          visitorMetadata={visitor_metadata}
          valueType={value_type}
          dataType={data_type}
          hideSelectField={true}
        />
      ),
      resInputArray: <InputArray value={value} onChange={onChangeValueItem} />,
      resInputNumber: <InputNumber value={value} onChange={onChangeValueItem} />,
      resDefault: (
        <Input
          value={value}
          onChange={e => onChangeValueItem(e.target.value)}
          placeholder={t(translations.inputYourValue.title)}
        />
      ),
    });
    return element;
  };

  const onSelectContentSource = () => {
    try {
      setFilters(defaultFilters);

      form.setFieldsValue({
        [FORM_FIELDS.ATTRIBUTE.name]: null,
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onSelectContentSource',
        args: {},
      });
    }
  };

  return (
    <Modal
      {...restOfProps}
      width={600}
      title={t(translations.selectAttributeCapitalize.title)}
      onOk={() => form.submit()}
      onCancel={onCancelModal}
    >
      <Form form={form} labelAlign="left" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }} onFinish={onFinishForm}>
        <Form.Item
          className="ants-items-center"
          label={FORM_FIELDS.ITEM_TYPE_ID.label}
          name={FORM_FIELDS.ITEM_TYPE_ID.name}
          rules={[{ required: true, message: FORM_VALIDATE.REQUIRED.message('Content source') }]}
        >
          <Select
            showSearch
            loading={isLoadingListBO}
            options={boOptions || []}
            placeholder={t(translations.selectAnItem.title)}
            onSelect={onSelectContentSource}
          />
        </Form.Item>

        <Form.Item
          className="ants-items-center"
          label={FORM_FIELDS.ATTRIBUTE.label}
          name={FORM_FIELDS.ATTRIBUTE.name}
          rules={[{ required: true, message: FORM_VALIDATE.REQUIRED.message('Attribute') }]}
        >
          <Select
            showSearch
            loading={isLoadingListAttribute}
            options={listAttribute?.map(({ value, label }) => ({ value, label }))}
            placeholder={t(translations.selectAnItem.title)}
          />
        </Form.Item>

        {filters.OR.map((orCondition, orIndex) => {
          const isShowOrCondition = !!orCondition.AND?.length;

          return isShowOrCondition ? (
            <React.Fragment key={orIndex}>
              {orCondition.AND.map((andCondition, andIndex) => {
                const { errorMessage, isDisable } = checkStatusAttr({
                  listBO: listBO as any,
                  itemTypeId,
                  field: andCondition.column || '',
                  listAttribute,
                });

                return (
                  <div key={orIndex + andIndex}>
                    <Text className="ants-mb-2">{t(translations.where.title)}</Text>

                    <div className="ants-relative ants-flex ants-space-x-5">
                      <div className="ants-flex ants-w-full ants-gap-5">
                        <Select
                          showSearch
                          disabled={isDisable}
                          style={{ width: 120 }}
                          options={buildOptionAttrArchive(listAttribute, andCondition.column)}
                          value={andCondition.column}
                          onChange={value => onChangeAttr({ ...andCondition, column: value }, orIndex, andIndex)}
                          placeholder={t(translations.selectAnItem.title)}
                          status={!get(errors, `OR[${orIndex}].AND[${andIndex}].column`, true) ? 'error' : undefined}
                          errorArchive={errorMessage}
                          errorMsg={t(translations.thisFieldIsRequired.title)}
                        />

                        {andCondition.column ? (
                          <Select
                            style={{ width: 120 }}
                            showSearch
                            options={
                              andCondition.data_type === 'string' &&
                              mapListAttr[andCondition.column]?.autoSuggestion === 1
                                ? OPERATORS['suggestion']
                                : OPERATORS[andCondition.data_type!]
                            }
                            value={andCondition.operator}
                            onChange={value =>
                              onChangeOperator({ ...andCondition, operator: value }, orIndex, andIndex)
                            }
                            placeholder={t(translations.selectAnItem.title)}
                            status={
                              !get(errors, `OR[${orIndex}].AND[${andIndex}].operator`, true) ? 'error' : undefined
                            }
                            errorMsg={t(translations.thisFieldIsRequired.title)}
                          />
                        ) : null}

                        {andCondition.operator ? (
                          <div>
                            <div className="ants-mt-0.5" style={{ width: 240 }}>
                              {renderInputValue(andCondition, orIndex, andIndex)}
                            </div>
                            {!get(errors, `OR[${orIndex}].AND[${andIndex}].value`, true) &&
                            !get(errors, `OR[${orIndex}].AND[${andIndex}].extend`, true) ? (
                              <Text color="#ff4d4f" className="ants-mt-2 ants-ml-2">
                                {t(translations.valueFilterEmpty.title)}
                              </Text>
                            ) : null}
                          </div>
                        ) : null}
                      </div>

                      <Icon
                        type="icon-ants-remove-slim"
                        className="ants-text-gray ants-cursor-pointer"
                        style={{
                          marginTop: 10,
                        }}
                        size={14}
                        onClick={() => onRemoveCondition({ orIndex, andIndex })}
                      />
                    </div>
                  </div>
                );
              })}
            </React.Fragment>
          ) : null;
        })}

        <div className="ants-flex ants-items-center ants-space-x-3">
          <Text>{t(translations.and.title)}</Text>
          <Button type="default" className="ants-rounded-full ants-border-primary" onClick={onClickAddRowCondition}>
            + {t(translations.refineByAttribute.title)}
          </Button>
        </div>
      </Form>
    </Modal>
  );
});

export default SelectMultiple;
