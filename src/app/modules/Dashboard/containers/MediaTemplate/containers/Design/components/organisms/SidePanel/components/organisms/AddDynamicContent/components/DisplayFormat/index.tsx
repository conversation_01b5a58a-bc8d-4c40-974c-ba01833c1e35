// Types
import FormatDatetime from '../FormatDatetime';
import FormatNumber from '../FormatNumber';

export type TDisplayFormat = 'number' | 'percentage' | 'currency' | 'datetime';

type DisplayFormatProps = {
  onSettingsChange: (settings: any) => void;
  formatSettings: any;
  displayFormatType: TDisplayFormat;
  dynamicContentType?: string;
};

const DisplayFormat = (props: DisplayFormatProps) => {
  const renderFormatSetting = (): JSX.Element | null => {
    switch (props.displayFormatType) {
      case 'number':
      case 'percentage':
      case 'currency': {
        return (
          <FormatNumber
            {...props.formatSettings}
            onSettingsChange={props.onSettingsChange}
            type={props.displayFormatType}
          />
        );
      }

      case 'datetime': {
        return (
          <FormatDatetime
            {...props.formatSettings}
            onSettingsChange={props.onSettingsChange}
            type={props.displayFormatType}
            dynamicContentType={props.dynamicContentType}
          />
        );
      }
    }
  };

  return renderFormatSetting();
};

export default DisplayFormat;
