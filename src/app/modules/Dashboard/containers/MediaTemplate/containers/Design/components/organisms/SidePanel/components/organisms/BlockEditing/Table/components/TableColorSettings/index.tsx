import { TTableSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import { getTranslateMessage } from 'utils/messages';
import { MultipleColorsSetting } from '../../../../../molecules';
import { TColorItem } from '../../../../../molecules/MultipleColorsSetting';

type TableColorSettingsProps = {
  tableSettings: TTableSettings['table']['settings'];
  onChange: (colors: {
    headerBackground: string;
    cellBorderColor: string;
    oddRowColor: string;
    evenRowColor: string;
  }) => void;
};

const TableColorSettings = (props: TableColorSettingsProps) => {
  const { t } = useTranslation();

  const { tableSettings } = props;

  const tableColors: TColorItem[] = [
    {
      label: getTranslateMessage(translations.tableHeaderBackground.title, 'Header background color'),
      color: tableSettings.headerBackground,
    },
    {
      label: getTranslateMessage(translations.tableCellBorder.title, 'Cell border color'),
      color: tableSettings.cellBorderColor,
    },
    {
      label: getTranslateMessage(translations.tableOddRowColor.title, 'Odd row color'),
      color: tableSettings.oddRowColor,
    },
    {
      label: getTranslateMessage(translations.tableEvenRowColor.title, 'Even row color'),
      color: tableSettings.evenRowColor,
    },
  ];

  return (
    <MultipleColorsSetting
      placement="topRight"
      className="ants-font-bold"
      label={t(translations.tableColor.title)}
      listColorItems={tableColors}
      onChange={colors =>
        props.onChange({
          headerBackground: colors[0] || tableSettings.headerBackground,
          cellBorderColor: colors[1] || tableSettings.cellBorderColor,
          oddRowColor: colors[2] || tableSettings.oddRowColor,
          evenRowColor: colors[3] || tableSettings.evenRowColor,
        })
      }
    />
  );
};

export default TableColorSettings;
