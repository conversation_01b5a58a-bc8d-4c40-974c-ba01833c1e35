import { Input, Space, Text } from 'app/components/atoms';
import { Select, SliderWithUnit } from 'app/components/molecules';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { WrapperSelectInline } from './styled';
import { AlignSetting } from '../../../../../../molecules';
import { LABEL_POSITION_OPTIONS } from '../../../constants';

type TFormOTP = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

export const FormOTP: React.FC<TFormOTP> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const { otpForm = {}, blockStyles } = settings;
  return (
    <Space size={20} direction="vertical">
      <SliderWithUnit
        label={t(translations.width.title)}
        labelClassName="!ants-text-gray-4"
        unit={otpForm.suffix || '%'}
        min={0}
        max={(otpForm.suffix || '%') === 'px' ? 1000 : 100}
        value={parseFloat(otpForm.width)}
        onAfterChange={value =>
          onChange({
            otpForm: {
              ...otpForm,
              width: value,
            },
          })
        }
        onChangeUnit={value => {
          const width = parseFloat(otpForm.width) || 100;
          let newWidth = '';

          newWidth = width + value;

          if (value === '%' && width > 100) {
            newWidth = '100%';
          }

          if (value === 'auto') {
            newWidth = 'auto';
          }

          onChange({
            otpForm: {
              ...otpForm,
              suffix: value,
            },
          });
        }}
      />
      <AlignSetting
        label={t(translations.align.title)}
        align={blockStyles.justifyContent}
        onChange={value => onChange({ blockStyles: { ...blockStyles, justifyContent: value } })}
      />
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">{t(translations.timerLabel.title)}</Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={otpForm.timerLabel}
          onAfterChange={value => onChange({ otpForm: { ...otpForm, timerLabel: value } })}
        />
      </Space>
      <WrapperSelectInline>
        <Text className="!ants-text-gray-4">{t(translations.labelPosition.title)}</Text>
        <Select
          options={Object.values(LABEL_POSITION_OPTIONS)}
          value={otpForm.labelPosition}
          onChange={value => {
            onChange({ otpForm: { ...otpForm, labelPosition: value } });
          }}
        />
      </WrapperSelectInline>
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">{t(translations.expiredMessage.title)}</Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={otpForm.expiredMessage}
          onAfterChange={value => onChange({ otpForm: { ...otpForm, expiredMessage: value } })}
        />
      </Space>
    </Space>
  );
};
