// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Atoms
import { Tag } from 'app/components/atoms/Tag';
import { StyledSelect } from './styled';

// Translations
import { translations } from 'locales/translations';

interface InputArrayProps {
  value: string[];
  onChange: (v: InputArrayProps['value']) => void;
  required?: boolean;
  focused?: boolean;
}

export const InputArray: React.FC<InputArrayProps> = props => {
  const { value, onChange, required, focused } = props;

  // Translations
  const { t } = useTranslation();

  const onRemove = (v: any) => {
    const index = value.findIndex(item => item === v);
    if (index > -1) {
      const newValue = [...value];
      newValue.splice(index, 1);
      onChange(newValue);
    }
  };

  // Them dieu kien nhap phim sau (string, number, datetime)
  const onInputKeyDown = (e: any) => {
    if (e.code === 'Enter' || e.key === 'Enter') {
      // remove duplicate values
      const newValue = [...(new Set([...value, e.target.value]) as unknown as string[])];
      return onChange(newValue);
    }
  };

  return (
    <StyledSelect
      mode="tags"
      required={required}
      focused={focused}
      placeholder={t(translations.pleaseInputValue.title)}
      onInputKeyDown={onInputKeyDown}
      onDeselect={onRemove}
      value={value}
      tagRender={v => <Tag {...v}>{v.label}</Tag>}
      dropdownStyle={{
        display: 'none',
      }}
    />
  );
};
