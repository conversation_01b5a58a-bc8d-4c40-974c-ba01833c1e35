// Translations
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { DoubleRightOutlined } from '@ant-design/icons';

// Atoms
import { Tooltip, Divider, Icon, Text } from 'app/components/atoms';

// Components

//Libraries
import styled from 'styled-components';
import { useState } from 'react';
// Molecules
import { DataSelect } from '..';

interface ColumnContent {
  data: DataSelect;
  extendValue?: any;
  onChange: (value) => void;
  onChangeRemoveExtend: (value) => void;
}
export const ColumnSuggestion: React.FC<ColumnContent> = props => {
  const { t } = useTranslation();
  const { data, onChange, extendValue, onChangeRemoveExtend } = props;
  const showContent = (childs, extend) => {
    const result: any[] = [];
    childs.forEach((item, key) => {
      result.push(
        <Tooltip title={item}>
          <ListItem>
            <TextField>{item}</TextField>
            <Icon onClick={e => onChange(item)} type="icon-ants-remove-slim" size={10} style={{ cursor: 'pointer' }} />
          </ListItem>
        </Tooltip>,
      );
    });
    extend?.forEach(element => {
      result.push(
        <Tooltip title={element}>
          <ListItem>
            <TextField>{element}</TextField>
            <Icon size={10} type="icon-ants-flag" />
            <Icon
              onClick={e => onChangeRemoveExtend(element)}
              type="icon-ants-remove-slim"
              size={10}
              style={{ cursor: 'pointer' }}
            />
          </ListItem>
        </Tooltip>,
      );
    });
    if (result.length > 0) {
      return <List>{result}</List>;
    }
    return null;
  };
  return <WrapperContent>{showContent(data, extendValue)}</WrapperContent>;
};
export const WrapperContent = styled.div`
  width: 100%;
  height: 100%;
`;
export const List = styled.ul`
  width: 100%;
  height: 90%;
  overflow: auto;
  margin-top: 10px;
  padding: 5px;
`;
export const ListItem = styled.li`
  border: 0.063rem solid rgb(230, 230, 230);
  padding: 0.5rem;
  font-size: 0.875rem;
  width: 100%;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  border-radius: 0.313rem;
  justify-content: space-between;
  margin-top: 5px;
`;
export const Span = styled.div`
  color: inherit;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;
export const TextField = styled(Text)`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 180px;
`;
