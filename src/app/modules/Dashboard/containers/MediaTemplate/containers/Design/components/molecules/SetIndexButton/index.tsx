// Libraries
import React, { memo, ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { cloneDeep, get } from 'lodash';
import { useTranslation } from 'react-i18next';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { Modal, ModalProps } from 'app/components/molecules/Modal';
import { Form, InputNumber } from 'app/components/molecules';

// Utils
import { handleError } from 'app/utils/handleError';
import { getAllChildrenBlockId } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/utils';

// Slice
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import {
  selectCurrentBlocks,
  selectCurrentTree,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Constants
import {
  MAX_DYNAMIC_INDEX,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Locales
import { translations } from 'locales/translations';
import { PrefixAutoIndex } from 'app/components/icons';
import { StyledBoxIndex, StyledIndexButton } from './styled';

const { SLIDE_SHOW, BUTTON, IMAGE, VIDEO, YES_NO, OPTIN_FIELDS, RATING, TEXT } = STANDARDS_BLOCKS;

interface SetIndexButtonProps {
  color?: string;
  hoverColor?: string;
  blockId: string;
  children?: ReactNode;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/molecules/SetIndexButton/index.tsx';

export const SetIndexButton: React.FC<SetIndexButtonProps> = memo(props => {
  // Hooks
  const dispatch = useDispatch();

  // I18n
  const { t } = useTranslation();

  // Actions
  const { updateCurrentPageTreeBlocks } = mediaTemplateDesignActions;

  // Props
  const { blockId, color = '#fff', hoverColor = '#fff', children } = props;

  // State
  const [indexModal, setIndexModal] = useState({
    visible: false,
  });

  // Selectors
  const blocks = useSelector(selectCurrentBlocks);
  const tree = useSelector(selectCurrentTree);

  // Variable
  const block = blocks[blockId] || {};

  const handleSetIndex = ({ fromIndex }) => {
    try {
      const childrenIds = tree[block.id] || [];
      let dataUpdateBlocks: any[] = [];

      childrenIds.forEach((childrenId, slideColIndex) => {
        let draftTree = cloneDeep(tree);
        const autoIndex = fromIndex + slideColIndex;

        // Update index for col/slide
        dataUpdateBlocks.push({
          fieldPath: `${childrenId}.settings.defaultIndex`,
          data: autoIndex,
        });

        // Exclude Slide show block
        draftTree[childrenId] = draftTree[childrenId].filter(blockId => blocks[blockId]?.type !== SLIDE_SHOW.name);

        const childrenBlockIds = getAllChildrenBlockId({ tree: draftTree, blockId: childrenId });

        childrenBlockIds.forEach(childrenBlockId => {
          if (childrenBlockId !== childrenId) {
            const childrenBlock = blocks[childrenBlockId] || {};
            const blockSettings = cloneDeep(childrenBlock.settings) || {};

            // Set auto index for display condition
            if (!!get(blockSettings, 'blockStylesSettings.displayCondition.condition', '')) {
              dataUpdateBlocks.push({
                fieldPath: `${childrenBlock.id}.settings.blockStylesSettings.displayCondition.index`,
                data: autoIndex,
              });
            }

            // Set auto index for Button, Yes/No, Image, Video, Rating, Text Block
            switch (childrenBlock.type) {
              case BUTTON.name:
              case RATING.name:
              case VIDEO.name:
              case OPTIN_FIELDS.name:
              case IMAGE.name:
                let { dynamic } = blockSettings;

                Object.keys({ ...dynamic }).forEach((key: string) => {
                  if (dynamic[key]?.isDynamic && dynamic[key]?.index != null) {
                    dynamic[key].index = autoIndex;
                  }
                });

                dataUpdateBlocks.push({
                  fieldPath: `${childrenBlock.id}.settings.dynamic`,
                  data: dynamic,
                });

                break;
              case YES_NO.name:
                let { yesDynamic, noDynamic } = blockSettings;

                Object.keys({ ...yesDynamic }).forEach((key: string) => {
                  if (yesDynamic[key]?.isDynamic && yesDynamic[key]?.index != null) {
                    yesDynamic[key].index = autoIndex;
                  }
                });

                Object.keys({ ...noDynamic }).forEach((key: string) => {
                  if (noDynamic[key]?.isDynamic && noDynamic[key]?.index != null) {
                    noDynamic[key].index = autoIndex;
                  }
                });

                dataUpdateBlocks = dataUpdateBlocks.concat([
                  {
                    fieldPath: `${childrenBlock.id}.settings.yesDynamic`,
                    data: yesDynamic,
                  },
                  {
                    fieldPath: `${childrenBlock.id}.settings.noDynamic`,
                    data: noDynamic,
                  },
                ]);

                break;
              case TEXT.name:
                const dynamicData = get(blockSettings, 'dynamic.data', {});
                const linkData = get(blockSettings, 'link.data', {});

                Object.keys({ ...dynamicData }).forEach((key: any) => {
                  const { index } = dynamicData[key] || {};
                  if (dynamicData[key] && index != null) {
                    dynamicData[key].index = autoIndex;
                  }
                });

                Object.keys({ ...linkData }).forEach((key: any) => {
                  const { index } = linkData[key] || {};
                  if (linkData[key] && index != null) {
                    linkData[key].index = autoIndex;
                  }
                });

                dataUpdateBlocks = dataUpdateBlocks.concat([
                  {
                    fieldPath: `${childrenBlock.id}.settings.dynamic.data`,
                    data: dynamicData,
                  },
                  {
                    fieldPath: `${childrenBlock.id}.settings.link.data`,
                    data: linkData,
                  },
                ]);

                break;
              default:
                break;
            }
          }
        });
      });

      // Update fromIndex of block
      dataUpdateBlocks.push({
        fieldPath: `${block.id}.settings.fromIndex`,
        data: fromIndex,
      });

      dispatch(
        updateCurrentPageTreeBlocks({
          blocks: dataUpdateBlocks,
        }),
      );

      setIndexModal(indexModal => ({
        ...indexModal,
        visible: false,
      }));

      const successInfo = {
        blockName: '',
        childBlockName: '',
      };

      switch (block.type) {
        case STANDARDS_BLOCKS.COLUMN.name:
          successInfo.blockName = 'Column block';
          successInfo.childBlockName = 'Columns';

          break;
        case STANDARDS_BLOCKS.SLIDE_SHOW.name:
          successInfo.blockName = 'Slideshow';
          successInfo.childBlockName = 'Slides';

          break;
        default:
          break;
      }

      Modal.info({
        icon: null,
        title: t(translations.setIndex.setIndexSuccess.title),
        content: t(translations.setIndex.setIndexSuccess.description, {
          blockName: successInfo.blockName,
          childBlockName: successInfo.childBlockName,
          fromIndex,
          toIndex: (childrenIds?.length || 1) + fromIndex - 1,
        }),
        okText: t(translations.close.title),
        okButtonProps: {
          type: 'default',
        },
      });
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleSetIndex',
        args: {},
      });
    }
  };

  return (
    <>
      {/* {cloneElement(children as any, {
        onClick: () => setIndexModal(indexModal => ({ ...indexModal, visible: true })),
      })} */}
      <StyledIndexButton
        color={color}
        hoverColor={hoverColor}
        onClick={() => setIndexModal(indexModal => ({ ...indexModal, visible: true }))}
      >
        <div>
          <PrefixAutoIndex />
        </div>
        <StyledBoxIndex>
          <Text size={10} style={{ fontWeight: 'bold' }}>
            {get(block, 'settings.fromIndex', 1)}
          </Text>
        </StyledBoxIndex>
        {children}
      </StyledIndexButton>

      <SetIndexModal
        visible={indexModal.visible}
        fromIndex={get(block, 'settings.fromIndex', 1)}
        total={tree[blockId]?.length || 1}
        onSummit={({ fromIndex }) => handleSetIndex({ fromIndex })}
        onCancel={() => setIndexModal(indexModal => ({ ...indexModal, visible: false }))}
      />
    </>
  );
});

SetIndexButton.defaultProps = {
  color: '#fff',
  hoverColor: '#fff',
};

interface SetIndexModalProps extends ModalProps {
  fromIndex: number;
  total: number;
  onSummit: (values: any) => void;
}

export const SetIndexModal: React.FC<SetIndexModalProps> = memo(props => {
  // Props
  const { total, onSummit, onCancel, ...restOf } = props;

  // Form
  const [form] = Form.useForm();

  // Watch
  const fromIndex = Form.useWatch('fromIndex', form);

  // I18n
  const { t } = useTranslation();

  // Variables
  const fromIndexMax = MAX_DYNAMIC_INDEX - total + 1;

  // Effects
  useEffect(() => {
    if (props.visible) {
      form.setFieldsValue({
        fromIndex: props.fromIndex,
      });
    }
  }, [form, props.fromIndex, props.visible]);

  // Handlers
  const onFinishForm = values => {
    try {
      onSummit && onSummit(values);

      form.resetFields();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onFinishForm',
        args: {},
      });
    }
  };

  return (
    <Modal
      {...restOf}
      width={300}
      destroyOnClose
      title={t(translations.setIndex.title)}
      onOk={form.submit}
      okButtonProps={{
        disabled: fromIndex > fromIndexMax,
      }}
      onCancel={e => {
        onCancel && onCancel(e);

        form.resetFields();
      }}
    >
      <Form
        form={form}
        name="basic"
        labelCol={{ span: 0 }}
        wrapperCol={{ span: 16 }}
        initialValues={{ fromIndex: 1 }}
        onFinish={onFinishForm}
        onValuesChange={(_changedValues, values: any) => {
          const { fromIndex } = values || {};

          form.setFieldsValue({
            fromIndex: Math.floor(fromIndex),
          });
        }}
        autoComplete="off"
      >
        <div className="ants-flex ants-items-center ants-gap-2">
          <Form.Item
            label={t(translations.setIndex.indexFrom)}
            name="fromIndex"
            style={{
              alignItems: 'center',
            }}
          >
            <InputNumber required min={1} max={fromIndexMax} className="ants-ml-2.5" />
          </Form.Item>
          <Text>
            {t(translations.to.title)} {Math.floor(fromIndex) + total - 1}
          </Text>
        </div>
        {fromIndex > fromIndexMax ? (
          <Text color="#ff4d4f" style={{ marginTop: 2, marginLeft: 70 }}>
            {t(translations.setIndex.maxIndex, { number: 90 })}
          </Text>
        ) : null}
      </Form>
    </Modal>
  );
});

SetIndexModal.defaultProps = {
  fromIndex: 1,
  total: 1,
};
