import { Input, Space, Text } from 'app/components/atoms';
import { Select, SliderWithUnit } from 'app/components/molecules';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';
import { AlignSetting } from '../../../../../../molecules';
import { BUTTON_SIZE, TO_FONT_SIZE, serialData } from '../../../../../ButtonGroupSetting/constants';

type TResendButton = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

export const ResendButton: React.FC<TResendButton> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const { resendButton = {}, blockStyles } = settings;
  return (
    <Space size={20} direction="vertical">
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">{t(translations.description.title)}</Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={resendButton.description}
          onAfterChange={value => onChange({ resendButton: { ...resendButton, description: value } })}
        />
      </Space>
      <Space size={5} direction="vertical">
        <Text className="!ants-text-gray-4">{t(translations.expiredMessage.title)}</Text>
        <Input
          placeholder={t(translations.buttonText.placeholder)}
          value={resendButton.buttonSettings?.buttonValue}
          onAfterChange={value =>
            onChange({
              resendButton: { ...resendButton, buttonSettings: { ...resendButton.buttonSettings, buttonValue: value } },
            })
          }
        />
      </Space>
      <SliderWithUnit
        label={t(translations.width.title)}
        labelClassName="!ants-text-gray-4"
        unit={resendButton.suffix || '%'}
        min={0}
        max={(resendButton.suffix || '%') === 'px' ? 1000 : 100}
        value={parseFloat(resendButton.width)}
        onAfterChange={value =>
          onChange({
            resendButton: {
              ...resendButton,
              width: value,
              buttonStyles: { ...resendButton.buttonStyles, width: value + resendButton.suffix },
            },
          })
        }
        onChangeUnit={value => {
          const width = parseFloat(resendButton.width) || 100;
          let newWidth: string | number = width;

          if (value === '%' && width > 100) {
            newWidth = 100;
          }

          if (value === 'auto') {
            newWidth = 'auto';
          }

          onChange({
            resendButton: {
              ...resendButton,
              suffix: value,
              width: newWidth,
              buttonStyles: { ...resendButton.buttonStyles, width: resendButton.width + value },
            },
          });
        }}
      />
      <AlignSetting
        label={t(translations.align.title)}
        align={resendButton?.align}
        onChange={value => onChange({ resendButton: { ...resendButton, align: value } })}
      />
      <Select
        label={t(translations.buttonSize.title)}
        options={Object.values(BUTTON_SIZE)}
        value={
          Object.values(BUTTON_SIZE).find(opt => opt.value === resendButton.buttonSize)
            ? resendButton.buttonSize
            : BUTTON_SIZE.XSMALL.value
        }
        onChange={value =>
          onChange({
            resendButton: {
              ...resendButton,
              buttonSize: value,
              buttonStyles: { ...resendButton.buttonStyles, ...TO_FONT_SIZE[serialData(value)] },
            },
          })
        }
      />
    </Space>
  );
};
