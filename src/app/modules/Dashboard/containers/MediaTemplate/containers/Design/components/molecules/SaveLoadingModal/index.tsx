// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Atoms
import { Icon, Spin, Text } from 'app/components/atoms';

// Molecules
import { ModalProps, Modal } from 'app/components/molecules/Modal';

// Hooks
import { useCountUp } from 'app/hooks/useCountUp';

// Locales
import { translations } from 'locales/translations';

const OVER_LOAD_COUNT = 7;

export const SaveLoadingModal: React.FC<ModalProps> = ({ ...restProps }) => {
  // Props
  const { visible } = restProps;

  // Hooks
  const countUp = useCountUp({
    duration: 1000,
    isStartCount: visible || false,
    end: OVER_LOAD_COUNT,
  });

  // I18n
  const { t } = useTranslation();

  return (
    <Modal
      {...restProps}
      destroyOnClose
      closable={false}
      footer={null}
      transitionName=""
      centered
      width={'max-content'}
      bodyStyle={{
        textAlign: 'center',
        padding: '20px 40px',
      }}
    >
      <Spin spinning />

      {countUp === OVER_LOAD_COUNT && (
        <div className="animate__animated animate__fadeIn ants-flex ants-items-center ants-space-x-2 ants-mt-5">
          <Icon type="icon-ants-info" className="ants-text-cus-warning" size={14} />
          <Text style={{ lineHeight: 1 }}>{t(translations.saveLongWarning.title)}</Text>
        </div>
      )}
    </Modal>
  );
};
