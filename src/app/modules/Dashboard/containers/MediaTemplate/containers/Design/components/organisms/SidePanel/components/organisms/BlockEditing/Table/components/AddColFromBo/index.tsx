// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Atoms
import { Button, Icon, Space, Text } from 'app/components/atoms';

// Molecules
import { Select } from 'app/components/molecules';

// Types
import { TColumnTableBlock } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Locales
import { translations } from 'locales/translations';

type TAddColFromBOProps = {
  isLoadingColumnOptions: boolean;
  columnsSelected: TColumnTableBlock[];
  columnOptions: any[];

  showDeleteColumnIcon: boolean;
  showAddColumnBtn?: boolean;

  addColBtnText: string;
  titleSelectInput: string;

  onDeleteColumn: (colId: string) => void;
  onChangeColumn: (colId: string, updatedData: TColumnTableBlock) => void;
  onAddColumn: () => void;
};

const AddColFromBO = ({ showAddColumnBtn = true, ...props }: TAddColFromBOProps) => {
  const { t } = useTranslation();

  return (
    <React.Fragment>
      {!!props.columnsSelected.length && (
        <Space direction="vertical" size="middle" className="ants-mb-4">
          {props.columnsSelected.map((column, index) => (
            <Select
              key={column.id}
              showSearch
              labelInValue
              disabled={props.isLoadingColumnOptions}
              label={
                <Space className="ants-justify-between ants-w-100">
                  <span>
                    <Text>
                      {props.titleSelectInput} #{index + 1}
                    </Text>
                  </span>
                  {props.showDeleteColumnIcon && (
                    <Icon
                      className="ants-cursor-pointer ants-text-primary"
                      style={{ color: '005fb8' }}
                      size={12}
                      type="icon-ants-remove-light"
                      onClick={() => props.onDeleteColumn(column.id)}
                    />
                  )}
                </Space>
              }
              options={props.columnOptions?.map(d => ({
                value: d.itemPropertyName,
                label: d.itemPropertyDisplay,
              }))}
              placeholder={t(translations.selectAnItem.title)}
              value={{
                value: column.value,
                label: column.label,
              }}
              onChange={value => {
                props.onChangeColumn(column.id, {
                  ...column,
                  id: column.id,
                  label: value.label,
                  value: value.value,
                  dataType:
                    props.columnOptions.filter(({ itemPropertyName }) => itemPropertyName === value.value)[0]
                      .dataType || '',
                });
              }}
            />
          ))}
        </Space>
      )}

      {showAddColumnBtn && (
        <Button type="text" onClick={props.onAddColumn} loading={props.isLoadingColumnOptions}>
          {props.addColBtnText}
        </Button>
      )}
    </React.Fragment>
  );
};

export default AddColFromBO;
