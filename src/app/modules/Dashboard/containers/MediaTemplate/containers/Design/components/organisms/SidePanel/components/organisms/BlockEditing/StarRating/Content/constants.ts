// Translations
import { translations } from 'locales/translations';

// Utils
import { getTranslateMessage } from 'utils/messages';

export const RATING_TYPE = {
  STAR: {
    value: 'star',
    label: getTranslateMessage(translations.rating.ratingType.options.star),
  },
  HEART: {
    value: 'heart',
    label: getTranslateMessage(translations.rating.ratingType.options.heart),
  },
};

export const RATING_NUMBER_ITEMS_MIN = 1;
export const RATING_NUMBER_ITEMS_MAX = 5;
export const RATING_VALUE_NUMBER_ITEMS_MIN = 0;
export const RATING_NUMBER_SIZE_MIN = 1;
export const RATING_NUMBER_SIZE_MAX = 100;
