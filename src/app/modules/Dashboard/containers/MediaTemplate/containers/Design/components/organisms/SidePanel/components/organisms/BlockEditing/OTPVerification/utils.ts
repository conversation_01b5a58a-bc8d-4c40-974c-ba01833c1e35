import { VALUE_STYLE_INPUT_FIELD } from './constants';

export const getSourceDefault = data => {
  let dataOut = {} as any;
  if (data) {
    data.forEach(source => {
      if (source.insightPropertyName.toLowerCase() === 'lead') {
        dataOut = { ...source, value: source.insightPropertyId, label: source.insightPropertyName };
      }
    });
  }
  return dataOut;
};
export const getEventDefault = data => {
  let dataOut = {} as any;
  if (data) {
    data.forEach(event => {
      if (event.eventTrackingName === 'verify_otp_successfully') {
        dataOut = { ...event, value: event.eventTrackingName, label: event.translateLabel };
      }
    });
    if (!Object.keys(dataOut).length) {
      const initEvent = data[0];
      dataOut = { ...initEvent, value: initEvent.eventTrackingName, label: initEvent.translateLabel };
    }
  }
  return dataOut;
};

export const optionSourceSerial = dataIn => {
  let dataOut = {} as any;
  if (dataIn.length > 0) {
    dataIn.forEach(data => {
      dataOut = {
        ...dataOut,
        [data.insightPropertyId]: {
          ...data,
          value: data.insightPropertyId,
          label: data.insightPropertyName,
        },
      };
    });
  }

  return dataOut;
};
export const optionEventSerial = dataIn => {
  let dataOut = {} as any;
  if (dataIn.length > 0) {
    dataIn.forEach(data => {
      dataOut = {
        ...dataOut,
        [data.eventTrackingName]: {
          ...data,
          value: data.eventTrackingName,
          label: data.translateLabel,
        },
      };
    });
  }

  return dataOut;
};

export const mapValueToStyleFieldInput = value => {
  let style = {};
  switch (value) {
    case VALUE_STYLE_INPUT_FIELD.WHOLE_BOX:
    case VALUE_STYLE_INPUT_FIELD.SEPARATE_BOX:
      style = {
        width: '80px',
        height: '80px',
      };
      break;

    case VALUE_STYLE_INPUT_FIELD.HYPHENS:
      style = {
        width: '80px',
        height: '10px',
      };
      break;

    default:
      break;
  }
  return style;
};
