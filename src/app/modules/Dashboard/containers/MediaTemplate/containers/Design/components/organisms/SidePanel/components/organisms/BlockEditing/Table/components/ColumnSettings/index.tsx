import {
  TColumnFormat,
  TColumnTableBlock,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { Select } from 'app/components/molecules';
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';
import { useTranslation } from 'react-i18next';
import { CHANGE_COL_SETTINGS } from '../../constants';
import produce from 'immer';
import { AlignSetting } from '../../../../../molecules';

type ColumnSettingsProps = {
  column: TColumnTableBlock;
  onChangeSettings: (updatedCol: TColumnTableBlock) => void;
  showFormatType?: boolean;
};

export type TColOptions = {
  label: string;
  value: TColumnFormat;
};

const ColumnSettings = (props: ColumnSettingsProps) => {
  const { t } = useTranslation();
  const { column, showFormatType = true } = props;

  const onChangeSettings = (type: string, value: any) => {
    let updatedCol = column;

    switch (type) {
      case CHANGE_COL_SETTINGS.CHANGE_STYLE_DISPLAY: {
        updatedCol = produce(column, draf => {
          draf.displayFormat = {
            ...draf.displayFormat,
            type: value,
          };
        });
        break;
      }
      case CHANGE_COL_SETTINGS.CHANGE_ALIGN: {
        updatedCol = produce(column, draf => {
          draf.placement = value;
        });
        break;
      }
      default:
        break;
    }

    props.onChangeSettings(updatedCol);
  };

  const columnTypeOptions: TColOptions[] = [
    {
      label: getTranslateMessage(translations.number.title, 'Number'),
      value: 'number',
    },
    // {
    //   label: getTranslateMessage(translations.heatMap.title, 'Heatmap'),
    //   value: 'heatmap',
    // },
    // {
    //   label: getTranslateMessage(translations.bar.title, 'Bar'),
    //   value: 'bar',
    // },
  ];

  return (
    <>
      {showFormatType && (
        <Select
          key={column.id}
          options={columnTypeOptions}
          label={t(translations.columnStyle.title)}
          value={column.displayFormat?.type}
          onChange={value => onChangeSettings(CHANGE_COL_SETTINGS.CHANGE_STYLE_DISPLAY, value)}
        />
      )}
      <AlignSetting
        align={column.placement}
        onChange={align => onChangeSettings(CHANGE_COL_SETTINGS.CHANGE_ALIGN, align)}
      />
    </>
  );
};

export default ColumnSettings;
