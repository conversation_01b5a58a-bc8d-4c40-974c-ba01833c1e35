// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';
import cloneDeep from 'lodash/cloneDeep';

// Atoms
import { SliderWithUnit } from 'app/components/molecules';
import { Button, Icon, Popover } from 'app/components/atoms';

// Molecules
import { SettingWrapper } from '../../molecules';

// Locales
import { translations } from 'locales/translations';

// Types
import { TRangeSettings, TRangeStyles } from './types';

// Utils
import { handleError } from 'app/utils/handleError';

interface RangeSettingProps {
  settings: TRangeSettings;
  styles: TRangeStyles;
  onChange: (settings: TRangeSettings, style: TRangeStyles) => void;
}

interface RangeSettingsPopoverProps extends RangeSettingProps {}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/RangeSetting/index.tsx';

export const RangeSetting: React.FC<RangeSettingProps> = props => {
  // Props
  const { styles, settings, onChange } = props;

  // Translations
  const { t } = useTranslation();

  const onChangeHeight = (key: string, value: any) => {
    try {
      let { heightSuffix = 'auto' } = cloneDeep(settings);
      let { height = 'auto' } = cloneDeep(styles);

      switch (key) {
        case 'height':
          height = value + heightSuffix;
          break;
        case 'heightSuffix':
          height = parseInt(height.toString()) || 100;

          heightSuffix = value;

          height = height + value;

          if (value === 'auto') {
            height = 'auto';
          }
          break;

        default:
          break;
      }

      // Callback onChange
      onChange(
        {
          ...settings,
          heightSuffix,
        },
        {
          ...styles,
          height,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeHeight',
        args: {},
      });
    }
  };

  return (
    <SliderWithUnit
      label={t(translations.height.title)}
      value={parseInt((styles.height || 'auto').toString())}
      unit={settings?.heightSuffix || 'auto'}
      min={0}
      max={1000}
      hideUnits={['%']}
      onAfterChange={value => onChangeHeight('height', value)}
      onChangeUnit={value => onChangeHeight('heightSuffix', value)}
    />
  );
};

export const RangeSettingPopover: React.FC<RangeSettingsPopoverProps> = props => {
  // Props
  const { settings, styles, onChange } = props;

  // Translations
  const { t } = useTranslation();

  const content = (
    <div className="ants-w-72">
      <RangeSetting settings={settings} styles={styles} onChange={onChange} />
    </div>
  );

  return (
    <SettingWrapper label={t(translations.height.title)} labelClassName="ants-font-bold">
      <Popover placement="bottomRight" content={content} trigger={['click']}>
        <Button icon={<Icon type="icon-ants-edit-2" />} type="text" />
      </Popover>
    </SettingWrapper>
  );
};
