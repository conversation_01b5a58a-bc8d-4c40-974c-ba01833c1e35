// Libraries
import styled from 'styled-components';

// Molecules
import { Table } from 'app/components/molecules';

export const StyledTable = styled(Table)`
  .ant-table {
    font-size: 12px;
  }

  .ant-table-thead > tr > th {
    font-weight: 700;
    background-color: #f9f9f9;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td,
  .ant-table tfoot > tr > th,
  .ant-table tfoot > tr > td {
    padding: 10px 10px;
  }

  .ant-table-footer {
    padding: 12px 10px;
    background-color: #fff;
  }

  .ant-table-tbody {
    vertical-align: top;

    > tr {
      td.no .no-label,
      td.can-win .switch-btn,
      td.pool .switch-btn,
      td.delete .icon-delete-section {
        position: relative;
        top: 15px;
        transform: translateY(-50%);
      }
    }

    > tr.use-pool.coupon-code-has-value > td > .cell-content-wrapper {
      margin-top: 17.28px;
    }
  }

  .ant-table.ant-table-bordered > .ant-table-footer {
    border: 1px solid #f0f0f0;
    border-bottom: 0;
  }
`;

export const StyledCellWrapper = styled.div.attrs({
  className: 'cell-content-wrapper',
})`
  position: relative;
`;
