import React from 'react';
import { Checkbox, Radio } from 'app/components/atoms';
import { Col, Row, Tooltip, Button } from 'antd';
import { Form, Select } from 'app/components/molecules';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Currencies from '../../currencies.json';

import classnames from 'classnames';
import { ATTRIBUTE_NUMBERIC_FORMAT_OPTION } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import { getCurrencyList } from 'app/services/MediaTemplateDesign/ListCurrency';
import classNames from 'classnames';
import { translations } from 'locales/translations';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TDisplayFormat } from '../DisplayFormat';

const groupingOptions = ATTRIBUTE_NUMBERIC_FORMAT_OPTION['GROUPING_SEPARATOR'];
const decimalOptions = ATTRIBUTE_NUMBERIC_FORMAT_OPTION['DECIMAL_SEPARATOR'];

export type TGroupingSeparator = typeof groupingOptions[keyof typeof groupingOptions]['value'];
export type TDecimalSeparator = typeof decimalOptions[keyof typeof decimalOptions]['value'];

export type TNumberFormatSettings = {
  decimalPlaces: number;
  grouping: TGroupingSeparator;
  decimal: TDecimalSeparator;
  isCompact: boolean;
  currencyCode: string;
  prefixType: 'symbol' | 'code';
  type?: TDisplayFormat;
};

type FormatNumberProps = {
  onSettingsChange?: (settingValues: TNumberFormatSettings) => void;
  type: TDisplayFormat;
} & TNumberFormatSettings;

type LabeledValue = {
  label: string;
  value: string;
  disabled?: boolean;
};

const FormatNumber = (props: FormatNumberProps) => {
  const {
    t,
    i18n: { language },
  } = useTranslation();

  const { onSettingsChange, type: displayType } = props;

  const { currencyCode, decimalPlaces, grouping, decimal, prefixType, isCompact } = props;

  const settingValues: TNumberFormatSettings = {
    decimalPlaces,
    grouping,
    decimal,
    isCompact,
    currencyCode,
    prefixType,
  };

  const [listCurrencyOptions, setListCurrencyOptions] = useState<LabeledValue[]>([]);
  const [isLoadingCurrencyOptions, setIsLoadingCurrencyOptions] = useState<boolean>(true);

  const onChangeSettingsHandle = (settingValues: TNumberFormatSettings) => {
    if (onSettingsChange) {
      onSettingsChange({
        ...settingValues,
        type: displayType,
      });
    }
  };

  useEffect(() => {
    (async () => {
      // Regex for getting text between the last brackets (with brakets) of line
      const regex = /\(([^)]*)\)[^(]*$/;

      if (listCurrencyOptions.length) {
        setListCurrencyOptions(prevOptions =>
          prevOptions.map(currencyOption => {
            //setLabel: Dollal Mỹ (USD) or Dollal Mỹ ($)
            currencyOption['label'] =
              prefixType === 'symbol'
                ? currencyOption.label.replace(regex, `(${Currencies[currencyOption.value]?.symbol})`)
                : currencyOption.label.replace(regex, `(${currencyOption.value})`);

            return currencyOption;
          }),
        );
      } else {
        setIsLoadingCurrencyOptions(true);
        try {
          const data = await getCurrencyList(language);

          const currencies = data.reduce((accumulate, currency) => {
            if (Currencies[currency.code]) {
              accumulate.push({
                value: currency.code,
                //setLabel: Dollal Mỹ (USD) or Dollal Mỹ ($)
                label:
                  prefixType === 'symbol'
                    ? currency.name.replace(regex, '') + ` (${Currencies[currency.code]?.symbol})`
                    : currency.name.replace(regex, '') + ` (${currency.code})`,
              });
            } else {
              // console.log('library not found ', currency.code + ' - ' + currency.name);
            }

            return accumulate;
          }, [] as LabeledValue[]);

          setListCurrencyOptions(currencies);
        } catch (err) {
          setListCurrencyOptions([]);
        } finally {
          setIsLoadingCurrencyOptions(false);
        }
      }
    })();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prefixType]);

  const listGroupingSeparatorOptions = useMemo((): LabeledValue[] => {
    return Object.values(groupingOptions)
      .sort((a, b) => a.index - b.index)
      .map(grouping => ({
        ...grouping,
        ...(grouping.value === decimal && { disabled: true }),
      }));
  }, [decimal]);

  const listDecimalSeparatorOptions = useMemo((): LabeledValue[] => {
    return Object.values(decimalOptions)
      .sort((a, b) => a.index - b.index)
      .map(decimal => ({
        ...decimal,
        ...(decimal.value === grouping && { disabled: true }),
      }));
  }, [grouping]);

  return (
    <Form className={classnames('ants-w-full', 'ants-text-[12px]')} labelAlign="left">
      {displayType === 'currency' && (
        <React.Fragment>
          <Form.Item
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            label={t(translations.dynamicContent.modal.label.formNumberDisplayFormat.currency)}
            colon={false}
          >
            <Radio.Group
              value={prefixType}
              onChange={({ target: { value } }) =>
                onChangeSettingsHandle({
                  ...settingValues,
                  prefixType: value as 'code' | 'symbol',
                })
              }
            >
              <Radio style={{ fontSize: 12 }} value="code">
                {t(translations.dynamicContent.modal.label.formNumberDisplayFormat.currencyCode)}
              </Radio>
              <Radio style={{ fontSize: 12 }} value="symbol">
                {t(translations.dynamicContent.modal.label.formNumberDisplayFormat.currencySymbol)}
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label=" " labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} colon={false}>
            <Select
              className={classnames('ants-h-7')}
              disabled={isLoadingCurrencyOptions}
              options={listCurrencyOptions}
              value={currencyCode}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  currencyCode: value,
                })
              }
              showSearch
            />
          </Form.Item>
        </React.Fragment>
      )}
      <Form.Item>
        <Checkbox
          style={{ fontSize: 12 }}
          checked={isCompact}
          onChange={({ target: { checked } }) => {
            onChangeSettingsHandle({
              ...settingValues,
              isCompact: checked,
            });
          }}
        >
          {t(translations.dynamicContent.modal.label.formNumberDisplayFormat.compact)}
        </Checkbox>
      </Form.Item>
      <Form.Item
        label={`${t(translations.dynamicContent.modal.label.formNumberDisplayFormat.decimalPlace)} (${decimalPlaces})`}
        className="ants-mb-3"
        colon={false}
      >
        <div className="ants-flex ants-ml-12 ants-space-x-10">
          <Button
            className="ants-w-16 ants-bg-blue-200 hover:ants-bg-blue-300 focus:ants-bg-blue-200"
            disabled={decimalPlaces <= 0}
            onClick={() =>
              onChangeSettingsHandle({
                ...settingValues,
                decimalPlaces: decimalPlaces - 1,
              })
            }
          >
            {'<< .0'}
          </Button>
          <Button
            className="ants-w-16 ants-bg-blue-200 hover:ants-bg-blue-300 focus:ants-bg-blue-200"
            onClick={() =>
              onChangeSettingsHandle({
                ...settingValues,
                decimalPlaces: decimalPlaces + 1,
              })
            }
          >
            {'.00 >>'}
          </Button>
        </div>
      </Form.Item>
      <Row gutter={15}>
        <Col span={12}>
          <Form.Item
            label={
              <span>
                {t(translations.dynamicContent.modal.label.formNumberDisplayFormat.grouping)}
                <Tooltip
                  placement="top"
                  title={t(translations.dynamicContent.modal.label.formNumberDisplayFormat.groupingIconTooltipTitle)}
                  className="ants-ml-1 ants-bg-opacity-40"
                >
                  <FontAwesomeIcon icon={['fas', 'info-circle']} />
                </Tooltip>
              </span>
            }
            colon={false}
          >
            <Select
              className={classNames('ants-h-7')}
              value={grouping}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  grouping: value,
                })
              }
              options={listGroupingSeparatorOptions}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={
              <span>
                {t(translations.dynamicContent.modal.label.formNumberDisplayFormat.decimal)}
                <Tooltip
                  placement="top"
                  title={t(translations.dynamicContent.modal.label.formNumberDisplayFormat.decimalIconTooltipTitle)}
                  className="ants-ml-1 ants-bg-opacity-40"
                >
                  <FontAwesomeIcon icon={['fas', 'info-circle']} />
                </Tooltip>
              </span>
            }
            colon={false}
          >
            <Select
              className={classNames('ants-h-7')}
              value={decimal}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  decimal: value,
                })
              }
              options={listDecimalSeparatorOptions}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default FormatNumber;
