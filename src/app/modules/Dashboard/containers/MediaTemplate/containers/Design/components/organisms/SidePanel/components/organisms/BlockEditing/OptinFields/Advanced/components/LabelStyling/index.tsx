// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';
import get from 'lodash/get';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Space } from 'app/components/atoms';

// Types
import { TRowSettings, TSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';

// Utils
import { handleError } from 'app/utils/handleError';

// Molecules
import { SliderWithInputNumber } from 'app/components/molecules';

// Constants
import { INPUT_SIZE } from './constants';

// Organisms
import { AlignSetting } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/components/organisms/SidePanel/components/molecules';
import { EdgeSetting } from '../../../../../../molecules';
import { FontSettingPopover } from '../../../../../FontSetting';

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/LabelStying/index.tsx';

type TStylesSettings = {
  borderRadiusStyle: string;
  borderRadiusSuffix: string;
  linkedPaddingInput: boolean;
  paddingSuffix: string;
  columnGap?: number;
  gapX?: string;
  gapY?: string;
  gapSuffix?: string;
  linkedGapInput?: boolean;
  textAlign?: 'left' | 'center' | 'right' | undefined;
  indentation?: number;
};

interface LabelStylingProps {
  blockSettings: TSettings<TRowSettings>;
  labelStylesSettings: TStylesSettings;
  labelStyles: React.CSSProperties;
  onChange: (styleSettings: TStylesSettings, styles: React.CSSProperties, displayInline?: boolean) => void;
}

export const LabelStyling: React.FC<LabelStylingProps> = props => {
  const { labelStyles, labelStylesSettings, blockSettings, onChange } = props;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { t, i18n } = useTranslation();

  // Handlers
  const onUpdateSettings = (newLabelStylesSettings = {}, newLabelStyles = {}) => {
    try {
      // Callback onChange
      onChange(
        {
          ...labelStylesSettings,
          ...newLabelStylesSettings,
        },
        {
          ...labelStyles,
          ...newLabelStyles,
        },
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: onUpdateSettings.name,
        args: {},
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <AlignSetting
        label={t(translations.align.title)}
        align={labelStylesSettings?.textAlign}
        onChange={value => {
          onUpdateSettings({ textAlign: value }, { textAlign: value });
        }}
      />
      <FontSettingPopover
        styles={blockSettings.labelStyles}
        settingsStyle={blockSettings.labelStylesSettings}
        onChange={(settings, styles) => {
          onUpdateSettings(
            {
              ...settings,
              ...(styles.fontSize !== get(blockSettings, 'styles.fontSize', '16px') && {
                inputSize: INPUT_SIZE.custom.value,
              }),
            },
            styles,
          );
        }}
      />
      <EdgeSetting
        label={t(translations.gap.title)}
        unit={labelStylesSettings.gapSuffix}
        linked={labelStylesSettings.linkedGapInput}
        values={[parseInt(labelStylesSettings.gapX || ''), parseInt(labelStylesSettings.gapY || ''), 0, 0]}
        edgeLabels={[t(translations.column.title), t(translations.row.title)]}
        onChange={({ values, linked, unit }) => {
          onUpdateSettings(
            {
              gapX: values[0] + unit,
              gapY: values[1] + unit,
              gapSuffix: unit,
              linkedGapInput: linked,
            },
            {
              gapX: values[0] + unit,
              gapY: values[1] + unit,
              gapSuffix: unit,
              linkedGapInput: linked,
            },
          );
        }}
      />
      <SliderWithInputNumber
        label={t(translations.indentation.title)}
        labelClassName="!ants-text-gray-4"
        min={0}
        max={100}
        value={Number(labelStylesSettings.indentation)}
        onAfterChange={value => {
          onUpdateSettings(
            {
              indentation: value,
            },
            {
              paddingLeft: `${value}px`,
            },
          );
        }}
      />
    </Space>
  );
};
