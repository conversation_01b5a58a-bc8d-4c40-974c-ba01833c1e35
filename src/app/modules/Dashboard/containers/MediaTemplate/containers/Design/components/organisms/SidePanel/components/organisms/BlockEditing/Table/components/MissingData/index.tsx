import {
  TShowCellMissingData,
  TTableSettings,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { Select } from 'app/components/molecules';
import { getTranslateMessage } from 'utils/messages';
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';
import { TABLE_MISSING_DATA_DISPLAY } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

type MissingDataProps = {
  tableBodySettings: TTableSettings['tableBody']['settings'];
  onChange: (MissingDataType: TShowCellMissingData) => void;
};

type TMissinngSelectOptions = {
  label: string;
  value: TShowCellMissingData;
};

const selectMissingTypeOptions: TMissinngSelectOptions[] = [
  {
    label: getTranslateMessage(translations.showNull.title, 'Show "null"'),
    value: TABLE_MISSING_DATA_DISPLAY.NULL as TShowCellMissingData,
  },
  {
    label: getTranslateMessage(translations.showZero.title, 'Show "0"'),
    value: TABLE_MISSING_DATA_DISPLAY.ZERO as TShowCellMissingData,
  },
  {
    label: getTranslateMessage(translations.showNoData.title, 'Show "No data"'),
    value: TABLE_MISSING_DATA_DISPLAY.NO_DATA as TShowCellMissingData,
  },
  {
    label: getTranslateMessage(translations.showBlank.title, 'Show "" (blank)'),
    value: TABLE_MISSING_DATA_DISPLAY.BLANK as TShowCellMissingData,
  },
];

const SelectMissingDataShowType = (props: MissingDataProps) => {
  const { t } = useTranslation();
  const { tableBodySettings } = props;

  return (
    <Select
      label={t(translations.missingData.title)}
      options={selectMissingTypeOptions}
      value={tableBodySettings.showMissingDataType}
      onChange={props.onChange}
    />
  );
};

export default SelectMissingDataShowType;
