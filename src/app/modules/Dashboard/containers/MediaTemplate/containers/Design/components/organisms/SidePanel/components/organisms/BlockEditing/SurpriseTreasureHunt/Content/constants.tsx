// Translations
import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const MIN_CELL_GAP = 0;

export const MIN_OF_METRICS = {
  ROWS: 1,
  COLUMNS: 1,
};

export const RANGE_DIMENSIONS = {
  BOXES: {
    MIN: 0,
    MAX: 100,
  },
  CELLS: {
    MIN: 0,
    MAX: 100,
  },
};

export const ANIMATED_VALUE = {
  BLOCK: 'replace_block',
  CELL: 'replace_cell',
  SHAKE: 'shake',
};

export const ANIMATION_CLICKED = {
  REPLACE_BLOCK: {
    label: getTranslateMessage(translations.replaceBlock.title),
    value: ANIMATED_VALUE.BLOCK,
  },
  REPLACE_CELL: {
    label: getTranslateMessage(translations.replaceCell.title),
    value: ANIMATED_VALUE.CELL,
  },
  SHAKE: {
    label: getTranslateMessage(translations.shake.title),
    value: ANIMATED_VALUE.SHAKE,
  },
};

export const ANIMATION_HOVER = {
  HIGHLIGHT: {
    label: getTranslateMessage(translations.highlight.title),
    value: 'highlight',
  },
  SHAKE: {
    label: getTranslateMessage(translations.shake.title),
    value: ANIMATED_VALUE.SHAKE,
  },
};

export const ACTION_TYPES_CHANGE = {
  METRICS: {
    ROWS: 'metrics_rows',
    COLUMNS: 'metrics_columns',
  },
  CONTAINER_WIDTH: 'container_width',
  CELL_GAP: 'cell_gap',
  ALIGN: {
    GIFT_BOXES: 'gift_boxes',
    CELLS: 'cells',
  },
  ANIMATION_CLICKED: {
    CHANGE_TYPE: 'animation_clicked',
    CHANGE_IMAGE: 'animation_image',
  },
  TIME_DELAY: 'time_to_delay',
  ERROR_MESSAGE: 'errorMessage',
  IS_APPLY_IMAGE_ALL: 'is_apply_image_all',
  UPDATE_IMAGE_ALL: 'update_image_all',
  CHANGE_IMAGE_DYNAMIC: 'change_image_dynamic',
  APPLY_CELL_IMAGES: 'apply_cell-images',
};
