// Atoms
import { Text, Tooltip } from 'app/components/atoms';
import React, { useEffect, useMemo, useState } from 'react';
export const Tag: React.FC<any> = props => {
  const { label, attribute, searchValue } = props;

  const onPreventMouseDown = event => {
    event.preventDefault();
    event.stopPropagation();
  };
  //style={{ textOverflow: 'ellipsis', overflow: 'hidden', width: '70px', whiteSpace: 'nowrap' }}
  return (
    <Tooltip title={label}>
      <Text style={{ textOverflow: 'ellipsis', overflow: 'hidden', width: '70px', whiteSpace: 'nowrap' }}>
        {/* {searchValue ? '' : attribute ? label : ''} */}
        {label}
      </Text>
    </Tooltip>
  );
};
