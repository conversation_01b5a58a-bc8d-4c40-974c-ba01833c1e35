import { Space } from 'app/components/atoms';
import { Select } from 'app/components/molecules';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectEvent,
  selectSource,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { getListEvents, getListSources } from 'app/services/MediaTemplateDesign/BusinessObject';
import _ from 'lodash';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { getEventDefault, getSourceDefault, optionEventSerial, optionSourceSerial } from '../../../utils';

type TEventTrigger = {
  onChange: (data: Record<string, any>) => void;
  settings: Record<string, any>;
};

export const EventTrigger: React.FC<TEventTrigger> = props => {
  const { onChange, settings } = props;
  const { t } = useTranslation();

  const dispatch = useDispatch();

  // selector
  const sourceBO = useSelector(selectSource);
  const eventBO = useSelector(selectEvent);

  // state
  const [isLoadingEvent, setIsLoadingEvent] = useState(false);

  // action
  const { setData } = mediaTemplateDesignActions;

  const { eventTrigger = {} } = settings;
  const { event = {}, source = {} } = eventTrigger;

  useEffect(() => {
    if (!sourceBO?.length) {
      handleGetListSourceEvent();
    }
  }, []);

  useEffect(() => {
    handleEvent(source);
  }, [source]);

  const handleGetListSourceEvent = async () => {
    const sources = await getListSources();
    if (sources) {
      let dataParams = source;
      if (_.isEmpty(dataParams)) {
        dataParams = getSourceDefault(sources);
      }
      handleEvent(dataParams);
      dispatch(
        setData({
          sources,
        }),
      );
    }
  };

  const handleEvent = async source => {
    setIsLoadingEvent(true);
    const events = await getListEvents({ source_id: source.insightPropertyId });
    dispatch(
      setData({
        events,
      }),
    );
    setIsLoadingEvent(false);
    if (_.isEmpty(event)) {
      const newEvent = getEventDefault(events);
      onChange({
        eventTrigger: {
          ...eventTrigger,
          event: newEvent,
        },
      });
    }
  };

  return (
    <Space size={20} direction="vertical">
      <Select
        label={t('Source')}
        showSearch
        options={Object.values(optionSourceSerial(sourceBO))}
        placeholder="Select an source"
        value={!isLoadingEvent ? source?.insightPropertyId : source?.insightPropertyName}
        filterOption={(val, option: any) => {
          return option.props?.label.toLowerCase().indexOf(val.toLowerCase()) >= 0;
        }}
        loading={isLoadingEvent}
        onChange={(value, option: any) =>
          onChange({
            eventTrigger: {
              ...eventTrigger,
              source: { ...option, insightPropertyId: value, insightPropertyName: option.label },
              event: {},
            },
          })
        }
      />
      <Select
        showSearch
        label={t('Event')}
        options={Object.values(optionEventSerial(eventBO))}
        placeholder="Select an event"
        disabled={isLoadingEvent}
        value={!isLoadingEvent ? event?.eventTrackingName : event?.translateLabel}
        onChange={(value, option: any) =>
          onChange({
            eventTrigger: {
              ...eventTrigger,
              event: { ...option },
            },
          })
        }
      />
    </Space>
  );
};
