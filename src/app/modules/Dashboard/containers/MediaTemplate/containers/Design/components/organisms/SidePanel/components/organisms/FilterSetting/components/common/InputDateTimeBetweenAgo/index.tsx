// Translations
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Atoms
import { Text } from 'app/components/atoms';

// Molecules
import { InputNumber, Select } from 'app/components/molecules';

interface InputDateTimeBetweenAgoProps {
  value: string;
  timeUnit?: string;
  onChange: ({
    value,
    time_unit,
  }: {
    value?: InputDateTimeBetweenAgoProps['value'];
    time_unit?: InputDateTimeBetweenAgoProps['timeUnit'];
  }) => void;
}

const DATE_TIME_BETWEEN_OPTIONS = [
  { value: 'DAY', label: 'Days' },
  { value: 'HOUR', label: 'Hours' },
];

export const InputDateTimeBetweenAgo: React.FC<InputDateTimeBetweenAgoProps> = props => {
  const { t } = useTranslation();
  const { value, timeUnit, onChange } = props;

  const [from, to] = value?.split(' AND ') || [];

  const onChangeFrom = val => {
    const newValue = `${val} AND ${to}`;
    onChange({ value: newValue });
  };

  const onChangeTo = val => {
    const newValue = `${from} AND ${val}`;
    onChange({ value: newValue });
  };

  const onChangeUnit = val => {
    onChange({ time_unit: val });
  };

  return (
    <div className="ants-flex ants-items-center">
      <InputNumber required value={from} onChange={onChangeFrom} width="100%" min={to} />
      <Text className="ants-mx-4">{t(translations.and.title)}</Text>
      <InputNumber required value={to} onChange={onChangeTo} width="100%" max={from} />
      <Select
        className="!ants-ml-4 ants-w-24"
        value={timeUnit}
        onChange={onChangeUnit}
        options={Object.values(DATE_TIME_BETWEEN_OPTIONS)}
      />
    </div>
  );
};
