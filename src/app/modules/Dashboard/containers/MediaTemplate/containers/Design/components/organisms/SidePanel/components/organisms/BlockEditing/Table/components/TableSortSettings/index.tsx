import { DefaultOptionType } from 'antd/lib/select';
import { Select, RadioGroup } from 'app/components/molecules';
import { TTableSettings } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/types';
import { translations } from 'locales/translations';
import { chain } from 'lodash';
import { useLayoutEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getTranslateMessage } from 'utils/messages';

interface TableSortSettingsProps {
  sortSettings: TTableSettings['sortBy'];
  columns: TTableSettings['columns'];
  isLoadingSelectCol?: boolean;
  disableSelectOrder?: boolean;
  className?: string;
  onChangeValues?: (values: TTableSettings['sortBy']) => void;
}

const orderOptions = [
  {
    label: getTranslateMessage(translations.ascending.title),
    value: 'asc' as TTableSettings['sortBy']['order'],
  },
  {
    label: getTranslateMessage(translations.descending.title),
    value: 'desc' as TTableSettings['sortBy']['order'],
  },
];

const TableSortSettings = ({
  sortSettings,
  columns,
  isLoadingSelectCol = false,
  disableSelectOrder = false,
  className,
  onChangeValues,
}: TableSortSettingsProps) => {
  const { t } = useTranslation();
  const [sortByOptions, setSortByOptions] = useState<DefaultOptionType[]>();

  useLayoutEffect(() => {
    const options = chain(columns)
      .filter(({ colType }) => colType !== 'index')
      .uniqBy('value')
      .map(col => ({
        value: col.value,
        label: col.label,
      }))
      .value();

    const indexColSort = options.findIndex(option => option.value === sortSettings.columnId);

    if (indexColSort === -1 && onChangeValues) {
      onChangeValues({
        ...sortSettings,
        columnId: options[0]?.value,
      });
    }

    setSortByOptions(options);
  }, [columns, sortSettings, onChangeValues]);

  return (
    <div className={`ants-flex ants-gap-2 ants-flex-col ${className}`}>
      <Select
        showSearch
        loading={isLoadingSelectCol}
        label={t(translations.sortBy.title)}
        labelClassname="!ants-text-black"
        options={sortByOptions}
        placeholder={t(translations.sortBy.title)}
        value={sortSettings.columnId}
        onChange={value =>
          onChangeValues &&
          onChangeValues({
            ...sortSettings,
            columnId: value,
          })
        }
      />

      <RadioGroup
        className="ants-flex ants-justify-between ants-w-100"
        disabled={disableSelectOrder}
        value={sortSettings.order}
        options={orderOptions}
        onChange={e =>
          onChangeValues &&
          onChangeValues({
            ...sortSettings,
            order: e.target.value,
          })
        }
      />
    </div>
  );
};

export default TableSortSettings;
