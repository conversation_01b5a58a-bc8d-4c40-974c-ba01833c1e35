// Libraries
import classNames from 'classnames';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useDragDropManager, useDrop } from 'react-dnd';

// Atoms
import { Icon, Text } from 'app/components/atoms';

// Translations
import { translations } from 'locales/translations';

// Hooks
import { useDeepCompareMemo } from 'app/hooks';

// Constants
import {
  BLOCK_ITEM,
  SIDE_PANEL_TYPE,
  STANDARDS_BLOCKS,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';

// Slices
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Styled
import { DragBlockHereContent, DragBlockHereWrapper } from './styled';

// Utils
import { handleError } from 'app/utils/handleError';

interface DragBlockHereProps {
  isDroppable?: boolean;
  blockType: string;
  blockId: string;
}

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/Workspace/components/molecules/DragBlockHere/index.tsx';

const { COLUMN, SLIDE_SHOW, GROUP } = STANDARDS_BLOCKS;

export const DragBlockHere: React.FC<DragBlockHereProps> = props => {
  // Hooks
  const dispatch = useDispatch();

  // Actions
  const { addBlock, reorderBlock, setSidePanel } = mediaTemplateDesignActions;

  // I18n
  const { t } = useTranslation();

  // Props
  const { blockId, blockType } = props;

  // React dnd
  const dragDropManager = useDragDropManager();
  const isDragging = dragDropManager.getMonitor().isDragging();
  const dragItem = dragDropManager.getMonitor().getItem();

  const checkIsDisableDrop = ({ dragBlockType, dropBlockType }) => {
    switch (dropBlockType) {
      case 'slide':
      case GROUP.name:
        if ([SLIDE_SHOW.name, COLUMN.name].includes(dragBlockType)) {
          return true;
        }
        break;

      default:
        if (dragBlockType === COLUMN.name) {
          return true;
        }
        break;
    }

    return false;
  };

  // Memo
  const isDisableDrop = useDeepCompareMemo(() => {
    if (isDragging) {
      const { blockType: dragBlockType } = dragItem || {};

      return checkIsDisableDrop({ dropBlockType: blockType, dragBlockType });
    }

    return false;
  }, [isDragging, dragItem]);

  const [{ isOver }, drop] = useDrop(() => ({
    // The type (or types) to accept - strings or symbols
    accept: [BLOCK_ITEM],
    // Props to collect
    collect: monitor => {
      return {
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      };
    },
    drop(item: any) {
      const { isAddBlock, blockType: dragBlockType, savedBlockId } = item || {};

      const isDisableDrop = checkIsDisableDrop({ dragBlockType, dropBlockType: blockType });

      if (isDisableDrop) {
        return;
      }

      if (isAddBlock) {
        dispatch(
          addBlock({
            dragBlockType,
            dropBlockId: blockId,
            dropIndex: 0,
            savedBlockId,
          }),
        );
      } else {
        dispatch(
          reorderBlock({
            source: {
              id: item.blockId,
              index: item.index,
            },
            destination: {
              id: blockId,
              index: 0,
            },
          }),
        );
      }
    },
  }));

  const onClickDragBlockHere = e => {
    e.stopPropagation();

    try {
      dispatch(
        setSidePanel({
          type: SIDE_PANEL_TYPE.BLOCKS.name,
          activePanel: '',
        }),
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: '',
        args: {},
      });
    }
  };

  return (
    <DragBlockHereWrapper>
      <DragBlockHereContent
        ref={drop}
        role={'drop-block-area'}
        className={classNames({
          '!ants-bg-[#e6f4ff] !ants-border-[#91caff]': isOver && !isDisableDrop,
          '!ants-bg-[#fff1f0] !ants-border-[#ffa39e]': isOver && isDisableDrop,
        })}
        onClick={onClickDragBlockHere}
      >
        <Icon type="icon-ants-plus-square-outlined" />
        <Text className="ants-text-center">{t(translations.dragBlockHere.title)}</Text>
      </DragBlockHereContent>
    </DragBlockHereWrapper>
  );
};
