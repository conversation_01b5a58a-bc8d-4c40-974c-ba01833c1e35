import styled from 'styled-components';

export const WrapperDatePicker = styled.div``;

export const DropdownContainer = styled.div`
  display: flex !important;

  .time-content {
    display: flex;
    align-items: center;
    min-width: 0;
    /* flex: 1; */

    > * {
      min-width: 0;
      overflow: hidden;
    }
    > div {
      flex: 1;
    }
  }
  .time-content:not(:first-child) {
    margin-left: 10px;
  }

  .divider-time {
    margin: 0 5px;
  }
`;

export const StyledSelect = styled.div`
  position: relative;
  min-width: 0;
  overflow: hidden;

  .selected-option {
    padding: 8px;
    box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.12);
    background-color: #fff;
    display: flex;
    align-items: center;
    cursor: pointer;
    justify-content: space-between;

    .value-selected {
      color: #000;
    }
    .placeholder-selected {
      color: #999;
    }
  }

  .list-option {
    position: absolute;
    top: 50px;
    left: 0;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none;
    background-color: #fff;
    border: 1px solid #ccc;
    height: 250px;
    width: 75px;
    overflow-y: auto;
    display: none;
    z-index: 99;

    &.open {
      display: block;
    }
  }

  .option {
    cursor: pointer;
    padding: 5px 14px 5px 13px;
    width: 65px;
    height: 28px;
    margin: 9px 0;

    &:hover {
      border-radius: 2px;
      background-color: #005fb8;
      color: #fff;
    }
  }
`;
