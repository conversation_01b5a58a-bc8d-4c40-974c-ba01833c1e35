export const mapFieldToFieldName = (dataIn: any[] = [], dataSource: any[] = [], isAction = false) => {
  let dataSourceOut = [] as any;
  let allDataSourceOut = [] as any;
  let isCouponCode = false;
  Object.keys(dataIn).forEach(key => {
    if (dataIn[key].order !== -1 && dataIn[key].id !== 'privacyText' && dataIn[key].id !== 'submitButton') {
      allDataSourceOut.push({ name: dataIn[key].nameField || mapNameField(dataIn[key].name), id: dataIn[key].id });

      if (dataIn[key].id === 'phoneInput' && dataIn[key].useCountryCode) {
        allDataSourceOut.push({ name: 'Country Code', id: 'countryCode' });
      }
    }
  });
  allDataSourceOut.push(
    { name: 'Coupon Code', id: 'couponCode' },
    { name: 'Visitor ID', id: 'visitorId' },
    { name: 'Customer ID', id: 'customerId' },
    { name: 'Identify type', id: 'identifyType' },
    { name: 'Session ID', id: 'sessionId' },
    { name: 'Journey ID', id: 'journeyId' },
    { name: 'Campaign ID', id: 'campaignId' },
    { name: 'Variant ID', id: 'variantId' },
  );

  // init toàn bộ form field khi chưa action ở table
  if (!isAction) {
    Object.keys(dataIn).forEach(key => {
      if (dataIn[key].order !== -1 && dataIn[key].id !== 'privacyText' && dataIn[key].id !== 'submitButton') {
        dataSourceOut.push({
          name: mapNameField(dataIn[key].name),
          id: dataIn[key].id,
          type: dataIn[key].type,
          attribute: '',
          hash: null,
          inputName: dataIn[key].inputName,
          order: dataIn[key].order,
        });
      }
    });
  } else {
    Object.keys(dataIn).forEach(key => {
      dataSource.forEach(value => {
        if (
          dataIn[key].order !== -1 &&
          dataIn[key].id === value.id &&
          dataIn[key].id !== 'privacyText' &&
          dataIn[key].id !== 'submitButton'
        ) {
          dataSourceOut.push({
            name: mapNameField(dataIn[key].nameField || dataIn[key].name),
            id: dataIn[key].id,
            type: dataIn[key].type,
            attribute: value.attribute || '',
            inputName: dataIn[key].inputName,
            hash: value.hash || null,
            order: dataIn[key].order,
          });
        }
      });
    });

    if (dataSource) {
      dataSource.forEach(data => {
        switch (data.id) {
          case 'couponCode':
            isCouponCode = true;
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: 'coupon',
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'coupon_code',
            });
            break;
          case 'countryCode': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'phone',
            });
            break;
          }
          case 'visitorId': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'visitor_id',
            });
            break;
          }
          case 'customerId': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'customer_id',
            });
            break;
          }
          case 'identifyType': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'identify_type',
            });
            break;
          }
          case 'sessionId': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'session_id',
            });
            break;
          }
          case 'journeyId': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'journey_id',
            });
            break;
          }
          case 'campaignId': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'campaign_id',
            });
            break;
          }
          case 'variantId': {
            dataSourceOut.push({
              name: data.name,
              id: data.id,
              type: data.type,
              attribute: data.attribute,
              hash: data.hash || null,
              inputName: 'variant_id',
            });
            break;
          }
          case FIELD_TYPE.SET_VALUES.value:
            dataSourceOut.push({
              ...data,
            });

            break;

          default:
            break;
        }
      });
    }
  }

  dataSourceOut = dataSourceOut.sort(
    (a: { order: any }, b: { order: any }) => (a.order ?? Infinity) - (b.order ?? Infinity),
  );

  return { dataSourceOut, allDataSourceOut, isCouponCode };
};
export const serialDatatoOptionAddField = (source, sourceAll) => {
  let dataOut = {} as any;
  (sourceAll || []).forEach(data => {
    let isCheck = 0;
    (source || []).forEach(item => {
      if (data.id === item.id) {
        isCheck += 1;
      }
    });
    if (isCheck < 3) {
      dataOut = {
        ...dataOut,
        [data.id]: {
          value: data.id,
          label: data.name,
        },
      };
    }
  });
  return dataOut;
};
export const syncDataConfig = {
  columnMapping: [],
  isAction: false,
  event: {},
  source: {},
};
export const optionSourceSerial = dataIn => {
  let dataOut = {} as any;
  if (dataIn.length > 0) {
    dataIn.forEach(data => {
      dataOut = {
        ...dataOut,
        [data.insightPropertyId]: {
          ...data,
          value: data.insightPropertyId,
          label: data.insightPropertyName,
        },
      };
    });
  }

  return dataOut;
};
export const optionEventSerial = dataIn => {
  let dataOut = {} as any;
  if (dataIn.length > 0) {
    dataIn.forEach(data => {
      dataOut = {
        ...dataOut,
        [data.eventTrackingName]: {
          ...data,
          value: data.eventTrackingName,
          label: data.translateLabel,
        },
      };
    });
  }

  return dataOut;
};
export const optionTreeAttribute = dataIn => {
  let dataOut = [] as any;
  dataOut = dataIn.map(attr => ({
    ...attr,
    value: attr.eventPropertyName,
    title: attr.eventPropertyDisplay,
    isLeaf: attr.items ? attr.items.length === 0 : true,
    children: attr.items?.length
      ? attr.items.map(item => ({
          ...item,
          value: `${attr.itemTypeId}.${item.itemPropertyName}`,
          title: item.translateLabel,
          isLeaf: true,
        }))
      : [],
  }));

  return dataOut;
};
export const mapNameField = name => {
  switch (name) {
    case 'Name field': {
      return 'Name';
    }
    case 'Email field': {
      return 'Email';
    }
    case 'Phone field': {
      return 'Phone';
    }
    case 'Last Name field': {
      return 'Last Name';
    }
    case 'First Name field': {
      return 'First Name';
    }
    default: {
      return name;
    }
  }
};
export const getSourceDefault = data => {
  let dataOut = {} as any;
  if (data) {
    data.forEach(source => {
      if (source.insightPropertyName.toLowerCase() === 'lead') {
        dataOut = { ...source, value: data.insightPropertyId, label: data.insightPropertyName };
      }
    });
  }
  return dataOut;
};
export const getEventDefault = data => {
  let dataOut = {} as any;
  if (data) {
    data.forEach(event => {
      if (event.eventNameDisplay.toLowerCase() === 'submit lead') {
        dataOut = { ...event, value: data.eventTrackingName, label: data.translateLabel };
      }
    });
  }
  return dataOut;
};
export const getAttrDefault = (dataSource, attribute) => {
  let dataSourceAdd = [] as any;

  dataSource.forEach(data => {
    let matchedAttribute = null;

    attribute.forEach(attr => {
      if (attr.eventPropertyDisplay.toLowerCase() === 'lead') {
        attr.items.forEach(item => {
          if (item.itemPropertyName === data.inputName) {
            matchedAttribute = {
              ...attr,
              value: attr.eventPropertyName,
              title: attr.eventPropertyDisplay,
              isLeaf: attr.items ? attr.items.length === 0 : true,
              attributeOpin: {
                ...item,
                value: `${attr.itemTypeId}.${item.itemPropertyName}`,
                title: item.translateLabel,
                isLeaf: true,
              },
            };
          }
        });
      }
    });

    dataSourceAdd.push({
      ...data,
      attribute: matchedAttribute || '',
    });
  });
  return dataSourceAdd;
};
export const HASH = [
  {
    value: 'none',
    label: 'None',
  },
  {
    value: 'md5',
    label: 'MD5',
  },
  {
    value: 'md5_uppercase',
    label: 'MD5 (Uppercase)',
  },
  {
    value: 'sha256',
    label: 'SHA256',
  },
  {
    value: 'sha256_uppercase',
    label: 'SHA256 (Uppercase)',
  },
];

export const FIELD_TYPE = {
  SET_VALUES: {
    value: 'set_values',
    label: 'Set Values',
  },
};

// //Check không cho chọn lại attribute Name
export const mapOptionAttributeNameExist = (attr, syncData) => {
  let isCheck = false;
  if (syncData?.columnMapping.length > 0 && syncData) {
    syncData?.columnMapping.forEach(item => {
      if (item.attribute && item.attribute.eventPropertyName === attr.eventPropertyName) {
        if (!item.attribute.attributeOpin) {
          isCheck = true;
        }
      }
    });
  }
  return isCheck;
};
export const mapOptionAttributeChildNameExist = (item, syncData, attr) => {
  let isCheck = false;
  if (syncData?.columnMapping.length > 0 && syncData) {
    syncData?.columnMapping.forEach(each => {
      if (each.attribute && each.attribute.eventPropertyName === attr.eventPropertyName) {
        if (each.attribute.attributeOpin && each.attribute.attributeOpin.itemPropertyName === item.itemPropertyName) {
          isCheck = true;
        }
      }
    });
  }
  return isCheck;
};
export const checkHashOptionExist = (item, data, syncData) => {
  let isCheck = false;
  if (syncData?.columnMapping.length > 0 && syncData) {
    syncData?.columnMapping.forEach(each => {
      if (item.value === each.hash && data.id === each.id) {
        isCheck = true;
      }
    });
  }
  return isCheck;
};

export const BO_SYNC = {
  CUSTOMER: 'customer',
  VISITOR: 'visitor',
};

export const BO_SYNC_DATA = [
  {
    value: BO_SYNC.CUSTOMER,
    label: 'Customer',
  },
  {
    value: BO_SYNC.VISITOR,
    label: 'Visitor',
  },
];

export const CUSTOMER_SYNC_METHOD = [
  {
    value: 'insert',
    label: 'Add new only',
  },
  {
    value: 'update',
    label: 'Update only',
  },
  {
    value: 'upsert',
    label: 'Add new & update',
  },
];

export const VISITOR_SYNC_METHOD = [
  {
    value: 'update',
    label: 'Update only',
  },
];
