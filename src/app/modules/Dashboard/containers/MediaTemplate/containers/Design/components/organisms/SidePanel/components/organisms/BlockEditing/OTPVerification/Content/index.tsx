import { Collapse, CollapsePanel } from 'app/components/molecules';
import {
  selectBlockSelected,
  selectSidePanel,
  selectTemplate,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';
import { translations } from 'locales/translations';
import React, { memo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';
import { handleError } from 'app/utils/handleError';
import produce from 'immer';
import { ButtonGroupSetting } from '../../../ButtonGroupSetting';
import { LAYOUT_TEMPLATE } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/constants';
import {
  BLOCK_SETTING_DEFAULT,
  DATA_MIGRATE,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/config';
import { DISPLAY_LAYOUT } from '../../../../../../Workspace/components/organisms/OTPVerification/constants';
import { getListEvents, getListSources } from 'app/services/MediaTemplateDesign/BusinessObject';
import _ from 'lodash';
import { LayoutForm } from './components/Layout';
import { ResendButton } from './components/ResendButton';
import { Messages } from './components/Messages';
import { FormOTP } from './components/FormOTP';
import { EventTrigger } from './components/EventTrigger';
import { getEventDefault, getSourceDefault } from '../utils';
import {
  RESEND_BUTTON_SPREAD_FORM_STYLE,
  TYPE_TAB_ACTIVE_ADVANCE,
  TYPE_TAB_ACTIVE_CONTENT,
  VERIFY_BUTTON_SPREAD_FORM_STYLE,
} from '../constants';

type TContent = {};
const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/BlockEditing/OTPVerification/Content/index.tsx';
export const Content: React.FC<TContent> = memo(props => {
  const dispatch = useDispatch();
  const { updateBlockSetting, setData, setSidePanel, setEventTrigger } = mediaTemplateDesignActions;
  const blockSelected = useSelector(selectBlockSelected);
  const blockSettings = blockSelected?.settings;
  const template = useSelector(selectTemplate);
  const sidePanel = useSelector(selectSidePanel);
  const { t } = useTranslation();

  const { dynamic = DATA_MIGRATE['otp-verification'].dynamic, eventTrigger = {} } = blockSettings;

  useEffect(() => {
    handleInitSourceEvent();
    if (sidePanel.keyActivePanel === TYPE_TAB_ACTIVE_ADVANCE.INPUT_FIELD_STYLING) {
      dispatch(
        setSidePanel({
          keyActivePanel: TYPE_TAB_ACTIVE_CONTENT.OTP_FORM,
        }),
      );
    }
  }, []);

  const handleInitSourceEvent = async () => {
    const { source = {}, event = {} } = eventTrigger;
    const sources = await getListSources();
    if (sources) {
      dispatch(
        setData({
          sources,
        }),
      );
    }
    if (_.isEmpty(event) && sources) {
      const sourceDefault = !Object.keys(source).length ? getSourceDefault(sources) : source;
      if (Object.keys(sourceDefault).length > 0) {
        const events = await getListEvents({ source_id: sourceDefault.insightPropertyId });
        if (events) {
          const eventDefault = getEventDefault(events);
          dispatch(
            setData({
              events,
            }),
          );
          dispatch(
            setEventTrigger({
              ...blockSettings.eventTrigger,
              source: { ...sourceDefault },
              event: { ...eventDefault },
            }),
          );
        }
      }
    }
  };

  const handleChangeSettings = useCallback(
    (data: Record<string, any>) => {
      try {
        if (blockSelected && data && typeof data === 'object') {
          dispatch(
            updateBlockSetting(
              produce(blockSelected.settings, draft => {
                Object.keys(data).forEach(key => {
                  draft[key] = data[key];

                  // set default config with layout
                  if (key === 'layout') {
                    const { actions, eventTrigger, ...defaultFormStyle } = BLOCK_SETTING_DEFAULT.OTP_VERIFICATION;
                    const formStyleUpdate = _.cloneDeep(defaultFormStyle);
                    formStyleUpdate.layout = data[key];
                    if (data[key] === DISPLAY_LAYOUT.SPREAD) {
                      [VERIFY_BUTTON_SPREAD_FORM_STYLE, RESEND_BUTTON_SPREAD_FORM_STYLE].forEach(configForm => {
                        Object.keys(configForm).forEach(key => {
                          const keyForm = configForm.key;
                          if (typeof configForm[key] === 'object') {
                            formStyleUpdate[keyForm][key] = {
                              ...formStyleUpdate[keyForm][key],
                              ...configForm[key],
                            };
                          } else {
                            formStyleUpdate[keyForm][key] = configForm[key];
                          }
                        });
                      });
                    }
                    Object.keys(formStyleUpdate).forEach(keyForm => {
                      draft[keyForm] = formStyleUpdate[keyForm];
                    });
                  }
                });
              }),
            ),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'handleChangeSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const onChangeButtonStyling = useCallback(
    (
      buttonSettings = {},
      buttonStyles = {},
      elementOptions = {},
      blockStyles = {},
      element = {},
      dynamicSetting = {},
      ignoreUndoAction = false,
    ) => {
      try {
        if (blockSelected) {
          dispatch(
            updateBlockSetting({
              ...produce(blockSelected.settings, draft => {
                draft.verifyButton.buttonSettings = {
                  ...draft.verifyButton.buttonSettings,
                  ...buttonSettings,
                };
                draft.verifyButton.buttonStyles = {
                  ...draft.verifyButton.buttonStyles,
                  ...buttonStyles,
                };
                draft.blockStyles = {
                  ...draft.blockStyles,
                  ...blockStyles,
                };
                draft.actions.OTPElementButton = {
                  ...draft.actions.OTPElementButton,
                  ...element,
                };
                draft.actions.OTPElementButton.options = {
                  ...draft.actions.OTPElementButton.options,
                  ...elementOptions,
                };
                draft.dynamic = {
                  ...draft.dynamic,
                  ...dynamicSetting,
                };
              }),
              ignoreUndoAction,
            }),
          );
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onUpdateBlockSettings',
          args: {},
        });
      }
    },
    [blockSelected, dispatch, updateBlockSetting],
  );

  const handleOnchangeTab = (key: number) => {
    const displayMessage = key === TYPE_TAB_ACTIVE_CONTENT.MESSAGES;
    handleChangeSettings({ displayMessage });
    dispatch(
      setSidePanel({
        keyActivePanel: key,
      }),
    );
  };

  return (
    <Collapse
      activeKey={sidePanel?.keyActivePanel || TYPE_TAB_ACTIVE_CONTENT.LAYOUT}
      onChange={(key: string | string[]) => handleOnchangeTab(Number(key))}
      defaultActiveKey={sidePanel?.keyActivePanel || TYPE_TAB_ACTIVE_CONTENT.LAYOUT}
      accordion
    >
      <CollapsePanel header={t(translations.layout.title)} key={TYPE_TAB_ACTIVE_CONTENT.LAYOUT}>
        <LayoutForm layout={blockSettings.layout} onChange={handleChangeSettings} />
      </CollapsePanel>
      <CollapsePanel header={t(translations.otpForm.title)} key={TYPE_TAB_ACTIVE_CONTENT.OTP_FORM}>
        <FormOTP onChange={handleChangeSettings} settings={blockSettings} />
      </CollapsePanel>
      <CollapsePanel header={t(translations.resendButton.title)} key={TYPE_TAB_ACTIVE_CONTENT.RESEND_BUTTON}>
        <ResendButton onChange={handleChangeSettings} settings={blockSettings} />
      </CollapsePanel>
      <CollapsePanel header={t(translations.verifyButton.title)} key={TYPE_TAB_ACTIVE_CONTENT.VERIFY_BUTTON}>
        <ButtonGroupSetting
          dynamicSetting={dynamic}
          buttonSettings={blockSettings.verifyButton.buttonSettings}
          buttonStyles={blockSettings.verifyButton.buttonStyles}
          blockStyles={blockSettings.blockStyles}
          ButtonElement={blockSettings.actions.OTPElementButton}
          onChange={onChangeButtonStyling}
          excludeActions={template?.type === LAYOUT_TEMPLATE.INLINE.name ? ['close'] : []}
        />
      </CollapsePanel>
      <CollapsePanel header={t(translations.messages.title)} key={TYPE_TAB_ACTIVE_CONTENT.MESSAGES}>
        <Messages onChange={handleChangeSettings} settings={blockSettings} />
      </CollapsePanel>
      <CollapsePanel header={t(translations.triggerEvent.title)} key={TYPE_TAB_ACTIVE_CONTENT.TRIGGER_EVENT}>
        <EventTrigger onChange={handleChangeSettings} settings={blockSettings} />
      </CollapsePanel>
    </Collapse>
  );
});
