// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

export const TemplatesWrapper = styled.div`
  ${tw`ants-grid ants-grid-cols-3 ants-gap-15px`}
`;

export const TemplateItem = styled.div`
  ${tw`
    ants-flex ants-flex-col ants-space-y-2.5 ants-items-center ants-justify-center
    ants-w-[130px] ants-h-[130px]
    ants-transition ants-duration-300 ants-cursor-pointer
    `}

  border: 1px solid ${globalToken?.blue1};
  border-radius: ${globalToken?.borderRadius}px;

  &:hover {
    border-color: ${globalToken?.blue3};
    background-color: ${globalToken?.blue};
  }
  &.active {
    border-color: ${globalToken?.blue7};
    border-width: 2px;
  }
`;
