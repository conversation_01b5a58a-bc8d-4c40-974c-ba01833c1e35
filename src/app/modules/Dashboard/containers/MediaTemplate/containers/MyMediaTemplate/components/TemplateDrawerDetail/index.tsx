// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { cloneDeep, get, isEmpty, omit } from 'lodash';

// Antsomi UI
import { MENU_PERMISSION } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/constants';
import { ObjectAccessInfo } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/types';
import { initAccessInfoDefault } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/utils';

// Components
import {
  Button,
  DrawerDetail,
  DrawerDetailProps,
  EditableName,
  Flex,
  Dropdown,
  Icon,
  useGetObjectTemplateDetail,
  useValidateTemplateName,
  usePersistTemplate,
  Spin,
  message,
  useGetSaveAsGalleryPermissionEmails,
  TemplateSaveAsModal,
  useTemplateSave,
  TemplateValueOptions,
  snakeCaseToCamelCase,
  camelCaseToSnakeCase,
  ItemNotFound,
} from '@antscorp/antsomi-ui';
import { MediaTemplateEditor } from '../../../Design';
import { SaveAsButton } from '../../../Design/components/molecules/SaveAsButton';
import { SelectModeDesign } from '../../../Design/components/organisms/Toolbar/components/organisms/SelectModeDesign';

// Hooks
import { useExternalServiceAuth } from 'app/hooks/useExternalServiceAuth';
import { useDeepCompareEffect, useDeepCompareMemo, useUserInfoV2 } from 'app/hooks';

// Constants
import {
  GET_LIST_TYPE,
  OBJECT_TYPES,
  PUBLIC_LEVEL,
  QUERY_KEYS,
  TEMPLATE_CATEGORY_KEYS,
} from '@antscorp/antsomi-ui/es/constants';
import { ACTIONS } from '../../../Design/slice/sagaActions';

// Translations
import { translations } from 'locales/translations';
import { useDispatch, useSelector } from 'react-redux';

// Slice
import {
  selectDeviceType,
  selectExportInfo,
  selectIsLoadingWorkspace,
  selectIsSavingTemplate,
  selectWorkspace,
  selectPromotionPool,
  selectIsLoadingSyncData,
} from '../../../Design/slice/selectors';
import { mediaTemplateDesignActions } from '../../../Design/slice';

// Store
import { createSagaAction } from 'store/configureStore';
import { permissionServices } from 'app/services/Permission';
import { buildUpdatedCategories } from 'modules/Dashboard/containers/MediaTemplate/utils';
import { getTemplateSetting } from '../../../Design/slice/utils';
import { handleUploadThumbnail } from '../../../Design/utils';
import { handleError } from 'app/utils';
import { Helmet } from 'react-helmet-async';

interface TemplateDrawerDetailProps extends DrawerDetailProps {
  templateId?: string;
}

type TState = {
  templateName: string;
  templateNameError: string;

  // SaveAsTemplateModal
  isOpenSaveTemplate: boolean;
  isLoadingUploadThumbnail: boolean;
  tempThumbnails: any[];
  selectedTemplateId: string | number;
  isOnClickSave: boolean;
};

const initialState: TState = {
  templateName: '',
  templateNameError: '',
  isOpenSaveTemplate: false,
  isLoadingUploadThumbnail: false,
  tempThumbnails: [],
  selectedTemplateId: '',
  isOnClickSave: false,
};

const PATH =
  'src/app/modules/Dashboard/containers/MediaTemplate/containers/MyMediaTemplate/components/TemplateDrawerDetail/index.tsx';

export const TemplateDrawerDetail: React.FC<TemplateDrawerDetailProps> = props => {
  // Props
  const { templateId, ...restProps } = props;

  // Hooks
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const auth = useExternalServiceAuth();
  const queryClient = useQueryClient();
  const userInfo = useUserInfoV2();
  const [messageApi, contextHolder] = message.useMessage();

  // State
  const [state, setState] = useState<TState>(initialState);

  // Selectors
  const isLoadingWorkspace = useSelector(selectIsLoadingWorkspace);
  const isLoadingSyncData = useSelector(selectIsLoadingSyncData);
  const workspace = useSelector(selectWorkspace);
  const promotionPool = useSelector(selectPromotionPool);
  const isSavingTemplate = useSelector(selectIsSavingTemplate);
  const exportInfo = useSelector(selectExportInfo);
  const deviceType = useSelector(selectDeviceType);

  // Variables
  const {
    templateName,
    templateNameError,
    isOpenSaveTemplate,
    isLoadingUploadThumbnail,
    tempThumbnails,
    selectedTemplateId,
    isOnClickSave,
  } = state;

  const { setWorkspace, setLoadingWorkspace } = mediaTemplateDesignActions;
  const isForceBuild = (exportInfo.exportPages?.length || 0) > 0;

  // Queries
  const { mutateAsync: validateTemplateName, isLoading: isLoadingTemplateName } = useValidateTemplateName({});
  const {
    data: objectTemplateDetail,
    isLoading: isLoadingGetObjectTemplate,
    refetch: refetchObjectTemplateDetail,
  } = useGetObjectTemplateDetail({
    args: {
      auth,
      params: {
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: PUBLIC_LEVEL.RESTRICTED,
        template_id: templateId || '',
      },
    },
    options: {
      onSettled: () => {
        dispatch(setLoadingWorkspace(false));
      },
    },
  });
  const { mutateAsync: updateTemplateName, isLoading: isLoadingUpdateTemplateName } = usePersistTemplate({
    options: {
      onSuccess: () => {},
      onSettled: () => {
        queryClient.invalidateQueries([QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST], {
          exact: false,
        });
      },
    },
  });
  const { data: saveAsGalleryPermissionEmails, isLoading: isLoadingSaveAsGalleryPermissionEmails } =
    useGetSaveAsGalleryPermissionEmails({
      args: {
        auth,
      },
    });
  const { mutateAsync: persistTemplate, isLoading: loadingPersistTemplate } = usePersistTemplate({});
  // NOTE: TEMPLATE SAVE AS V2
  const { data: saveExistingTemplate } = useGetObjectTemplateDetail({
    args: {
      auth,
      params: {
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: PUBLIC_LEVEL.RESTRICTED,
        template_id: selectedTemplateId || objectTemplateDetail?.id || '',
      },
    },
  });

  const { settings: objectTemplateSettings } = objectTemplateDetail || {};

  // Memo
  const mediaTemplateDetail: any = useDeepCompareMemo(() => {
    const categories = {};
    Object.values(TEMPLATE_CATEGORY_KEYS).forEach(key => {
      if (objectTemplateDetail?.[key]) {
        categories[key] = objectTemplateDetail?.[key];
      }
    });

    return {
      ...omit({ ...objectTemplateDetail?.settings, thumbnail: objectTemplateDetail?.thumbnail }, [
        'journeySettings',
        'warnings',
      ]),
      id: `${objectTemplateDetail?.id}`,
      name: objectTemplateDetail?.name,
      objectiveTypes: objectTemplateDetail?.objectiveTypes,
      description: objectTemplateDetail?.description,
      categories,
    };
  }, [objectTemplateDetail]);

  const dropdownOnSave = useMemo(() => {
    const options: any[] = [
      {
        label: 'Save as...',
        key: '0',
        disabled: true,
      },
    ];

    if (saveAsGalleryPermissionEmails?.includes(userInfo?.email || '')) {
      options.push({
        label: <SaveAsButton saveAsGallery />,
        key: '1',
      });
    }

    return options;
  }, [saveAsGalleryPermissionEmails, userInfo?.email]);

  const defaultThumbnail = useDeepCompareMemo(() => {
    const thumbnailIdx = objectTemplateDetail?.settings?.viewPages
      .filter(page => page.settings?.isActive)
      .map(item => item.thumbnail)
      .findIndex(item => item === objectTemplateSettings?.thumbnail);

    return thumbnailIdx === -1 ? objectTemplateSettings?.defaultThumbnailIndex || 0 : thumbnailIdx;
  }, [
    objectTemplateDetail?.settings?.viewPages,
    objectTemplateSettings?.defaultThumbnailIndex,
    objectTemplateSettings?.thumbnail,
  ]);

  const shareAccessInfo: ObjectAccessInfo = useDeepCompareMemo(() => {
    const { shareAccess } = saveExistingTemplate || {};

    if (!isEmpty(shareAccess)) {
      return snakeCaseToCamelCase(shareAccess || {}, true);
    }

    return initAccessInfoDefault(userInfo || {});
  }, [saveExistingTemplate]);

  const templateSaveDefaultValue: Partial<TemplateValueOptions> = useDeepCompareMemo(() => {
    const defaultValue: Partial<TemplateValueOptions> = {
      accessInfo: shareAccessInfo,
      categories: workspace.categories || objectTemplateSettings?.categories,
      defaultThumbnail,
      description: workspace.description || objectTemplateSettings?.description,
      templateName: {
        id: +(objectTemplateDetail?.id || 0),
        label: undefined,
      },
      saveOption: 'save-exist',
    };

    return defaultValue;
  }, [
    defaultThumbnail,
    workspace,
    objectTemplateSettings?.categories,
    objectTemplateSettings?.description,
    objectTemplateDetail?.id,
    shareAccessInfo,
  ]);

  const isNotFoundTemplate = useMemo(() => {
    return !isLoadingGetObjectTemplate && !objectTemplateDetail?.id;
  }, [isLoadingGetObjectTemplate, objectTemplateDetail]);

  const {
    categoryItems,
    templateItems,
    value: templateValue,
    onChange: onChangeTemplateValue,
    setValue,
    searchNameProps,
    form,
  } = useTemplateSave({
    service: auth,
    config: {
      getListType: GET_LIST_TYPE.OWNER,
      objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
      publicLevel: PUBLIC_LEVEL.RESTRICTED,
      limitListPerPage: 20,
    },
    defaultValue: templateSaveDefaultValue,
  });
  const { saveOption } = templateValue;

  // Effects
  /* Handle Save Template */
  useEffect(() => {
    if (!isForceBuild && !isSavingTemplate) {
      if (isOnClickSave) {
        setState(prev => ({ ...prev, isOnClickSave: false, isOpenSaveTemplate: true }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSavingTemplate, isForceBuild]);

  /* Fetch object template detail when drawer detail is opened */
  useEffect(() => {
    if (restProps.open) {
      refetchObjectTemplateDetail();
    }
  }, [refetchObjectTemplateDetail, restProps.open]);

  /* Update template name state from object template */
  useEffect(() => {
    if (objectTemplateDetail?.name) {
      setState(prev => ({ ...prev, templateName: objectTemplateDetail?.name || '' }));
    }
  }, [objectTemplateDetail?.name]);

  /* Update loading of workspace */
  useEffect(() => {
    // If Drawer is opened
    if (restProps.open) {
      setTimeout(() => {
        dispatch(setLoadingWorkspace(isLoadingGetObjectTemplate));
      }, 200);
    }
  }, [dispatch, setLoadingWorkspace, restProps.open, isLoadingGetObjectTemplate]);

  /* Update template value */
  useDeepCompareEffect(() => {
    if (!!templateSaveDefaultValue) {
      onChangeTemplateValue(templateSaveDefaultValue);
    }
  }, [omit(templateSaveDefaultValue, 'accessInfo')]);

  /* Update template thumbnails */
  useDeepCompareEffect(() => {
    const newThumbnails = (workspace?.viewPages || objectTemplateSettings?.viewPages || [])
      .flatMap(item => (item.settings.isActive ? item.thumbnail : []))
      .filter(Boolean);

    setState(prev => ({ ...prev, tempThumbnails: newThumbnails }));
  }, [objectTemplateSettings?.viewPages, workspace?.viewPages]);

  /* Update template value base save option */
  useDeepCompareEffect(() => {
    if (saveOption === 'save-exist') {
      // Set timeout for get updated value
      const currentShareAccess = snakeCaseToCamelCase(
        saveExistingTemplate?.shareAccess || {},
        true,
      ) as ObjectAccessInfo;

      setValue(prev => ({
        ...prev,
        description: saveExistingTemplate?.description,
        accessInfo: !isEmpty(currentShareAccess) ? currentShareAccess : initAccessInfoDefault(userInfo || {}),
      }));
    }
  }, [saveExistingTemplate, saveOption]);

  // Handlers
  const onBlurEditableName = async () => {
    const formattedName = templateName?.trim() || '';
    let messageError = '';

    // If name is the same, do nothing
    if (formattedName === workspace.name) {
      setState(prev => ({ ...prev, templateNameError: '' }));
      return;
    }

    // Validate required name
    if (!formattedName) {
      messageError = t(translations.messageError.fieldEmpty.message);
    } else if (formattedName.length > 255) {
      messageError = t(translations.messageError.maxLength.message, { name: 'Template Name', maxLength: 255 });
    } else {
      // Validate duplicate name
      const resultValidate = await validateTemplateName({
        auth,
        params: {
          object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
          public_level: PUBLIC_LEVEL.RESTRICTED,
          template_name: formattedName,
        },
      });

      if (resultValidate && resultValidate.existed === false) {
        const resultUpdate = await updateTemplateName({
          persistType: 'update',
          params: {
            auth,
            data: {
              template_id: +(objectTemplateDetail?.id || -1),
              template_name: formattedName,
            },
          },
        });
        if (resultUpdate?.status) {
          dispatch(
            setWorkspace({
              name: formattedName,
            }),
          );
        }
      } else {
        messageError = t(translations.messageError.nameExisted.message);
      }
    }

    setState(prev => ({ ...prev, templateNameError: messageError }));
  };

  const onClickCancel = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (restProps.onClose) {
      restProps.onClose(e);
    }

    /* Reset State */
    setState(initialState);
  };

  const onClickSave = () => {
    if (isForceBuild) {
      dispatch(createSagaAction(ACTIONS.SAVE_MEDIA_TEMPLATE, { isForceBuild: true }));
    } else {
      setState(prev => ({ ...prev, isOpenSaveTemplate: true }));
    }
    setState(prev => ({ ...prev, isOnClickSave: true }));
  };

  const handleTemplateSaveAsCancel = () => {
    onChangeTemplateValue(templateSaveDefaultValue);
    setState(prev => ({ ...prev, selectedTemplateId: workspace.id, isOpenSaveTemplate: false, isOnClickSave: false }));
  };

  const handleUpdateTemplate = async (value: TemplateValueOptions) => {
    const { templateName, accessInfo, categories, defaultThumbnail, description, saveOption } = value;
    const updatedCategories = buildUpdatedCategories({
      categories,
      categoryItems,
    });
    const persistType: 'create' | 'update' = saveOption === 'save-new' ? 'create' : 'update';
    const isCreateNew = saveOption === 'save-new';

    let thumbnail = '';
    try {
      let cloneWorkspace = cloneDeep(omit(workspace, ['warnings']));

      // Handle Update new thumbnails
      cloneWorkspace.viewPages
        .filter(viewPage => viewPage.settings.isActive)
        .forEach((viewPage, idx) => {
          viewPage.thumbnail = tempThumbnails[idx] || '';
        });

      let updateData = {
        ...(isCreateNew ? { template_name: templateName?.label?.trim() } : { template_id: +templateName?.id! }),
        properties: {},
        template_setting: getTemplateSetting(cloneWorkspace, { promotionPool }),
        template_type: cloneWorkspace.template.id,
        device_type: deviceType,
        object_type: OBJECT_TYPES.MEDIA_TEMPLATE,
        public_level: PUBLIC_LEVEL.RESTRICTED,
        share_access: camelCaseToSnakeCase(accessInfo!, true),
        description,
        thumbnail: '',
        ...updatedCategories,
      };

      // Case Update
      if (!isCreateNew) {
        setState(prev => ({ ...prev, isLoadingUploadThumbnail: true }));

        // Handle upload images
        cloneWorkspace = await handleUploadThumbnail({ templateId: +templateName?.id!, workspace: cloneWorkspace });

        setState(prev => ({ ...prev, isLoadingUploadThumbnail: false }));

        // Set thumbnail
        thumbnail =
          cloneWorkspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
            defaultThumbnail || 0
          ] || '';

        updateData = {
          ...updateData,
          properties: {
            ...cloneWorkspace,
            viewPages: cloneWorkspace.viewPages,
            categories: categories,
            defaultThumbnailIndex: defaultThumbnail,
            thumbnail,
            ...(persistType === 'create' ? { isInitial: true } : {}),
          },
          thumbnail,
        };
      }

      const { id: templateId } = await persistTemplate({
        persistType,
        params: {
          auth,
          data: updateData,
        },
      });

      if (templateId && isCreateNew) {
        setState(prev => ({ ...prev, isLoadingUploadThumbnail: true }));

        cloneWorkspace = await handleUploadThumbnail({ templateId: templateId, workspace: cloneWorkspace });

        setState(prev => ({ ...prev, isLoadingUploadThumbnail: false }));

        // Set thumbnail
        thumbnail =
          cloneWorkspace.viewPages.filter(page => page.settings.isActive).map(item => item.thumbnail)[
            defaultThumbnail || 0
          ] || '';

        await persistTemplate({
          persistType: 'update',
          params: {
            auth,
            data: {
              template_id: templateId,
              properties: {
                ...cloneWorkspace,
                viewPages: cloneWorkspace.viewPages,
                categories: categories,
                defaultThumbnailIndex: defaultThumbnail,
                thumbnail,
                ...(persistType === 'create' ? { isInitial: true } : {}),
              },
              thumbnail,
            },
          },
        });
      }

      messageApi.success(
        t(
          isCreateNew
            ? translations.createTemplate.notification.createSuccess
            : translations.updateTemplate.notification.updateSuccess,
        ),
      );

      handleTemplateSaveAsCancel();
    } catch (error) {
      const errorMessage = get(error, 'response.data.data', {});
      messageApi.error(errorMessage);

      handleError(error, {
        path: PATH,
        name: 'handleUpdateTemplate',
        args: { value },
      });
    }
  };

  const Header = (
    <Flex align="center" justify="space-between" className="ants-w-full" gap={50}>
      <EditableName
        value={templateName || ''}
        error={templateNameError}
        isLoading={isLoadingTemplateName || isLoadingUpdateTemplateName}
        onChange={value => setState(prev => ({ ...prev, templateName: value }))}
        onBlur={() => onBlurEditableName()}
      />

      <Flex align="center" gap={10}>
        <Spin spinning={false} indicatorSize={16} size="small">
          <Dropdown.Button
            menu={{ items: dropdownOnSave, style: { width: 150 } }}
            type="primary"
            icon={<Icon type="icon-ants-expand-more" size={20} />}
            disabled={isLoadingSaveAsGalleryPermissionEmails || isLoadingWorkspace || isLoadingSyncData}
            trigger={['click']}
            onClick={onClickSave}
          >
            Save
          </Dropdown.Button>
        </Spin>
        <Button onClick={onClickCancel}>Cancel</Button>
      </Flex>
    </Flex>
  );

  return (
    <>
      <DrawerDetail
        {...restProps}
        {...(!isNotFoundTemplate && {
          headerProps: {
            children: Header,
            showBorderBottom: true,
            height: 50,
            style: {
              padding: '0 15px',
            },
          },
        })}
        menuProps={{
          show: false,
          showExpandButton: false,
        }}
        destroyOnClose
        getContainer={() => document.querySelector('#root')!}
      >
        <Helmet title={templateName || ''} />
        {isNotFoundTemplate ? (
          <ItemNotFound />
        ) : (
          <MediaTemplateEditor
            leftToolbar={
              <>
                <SelectModeDesign />
              </>
            }
            rightToolbar={<></>}
            dropdownOnSave={[]}
            mediaTemplateDetail={mediaTemplateDetail}
            onSave={(data: any) => {}}
            onChange={(data: any) => {}}
          />
        )}
      </DrawerDetail>

      {/* Render Save As Modal */}
      <TemplateSaveAsModal
        open={isOpenSaveTemplate}
        destroyOnClose
        okButtonProps={{ loading: loadingPersistTemplate || isLoadingUploadThumbnail }}
        cancelButtonProps={{ disabled: loadingPersistTemplate || isLoadingUploadThumbnail }}
        onCancel={() => {
          !loadingPersistTemplate && handleTemplateSaveAsCancel();
          setState(prev => ({
            ...prev,
            tempThumbnails: workspace.viewPages
              .flatMap(item => (item.settings.isActive ? item.thumbnail : []))
              .filter(Boolean),
          }));
        }}
        onOk={(e, value) => value && handleUpdateTemplate(value)}
        templateProps={{
          form,
          value: templateValue,
          onChange: onChangeTemplateValue,
          onEvent: event => {
            if (event.templateName && event.templateName.id) {
              setState(prev => ({ ...prev, selectedTemplateId: event.templateName?.id?.toString() || '' }));
            }
          },
          imageReview: {
            thumbnails: tempThumbnails,
            skeleton: true,
          },
          onSaveThumbnail(data) {
            const { thumbnails: tempThumbnails } = data;

            setState(prev => ({ ...prev, tempThumbnails }));
          },
          templateNames: templateItems,
          categories: categoryItems,
          omitCategories: ['device_type', 'template_type'],
          shareAccess: {
            getUserInfo: email => permissionServices.getUserInfo(email, userInfo?.user_id!) as any,
            show: true,
            userId: +userInfo?.user_id!,
            userPermission: {
              edit: MENU_PERMISSION.CREATED_BY_USER,
              view: MENU_PERMISSION.CREATED_BY_USER,
            },
            placeholder: 'Add people',
            generalAccessSettings: {
              publicOnlyWith: true,
            },
            allowAddUser: true,
          },
          templateNamesOptions: {
            ...searchNameProps,
          },
        }}
      />
      {contextHolder}
    </>
  );
};
