// Libraries
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalV2, ModalProps, RadioGroup, Spin, Typography } from '@antscorp/antsomi-ui';

// Translations
import { translations } from 'locales/translations';
import { DEVICE_TYPE } from 'constants/variables';
import { TEMPLATE_TYPE_THUMBNAIL } from '../constants';

// Queries
import { useGetListMediaTemplateTypes } from 'app/queries/MediaTemplateType';

// Styled
import { TemplateItem, TemplatesWrapper } from './styled';

interface CreateTemplateModalProps extends ModalProps {
  isLoading?: boolean;
  onFinish?: (data: TState) => void;
}

type TState = {
  deviceType: number;
  templateType: Record<string, any>;
};

const defaultState = {
  deviceType: DEVICE_TYPE.DESKTOP.value,
  templateType: {},
};

export const CreateTemplateModal: React.FC<CreateTemplateModalProps> = props => {
  // Props
  const { isLoading = false, open, cancelButtonProps, okButtonProps, onOk, onCancel, onFinish } = props;

  // State
  const [state, setState] = useState<TState>(defaultState);
  const { deviceType, templateType } = state;

  const { t } = useTranslation();

  // Queries
  const { data: mediaTemplateTypes } = useGetListMediaTemplateTypes<
    { id: number; name: string; label: string; thumbnail: string }[]
  >({
    options: {
      select(data) {
        return data?.map(item => ({ ...item.toJson(), thumbnail: TEMPLATE_TYPE_THUMBNAIL[item.name] })) || [];
      },
    },
  });

  // Effects
  useEffect(() => {
    // const templateType = data[0].toJson();
    if (mediaTemplateTypes && mediaTemplateTypes.length) {
      setState(prev => ({ ...prev, templateType: mediaTemplateTypes[0] }));
    }
  }, [mediaTemplateTypes]);

  const onHandleCancel = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    setState(defaultState);
    onCancel && onCancel(e);
  };

  const onHandleOk = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    onFinish && onFinish(state);
    onOk && onOk(e);
  };

  return (
    <ModalV2
      width={'max-content'}
      okText="Continue"
      title={t(translations.createTemplate.title)}
      open={open}
      centered
      onCancel={onHandleCancel}
      cancelButtonProps={cancelButtonProps}
      okButtonProps={{ ...okButtonProps, disabled: !templateType.id }}
      onOk={onHandleOk}
    >
      <Spin spinning={isLoading}>
        <RadioGroup
          className="!ants-mb-[20px]"
          value={deviceType}
          options={Object.values(DEVICE_TYPE)}
          onChange={e => {
            setState(prev => ({ ...prev, deviceType: e.target.value }));
          }}
        />
        <TemplatesWrapper>
          {mediaTemplateTypes?.map(item => {
            const { id, label, thumbnail } = item;

            return (
              <TemplateItem
                key={id}
                className={templateType?.id === id ? 'active' : ''}
                onClick={() => setState(prev => ({ ...prev, templateType: item }))}
              >
                <img src={thumbnail} alt={label} />
                <Typography.Text>{label}</Typography.Text>
              </TemplateItem>
            );
          })}
        </TemplatesWrapper>
      </Spin>
    </ModalV2>
  );
};
