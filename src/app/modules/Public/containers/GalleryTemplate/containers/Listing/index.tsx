// Libraries
import React, { useCallback, useEffect, useMemo, useState } from 'react';

// Styled
import { PreviewImageWrapper, Wrapper } from './styled';

// Hooks
import { useExternalServiceAuth } from 'app/hooks/useExternalServiceAuth';
import { useDeepCompareMemo, useTabs } from 'app/hooks';

// Locales
import { useTranslation } from 'react-i18next';
import { translations } from 'locales/translations';

// Constants
import { LAYOUT_TEMPLATE, PUBLIC_LEVEL } from '@antscorp/antsomi-ui/es/constants';
import { LIMIT_PER_PAGE_DEFAULT, TEMPLATE_PREVIEW_ID } from 'constants/variables';
import { GALLERY_TAB_MENU_KEYS, GALLERY_TAB_MENU_ITEMS } from './constants';
import { POST_MESSAGE_TYPES } from 'constants/postMessage';

// Components
import {
  Tabs,
  TemplateListing,
  useTemplateListing,
  TGetListType,
  Icon,
  TObjectType,
  ThumbnailCardProps,
} from '@antscorp/antsomi-ui';

// Utils
import {
  getMediaTemplateViewPageThumbnails,
  initPreviewMediaTemplate,
} from 'modules/Dashboard/containers/MediaTemplate/utils';
import { PreviewTemplateWrapper } from 'styles/global-styles';

interface ListingTemplateProps {}

const { mediaTemplate, journeyTactic, emailTemplate } = GALLERY_TAB_MENU_KEYS;
const CARD_HEIGHT_EMAIL = 410;

export const ListingTemplate: React.FC<ListingTemplateProps> = () => {
  // Hooks
  const { t } = useTranslation();

  const { activeTab, onChangeTab } = useTabs({
    tabMenu: Object.values(GALLERY_TAB_MENU_KEYS),
    defaultTab: GALLERY_TAB_MENU_KEYS.mediaTemplate,
  });
  const serviceAuth = useExternalServiceAuth();

  // Memo
  const getListType: TGetListType = useMemo(() => {
    return GALLERY_TAB_MENU_ITEMS[activeTab].getListType;
  }, [activeTab]);
  const objectType: TObjectType = useMemo(() => {
    return GALLERY_TAB_MENU_ITEMS[activeTab].objectType;
  }, [activeTab]);

  const {
    // Values
    categoryItems,
    templateItems,
    templateDetail,
    similarTemplates,
    checkedCategories,
    openCategoryKeys,
    previewTemplateCategories,
    selectTemplateAction,

    // Loading
    isLoadingCategoryList,
    isLoadingTemplateList,
    isLoadingTemplateDetail,

    // Functions
    onLoadMore: onLoadMoreTemplates,
    onChangeOpenCategoryKeys,
    onChangeCheckedCategories,
    onSelectTemplate,
  } = useTemplateListing({
    serviceAuth,
    config: {
      getListType, // TODO: need to remove getListType
      objectType,
      publicLevel: PUBLIC_LEVEL.PUBLIC, // 1: Public, 0: Restricted
      limitListPerPage: LIMIT_PER_PAGE_DEFAULT,
    },
  });

  // State
  const [state, setState] = useState({
    previewId: '',
    previewUrl: '',
  });

  const onClickPreviewThumbnail = useCallback(
    ({ id }) => {
      window.postMessage({
        messageType: POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN_VIEW_NAVIGATE,
        targetScript: state.previewId,
        viewId: id,
      });
    },
    [state.previewId],
  );

  const pagePreviewThumbnails = useDeepCompareMemo(() => {
    return getMediaTemplateViewPageThumbnails(templateDetail);
  }, [templateDetail]);

  const listingComponentProps = useMemo(() => {
    const { settings, name, description } = templateDetail || {};

    switch (activeTab) {
      case emailTemplate: {
        const { viewPages } = settings || {};
        const html: string | undefined = viewPages?.[1]?.html || viewPages?.[0]?.html;

        return {
          name,
          description,
          showSkeleton: false,
          cardHeight: CARD_HEIGHT_EMAIL,
          bannerHeight: '78vh',
          children: !!html ? (
            <iframe title="Preview" srcDoc={html} style={{ width: '100%', height: '100%' }} />
          ) : undefined,
        };
      }
      case journeyTactic: {
        const { model } = templateDetail || {};
        const thumbnails = model?.properties?.thumbnails || [];
        const previewUrl = model?.thumbnail || (thumbnails.length ? thumbnails[0].url : '');
        const urlThumbnails = Array.isArray(thumbnails) ? thumbnails.map(thumb => thumb.url) : [];

        if (!state.previewUrl || (state.previewUrl && !urlThumbnails.includes(state.previewUrl))) {
          setState(prev => ({
            ...prev,
            previewUrl,
          }));
        }
        const children = (
          <PreviewImageWrapper>
            <img src={state.previewUrl} alt="preview" />
          </PreviewImageWrapper>
        );

        return {
          name,
          showDeviceRadios: false,
          thumbnails,
          bannerHeight: '55vh',
          onClickThumbnail: ({ url }) => {
            setState(prev => ({
              ...prev,
              previewUrl: url,
            }));
          },
          showSkeleton: ({ thumbnail }: ThumbnailCardProps) => `${thumbnail || ''}`.includes('base64-img'),
          description,
          children,
        };
      }
      default: {
        return {
          name,
          description,
          thumbnails: pagePreviewThumbnails,
          bannerHeight: '55vh',
          onClickThumbnail: onClickPreviewThumbnail,
          showDeviceRadios: false,
          children: (
            <PreviewTemplateWrapper
              id={TEMPLATE_PREVIEW_ID}
              $isSlideIn={+(templateDetail?.type || 1) === +LAYOUT_TEMPLATE.SLIDE_IN.id}
              className="ants-relative ants-h-full ants-w-full"
            />
          ),
        };
      }
    }
  }, [templateDetail, activeTab, state.previewUrl, pagePreviewThumbnails, onClickPreviewThumbnail]);

  const handleGoToContactInfo = () => {
    window.location.replace('https://www.antsomi.com/contact-us/');
  };

  // Effects
  useEffect(() => {
    (async () => {
      if (templateDetail) {
        switch (selectTemplateAction) {
          case 'edit': {
            // Do nothing
            break;
          }
          case 'preview':
          default: {
            if (activeTab === mediaTemplate) {
              if (state.previewId) {
                previewTemplateMedia({ type: POST_MESSAGE_TYPES.PREVIEW_CDP_CAMPAIGN_WAITING, id: state.previewId });
              } else {
                initPreviewMediaTemplate({
                  templateId: `${templateDetail.id}`,
                  zoneSelector: `#${TEMPLATE_PREVIEW_ID}`,
                  isPreview: true,
                });
              }
            }

            break;
          }
        }
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateDetail, selectTemplateAction, activeTab]);

  useEffect(() => {
    window.addEventListener('message', e => previewTemplateMedia(e.data));

    return () => {
      window.removeEventListener('message', e => previewTemplateMedia(e.data));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setState(prev => ({ ...prev, previewUrl: '' }));

    return () => {
      setState(prev => ({ ...prev, previewUrl: '' }));
    };
  }, [activeTab]);

  const previewTemplateMedia = ({ type, id }: { type: string; id: string }) => {
    if (type === POST_MESSAGE_TYPES.PREVIEW_CDP_CAMPAIGN_WAITING) {
      if (!state.previewId) {
        setState(prev => ({ ...prev, previewId: id }));
      }

      setTimeout(() => {
        window.postMessage(
          {
            ...templateDetail?.templateSettings,
            views: templateDetail?.settings.viewPages,
            messageType: POST_MESSAGE_TYPES.PREVIEW_ANTSOMI_CDP_CAMPAIGN,
            targetScript: id,
          },
          '*',
        );
      }, 100);
    }
  };

  return (
    <Wrapper>
      <div className="__container">
        <Tabs
          activeKey={activeTab}
          items={Object.values(GALLERY_TAB_MENU_ITEMS)}
          // shadow
          onChange={key => {
            onChangeTab(key);
            // Reset category filter
            onChangeCheckedCategories({});
          }}
        />

        <div className="ants-h-full ants-overflow-auto">
          <TemplateListing
            templatesProps={{
              items: templateItems,
              loading: isLoadingTemplateDetail,
              onLoadMoreTemplates,
            }}
            categoryListingProps={{
              items: categoryItems,
              loading: isLoadingCategoryList,
              checkedCategories,
              openKeys: openCategoryKeys,
              onOpenChange: onChangeOpenCategoryKeys,
              onMenuChange: onChangeCheckedCategories,
            }}
            templateItemProps={{
              showSkeleton: listingComponentProps.showSkeleton,
              height: listingComponentProps.cardHeight,
              editBtnProps: {
                text: t(translations.templateListing.useTemplate),
                onClick: handleGoToContactInfo,
              },
              previewBtnProps: {
                onClick: id => onSelectTemplate(id, 'preview'),
              },
              removable: false,
            }}
            blankTemplateProps={{
              show: false,
            }}
            previewModalProps={{
              bannerProps: {
                height: listingComponentProps.bannerHeight,
                children: listingComponentProps.children,
              },
              thumbnailProps: {
                thumbnails: listingComponentProps.thumbnails,
                onClickThumbnail: listingComponentProps.onClickThumbnail,
              },
              informationProps: {
                itemName: listingComponentProps.name,
                categoryListing: previewTemplateCategories,
                description: listingComponentProps.description,
                showDeviceRadios: listingComponentProps.showDeviceRadios,
                buttonText: t(translations.templateListing.useTemplate),

                /* Click use/create template button */
                onButtonClick: handleGoToContactInfo,
              },
              similarTemplateProps: {
                similarTemplates,
                similarCardProps: {
                  height: listingComponentProps.cardHeight,
                  onClickWrapper: id => {
                    onSelectTemplate(id, 'preview');
                  },
                },
              },
              loading: isLoadingTemplateDetail,
            }}
            emptyProps={{
              icon: <Icon type="icon-ants-media-template" />,
              description: t(translations.templateListing.emptyDescription),
            }}
          />
        </div>
      </div>
    </Wrapper>
  );
};
