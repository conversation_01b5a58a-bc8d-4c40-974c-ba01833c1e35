// Libraries
import styled from 'styled-components';
import tw from 'twin.macro';

export const Wrapper = styled.div`
  ${tw`ants-h-screen`}

  .__container {
    ${tw`ants-relative ants-flex ants-flex-col ants-w-full ants-h-full ants-bg-background`}

    .__redirect_cell {
      ${tw`ants-block ants-font-bold ants-truncate`}

      text-decoration-line: none !important;
    }
  }
`;

export const PreviewImageWrapper = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
`;
