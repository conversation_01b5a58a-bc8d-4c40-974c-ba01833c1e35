import * as React from 'react';
import { render } from '@testing-library/react';

import { Public } from '..';

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: str => str,
      i18n: {
        changeLanguage: () => new Promise(() => {}),
      },
    };
  },
}));

describe('<Public  />', () => {
  it('should match snapshot', () => {
    const loadingIndicator = render(<Public />);
    expect(loadingIndicator.container.firstChild).toMatchSnapshot();
  });
});
