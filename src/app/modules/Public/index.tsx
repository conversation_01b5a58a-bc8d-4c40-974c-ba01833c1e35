// Libraries
import { HashRouter as Router, Route, Switch, Redirect } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

// Routes
import routes from './routes';

interface Props {}
export const Public = (props: Props) => {
  const { i18n } = useTranslation();

  return (
    <Router>
      <Helmet htmlAttributes={{ lang: i18n.language }}></Helmet>
      <Switch>
        {routes.map(route => {
          const { exact, path, component, key } = route;
          return <Route key={key} path={path} exact={exact} component={component} />;
        })}
        <Redirect to="/public" />
      </Switch>
    </Router>
  );
};
